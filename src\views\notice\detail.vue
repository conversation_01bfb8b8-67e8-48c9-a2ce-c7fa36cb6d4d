<template>
  <div class="detail-wrapper">
    <page-title title="系统公告" />
    <div class="notice-title">
      <h2>{{detail.title}}</h2>
      <p>{{detail.createTime}}</p>
    </div>
    <div class="notice-content" v-html="detail.content"></div>
</div>
</template>

<script>
  import {detailNotice} from '@/api/group'
  export default {
    name: "detail",
    data() {
        return {
          id: '',
          detail: {},
          activeName: 'first'
        }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
        async getDetail() {
          this.id = this.$route.query.id
          if(this.id) {
            const {data} = await detailNotice(this.id)
            this.detail = data
          }
        }
    }
  }
</script>

<style lang="scss">
  .notice-title {
    border-bottom: 1px solid #ddd;
    text-align: center;
    margin: 0 40px;
    padding-top: 10px;
    margin-bottom: 20px;
  }
  .notice-content {
    min-height: 500px;
    width: 100%;
    padding: 0 40px;
    word-wrap:break-word;
  }
</style>
