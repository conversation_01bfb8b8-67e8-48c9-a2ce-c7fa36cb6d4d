<template>
  <div class="group_manager">
    <tabs-layout v-model="currentTab" ref="tabs-layout" :tabs="tabList">
      <template slot="button">
        <el-button size="mini" @click="setProductGroupList(true)">刷新</el-button>
      </template>
    </tabs-layout>
    <div class="group_inner">
      <el-button class="add_btn" type="primary" size="small" @click="showAddGroupDialog">新增分组</el-button>
    </div>
    <div class="group_list" v-loading="loading">
      <div :class="['group_item', currentGroupId === item.id ? 'activated' : '']" v-for="item in groupList" :key="item.id" @click="setCurrentGroupId(item.id)">
        <span class="name">{{ item.groupingName }}</span>
        <span class="btns" v-if="!['0', ''].includes(item.id)">
          <el-button type="text" size="mini" @click="editGroup(item)">编辑</el-button>
          <el-button type="text" size="mini" @click="deleteGroup(item)">删除</el-button>
        </span>
      </div>
    </div>
    <AddGroupDialog ref="addGroupDialogRef" @reload="setProductGroupList" />
  </div>
</template>

<script>
import AddGroupDialog from './addGroupDialog.vue';
import { batchDeleteGroup, getProductGroupList } from '@/api/retailStore'

export default {
  name: 'groupManager',
  components: {
    AddGroupDialog
  },
  data() {
    return {
      loading: false,
      currentTab: '1',
      currentGroupId: '',
      groupList: [],
      tabList: [
        { name: '分组管理', value: '1' },
      ],
    }
  },
  created() {
    this.setProductGroupList()
  },
  methods: {
    setCurrentGroupId(id, isUpdate) {
      this.currentGroupId = id
      this.$emit('updateGroupId', id, isUpdate)
    },
    showAddGroupDialog() {
      this.$refs.addGroupDialogRef.show()
    },
    editGroup(item) {
      this.$refs.addGroupDialogRef.show(item)
    },
    deleteGroup(item) {
      this.$confirm(`确定要删除【${item.groupingName}】分组吗`, '删除分组', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        batchDeleteGroup([item.id]).then(() => {
          this.$message.success('删除分组成功')
          this.setProductGroupList()
        })
      }).catch(() => {})
    },
    // 设置分组列表
    setProductGroupList(isUpdate=false) {
      this.loading = true
      getProductGroupList().then(res => {
        this.groupList = res.records
        this.groupList.unshift({ id: '', groupingName: '全部' })
        if (this.groupList.length > 0 && !this.groupList.find(item => item.id === this.currentGroupId)) {
          this.setCurrentGroupId(this.groupList[0].id, isUpdate)
        } else {
          this.setCurrentGroupId(this.currentGroupId, isUpdate)
        }
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.group_manager {
  height: 100%;
  background-color: #fff;
  padding: 0 10px 10px;
  .group_inner {
    box-sizing: border-box;

    .add_btn {
      width: 100%;
      margin-bottom: 10px;
    }
  }
  .group_list {
    .group_item {
      display: flex;
      height: 35px;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      &.activated {
        background-color: #ddd !important;
      }
      &:hover {
        background-color: #eee;
      }
    }
  }
}
</style>