<template>
  <div class="upload-image" @mouseover="showOver=true" @mouseout="showOver=false">
    <el-image :src="src"/>
    <div v-show="showOver" class="over-content">
      <i class="el-icon-delete delete-icon" @click="onDelete"/>
    </div>
    <div class="over-footer" @click="onSet" v-show="showOver">设置为主图</div>
  </div>
</template>

<script>
  export default {
    name: 'upload-image',
    props: {
      src: {
        type: String,
        required: true
      }
    },
    data () {
      return {
        showOver: false,
      };
    },
    methods: {
      onSet () {
        this.$emit('on-set')
      },
      onDelete () {
        this.$emit('on-delete');
      }
    }
  }
</script>

<style lang="scss" scoped>
  .upload-image {
    width: 148px;
    height: 148px;
    margin-right: 10px;
    position: relative;
  }
  .over-content {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    background-color:#000000;
    opacity: 0.4;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .over-footer {
    position: absolute;
    z-index: 2;
    background: #000;
    overflow: hidden;
    width: 100%;
    bottom: 0;
    left: 0;
    text-align: center;
    color: #FFF;
    line-height: 36px;
    cursor: pointer;
  }
  .delete-icon {
    font-size: 24px;
    color: #FFF;
    cursor: pointer;
  }
</style>
