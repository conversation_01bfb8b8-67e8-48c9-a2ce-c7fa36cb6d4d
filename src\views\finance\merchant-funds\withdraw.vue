<template>
  <div class="tab_bg" style="position: relative;">
    <tabs-layout
      v-model="activeName"
      :tabs="[{ name: '提现', value: '1' }, { name: '提现记录', value: '2' }]" />
    <template v-if="activeName === '1'">
      <el-form label-width="90px" :model="caseOutForm" :rules="rules" class="caseOutForm" ref="caseOutForm">
        <el-form-item label="提现金额：" prop="amount">
          <el-input v-model="caseOutForm.amount"></el-input><span class="balance">可提现余额{{balance|getDecimals}}（元）</span>
        </el-form-item>
        <el-form-item label="提现账号：">
          <p>开户名称：{{caseOut.bankAccount}}</p>
          <p>账户号码：{{caseOut.bankNumber}}</p>
          <p>开户银行：{{caseOut.bankName}}</p>
          <p>用户手机号：{{caseOut.ceoMobile}}</p>
        </el-form-item>
        <el-form-item label="验证码：" prop="verificationCode">
          <el-input v-model="caseOutForm.verificationCode" />
          <el-button v-if="!ifSend" type="primary" style="margin-left: -5px;" @click="sendCode">发送验证码</el-button>
          <el-button v-if="ifSend" type="primary" style="margin-left: -5px;">{{ times }}s</el-button>
          <p v-if="ifSend">验证码将发送至您的手机：{{ceoMobile}}</p>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-if="checkPermission(['admin','caseOut:submit'])" :disabled="balance === 0" @click="submitForm('caseOutForm')">确定</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template v-if="activeName === '2'">
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData" :isNeedButton="false">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </template>
  </div>
</template>

<script>
  import {caseOut,caseOutList,getMerchantDetails,financeCapitalAccount,send,codeVerify} from '@/api/finance'
  const TableColumns = [
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "业务类型", name: "businessType.desc", prop:"businessType.desc",width: "80" },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: '90' },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName",width: '200' },
    { label: "金额（元）", name: "amount", prop:"amount",slot: true  },
    { label: "制单人", name: "createUserName",prop:'createUserName',width: "100" },
    { label: "制单时间", name: "createTime",prop:'createTime',width: "170" },
    { label: "备注", name: "remarks", prop:"remarks"  },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "@/utils/permission";
export default {
  name: "widthdraw",
  data() {
    var validateAmount = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入提现金额'));
        } else {
          if (value > this.balance || value <= 0) {
            callback(new Error('提现金额必须小于可提现金额且大于0'));
          } else {
            callback()
          }
        }

        }
    return {
      tableTitle: TableColumnList,
      activeName: '1',
      ifSend: false,
      tableData: [],
      caseOut: {
        bankNumber: '',
        bankName: '',
        bankAccount: ''
      },
      ceoMobile: '',
      balance: 0,
      times: 60,
      caseOutForm: {
        amount: '',
        verificationCode: ''
      },
      rules:{
        amount: [{validator: validateAmount, trigger: 'blur' }],
        verificationCode: [{required: true, message: '请填写验证码', trigger: 'blur'}]
      }
    }
  },
  mounted() {
    this.getCaseOut();
    this.getBalance();
  },
  methods: {
    checkPermission,
    getCaseOut() {
      //const {data} = await getMerchantDetails()
      try {
        const data = JSON.parse(sessionStorage.getItem('merchants-detail'));
        this.caseOut = data;
        console.log('--------->',data);
        this.ceoMobile=data.ceoMobile? data.ceoMobile.substring(0,3)+"****"+data.ceoMobile.substring(8,11):''; 
      } catch (error) {
        console.log('error---->',error);
      }
    },
    async getBalance() {
      console.log('=====================');
      const {data} = await financeCapitalAccount()
      if(data) {
        this.balance = data.amount
      }

    },
    async load(params) {
      this.loading = true
      const listQuery = {
        model: {}}
      Object.assign(listQuery, params)
      return await caseOutList(listQuery)
    },
    sendCode() {
      if(this.caseOut && (this.caseOut.ceoMobile==null || this.caseOut.ceoMobile == '')){
        this.$message.warning('手机号不能为空');
        return
      }
      this.times = 60
      this.ifSend = true
      const params = {
        mobile: this.caseOut.ceoMobile,
        type: 'WITHDRAWAL'
      }
      send(params).then(res=>{
        this.timer = setInterval(()=>{
          this.times--
          if(this.times === 0){
            this.ifSend = false
            clearInterval(this.timer)
          }
        },1000)
      })
    },
    handleClick(val) {
      if(val.name==='2') {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      }

    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    submitForm(formName) {
      let _this = this
      this.$refs[formName].validate(async valid => {
        if (valid) {
          const params = {
            "code": this.caseOutForm.verificationCode,
            "mobile": this.caseOut.ceoMobile,
            "type": "WITHDRAWAL",
          }
          const data = await codeVerify(params)
          if(data.code===0) {
            const query = {
              ..._this.caseOutForm,
              mobile: _this.caseOut.ceoMobile,
              account: _this.caseOut.bankNumber,
              accountBank: _this.caseOut.bankName,
              accountName: _this.caseOut.bankAccount
            }
            const data = await caseOut(query)
            if(data.code === 0) {
              this.ifSend = false
              clearInterval(this.timer)
              this.$confirm('提现申请提交成功，一般预计3-5个工作日到账，实际到账以具体渠道处理为准，', '提现操作成功！').then(_ => {
                this.getBalance()
                this.caseOutForm = {
                  amount: '',
                  verificationCode: ''
                }
              }).catch(_ => {

              });
            }
          }


        } else {
          return false;
        }
      })
    }

  }
}
</script>

<style lang="scss">
  .caseOutForm {
    p {
      margin: 0;
    }
    .el-input {
      width: 200px;
    }
    .balance {
      padding-left: 10px;
      color: #999;
    }
  }
</style>
