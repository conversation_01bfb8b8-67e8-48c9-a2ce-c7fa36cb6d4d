<template>
  <div class="archivesPageContent">
    <div class="tab_bg">
      <tabs-layout :tabs="[ { name: '内容分类' } ]">
        <template slot="button">
          <el-button type="primary" v-if="checkPermission(['admin' , 'sale-saas-content-manage-helpCategory:add', 'sale-platform-content-manage-helpCategory:add'])" @click="newFun(parentItem)">+新增分类</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table ref="tableDom" border :data="list" v-loading="listLoading" style="width: 100%; margin-bottom: 20px" row-key="id">
          <el-table-column :align="item.prop == 'label' || item.prop == 'id' ? 'left' : 'center'" v-for="(item, index) in tableList" :key="index" :label="item.label" :prop="item.prop"
                          :min-width="item.width ? item.width : '350px'">
            <template slot-scope="scope">
            <span v-if="item.prop == 'frontEndDisplay'" :class="
                scope.row[item.prop] == 'Y'
                  ? 'el-tag el-tag--success'
                  : 'el-tag el-tag--warning'
              ">{{ scope.row[item.prop] == "Y" ? "是" : "否" }}</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin' , 'sale-saas-content-manage-helpCategory:edit', 'sale-platform-content-manage-helpCategory:edit'])" class="table-edit-row-item">
                  <el-button @click="editFun(scope.row)"  type="text">编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin' , 'sale-saas-content-manage-helpCategory:del', 'sale-platform-content-manage-helpCategory:del'])" class="table-edit-row-item">
                  <el-button @click="delFun(scope.row)"  type="text">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog append-to-body v-dialogDrag :title="(informationForm.id ? '编辑' : '新增') + '分类'" :visible.sync="editFlag" width="30%" @close="close" :close-on-click-modal="false">
      <el-form ref="editForm" :model="informationForm" label-width="130px">
        <el-form-item v-if="!informationForm.id" class="formItem" prop="pLabel" label="父分类:" :rules="[
            { required: true, message: '请选择父分类编码', trigger: 'blur' },
          ]">
          <el-input :disabled="true" style="width: 250px" v-model="informationForm.pLabel" placeholder="请填写分类名称"></el-input>
        </el-form-item>
        <el-form-item class="formItem" prop="label" label="分类名称:" :rules="[
            { required: true, message: '请填写分类名称', trigger: 'blur' },
          ]">
          <el-input clearable style="width: 250px" v-model="informationForm.label" placeholder="请填写分类名称"></el-input>
        </el-form-item>

        <el-form-item class="formItem" prop="frontEndDisplay" label="是否前端显示:" :rules="[
            { required: true, message: '请填写是否前端显示', trigger: 'blur' },
          ]">
          <el-radio v-model="informationForm.frontEndDisplay" label="Y">前端显示</el-radio>
          <el-radio v-model="informationForm.frontEndDisplay" label="N">前端隐藏</el-radio>
        </el-form-item>
        <el-form-item class="formItem" prop="sortValue" label="分类排序:">
          <el-input-number clearable style="width: 250px" v-model="informationForm.sortValue" placeholder="请填写分类排序"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission';
import pagenation from '@/components/Pagination';
import {
  add,
  category,
  delFun,
  edit,
} from '@/api/content-center/category';
export default {
  data() {
    return {
      tableList: [
        {
          label: "分类名称",
          prop: "label",
        },
        {
          label: "分类ID",
          prop: "categoryCode",
          width: "200px",
        },
        {
          label: "排序",
          prop: "sortValue",
          width: "130px",
        },
        {
          label: "是否前端显示",
          width: "130px",
          prop: "frontEndDisplay",
        },
      ],
      list: [],
      informationForm: {
        sortValue: 1,
        publishStatus: false,
        parentCategoryCode: "3",
        frontEndDisplay:'Y',
        label: "",
      },
      parentItem: {
        label: "章节",
        categoryCode: "0003",
        parentId: "2",

      },
      editFlag: false,
      options: [],
      categoryId: "",
      loadNodeMap: new Map(),
      total: 10,
      listLoading: false,
    };
  },
  methods: {
    checkPermission,
    newFun(row) {
      this.informationForm.pLabel = row.label;
      this.informationForm.parentCategoryCode = row.categoryCode;
      this.informationForm.parentId = row.parentId;
      this.informationForm.level = row.level;
      this.editFlag = true;
    },
    async category(parentId) {
      let { data } = await category({
        parentId: parentId,
      });
      this.options = data;
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await category({ parentId: this.parentItem.parentId });
      data.forEach((item) => {
        item.frontEndDisplay = item.frontEndDisplay.code;
        item.hasChildren = true;
        item.length = "0";
        item.level = 0;
      });
      this.listLoading = false;
      this.list = data;
    },
    async delFun(row) {
      this.$confirm("此操作将永久该分类, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { data } = await delFun({
          ids: [row.id],
        });
        if (data) {
          this.$message.success("已删除-" + row.label + "-的分类");
          this.getlist();

        }
      });
    },
    editFun(row) {
      this.informationForm = JSON.parse(JSON.stringify(row));
      this.editFlag = true;
    },
    async submit() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          if (this.informationForm.id) {
            let { data } = await edit(this.informationForm);
            if (data) {
              this.list.forEach((item, index) => {
                if (item.id == this.informationForm.id) {
                  this.$set(this.list, index, this.informationForm);
                }
              });
              this.$message.success("修改分类成功");
              this.editFlag = false;
            }

          } else {
            let { data } = await add(this.informationForm);
            if (data) {
              this.$message.success("添加分类成功");
              this.editFlag = false;
              this.getlist();
            }

          }
        } else {
          this.$message.error("请检查表单是否完成");
        }
      });
    },
    close() {
      this.editFlag = false;
      this.informationForm = {};
    },
    getRowKeys(row) {
      return row.id;
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  created() {
    this.getlist();
  },
  components: {
    pagenation,
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #efefef;
}
</style>
