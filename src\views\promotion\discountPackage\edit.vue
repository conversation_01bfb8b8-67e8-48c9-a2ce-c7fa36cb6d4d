<template>
  <div class="detail-wrapper">
    <page-title :title="id ? '编辑套餐' : '新增套餐'">
      <template>
        <el-button type="primary" @click="submit">保存</el-button>
      </template>
    </page-title>
    <el-form label-width="120px" ref="ruleForm" :model="form" :rules="rules">
      <div class="detail-items" v-loading="loading">
        <page-module-card title="基础信息">
          <el-form-item
            label="套餐名称："
            prop="name"
            :rules="[{ required: true, message: '请输入套餐名称' }]"
            style="width:370px;"
          >
            <el-input v-model="form.name" placeholder="请输入套餐名称，最多20个字"></el-input>
          </el-form-item>
          <el-form-item
            label="销售状态："
            required
          >
            <el-radio-group v-model="form.promotionPackageState">
              <div class="block-radio block-radio-top">
                <el-radio label="ON">上架</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="OFF">下架</el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="套餐商品：" prop="productRelList">
            <div>
              <div>
                <p style="font-size: 12px; color: #B4B6BD;" class="block-radio-none" >最多选择10个商品</p>
                <el-button type="primary" @click="openProducts()">选择商品</el-button>
              </div>
              <el-table
                style="margin-top:15px;"
                v-if="goodsList.length > 0"
                :data="goodsList"
                border
              >
                <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
                <el-table-column label="主商品" :render-header="renderTip" prop="main" align="center" width="90">
                  <template slot-scope="{row}">
                    <el-checkbox v-model="row.main" @change="checkBoxChange(row)"/>
                  </template>
                </el-table-column>
                <el-table-column label="主图" align="center" width="64" class-name="img-cell">
                  <template slot-scope="{row}">
                    <img :src="splitString(row.pictIdS)" width="50px" v-if="row.pictIdS">
                    <img :src="pictImg" width="50px" v-if="row.pictIdS == null || row.pictIdS == ''">
                  </template>
                </el-table-column>
                <el-table-column label="ERP商品编码/仓库" prop="productCode" width="200px">
                  <template v-slot="{row}">
                    <span>编码：{{row.erpCode || '无'}}</span> <br />
                    <span>仓库：{{ row.warehouseName || '无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="productName" min-width="200px"></el-table-column>
                <el-table-column label="规格" prop="spec" width="200px"></el-table-column>
                <el-table-column label="生产厂家" prop="manufacturer" min-width="200px"></el-table-column>
                <el-table-column label="套餐单价" prop="price" width="122">
                  <template slot-scope="scope">
                    <el-input-number class="no-button"  v-model="scope.row.price"  controls-position="right" @change="getTotalPrice(scope.row)" @blur="getItemPrice(scope.row)" :min="0" :max="Number(scope.row.salePrice)" style="width:100px;"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="num" width="122">
                  <template slot-scope="scope">
                    <el-input-number class="no-button"  v-model="scope.row.num"  controls-position="right" @change="getTotalPrice(scope.row)" @blur="getItemNum(scope.row)" :min="1" :max="Number(scope.row.stockQuantity)" style="width:100px;"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="销售价" prop="salePrice" width="100"/>
                <el-table-column label="成本价" prop="costPrice" width="100" />
                <el-table-column label="库存" prop="stockQuantity" width="100" />
                <el-table-column
                  label="操作"
                  prop="stockQuantity"
                  width="100"
                  v-if="!form.couponStatus || form.couponStatus.code == 'NOT_START'"
                  align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="deleteRowGoods(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <p style="font-size: 14px; color: #505465;">已选 {{goodsList.length || 0}} 个商品</p>
            </div>

          </el-form-item>
          <el-form-item
            label="套餐价格："
            prop="totalPrice"
            :rules="[{ required: true, message: '系统自动计算' }]"
            style="width:370px;"
          >
            <el-input v-model="form.totalPrice" placeholder="系统自动计算" :disabled="true">
              <span slot="prepend">￥</span>
            </el-input>
          </el-form-item>
          <el-form-item
            label="套餐主图："
            prop="image"
            disabled="true"
            :rules="[{ required: true, message: '请上传套餐主图',trigger: 'change' }]"
          >
            <el-upload
              action
              class="avatar-uploader"
              :show-file-list="false"
              :before-upload="handleBefore"
              :http-request="upload"
              accept=".jpg,.png,.bmp,.jpeg">
                <img class="el-upload-list__item-thumbnail" v-if="form.image"  :src="form.image" style="width: 100px;height: 100px;border: 1px dashed #ddd;">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>

            </el-upload>
            <p style="margin-top: -10px;color: #b4b6bd;">用于商品详情或套餐专题页显示套餐入口，建议尺寸800x800px</p>
          </el-form-item>
        </page-module-card>
        <page-module-card title="套餐规则">
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{  required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                </el-radio>
                <el-checkbox-group style="margin-top: 10px;" v-model="form.customerTypeIds" v-if="merchantList.length > 0 && form.limitObjectType === 'CUSTOMER_TYPE' ">
                  <el-checkbox
                    v-for="(item) in merchantList"
                    :key="item.id"
                    :label="item.id">
                    {{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>
                <div style="margin: 15px 0;" v-if="form.limitObjectType == 'CUSTOMER_GROUP'">
                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>
                </div>
                <el-table
                  v-if="groupTableData && form.limitObjectType == 'CUSTOMER_GROUP' "
                  :data="groupTableData"
                  border
                >
                  <el-table-column
                    prop="name"
                    label="客户分组"
                    width="234" />
                  <el-table-column
                    prop="customerNumber"
                    label="客户数量"
                    width="120" />
                  <el-table-column
                    label="操作"
                    width="52">
                    <template slot-scope="scope">
                      <el-button @click="deleteRow(scope.$index)" type="text">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
    <!-- <products-modal ref="products-modal" @change="handleAddProducts" :checkList="goodsList" :limit="limit"/> -->
    <div v-if="productVisible">
      <products-modal 
          :productVisible="productVisible" 
          ref="products-modal"  
          @closeProductDia="closeProductDia" 
          @change="handleAddProducts" 
          :checkList="goodsList" 
          :limit="limit">
      </products-modal>
    </div>
    <add-group :visible="showAdd" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser" :select-data="groupTableData" />
  </div>
</template>

<script>
  import request from '@/utils/request'
  import addGroup from '@/views/promotion/components/addGroup.vue'
  import ProductsModal from '@/components/eyaolink/productModel/index'
  import { merchantGroupListNew } from "@/api/group";
  import { postGoodsList } from "@/api/promotion"
  import productImg from "@/assets/product.png";
  import { getUploadFileUrl,beforeFileUpload } from '@/api/upload'

  export default {
    components: {
      ProductsModal,
      addGroup
    },
    data() {
      var validateImage = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请选择套餐商品'));
        } else {
          callback()
        }
      }
      return {
        pictImg: productImg,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        },
        id: '',
        loading: false,
        showAdd: false,
        saleMerchantId: '', //经销商id
        active: 0,
        merchantList: [],   // 客户类型列表
        groupTableData: [], // 已选分组数据
        form: {
          promotionPackageState: 'ON',
          name: '',
          totalPrice: '',
          image: '',
          limitObjectType: 'NONE',//指定人类型 NONE,CUSTOMER_TYPE,CUSTOMER_GROUP,CUSTOMER,UN_CUSTOMER
          customerTypeIds: [], //客户类型
          productRelList: [],
          customerGroupIds: [],//分组集合
        },
        total: 0,
        rules: {
          productRelList: [{required: true, validator: validateImage, trigger: 'blur'}]
        },
        goodsList: [],
        limit: 10,
        productVisible: false,
        timer: null
      }
    },
    created() {
      this.id = this.$route.query.id;
      this.getMerchantType()
      this.getDetail()
    },
    methods: {
      /**
       * @describe 关闭弹窗
       */
      closeProductDia(){
        this.productVisible = false;
      },
      renderTip(h, {column}) {
        return ( <el-tooltip class="tooltip" effect = "dark" placement="top">
          <p slot="content"> 设为主商品后，可在前端的商品详情页展示该套餐，如不设置，则不展示 </p>
        <p> 主商品 <i class="el-icon-question"> </i></p>
        </el-tooltip>);
      },
      //套餐价设置
      getItemPrice(row) {
        if (!row.price) {
          this.$message.warning('请设置套餐单价')
        } else {
          this.getTotalPrice()
        }
      },
      //打开商品
      openProducts() {
        this.form.productRelList = this.goodsList
        this.productVisible = true;
      },
      getItemNum(row) {
        if (!row.num) {
          this.$message.warning('请设置套餐商品数量')
          this.row.num = 1
        } else {
          this.getTotalPrice()
        }
      },
      checkBoxChange(row) {
        if (row.main) {
          row.main = true
        } else {
          row.main = false
        }
      },
      async upload(fileObj) {
        const {data} = await getUploadFileUrl(fileObj.file)
        this.form.image = data.url
      },
      handleBefore(file) {
        return beforeFileUpload(file, this.$message)
      },
      // 获取详情
      async getDetail() {
        if (this.id) {
          let {data} = await request.get(`product/merchant/package/${this.id}`)
          this.form = data
          this.form.limitObjectType = data.limitObjectType.code;
          this.form.promotionPackageState = data.promotionPackageState.code
          this.form.productRelList = data.productRelList
          this.goodsList = data.productRelList
          if (data.customerGroupIds.length > 0) {
            this.getGroupList();
          }
          if (this.goodsList.length > 0) {
            this.getCheckGoodsList()
          }
          if (data.customerTypeIds.length > 0) {

          }
        }
      },
      // 获取已有客户分组列表
      async getGroupList() {
        this.loading = true;
        const query = {
          model: {}
        }
        const {data} = await merchantGroupListNew(query);
        this.loading = false;
        this.groupTableData = data.records.filter((items) => {
          if (this.form.customerGroupIds.includes(items.id)) {
            return {
              items
            };
          }
        })
      },
      //套餐价格
      getTotalPrice() {
        this.total = 0
        this.goodsList.filter(items=>{
          let salePrice = Number(items.price)
          this.total += salePrice * items.num
          this.form.totalPrice = this.total.toFixed(2)
        })
      },
      // 获取已有的产品列表
      async getCheckGoodsList(ids) {
        const formData = new FormData()
        let idArr = ids ? ids : this.form.productRelList.map((items) => items.productId)
        formData.append('ids', idArr.toString())
        let {data} = await postGoodsList(formData)
        this.goodsList = []
        this.form.totalPrice = 0
        this.goodsList = data.filter((items,index) => {
          if (idArr.includes(items.id)) {
            if (this.form.productRelList.length === 0) {
              this.$set(items, 'main', true)
              this.$set(items, 'num', 1)
              this.$set(items, 'price', items.salePrice)
            } else {
              if(this.form.productRelList.length-1 < index) {
                this.$set(items, 'main', true)
                this.$set(items, 'num', 1)
                this.$set(items, 'price', items.salePrice)
              } else {
                let main = this.form.productRelList[index].main
                if(main.code==='Y') {
                  main = true
                } else if(main.code === 'N') {
                  main = false
                }
                this.$set(items, 'main', main)
                this.$set(items, 'num', this.form.productRelList[index].num)
                this.$set(items, 'price', this.form.productRelList[index].price)
              }
              let salePrice = Number(items.price)
              this.form.totalPrice += (salePrice * items.num)
            }
            return items;
          }
        })
        this.getTotalPrice()
      },
      // 清空表单
      resetForm() {
        this.$refs['ruleForm'].resetFields();
      },
      // 提交表单
      submit() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            if (this.form.limitObjectType === 'CUSTOMER_TYPE'&&this.form.customerTypeIds.length===0) {
              this.$message.warning('请点击选择相关的客户类型')
              return false
            } else if(this.form.limitObjectType === 'CUSTOMER_GROUP'&&this.form.customerGroupIds.length===0) {
              this.$message.warning('请点击选择相关的客户分组')
              return false
            }
            // 选择指定客户类型
            if (this.form.limitObjectType === 'CUSTOMER_TYPE') {
              this.form.customerGroupIds = []
            }
            if(this.form.limitObjectType === 'CUSTOMER_GROUP') {
              this.form.customerTypeIds = []
            }
            // 商品ids
            if (this.goodsList.length > 0) {
              this.form.productRelList = this.goodsList.map((item) => {
                const main = item.main ? 'Y' : 'N'
                return {
                  productId: item.id,
                  price: item.price,
                  num: item.num,
                  main: main
                }
              })
            }
            // 是否有id
            if (this.id) {
              this.form.id = this.id
            } else {
              delete this.form.id
            }
            this.loading = true;
            let method = this.id ? request.put('product/merchant/package', this.form) : request.post('product/merchant/package', this.form)
            method.then((res) => {
              if (res.code == 0) {
                this.loading = false;
                this.$message.success('保存成功')
                if(!this.id) {
                  sessionStorage.setItem('discount','123')
                }
                this.timer = setTimeout(() => {
                  this.$router.push({
                    path: '/promotion/discount/list'
                  })
                }, 500)
              } else {
                this.loading = false;
                this.$message.error(res.msg)
              }
            }).catch(() => {
              this.loading = false;
            })

          }
        });
      },
      // 删除选中客户组
      deleteRow(i) {
        this.groupTableData.splice(i, 1)
      },
      deleteRowGoods(i) {
        this.goodsList.splice(i, 1)
        this.$refs['products-modal'].isSelect = true;
      },
      // 改变客户分组回调
      changeAddUser(data) {
        this.groupTableData =  data;
        this.form.customerGroupIds = data.map((items) => items.id)
        this.showAdd = false
      },
      // 选择商品后回调
      handleAddProducts(list) {
        this.getCheckGoodsList(list)
      },
      // 获取客户类型
      async getMerchantType() {
        this.loading = true;
        const {data} = await request.get('merchant/admin/merchantType', {})
        this.loading = false;
        this.merchantList = data;
      },
      splitString(val) {
        return String(val).split(',')[0]
      }
    },
    beforeDestroy() {
      clearTimeout(this.timer);
    }
  }
</script>

<style lang="scss">
  .detail-wrapper {
    .el-input.is-disabled .el-input__inner {
      //margin-left: -10px;
    }
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    background: #f7f7f8;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 20px;
    color: #8c939d;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .detail-item{
    border: none !important;
  }
  .detail-tit{
    display: flex;
    align-items: center;
    margin: 0;
  }
  .inline-input{
    width: 80px;
    margin: 0 10px;
  }
  .inline-item{
    display: inline-block;
  }
  .block-radio{
    margin-bottom: 16px;
    &-top{
      margin-top: 11px;
    }
    &-none{
      margin: 0;
    }
  }
  .detail{
    &-header{
      // width: 100%;
      margin: 0 12px;
      padding: 19px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ddd;
      p{
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  .tips{
    color: #999999;
    margin: 0;
    &-btn{
      border: none;
      margin: 0;
      padding: 0;
    }
  }
  .no-button {
    .el-input-number__decrease,.el-input-number__increase {
      display: none;
    }
  }
</style>
