import requestAxios from '@/utils/requestAxios'
import qs from 'qs'

// 获取分页数据列表
export  function list(data) {
  return requestAxios({
    url: '/merchant/admin/feedback/page',
    method: 'post',
    data
  })
}

// 处理
export  function dispose(data) {
  return requestAxios({
    url: '/merchant/admin/feedback/updateFeedbackHandleStatusById',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// 删除
export  function del(data) {
  return requestAxios({
    url: '/merchant/admin/feedback',
    method: 'delete',
    params: data
  })
}
