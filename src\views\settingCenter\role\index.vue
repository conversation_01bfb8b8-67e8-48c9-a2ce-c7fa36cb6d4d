<template>
  <div class="tab_bg">
    <tabs-layout :tabs="[{ name: '角色管理', value: '' }]">
      <template slot="button">
        <el-button  @click="getList()">刷新</el-button>
        <el-button v-if="checkPermission(['admin','sale-saas-setting-manage-role:add', 'sale-platform-setting-manage-role:add'])"  type="primary" @click="editClickFun(null)">+新增角色</el-button>
      </template>
    </tabs-layout>
    <div class="table">
      <el-table
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="60"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :width="item.width"
          :min-width="(item.width?item.width:'350px')"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span v-if="item.name=='status'">{{ row[item.name]?'启用':'禁用' }}</span>
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="100"
          class="itemAction"
        >
          <template slot-scope="{row}">
            <el-row class="table-edit-row">
              <span v-if="checkPermission(['admin','sale-saas-setting-manage-role:edit', 'sale-platform-setting-manage-role:edit'])" class="table-edit-row-item">
                <el-button type="text" @click="editClickFun(row)">编辑</el-button>
              </span>
              <span v-if="checkPermission(['admin','sale-saas-setting-manage-role:del', 'sale-platform-setting-manage-role:del'])" class="table-edit-row-item">
                <el-button type="text" @click="deleteFun(row)">删除</el-button>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-if="total>0" :page-sizes="[ 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <!-- 设置 编辑 -->
    <el-dialog v-if="showEdit" :close-on-click-modal="false" :title="(row.id>0?'编辑':'新增')+'角色'" :show-close="false" :visible.sync="showEdit" :width="'1000px'">
      <edit :visible.sync="showEdit" :is-reload.sync="submitReload" :row.sync="row" />
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import { setContextData, getContextData } from '@/utils/auth'
import checkPermission from '@/utils/permission'
import { list, deleteApi } from '@/api/setting/permission/userRole'
import edit from '@/views/settingCenter/role/edit'
import Pagination from '@/components/Pagination'
export default {
  components: {
    Pagination,
    edit
  },
  data() {
    return {
      showEdit: false,
      row: {
        id: 0,
        type: '',
        name: ''
      },

      tableTitle: [
        {
          label: '角色名称	',
          name: 'name',
          width: '150px'
        },
        {
          label: '角色状态	',
          name: 'status',
          width: '150px'
        },
        {
          label: '描述',
          name: 'describe'
        }
      ],
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model: {},
        current: 1,
        size: 10
      },
      titles: ['显示字段项', '隐藏字段项'],
      showSelectTitle: false
    }
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.getList()
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path === '/settingCenter/role/edit') {
        vm.listQuery = getContextData('permission_role_list')
      }
      vm.getList()
    })
  },
  mounted() {
    this.getList()
  },
  beforeDestroy() {},
  methods: {
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (<div style='position:relative'>
        <div onClick={this.setHeaer}>
          <i class='el-icon-menu' />
        </div>
        <el-dialog
          title='设置显示列表'
          showClose={false}
          visible={this.showSelectTitle}
          width='640px'
          center
        >
          <el-transfer
            vModel={this.tableVal}
            data={this.tableTitle}
            titles={this.titles}
          ></el-transfer>
          <div style='margin-top: 25px;text-align: center;'>
            <el-button onClick={this.cancel}>
        取消
            </el-button>
            <el-button type='primary' onClick={this.setHeaer}>
        确认
            </el-button>
          </div>
        </el-dialog>
      </div>)
    },
    cancel: function() {
      this.showSelectTitle = false
    },
    setHeaer: function() {
      this.showSelectTitle = !this.showSelectTitle
    },
    checkPermission,
    editClickFun(row) {
      setContextData('permission_role_list', this.listQuery)
      this.$router.push({ path: '/settingCenter/permission/role/edit',
        query: {
          id: row === null ? undefined : row.id
        }
      })
    },
    deleteFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async actionDeleteFun(row) {
      var data = await deleteApi(row.id)
      if (data.code === 0) {
        this.listQuery.current = 1
        this.getList()
      }
    },
    async getList() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
