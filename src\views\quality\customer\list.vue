<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="handleReset"
      @search="onSubmit"
    >
      <im-search-pad-item prop="purMerchantCode">
        <el-select v-model="listQuery.model.licenseStatusIsNormal.code" placeholder="请选择资质状态">
          <el-option label="异常" value="N" />
          <el-option label="正常" value="Y" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantCode">
        <el-input v-model.trim="listQuery.model.purMerchantCode" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="address">
        <el-cascader
          v-model="address"
          placeholder="请选择所在区域"
          :options="categoryOpts"
          :props="{ expandTrigger: 'hover',
                    value: 'id',
                    label: 'label',
                    children: 'children'}"
          clearable
          @change="parentChangeAction"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="ceoName">
        <el-input v-model.trim="listQuery.model.ceoName" placeholder="请输入负责人" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[{ name: '客户资质', value: '' }]" />
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <el-table-column label="资质状态"  width="140" slot="licenseStatusIsNormal">
          <slot slot-scope="scope">
            <el-badge :value="scope.row.abnormalNumber" class="item">
              <span v-if="scope.row.licenseStatusIsNormal && scope.row.licenseStatusIsNormal.code=='Y'" style="color:#409EFF; padding-top: 7px; padding-bottom: 7px;">正常</span>
              <span v-else style="color:#ff3c54; padding-top: 7px; padding-bottom: 7px;">异常</span>
            </el-badge>
          </slot>
        </el-table-column>  
        <div slot-scope="props">
          <!--  此处已经跳转到了客户管理的客户详情里面去了-->
          <el-button v-if="checkPermission(['admin','qualityCustomer:detail'])" type="text" @click="$router.push({path: '/merchant/clientDetail', query: { id: props.row.id } })">查看详情</el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { postMerchantLicense } from '@/api/quality'
import { trees } from '@/api/group'
import checkPermission from '../../../utils/permission'

const TableColumns = [
  { label: '资质状态', name: "licenseStatusIsNormal", prop:'licenseStatusIsNormal',width: 150,slot:true },
  { label: '客户编码', name: 'code', prop: 'code', width: 150 },
  { label: '客户名称', name: 'name', prop: 'name', width: 150 },
  { label: '社会统一信用代码', name: 'socialCreditCode', prop: 'socialCreditCode', width: 150 },
  { label: '法人代表', name: 'legalPerson', prop: 'legalPerson', width: 150 },
  { label: '企业类型', name: 'merchantType', prop: 'merchantType', width: 150 },
  { label: '负责人', name: 'ceoName', prop: 'ceoName', width: 150 },
  { label: '联系电话', name: 'ceoMobile', prop: 'ceoMobile', width: 150 },
  { label: '所在地区', name: 'region', prop: 'region', width: 150 },
  { label: '资质过期提醒', name: 'licenseExpireRemind', prop: 'licenseExpireRemind', width: 150 },
  { label: '创建时间', name: 'createTime', prop: 'createTime', width: 150 }
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  data() {
    return {
      isExpand: false,
      address: '',
      activeName: 'first',
      categoryOpts: [],
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      tableData: [],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        current: 1,
        size: 8,
        model: {
          purMerchantCode: '',
          purMerchantName: '',
          provinceId: '',
          cityId: '',
          countyId: '',
          ceoName: '',
          merchantGroupId: '',
          licenseStatusIsNormal: {
            code: 'N'
          }
        }
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: []
    }
  },
  created() {
    this.postMerchantLicense()
    this.getTrees()
  },
  methods: {
    checkPermission,
    // 获取地区
    async getTrees() {
      const { data } = await trees()
      this.categoryOpts = data
    },
    // 获取客商资质列表
    async postMerchantLicense() {
      const param = {
        ...this.listQuery
      }

      this.listLoading = true
      const { data } = await postMerchantLicense(param)
      this.list = data.records
      this.total = data.total
      this.page = data.current
    },
    parentChangeAction(val) {
      this.listQuery.model.countyId = val[0]
      this.listQuery.model.cityId = val[1]
      this.listQuery.model.countyId = val[2]
    },
    handleReset() {
      this.listQuery.model = {
        purMerchantCode: '',
        purMerchantName: '',
        provinceId: '',
        cityId: '',
        countyId: '',
        ceoName: '',
        merchantGroupId: '',
        licenseStatusIsNormal: {
          code: 'N'
        }
      }
      this.address = '';
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listLoading = true
      Object.assign(this.listQuery, params)
      return await postMerchantLicense(this.listQuery)
    }
  }
}
</script>

<style lang="scss" scoped>
  .detailTable{
    background: #fff;
    padding: 20px;
  }
  .tab_bg ::v-deep.el-badge__content.is-fixed {
     transform: translateY(0%) translateX(0%);
     right: unset;
  }
</style>
