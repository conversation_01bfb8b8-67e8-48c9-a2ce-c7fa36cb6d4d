<template>
  <div class="manual_quality_control">
    <div class="list">
      <div class="top">
        <el-tabs v-model="query.model.listType" @tab-click="search">
          <el-tab-pane v-for="item in listTypeList" :label="item.label" :name="item.value" />
        </el-tabs>
        <el-button class="refresh" size="mini" @click="search">刷新</el-button>
      </div>
      <el-select v-if="query.model.listType === 'MONTH'" v-model="query.model.searchYear" placeholder="请选择年份" @change="changeSearchYear">
        <el-option v-for="item in yearList" :key="item" :label="item" :value="item" />
      </el-select>
      <div class="task_list" v-loading="loading" v-infinite-scroll="load">
        <TaskItem
          v-for="item in levelDataList"
          :activated="item.id === currentItemId"
          :key="item.id" :item="item"
          @click="updateCurrentTaskId"
        />
        <div v-if="levelDataList.length === 0 && !loading" style="text-align: center;color: #999;">暂无数据</div>
      </div>
    </div>
    <div class="content">
      <template v-if="currentItem">
        <div class="title">{{ currentItem.listName }}</div>
        <div class="level_statistic">
          <div class="statistic_item" v-for="item in statisticLevelTotalData">
            <div>{{ item.levelName }}</div>
            <div>等级值{{ item.levelNum }}</div>
            <div style="font-size: 18px">{{ item.total }}</div>
          </div>
        </div>
      </template>
      <TaskTable ref="taskTable" :statisticLevelTotalData="statisticLevelTotalData" @handleTaskSuccess="search" :taskStatus="query.model.taskStatus" />
    </div>
  </div>
</template>

<script>
import TaskItem from './components/taskItem.vue';
import { getLevelDataList, getStatisticLevelTotal, getYearList } from '@/api/retailStore'
import TaskTable from "./components/taskTable.vue";

export default {
  name: 'levelList',
  components: {
    TaskItem,
    TaskTable,
  },
  data() {
    return {
      listTypeList: [ // 任务状态列表
        { label: '月度等级', value: 'MONTH' },
        { label: '年度等级', value: 'YEAR' },
      ],
      currentItemId: '', // 当前任务id
      levelDataList: [], // 任务列表
      total: 0,
      loading: false,
      yearList: [],
      statisticLevelTotalData: [],
      query: {
        current: 12,
        model: {
          listType: '',
          searchYear: ''
        }
      }
    }
  },
  mounted() {
    this.query.model.listType = 'MONTH'
    this.search()
  },
  computed: {
    currentItem() {
      return this.levelDataList.find(item => item.id === this.currentItemId)
    }
  },
  methods: {
    // 统计数据
    setLevelNumList() {
      if (!this.currentItemId) return
      getStatisticLevelTotal(this.currentItemId).then((res) => {
        this.statisticLevelTotalData = res
      })
    },
    // 触底加载
    load() {
      if (this.total === this.levelDataList.length || this.levelDataList.length === 0) return;
      this.query.current++
      this.setLevelList()
    },
    // 改变年份重新请求数据
    changeSearchYear() {
      this.loading = true
      getLevelDataList(this.query).then(res => {
        this.levelDataList = res.records
        this.total = res.total
        if (res.records.length > 0) {
          this.updateCurrentTaskId(res.records[0].id)
        } else {
          this.updateCurrentTaskId()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    search() {
      this.query.current = 1
      this.setLevelList()
    },
    // 更新当前任务id
    updateCurrentTaskId(id) {
      this.currentItemId = id
      this.setLevelNumList()
      this.$refs.taskTable.init(this.currentItemId)
    },
    // 设置列表
    setLevelList() {
      // 获取年份列表
      if (this.query.model.listType === 'YEAR') {
        this.query.model.searchYear = undefined
        this.changeSearchYear()
      } else {
        getYearList(this.query.model.listType).then((res) => {
          if (Array.isArray(res) && res.length > 0) {
            this.yearList = res
            this.query.model.searchYear = res[0]
            this.changeSearchYear()
          } else {
            this.yearList = []
            this.levelDataList = []
            this.updateCurrentTaskId()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.manual_quality_control {
  $backgroundColor: #fff;
  $padding: 16px;
  display: flex;
  box-sizing: border-box;
  height: calc(100vh - 86px - 32px);
  .list {
    width: 300px;
    background-color: $backgroundColor;
    border-radius: $padding / 2;
    box-sizing: border-box;
    padding: $padding;
    display: flex;
    flex-direction: column;
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      .refresh {
        cursor: pointer;
        font-size: 14px;
        position: relative;
        margin-left: 10px;
        top: -3px;
      }
    }
    .task_list {
      margin-top: $padding;
      flex: 1;
      overflow-y: auto;
    }
  }
  .content {
    flex: 1;
    box-sizing: border-box;
    margin-left: $padding;
    border-radius: $padding / 2;
    background-color: $backgroundColor;
    overflow: hidden;
    overflow-y: auto;
    padding: 10px;
    .title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
    }
    .level_statistic {
      background-color: #eee;
      display: flex;
      justify-content: space-between;
      padding: 10px;
      box-sizing: border-box;
      .statistic_item {
        flex: 1;
        text-align: center;
      }
    }
  }
}
</style>
