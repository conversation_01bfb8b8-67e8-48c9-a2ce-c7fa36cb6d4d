<template>
  <el-table border :data="tableData">
    <el-table-column type="index" label="序号" width="50"/>
    <el-table-column label="客户分组" prop="name"/>
    <!--<el-table-column v-if="varietiesBanType === '1'" label="控销数量" prop="amount"/>-->
    <el-table-column v-if="controlType === 'RESTRICTION'" label="控销数量">
      <template slot-scope="scope">
        <el-input v-model="scope.row.amount"/>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="60">
      <template slot-scope="scope">
        <el-button type="text" @click="handleDelete(scope.row,scope.$index)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import {map as _map} from "lodash";
import { deleteControlItems } from '@/api/product'
export default {
  props: {
    controlType: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  mounted() {
    this.$emit('done')
  },
  methods: {
    setData(type,data) {
      if(type === 2) {
        this.tableData = _map(data, (item, idx) => {
          return {
            innerId: item.id,
            amount: item.amount,
            id: item.merchantGroupId,
            name: item.merchantGroupName
          }
        })
      } else {
        this.tableData.push(...data)
      }
    },
    handleDelete(row,idx) {
      if(row.innerId) {
        this.$confirm('是否确定删除？').then(_ => {
          deleteControlItems(row.id,'MERCHANT_GROUP').then(res=> {
            this.$message.success('删除成功！')
          }).catch()
        }).catch(_ => {});
      } else {
        this.tableData.splice(idx, 1)
      }
    }
  }
}
</script>
