<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="refreshFun"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="salesmanCode">
        <el-input v-model.trim="listQuery.model.salesmanCode" placeholder="请输入业务员编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="realName">
        <el-input v-model.trim="listQuery.model.realName" placeholder="请输入业务员姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="productName">
        <el-input v-model.trim="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="manufacturer">
        <el-input v-model.trim="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="address">
        <el-cascader
          v-model="listQuery.model.address"
          placeholder="请选择所在区域"
          :options="areaTree"
          :props="{
            checkStrictly: true,
            expandTrigger: 'hover',
            value: 'id',
            label: 'label',
            children: 'children',
          }"
          clearable
          style="width: 240px"
          @change="parentChangeAction"
        />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <div class="title flex_between_center">
        <div>
          <el-tabs
            v-model="listQuery.model.publishStatusEnum"
            class="typeTabs"
            @tab-click="chageTabsFun"
          >
            <el-tab-pane label="待审核" name="PENDING" />
            <el-tab-pane label="已驳回" name="REJECTED" />
            <el-tab-pane label="已撤销" name="REPEAL" />
          </el-tabs>
        </div>
        <div>
          <el-button  @click="refreshFun">刷新</el-button>
        </div>
      </div>

      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          v-loading="listLoading"
          :data="list"
          row-key="agencyAreaId"
          border
          fit
          highlight-current-row
          style="width: 100%"
          @selection-change="selectTableItemFun"
        >
          <el-table-column
            align="center"
            width="80"
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name == 'applyReason'">
                {{ row[item.name] }}
              </span>
              <img
                v-else-if="item.name == 'pictIdS'"
                :src="row.pictIdS|imgFilter"
                style="width:50px;height:50px"
              >
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="flex_between_center">
          <div />
          <pagination
            v-if="total > 0"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page.sync="listQuery.current"
            :limit.sync="listQuery.size"
            @pagination="getlist"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import tableInfo from '@/views/promote/withdrawalAgency/tableInfo'
import requestAxios from '@/utils/request'
import request from '@/utils/request'
export default {
  name: 'revocationAgency',
  components: {
    Pagination
  },
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: { publishStatusEnum: 'PENDING' }
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      areaTree: []
    }
  },
  created() {
    this.initTbaleTitle()
    this.getlist()
    this.getAreaTree()
  },
  methods: {
    refreshFun() {
      this.list = []
      this.listQuery = {
        current: 1,
        size: 10,
        model: { 
          publishStatusEnum: 'PENDING',
          salesmanCode:'',
          realName:'',
          productCode:'',
          productName:'',
          manufacturer:'',
          address:''
        }
      }
      this.getlist()
    },
    resetForm() {
      this.list = []
      this.listQuery = {
        current: 1,
        size: 10,
        model: { publishStatusEnum: this.listQuery.model.publishStatusEnum }
      }
    },
    parentChangeAction(val) {
      let { publishStatusEnum,salesmanCode,realName,productCode,productName,manufacturer } = this.listQuery.model
      this.listQuery.model = {
        publishStatusEnum,
        salesmanCode,
        realName,
        productCode,
        productName,
        manufacturer,
        provinceId: val[0],
        cityId: val[1],
        districtId: val[2]
      }
    },
    async getAreaTree() {
      const { data } = await request({
        url: '/authority/area/anno/tree',
        method: 'get'
      })
      this.areaTree = data
    },
    splitString(val) {
      if (!val) {
        return ''
      }
      return val.split(',')
    },
    chageTabsFun() {
      this.list = []
      this.total = 0
      this.initTbaleTitle()
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0]
      this.listQuery.model.cityId = e[1]
      this.listQuery.model.countyId = e[2]
    },
    selectTableItemFun: function(val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {
      this.list = []
      this.getlist()
    },
    async getlist() {
      this.listLoading = true
      const { data } = await requestAxios({
        url: '/agent/agentProduct/salesmanProduct/queryRepeals-merchant',
        method: 'post',
        data: this.listQuery
      })
      this.total = data.total
      this.list = data.records
      this.listLoading = false
    },
    initTbaleTitle() {
      this.tableSelectTitle = []
      this.tableTitle = tableInfo[this.listQuery.model.publishStatusEnum]
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style='position:relative'>
          <div onClick={this.showHeaer}>
            <i class='el-icon-menu' />
          </div>
          <el-dialog
            title='设置显示列表'
            showClose={false}
            visible={this.showSelectTitle}
            width='640px'
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style='margin-top: 25px;text-align: center;'>
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type='primary' onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    showHeaer: function() {
      this.showSelectTitle = true
    },
    closeHeaer: function() {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    setHeaer: function() {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    }
  }
}
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  padding-bottom: 16px;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    padding: 0 12px;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
