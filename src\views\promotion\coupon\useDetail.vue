<template>
  <div>
    <div class="page_title">
      <page-title :title="pageData.couponName"></page-title>
    </div>
    <!-- <el-divider></el-divider> -->
    <!-- 数字描述板块 -->
    <div class="detail_box">
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              使用该优惠券的订单总额(排除已取消订单)
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>用券成交总额(元)</span>
        </div>
        <div class="detail_num" v-if="pageData.useTotalMoney!=null">
          {{ pageData.useTotalMoney }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              使用该优惠券的优惠总额(排除已取消订单)
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>优惠总金额(元)</span>
        </div>
        <div class="detail_num" v-if="pageData.discountTotalMoney!= null">
          {{ pageData.discountTotalMoney }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              优惠总金额/用券总金额
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>费效比(%)</span>
        </div>
        <div class="detail_num" v-if="pageData.ratePercent!= null">
          {{ pageData.ratePercent }} %
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              使用该优惠券的订单笔数(排除已取消订单)
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <!-- <span>用券总订单数(笔)<el-button type="text">明细</el-button></span> -->
          <span>用券总订单数(笔)<span class="detail_btn" @click="handleReport">明细</span></span>
        </div>
        <div class="detail_num" v-if="pageData.orderCount!=null">
          {{ pageData.orderCount }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              用券总成交额 / 用券总订单数
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>用券笔单价(元)</span>
        </div>
        <div class="detail_num" v-if="pageData.useUnitPrice != null">
          {{ pageData.useUnitPrice }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              领取该优惠券的券总额
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>领券总金额(元)</span>
        </div>
        <div class="detail_num" v-if="pageData.receiveTotalMoney != null">
          {{ pageData.receiveTotalMoney }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              领取该优惠券的张数
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>领券总张数(张)</span>
        </div>
        <div class="detail_num" v-if="pageData.receiveTotalCount != null">
          {{ pageData.receiveTotalCount }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              使用该优惠券的张数
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>用券总张数(张)</span>
        </div>
        <div class="detail_num" v-if="pageData.useTotalCount!= null">
          {{ pageData.useTotalCount }}
        </div>
      </div>
      <div class="detail_item">
        <div class="detail_top">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              用券数 / 领券数
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
          <span>用券率 (%)</span>
        </div>
        <div class="detail_num" v-if="pageData.usePercent!= null">
          {{ pageData.usePercent }} %
        </div>
      </div>
    </div>
    <!-- 数字描述板块结束 -->
    <!-- 搜索模块 -->
    <div class="search_box">
      <im-search-pad :has-expand="false" :model="model" @reset="reload" @search="searchLoad">
        <im-search-pad-item prop="customerCode">
          <el-input v-model="model.customerCode" placeholder="ERP客户编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="customerName">
          <el-input v-model="model.customerName" placeholder="客户名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="during">
          <el-date-picker v-model="during" type="daterange" range-separator="至" value-format="yyyy-MM-dd"
            start-placeholder="领券开始日期" end-placeholder="领券结束日期" />
        </im-search-pad-item>
        <im-search-pad-item prop="pastDuring">
          <el-date-picker v-model="pastDuring" type="daterange" range-separator="至" value-format="yyyy-MM-dd"
            start-placeholder="失效开始日期" end-placeholder="失效结束日期" />
        </im-search-pad-item>
      </im-search-pad>
    </div>
    <div class="tag_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{name:'优惠券明细'}]">
        <template slot="button">
          <paged-export :max-usable="maxUsableExport" controllable :before-close="handleExport" style="margin-right: 0">
          </paged-export>
        </template>
      </tabs-layout>
      <!--分页table-->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="false" :pageSize="pageSize" :operation-width="120">
        <template slot="orderNo">
          <el-table-column label="订单编码" width="180" show-overflow-tooltip>
            <slot slot-scope="{row}">
              {{ (row.usedStatus && row.usedStatus.code == 'Y') ? row.orderNo : '' }}
            </slot>
          </el-table-column>
        </template>
        <template slot="totalMoney">
          <el-table-column prop="totalMoney" width="110" show-overflow-tooltip>
            <template slot="header">
              订单金额
              <el-tooltip placement="top" effect="light">
                <div slot="content">
                  使用该优惠券的优惠金额（排除已取消订单）
                </div>
              <i style="margin-left:5px" class="el-icon-question"></i>
            </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span>
                {{ scope.row.totalMoney }}
              </span>
            </template>
          </el-table-column>
        </template>
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item" v-if="row.usedStatus && row.usedStatus.code == 'N'">
              <!-- 未使用的优惠券可以作废 -->
              <el-button type="text" @click="handleInvalid(row.id)">作废优惠券</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="作废原因" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-input type="textarea" :rows="4" placeholder="请输入作废原因" v-model.trim="invalidReason">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleDeal">确 定</el-button>
        <!-- <del-el-button style="margin-left:5px" btnText="确定" :targetId="row.id" :text="delText" @handleDel="handleDeal"></del-el-button> -->
      </span>
    </el-dialog>
  </div>
</template>


<script>
  const TableColumns = [{
      label: "ERP客户编码",
      name: "customerCode",
      prop: "customerCode",
      width: "130"
    },
    {
      label: "客户名称",
      name: "customerName",
      prop: "customerName",
      width: "180"
    },
    {
      label: '领券方式',
      name: "receiveWay",
      prop: "receiveWay.desc",
      width: "120"
    },
    {
      label: "领券时间",
      name: "receiveTime",
      prop: 'receiveTime',
      width: "150"
    },
    {
      label: '失效时间',
      name: "useEndTime",
      prop: 'useEndTime',
      width: "150"
    },
    {
      label: "券面值(元)",
      name: "reduceMoney",
      prop: 'reduceMoney',
      width: "100"
    },
    {
      label: "使用状态",
      name: "usedStatus",
      prop: 'usedStatus.desc',
      width: "100"
    },
    {
      label: "用券时间",
      name: "useTime",
      prop: 'useTime',
      width: "140"
    },
    {
      label: "订单编码",
      name: "orderNo",
      prop: 'orderNo',
      width: "180",
      slot: 'true'
    },
    {
      label: "订单金额(元)",
      name: "totalMoney",
      prop: 'totalMoney',
      width: "110",
      slot: 'true'
    },
    {
      label: "作废原因",
      name: 'abolishReason',
      prop: 'abolishReason',
      width: '130'
    }
  ]
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  };
  import delElButton from "@/components/eyaolink/delElButton";
  import checkPermission from "@/utils/permission";
  import PagedExport from "@/components/PagedExport/index";
  import {
    exoprtToExcel
  } from "@/utils/commons";
  import {
    formatDataTime
  } from '@/utils/index';
  import {
    itemDataPage,
    itemDataStatistics,
    exportCoupon,
    updateAbolish
  } from '@/api/promotion'
  export default {
    //import引入的组件
    components: {
      delElButton,
      PagedExport
    },

    data() {
      return {
        couponName: '活动:基药云联盟会员专享券6000元【全场通用】',
        model: {
          customerName: '',
          customerCode: ''
        },
        id: '',
        dialogVisible: false,
        invalidReason:'',
        delText: '您确定作废该优惠券吗？',
        tableData: [],
        pageSize: 10,
        tableColumns: TableColumnList,
        pageData: {},
        during: [],
        pastDuring: [],
        maxUsableExport: 0,
        currentId:'',
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.id = this.$route.query.id;
      this.initData();
    },
    //方法集合
    methods: {
      checkPermission,
      async load(params) {
        let queryModel = JSON.parse(JSON.stringify(this.model));
        if (this.during.length == 2) {
          queryModel.receiveStartTime = this.during[0];
          queryModel.receiveEndTime = this.during[1];
        };
        if (this.pastDuring.length == 2) {
          queryModel.invalidStartTime = this.pastDuring[0];
          queryModel.invalidEndTime = this.pastDuring[1];
        }
        let listQuery = {
          model: {
            ...queryModel,
            couponId: this.$route.query.id,
          },
          sort: 'id',
          order: 'descending',
        }

        Object.assign(listQuery, params);

        // TODO 替换成对应用的列表api
        // return await itemDataPage(listQuery);
        const result = await itemDataPage(listQuery);
        this.maxUsableExport = result.data.total
        return result;
      },
      initData() {
        itemDataStatistics(this.id).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            console.log('res.code ----->', res);
            this.pageData = res.data;
          }
        })
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.model = {
          customerCode: '',
          customerName: ''
        };
        this.during = [];
        this.pastDuring = [];
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      handleInvalid(id) {
        this.currentId = id;
        this.dialogVisible = true;
      },
      handleClose() {
        this.dialogVisible = false;
      },
      handleDeal() {
        // console.log('作废该优惠券', id);
        if(this.invalidReason.length == 0) {
          this.$message.warning('请填写作废原因');
          return
        }
        let list = [this.currentId];
        let params = {
          'ids[]': list.toString(),
          baseCommonEnum: 'Y',
          abolishReason: this.invalidReason
        };
        updateAbolish(params).then(res => {
          if(res.code == 0 && res.msg == 'ok') {
            this.$message.success('作废优惠券成功');
            this.dialogVisible = false;
            this.reload();
          }
        })
      },
      handleReport(){
        this.$router.push({
          path: '/reportCenter/couponReport/index',
          
        });
        localStorage.setItem('COUPON_USER_NAME', this.pageData.couponCode)
      },
      async handleExport(query) {
        const loading = this.$loading({
          lock: true,
          text: '正在导出中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });
        let queryModel = JSON.parse(JSON.stringify(this.model));
        if (this.during.length == 2) {
          queryModel.receiveStartTime = this.during[0];
          queryModel.receiveEndTime = this.during[1];
        };
        if (this.pastDuring.length == 2) {
          queryModel.invalidStartTime = this.pastDuring[0];
          queryModel.invalidEndTime = this.pastDuring[1];
        }
        let listQuery = {
          model: {
            ...queryModel,
            ...query,
            couponId: this.$route.query.id,
          },
          sort: 'id',
          order: 'descending',
        };
        exportCoupon(listQuery).then(res => {
          loading.close();
          exoprtToExcel(res.data, `优惠券明细数据${formatDataTime('yyyyMMDDHHmmss')}.xlsx`);
        })
      },
    },

  }

</script>


<style lang='scss' scoped>
  .page_title {
    background-color: #fff;
    padding: 0 20px;
    // border-bottom: 1px  solid #DCDDE0;
  }

  .detail_box {
    background-color: #fff;
    padding: 20px 20px;
    display: flex;
    flex-wrap: wrap;

    .detail_item {
      width: 20%;
      height: 100px;

      .detail_top {
        span {
          margin-left: 5px;
        }
        .detail_btn {
          color: #0056E5;
          cursor: pointer;
          margin-left: 5px;
        }
        margin-bottom: 10px;
      }

      .detail_num {
        font-size: 32px;
        color: #86c12a;
        line-height: 60px;
      }
    }
  }

  .search_box {
    margin: 20px 0;
    // padding: 0 20px;
  }

  .tag_bg {
    background-color: #fff;
    padding: 10px 20px;
  }

</style>
