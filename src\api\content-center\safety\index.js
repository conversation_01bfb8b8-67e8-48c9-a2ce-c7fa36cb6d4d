import request from '@/utils/request'
import qs from 'qs'

// 安全协议分页查询
export function securityProtocolPage(data) {
  return request({
    url: '/general/admin/securityProtocol/page',
    method: 'post',
    data
  })
}

// 安全协议详情查询
export function securityProtocolDetail(id){
    return request({
        url:`/general/admin/securityProtocol/${id}`,
        method:'get'
    })
}

// 批量修改发布状态
export function batchPublishStatus(data) {
    return request({
        url:'/general/admin/securityProtocol/batchPublishStatus',
        method:'post',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        data: qs.stringify(data)
    })
}

// 删除安全协议
export function securityProtocolDelete(ids){
    return request({
        url:'/general/admin/securityProtocol',
        method:'delete',
        params: {
            'ids[]': ids
          }
    })
}

// 新增安全协议
export function securityProtocolAdd(data){
    return request({
        url:'/general/admin/securityProtocol',
        method:'post',
        data
    })
}

// 修改安全协议
export function securityProtocolEdit(data){
    return request({
        url:'/general/admin/securityProtocol',
        method:'put',
        data
    })
}

// 获取协议类型选择
export function listProtocolTypeByMerchantId(){
    return request({
        url: '/general/admin/securityProtocol/listProtocolTypeByMerchantId',
        method: 'get'
    })
}

// 获取协议类型选择
export function listTerminalByMerchantId(){
    return request({
        url: '/general/admin/securityProtocol/listTerminalByMerchantId',
        method: 'get'
    })
}