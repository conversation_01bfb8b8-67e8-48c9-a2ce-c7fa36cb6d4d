<template>
  <div>
    <el-card class="product-show page-card" shadow="never">

      <template slot="header">
        <div class="title">枸橼酸莫沙必利分散片</div>
        <div>
          <el-button type="primary">申请品种下架</el-button>
          <el-button @click="$router.push({ name: 'productEdit', params: { id: $route.params.id } })">返回</el-button>
        </div>
      </template>

      <div class="product-detail">
        <div class="product-detailFlex">
          <p>规格: {{product.specifications}}</p>
          <p>销售价: {{product.salePrice}}元</p>
          <p>已代理区域: {{product.salesArea}}</p>
          <p>批准文号: {{product.Num}}</p>
          <p>推广费: {{product.promotionFee}}元</p>
          <p>累积推广销售额: {{product.totalSalesFee}}元</p>
          <p>生产厂家: {{product.manufacturer}}</p>
        </div>
      </div>
    </el-card>

    <div class="detailTable">
      <el-tabs v-model="activeName">
        <el-tab-pane label="已发布推广品种" :name="0"></el-tab-pane>
      </el-tabs>
      <el-table
        border
        fit
        highlight-current-row
        :data="tableData"
      >
        <el-table-column label="代理区域" prop="area" />
        <el-table-column label="推广费" prop="promotionFee"></el-table-column>
        <el-table-column label="业务员姓名" prop="saleman" />
        <el-table-column label="销售任务" prop="saleRask" />
        <el-table-column label="本月销售业绩" prop="monthSale" />
        <el-table-column label="累积销业绩" prop="totalSale" />
        <el-table-column label="累积销售数量" prop="totalSalesNum" />
        <el-table-column label="销售任务是否达标" prop="isOver" />
        <el-table-column width="320" label="操作">
            <span>
              <el-link type="primary" @click="dialogVisible = true">申请撤销该业务员资格</el-link>
              <el-link type="primary" style="margin-left: 10px;" @click="$router.push({name: 'promoteParticulars'})">查看业务员订单</el-link>
            </span>
        </el-table-column>
      </el-table>
    </div>
    <!-- 申请撤销该业务员资格 -->
    <el-dialog
      title="申请撤销该业务员资格"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose">
      <el-input v-model="reson" type="textarea" :rows="4" placeholder="请填写撤销该业务员资格原因" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      reson: '',
      dialogVisible: false,
      activeName: 0,
      product: {
        productName: '枸橼酸莫沙必利分散片',
        specifications: '120mg*21粒',
        manufacturer: '成都康弘药业集团.',
        salePrice: '20000.00',
        promotionFee: '200.00~300.00',
        goodsClass: '../补益安神/补气补血',
        salesArea: '省份1 市3 区4',
        Num: '国药准*********',
        totalSalesFee: 2512123.00,
        salesman: 5
      },
      tableData: [
        {
          area: '南澳县',
          promotionFee: '200.00~300.00',
          saleman: '楚中天',
          saleRask: '20000.00',
          monthSale: '23429.00',
          totalSale: '323429.00',
          totalSalesNum: '12353',
          isOver: '已3个月达标'
        },
        {
          area: '南澳县',
          promotionFee: '200.00~300.00',
          saleman: '楚中天',
          saleRask: '20000.00',
          monthSale: '23429.00',
          totalSale: '323429.00',
          totalSalesNum: '12353',
          isOver: '已2个月达标'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
  .product{
    &-detailFlex{
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      p{
        width: 33.333%;
      }
    }
  }

  .detailTable{
    margin-top: 20px;
    padding: 20px;
    background: #ffffff;
  }
</style>