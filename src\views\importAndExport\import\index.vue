<!--
 * @: 导入列表
-->
<template>
  <div>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[ { name: '导入列表' } ]">
        <template slot="button">
          <el-button icon="el-icon-refresh" @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="pager-table" :pageSize="pageSize" :options="tableTitle" :data.sync="tableData"
                   :operation-width="180" :remote-method="load" :selection="false"
      >
        <el-table-column slot="uploadStatus" label="导入状态">
          <template slot-scope="{row}">
            <!-- <span v-if="scope.row.uploadStatus">{{ scope.row.uploadStatus.code === 'SUCCESS' ? '成功' : (scope.row.uploadStatus.code == 'FAILURE' ? '失败' : '处理中') }}</span> -->
            <el-tag v-if="row['uploadStatus'].code === 'SUCCESS'" type="success">{{ row.uploadStatus.desc }}</el-tag>
            <el-tag v-if="row['uploadStatus'].code === 'FAILURE'" type="danger">{{ row.uploadStatus.desc }}</el-tag>
            <el-tag v-if="row['uploadStatus'].code === 'PROCESSING'">{{ row.uploadStatus.desc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column slot="uploadResult" label="导入结果" width="180">
          <template slot-scope="{row}">
            <el-tooltip class="item" effect="dark" :content="row.description" placement="top-start">
              <span>{{ row.uploadResult }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <div slot-scope="scope" align="center">
          <el-row class="table-edit-row" v-if="scope.row['uploadStatus']">
            <span v-if="scope.row['uploadStatus'].code !== 'PROCESSING' && checkPermission(['admin', 'sale-saas-batchProcessing-import:download', 'sale-platform-batchProcessing-import:download'])" class="table-edit-row-item">
              <el-button v-throttle type="text" @click="downloadFun(scope.row)">下载失败结果</el-button>
            </span>
            <span v-if="scope.row['uploadStatus'].code !== 'PROCESSING' && checkPermission(['admin', 'sale-saas-batchProcessing-import:del', 'sale-platform-batchProcessing-import:del'])" class="table-edit-row-item">
              <del-el-button :targetId="scope.row.id" :text="delText" @handleDel="delCoupon"></del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
const TableColumns = [{
  prop: 'createTime',
  name: 'createTime',
  label: '导入时间',
  width: '180'
},
  {
    prop: 'importType.desc',
    name: 'importType.desc',
    label: '导入类型',
    width: '180'
  },
  {
    prop: 'fileName',
    name: 'fileName',
    label: '文件名称',
    width: '180'
  },
  {
    prop: 'uploadStatus',
    name: 'uploadStatus',
    label: '导入状态',
    width: '80',
    slot: true
  },
  {
    prop: 'uploadResult',
    name: 'uploadResult',
    label: '导入结果',
    width: 180,
    slot: true
  },
  {
    prop: 'createUserName',
    label: '操作人'
  }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}
import delElButton from '@/components/eyaolink/delElButton'
import { recordImport, downloadImportResults, deleteRecodeInport } from '@/api/product'
import { exoprtToExcel } from '@/utils/commons'
import { formatDataTime } from '@/utils/index'
import checkPermission from '@/utils/permission'

export default {
  components: {
    delElButton
  },
  data() {
    return {
      tableTitle: TableColumnList,
      pageSize: 10,
      delText: '您确定删除该导入记录吗？',
      tableData: [],
      model: {
        productType: '',
        uploadStatus: ''
      }
    }
  },
  mounted() {

  },
  methods: {
    checkPermission,
    async load(params) {
      let listQuery = {
        model: {
          ...this.model
        }
      }
      Object.assign(listQuery, params)
      return await recordImport(listQuery)
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    delCoupon(id) {
      deleteRecodeInport(id).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('删除成功')
          this.reload()
        }
      })
    },
    downloadFun(row) {
      downloadImportResults(row.id).then(res => {
        exoprtToExcel(res.data, `失败结果${formatDataTime('yyyyMMDDHHmmss')}.xlsx`,)
      })
    }
  }
}

</script>

<style>

</style>
