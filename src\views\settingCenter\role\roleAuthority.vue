<template>
  <div>
    <el-row  style="border:1px solid #DCDDE0;border-radius: 4px;   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);">
      <el-col :span="8" class="title">菜单栏目</el-col>
      <el-col :span="16"  class="title">按钮功能</el-col>
      <el-col :span="8" >
        <el-scrollbar  style="height:400px;">
          <el-card class="box-card" >
            <div align="left" style="margin-left:24px;">
              <el-checkbox
                :indeterminate="isIndeterminate"
                @change="checkedAll"
                v-model="checkedMenu"
              />
              全选/反选
            </div>
          <el-tree
                  :check-strictly="true"
                  :data="menuTree"
                  :default-checked-keys="roleAuthority.menuIdList"
                  :default-expanded-keys="roleAuthority.menuIdList"
                  :disabled="disabled"
                  :expand-on-click-node="false"
                  @check="checkMenu"
                  @node-click="nodeClick"
                  default-expand-all
                  highlight-current
                  node-key="id"
                  ref="menuTree"
                  show-checkbox
                />
          </el-card>
        </el-scrollbar>
      </el-col>
      <el-col :span="16">
        <el-card class="box-card">
          <el-table
            :data="tableData.records"
            :key="tableKey"
            @select="onSelect"
            @select-all="onAllSelect"
            border
            fit
            ref="table"
            row-key="id"
            style="width: 100%;"
            v-loading="loading"
          >
            <el-table-column
              :reserve-selection="true"
              align="center"
              type="selection"
              width="80px"
            />
            <el-table-column
              label="编码"
              :show-overflow-tooltip="true"
              align="left"
              prop="code"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="名称"
              :show-overflow-tooltip="true"
              align="left"
              prop="name"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  list as menuListApi,
  deleteApi as menuDeleteApi
} from "@/api/setting/permission/menu";
import {
  list as roleListApi,
  findAuthorityIdByRoleId,
  deleteApi as roleDeleteApi
} from "@/api/setting/permission/userRole";
import {
  list as resourceListApi,
  editApi as resourceEditApi,
  deleteApi as resourceDeleteApi
} from "@/api/setting/permission/resource";
export default {
  name: "RoleAuthorityEdit",
  components: {},
  props: [
    "roleAuthorityInfo"
  ]  ,
  data() {
    return {
      roleAuthority: this.initRoleAuthority(),
      screenWidth: 0,
      // width: this.initWidth(),
      menuTree: [],
      resourceList: [],
      //回显的数据
      echoResourceIdList: [],
      rules: {},
      tableKey: 0,
      loading: false,
      tableData: {
        total: 0
      },
      selection: [],
      disabled: false,
      isIndeterminate: false,
      checkedMenu: false
    };
  },
  watch: {

  },
  mounted() {
    this.initMenuTree();

  },
  methods: {
     allMenuIdList() {
        const menuIdList = [];
        this.getMenuIdList(this.menuTree, menuIdList);
        return menuIdList;
      },
      getMenuIdList(menuList, menuIdList) {
        if (menuList) {
          menuList.forEach(item => {
            menuIdList.push(item.id);
            if (item.children && item.children.length > 0) {
              this.getMenuIdList(item.children, menuIdList);
            }
          });
        }
      },
      checkedAll() {
        if (this.checkedMenu) {
          //全选
          this.$refs.menuTree.setCheckedKeys(this.allMenuIdList());
          this.isIndeterminate = false;
        } else {
          //取消选中
          this.$refs.menuTree.setCheckedKeys([]);
          this.isIndeterminate = false;
        }
        this.returnSelectInfo()
      },
      nodeClick(data) {
          const vm = this;
          vm.loading = true;
          resourceListApi({
              current: 1,
              size: 10000,
              model: { menuId: data.id }
            })
            .then(response => {
              const res = response;
              vm.tableData = res.data;
              vm.loading = false;
              vm.displayTable();
            });
      },
      displayTable() {
        const vm = this;
        vm.tableData.records.forEach(item => {
          vm.roleAuthority.resourceIdList.forEach(resourceId => {
            if (item.id === resourceId) {
              vm.$refs.table.toggleRowSelection(item, true);
            }
          });
        });
      },
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(selection, row) {
        this.mergeResourceIdList(selection, row);
        this.selection = selection;
        // 根据右侧选中的资源，强制勾选左侧的 树状层级菜单
        const old = this.$refs.menuTree.getCheckedKeys();
        const must = selection.map(item => item.menuId);
        const newSelected = Array.from(new Set([...old, ...must]));
        this.$refs.menuTree.setCheckedKeys(newSelected);
        newSelected.forEach(item => {
          this.selectedParent(item);
        });
        this.returnSelectInfo()
      },
      mergeResourceIdList(selection, row) {
        // true就是选中，0或者false是取消选中
        let selected = true;
        if (row) {
          selected = selection.length && selection.indexOf(row) !== -1;
        } else {
          selected = selection.length > 0;
        }
        //本次选中的
        const curResourceIdList = selection.map(item => item.id);
        const ridList = this.echoResourceIdList;
        if (!selected && row) {
          var index = ridList.findIndex(item => {
            if (item == row.id) {
              return true;
            }
          });
          ridList.splice(index, 1);
        }
        // 本次选中的 + 回显的 然后去重
        this.roleAuthority.resourceIdList = [
          ...new Set([...curResourceIdList, ...ridList])
        ];
      },
      initMenuTree() {
        var _this = this;
        menuListApi().then(response => {
          _this.menuTree = response.data;
          _this.roleAuthority= Object.assign(_this.roleAuthority, _this.roleAuthorityInfo);
        });


     },
      initRoleAuthority() {
        return {
          roleId: 0,
          menuIdList: [],
          resourceIdList: []
        };
      },

      setRoleAuthority(val) {
        const vm = this;
        vm.roleAuthority.roleId = val.id;
        // vm.disabled = val.readonly
        // 回显
        findAuthorityIdByRoleId(val.id).then(response => {
          const res = response.data;
          vm.roleAuthority.menuIdList = res.data.menuIdList;
          vm.roleAuthority.resourceIdList = res.data.resourceIdList;
          vm.echoResourceIdList = res.data.resourceIdList;
          vm.$refs.menuTree.setCheckedKeys(res.data.menuIdList);
          res.data.menuIdList.forEach(item => {
            vm.selectedParent(item);
          });
        });
      },
      returnSelectInfo() {
        const vm = this;
        this.roleAuthority.menuIdList = vm.$refs.menuTree
          .getHalfCheckedKeys()
          .concat(vm.$refs.menuTree.getCheckedKeys());
          this.$emit('update:roleAuthorityInfo', this.roleAuthority)
      },
      checkMenu(data, node) {
        if (node.checkedKeys.length === 0) {
          //取消
          this.checkedMenu = false;
          this.isIndeterminate = false;
        } else if (node.checkedKeys.length === this.allMenuIdList().length) {
          //全选
          this.checkedMenu = true;
          this.isIndeterminate = false;
        } else {
          // 中立
          this.checkedMenu = false;
          this.isIndeterminate = true;
        }
        // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
        let selected = node.checkedKeys.indexOf(data.id); // -1未选中
        // 选中
        if (selected !== -1) {
          // 子节点只要被选中父节点就被选中
          this.selectedParent(data);
          // 统一处理子节点为相同的勾选状态
          this.uniteChildSame(data, true);
        } else {
          // 未选中 处理子节点全部未选中
          if (data.children && data.children.length !== 0) {
            this.uniteChildSame(data, false);
          }
        }
        this.returnSelectInfo();
      },
      // 统一处理子节点为相同的勾选状态
      uniteChildSame(data, isSelected) {
        this.$refs.menuTree.setChecked(data.id, isSelected);
        if (data.children) {
          for (let i = 0; i < data.children.length; i++) {
            this.uniteChildSame(data.children[i], isSelected);
          }
        }

      },
      // 统一处理父节点为选中
      selectedParent(data) {
        let currentNode = this.$refs.menuTree.getNode(data);
        if (currentNode.parent.key !== undefined) {
          this.$refs.menuTree.setChecked(currentNode.parent, true);
          this.selectedParent(currentNode.parent);
        }

      }
    }
};
</script>
<style lang="scss" scoped>
.title{
    background: #d9d6d6;
    font-weight: 600;
    text-indent: 40px;
    color: #535151;
}
</style>
