import request from '@/utils/request'
import requestAxios from '@/utils/requestAxios'

export function list(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productPlatform/page',
    data
  })
}

export function pageCount(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/productPlatform/pageCount',
    data
  })
} 


export function editApi(data) {
  return requestAxios({
    url: '/api/product/admin/productPlatform',
    method: data.id > 0 ? 'put' : 'post',
    data
  })
}
export function getApi(id) {
  return requestAxios({
    url: '/api/product/admin/productPlatform/'+id,
    method: 'get'
  })
}

//批量审核通过产品
export function acceptProductPlatformApi(data) {
  return requestAxios({
    url: '/api/product/admin/productPlatform/batchAcceptProductPlatform',
    method: 'post',
    data
  })
}
//批量驳回产品
export function rejectProductPlatformApi(data) {
  return requestAxios({
    url: '/api/product/admin/productPlatform/batchRejectProductPlatform',
    method: 'post',
    data
  })
}


export function editProductStatusApi(data) {
  return requestAxios({
    url: '/api/merchant/admin/saleMerchant',
    method:'put',
    data
  })
}

