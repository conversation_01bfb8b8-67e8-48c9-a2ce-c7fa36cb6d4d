<template>
  <div class="agreement-price">
    <im-search-pad
      :has-expand="false"
      @reset="onReset"
      @search="refresh"
    >
      <im-search-pad-item prop="customerCode">
        <el-input v-model.trim="customerCode" placeholder="请输入ERP客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="agreement-price__container">
      <tabs-layout
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button  @click="refresh">刷新</el-button>
          <el-button type="primary" v-if="checkPermission(['admin','sale-saas-agreementPrice:add','sale-platform-agreementPrice:add'])"  @click="$router.push({ path: '/agreementPrice/create' })"><i class="el-icon-plus"></i> 新增协议价</el-button>
        </template>
      </tabs-layout>
      <transfer-table
        :tableOptions="allColumn"
        :tableData="tableData"
        :loading="loading"
        :selection="false"
        v-loading="loading"
      >
        <el-table-column label="协议状态" align="center" slot="activeStatus">
          <template slot-scope="scope">
            <span :class="scope.row.activeStatus.code === 'FREEZE' ? 'red-color' : ''">{{ scope.row.activeStatus.code === 'FREEZE' ?  '已冻结' : '已启用' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" slot="approvalStatus">
          <template slot-scope="scope">
            <span>
              {{ scope.row.approvalStatus.desc }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="协议商品" width="130" slot="list">
          <template v-slot="scope">
            <div>共<span>{{ scope.row.itemCount }}</span>个
              <el-button type="text" style="margin-left: 10px" @click="handleCheckProducts(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column slot="operator" fixed="right" label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-row class="table-edit-row">
              <span v-if="tabs[currentTab].value === 'PENDING'">
                <span v-if="scope.row.pictIdS && checkPermission(['admin','sale-saas-agreementPrice:preview','sale-platform-agreementPrice:preview'])" class="table-edit-row-item">
                  <el-button type="text" @click="showPreview(scope.row)">附件</el-button>
                </span>
                <span  v-if="checkPermission(['admin','sale-saas-agreementPrice:detail','sale-platform-agreementPrice:detail'])"  class="table-edit-row-item">
                  <el-button type="text" @click="$router.push({ path: '/agreementPrice/create', query: { id: scope.row.id } })">编辑</el-button>
                </span>
                <span  v-if="checkPermission(['admin','sale-saas-agreementPrice:del','sale-platform-agreementPrice:del'])" class="table-edit-row-item">
                  <el-button type="text" @click="handleDeleteAgree(scope.row.id)">删除</el-button>
                </span>
                <span  v-if="checkPermission(['admin','sale-saas-agreementPrice:accept','sale-platform-agreementPrice:accept'])"  class="table-edit-row-item">
                  <el-button type="text" @click="handlePass(scope.row.id)">通过</el-button>
                </span>
                <span v-if="checkPermission(['admin','sale-saas-agreementPrice:reject','sale-platform-agreementPrice:reject'])">
                  <el-button type="text" @click="handleReject(scope.row.id)">驳回</el-button>
                </span>
              </span>
              <span v-else>
                <span v-if="scope.row.pictIdS && checkPermission(['admin','sale-saas-agreementPrice:preview','sale-platform-agreementPrice:preview'])" class="table-edit-row-item">
                  <el-button type="text" @click="showPreview(scope.row)">附件</el-button>
                </span>
                <span  v-if="checkPermission(['admin','sale-saas-agreementPrice:detail','sale-platform-agreementPrice:detail'])"  class="table-edit-row-item">
                  <el-button type="text" @click="$router.push({ path: '/agreementPrice/create', query: { id: scope.row.id } })">编辑</el-button>
                </span>
                <span  v-if="checkPermission(['admin','sale-saas-agreementPrice:del','sale-platform-agreementPrice:del'])">
                  <el-button type="text" @click="handleDeleteAgree(scope.row.id)">删除</el-button>
                </span>
              </span>
              <el-image :ref="`ref${scope.row.id}`" style="width:0;height:0" :src="previewImages[0]"  :preview-src-list="previewImages"></el-image>
            </el-row>
          </template>
        </el-table-column>
      </transfer-table>
      <div class="pagination">
        <el-pagination
          background
          :total="total"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          @current-change="load"
          @size-change="load"
          layout="prev, pager, next, sizes, jumper">
        </el-pagination>
      </div>
    </div>

    <!-- 查看商品弹窗 -->
    <!-- <v-item-modal ref="itemList" /> -->
    <CheckProductDialog :visible.sync="isCheckProduct" :negotiatedPriceId="negotiatedPriceId" />

    <preview :images="previewImages" ref="preview"/>
    <RejectResonDialog :visible.sync="rejectResonVisible" @onsuccess="handleSuccess" :agreeId="agreeId" />
  </div>
</template>

<script>
// 协议价详情：/productCenter/agreementPrice/detail?id=244283759677931598
// 新增协议价：/agreementPrice/create
import request from '@/utils/request'
import itemModal from './components/itemModal'
import transferTable from '@/components/TransferTable/index'
import RejectResonDialog from './components/RejectResonDialog.vue'
import Preview from './components/preview';
import checkPermission from "../../../utils/permission";
import { deleteAgreePrice,approvedPass } from '@/api/products/agreemenPrice'
import CheckProductDialog from './components/CheckProductDialog.vue'
export default {
  name: 'agreementPrice',
  components: {
    'v-item-modal': itemModal,
    transferTable,
    Preview,
    RejectResonDialog,
    CheckProductDialog
  },

  data() {
    return {
      customerCode: '',
      purMerchantName: '',
      currentTab: 0,
      previewImages: [],
      allColumn: [
        {
          prop: 'activeStatus',
          label: '协议状态',
          slot: true
        },
        {
          prop: 'purMerchantVo.customerCode',
          label: 'ERP客户编码',
          width: 150
        },
        {
          prop: 'purMerchantVo.name',
          label: '客户名称',
          width: 120
        },
        {
          prop: 'purMerchantVo.merchantType',
          label: '企业类型',
          width: 120
        },
        {
          prop: 'purMerchantVo.ceoName',
          label: '负责人',
          width: 120
        },
        {
          prop: 'purMerchantVo.ceoMobile',
          label: '联系电话',
          width: 200
        },
        {
          prop: 'purMerchantVo.region',
          label: '所在地区',
          width: 200
        },
        {
          prop: 'list',
          label: '协议商品',
          slot: true,
          width: 150
        },
        {
          prop: 'negotiatedTime',
          label: '有效期至',
          width: 200
        },
        {
          prop: 'approvalStatus',
          label: "审核状态",
          slot: true,
          width: 150
        },
        {
          prop: 'createUser',
          label: '创建人',
          width: 120
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 200
        },
        {
          prop: 'approvalUser',
          label: '审批人',
          width: 120
        },
        {
          prop: 'approvalTime',
          label: '审批时间',
          width: 200
        },
        {
          prop: 'operator',
          label: '操作',
          slot: true,
          show: true,
          fixed: 'right'
        }
      ],
      tableData: [],
      loading: false,
      page: 1,
      pageSize: 10,
      total: 10,
      rejectResonVisible: false,
      agreeId: '',
      negotiatedPriceId: '',
      isCheckProduct: false
    }
  },
  computed: {
    tabs() {
      return [
        { name: '已通过', value: 'ACCEPTED', hide: !checkPermission(['admin', 'sale-saas-agreementPrice:acceptedView', 'sale-platform-greementPrice:acceptedView']) },
        { name: '待审核', value: 'PENDING', hide: !checkPermission(['admin', 'sale-saas-agreementPrice:pendingView', 'sale-platform-agreementPrice:pendingView']) },
        { name: '已驳回', value: 'REJECTED', hide: !checkPermission(['admin', 'sale-saas-agreementPrice:rejectedView', 'sale-platform-agreementPrice:rejectedView']) },
        { name: '已过期', value: 'EXPIRED', hide: !checkPermission(['admin', 'sale-saas-agreementPrice:expiredView', 'sale-platform-agreementPrice:expiredView']) }
      ]
    }
  },
  mounted() {
    this.load()
  },
  activated() {
    if(sessionStorage.getItem('agreementPrice')) {
      this.onReset()
      sessionStorage.removeItem('agreementPrice')
    }
  },
  methods: {
    checkPermission,
    showPreview(item) {
      this.previewImages = item.pictIdS.split(',') || []
      this.$refs[`ref${item.id}`].showViewer = true;
    },
    handleChangeTab(tab, index) {
      this.currentTab = index
      this.page = 1
      this.total = 0
      this.load()
    },
    handleSuccess() {
      this.load()
    },
    /**
     * @description 审核通过
     */
    handlePass(id){
      this.$confirm('确认要审核通过该条协议吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          approvedPass(id).then(res => {
            if(res.code === 0) {
              this.$message.success('审核通过成功!');
              this.load()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消审核'
          });          
      });
    },
    /**
     * @description 申请驳回
     * <AUTHOR>
     */
    handleReject(id) {
      this.agreeId = id;
      this.rejectResonVisible = true
    },
    /**
     * @description 删除协议
     * <AUTHOR>
     */
    handleDeleteAgree(id) {
      this.$confirm('确认删除该条协议吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteAgreePrice(id).then(res => {
            if(res.code === 0) {
              this.$message.success('删除成功!');
              this.load()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
      });
    },
    async load() {
      if (this.tabs.every(item => item.hide)) return
      this.loading = true
      try {
        const { data } = await request.post('product/admin/negotiatedPrice/page', {
          current: this.page,
          size: this.pageSize,
          model: this.tabs[this.currentTab].value === 'EXPIRED' ? {
            workingStatus: this.tabs[this.currentTab].value,
            purMerchantName: this.purMerchantName,
            purMerchantCode: this.customerCode
          } : {
            approvalStatus: this.tabs[this.currentTab].value,
            purMerchantName: this.purMerchantName,
            purMerchantCode: this.customerCode
          }
        })

        this.total = data.total
        this.tableData = data.records
      } catch (e) {

      }

      this.loading = false
    },

    handleCheckProducts(row) {
      this.negotiatedPriceId = row.id;
      this.isCheckProduct = true
    },

    refresh() {
      this.page = 1
      this.load()
    },
    onReset() {
      this.page = 1
      this.purMerchantName = '';
      this.customerCode = '';
      this.load()
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-price {
  &__filter {
    padding: 16px 16px 16px 12px;
    background-color: #ffffff;
    border-left: 4px solid #1890ff;

    .el-input {
      margin-right: 16px;
    }
    .el-button + .el-button {
      margin-left: 8px;
    }
  }

  &__container {
    background-color: #ffffff;
    padding: 8px 20px 20px;
    margin-top: 16px;
    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #505465;
      font-size: 14px;
      margin-top: 20px;
    }
  }
}
</style>

<style>
  .red-color {
    color: #e62940;
  }
</style>
