<template>
  <div class="detail-wrapper">
    <p class="detail-title">
      {{title}}
    </p>
    <div class="fr" style="margin-top: -60px;margin-right: 20px;">
      <el-button @click="reload">取消</el-button>
      <el-button type="primary" @click="onsubmit('roleForm')" >保存</el-button>
    </div>
    <div class="detail-items">
      <el-form label-width="100px" ref="roleForm" :model="roleForm" :rules="rules">
        <el-form-item label="员工账号：" prop="account">
          <el-input placeholder="请输入员工账号" v-model="roleForm.account" @blur="onInputBlur" :disabled="isEdit"></el-input>
        </el-form-item>
        <!--<el-form-item label="员工密码：">
          <el-input placeholder="请输入员工密码" type="area"></el-input>
        </el-form-item>-->
        <el-form-item label="员工姓名：" prop="name">
          <el-input placeholder="请输入员工姓名" v-model="roleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="员工手机号：" prop="contactNumber">
          <el-input placeholder="请输入员工手机号" v-model="roleForm.contactNumber"></el-input>
        </el-form-item>
        <!--<el-form-item label="所属角色：">
          <el-select placeholder="请选择所属角色" type="area"></el-select>
        </el-form-item>
        <el-form-item label="权限设置：">
          <el-tree
            ref="tree"
            :data="menuData"
            show-checkbox
            accordion
            node-key="id"
          ></el-tree>
        </el-form-item>-->
      </el-form>
    </div>
  </div>
</template>
<script>
  import { menuTree,addEmployee,getByAccount,detail,editEmployee } from '@/api/settingCenter'
  let checkPhone = (rule, value, callback) => {
    let reg = /^1\d{10}$/
    if (!reg.test(value)) {
      callback(new Error('请输入11位手机号'))
    } else {
      callback()
    }
  }
  export default {
    inject:  ['reload'],
    data() {
      const checkAccount = (rule, value, callback) => {
          if (!value) {
            //callback 是提示的信息
            return callback(new Error('请输入员工账号'));
          } else {
          getByAccount(this.roleForm.account).then(res =>{
            if(res.data) {
              this.userId = res.data.id
              callback();
            } else {
              callback("当前账号不存在于用户列表");
            }
          })
        }
      }
      return {
        title: '新增员工',
        menuData: [],
        isEdit: false,
        roleForm: {
          contactNumber: '',//
          account: '',
          name: ''
        },
        rules: {
          name: [
            {required: true, message: '请输入员工名称',trigger: 'blur'}
          ],
          account: [
            {validator: checkAccount, trigger: 'blur'}
          ],
          contactNumber: [{
            type: 'Number', validator: checkPhone, message: '请填写正确的手机号码', trigger: 'blur'
          }]
        },
        userId: '',
        id: ''
      };
    },
    props: {},
    methods: {
      onsubmit(formName){
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if(this.id) {
              let params = {
                "contactNumber": this.roleForm.contactNumber,
                "id": this.id,
                "name": this.roleForm.name,
                "positionStatus": "WORKING"
              }
              editEmployee(params).then(res=>{
                this.$message.success('编辑员工成功')
              })
            } else {
              let params = {
                userId: this.userId,
                name: this.roleForm.name,
                positionStatus: 'WORKING',
                contactNumber: this.roleForm.contactNumber
              }
              addEmployee(params).then(res => {
                this.$message.success('新增员工成功')
              })
            }

          } else {
            return false;
          }
        });

      },
      async onInputBlur() {

      },
      async getTree() {
        const { data } = await menuTree()
        this.menuData = data
      },
      async getDetail(id) {
        const {data} = await detail(id)
        this.roleForm = {
          contactNumber: data.contactNumber,//
          account: data.account,
          name: data.name
        }
        this.userId = data.userId
      },

    },
    mounted() {
      //this.getTree()
      this.id = this.$route.query.id
      if(this.id) {
        this.getDetail(this.id)
        this.isEdit = true
        this.title = '编辑员工'
      }
    },
    beforeDestroy() {}
  };
</script>
<style lang="less" scoped></style>
