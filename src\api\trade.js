import qs from "qs";
import request from '@/utils/request'
import requestAxios from '@/utils/requestAxios'
import requestExport from '@/utils/requestExport'
//订单数量
export function orderList(query) {
  return request({
    url: '/order/merchant/orderInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//订单统计数量
export function orderStatistics(data) {
  return request({
    url: `/order/merchant/orderInfo/statistics`,
    method: 'post',
    data
  })
}
//退货统计数量
export function salesReturnStatistics() {
  return request({
    url: `/order/merchant/salesReturnInfo/statistics`,
    method: 'get'
  })
}
//拒收统计数量
export function rejectionStatistics() {
  return request({
    url: `/order/merchant/rejection/statistics`,
    method: 'get'
  })
}
//退款统计数量
export function refundStatistics() {
  return request({
    url: `/order/merchant/salesRefundInfo/statistics`,
    method: 'get'
  })
}
export function orderDetail(id) {
  return request({
    url: `/order/merchant/orderInfo/getOrderInfoDetail/${id}`,
    method: 'get'
  })
}
//发货弹框
export function listProductDelivery(orderId) {
  return request({
    url: `/order/merchant/orderInfo/listProductDelivery/${orderId}`,
    method: 'get'
  })
}
//确认发货
export function addDelivery(query) {
  return request({
    url: '/order/merchant/orderInfo/addDelivery',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//审核
export function auditOrderInfo(query) {
  return request({
    url: '/order/merchant/orderInfo/auditOrderInfo',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//取消订单
export function addOrderCancel(query) {
  return request({
    url: '/order/merchant/orderInfo/addOrderCancel',
    method: 'post',
    params: query
  })
}

/**
 * 批量取消订单
 */
export function updateOrdersStatusToCancel(query) {
  return request({
    url: '/order/merchant/orderInfo/batchOrderCancel',
    method: 'post',
    data: query
  })
}
//申请拒收
export function saveApplyReject(query) {
  return request({
    url: `order/merchant/orderInfo/saveApplyReject`,
    method: 'post',
    params: query
  })
}
//修改订单备注
export function updateRemark(query) {
  return request({
    url: '/order/merchant/orderInfo/updateRemark',
    method: 'post',
    params: query
  })
}
//发货单类表
export function orderDeliveryList(query) {
  return request({
    url: '/order/merchant/orderDelivery/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//拒收弹出框
export function listRejectionWindowVo(deliveryInfoIdId) {
  return request({
    url: `/order/merchant/orderInfo/listRejectionWindowVo/${deliveryInfoIdId}`,
    method: 'get'
  })
}
//退款弹出框
export function listRefundWindowVo(orderId) {
  return request({
    url: `/order/merchant/orderInfo/listRefundWindowVo/${orderId}`,
    method: 'get'
  })
}
//调价
export function listModifyPriceVo(orderId) {
  return request({
    url: `/order/merchant/orderInfo/listModifyPriceVo/${orderId}`,
    method: 'get'
  })
}
//拒收列表
export function rejectionList(query) {
  return request({
    url: '/order/merchant/rejection/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//拒收详情
export function rejectionDetail(id) {
  return request({
    url: `/order/merchant/rejection/getRejectionInfoDetail/${id}`,
    method: 'get'
  })
}
//拒收确定收货
export function rejectionComfirm(id) {
  return request({
    url: '/order/merchant/rejection/confirmReceipt',
    method: 'post',
    params: {
      id: id
    }
  })
}
//审核成功
export function refuseRejection(id, refuseReason) {
  return request({
    url: '/order/merchant/rejection/refuseRejection',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核拒绝
export function agreeRejection(query) {
  return request({
    url: '/order/merchant/rejection/agreeRejection',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}


//退货
export function salesReturnInfoList(query) {
  return request({
    url: '/order/merchant/salesReturnInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function returnDetail(id) {
  return request({
    url: `/order/merchant/salesReturnInfo/getSalesReturnInfo/${id}`,
    method: 'get'
  })
}
//审核拒绝
export function refuseSalesReturn(id, refuseReason) {
  return request({
    url: '/order/merchant/salesReturnInfo/refuseSalesReturn',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核成功
export function agreeSalesReturn(query) {
  return request({
    url: '/order/merchant/salesReturnInfo/agreeSalesReturn',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//退款
export function salesRefundInfoList(query) {
  return request({
    url: '/order/merchant/salesRefundInfo/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function refundDetail(id) {
  return request({
    url: `/order/merchant/salesRefundInfo/getSalesRefundDetail/${id}`,
    method: 'get'
  })
}
//审核拒绝
export function refuseSalesRefund(id, refuseReason) {
  return request({
    url: '/order/merchant/salesRefundInfo/refuseSalesRefund',
    method: 'post',
    params: {
      id: id,
      refuseReason: refuseReason
    }
  })
}
//审核成功
export function agreeSalesRefund(query) {
  return request({
    url: '/order/merchant/salesRefundInfo/agreeSalesRefund',
    method: 'post',
    // data: query,
    data:qs.stringify(query),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
//确认收货
export function saleComfirm(id) {
  return request({
    url: '/order/merchant/salesReturnInfo/confirmReceipt',
    method: 'post',
    params: {
      id: id
    }
  })
}
//退款流水
export function refundFlowList(query) {
  return request({
    url: '/order/merchant/refundFlow/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//代客下单，获取订单价格信息

export function OrderConfirm(query) {
  return request({
    url: '/cart/admin/cart/OrderConfirm',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//保存下单

export function saveOrder(query) {
  return request({
    url: '/cart/admin/cart/saveOrder',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//物流
export function getExpressInfo(logisNo) {
  return request({
    url: `/general/admin/expressInfo/get`,
    method: 'get',
    params: {
      logisNo: logisNo
    }
  })
}
//快递种类
export function getExpressType(id) {
  return request({
    url: `/authority/parameter/${id}`,
    method: 'get'
  })
}
//获取订单统计价格
export function statisticsMoney(query) {
  return request({
    url: '/order/merchant/orderInfo/statisticsMoney',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}


// ++
// 获取订单列表 获取客户名称
export function purMerchantName(params) {
  return request({
    url: `/merchant/admin/purMerchant/feign/findByPurMerchantLikeName`,
    method: 'post',
    params: params,
    data: {
      "current": 1,
      "map": {},
      "model": {},
      "order": "descending",
      "size": 10,
      "sort": "id"
    }
  })
}
// ++

// 订单切换统计
export function statisticsGroupOrder() {
  return request({
    url: '/order/merchant/orderInfo/statisticsGroupOrder',
    method: 'get',
  })
}
// 订单切换统计
export function afterSaleMerchant(query) {
  return request({
    url: '/order/merchant/salesReturnInfo/afterSaleMerchant',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })

}


/**
 * 编辑客户ERP编码
 * @param {object} query { id: number -> 采购商id, customerCode: string -> 客户编码 }
 */
export function updateCustomerERPCode(query) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/updateCustomerCode',
    method: 'post',
    data: qs.stringify(query)
  })
}

/**
 * 编辑商品ERP编码
 * @param {object} query { productId: number -> 商品id, erpCode: string -> erp编码 }
 */
export function updateProductERPCode(query) {
  return request({
    url: '/product/admin/erpProductRel/orderErpMaintain',
    method: 'post',
    data: query
  })
}
/**
 * 订单自动审核设置
 * @param {object} query
 * @param {string} query.auditStatus 审核状态 [Y: 自动审核 | N: 人工审核]
 */
export function updateAutomaticOrderApprovalSettings(query) {
  return request({
    url: '/merchant/admin/saleMerchant/orderAutoAuditSet',
    method: 'post',
    data: qs.stringify(query)
  })
}

/**
 * 获取支付方式列表
 */
export function fetchPaymentMethodList() {
  return request({
    url: '/order/merchant/orderInfo/getOrderPayWay',
    method: 'get'
  })
  
}

/**
 * 获取订单明细导出数量
 */
export function fetchOrderDetailExportCount(data) {
  return request({
    url: '/order/merchant/orderInfo/detailExportCount',
    method: 'post',
    data
  })
}

/**
 * 查询订单自动审核设置
 */
export function fetchOrderAuditSettingStatus() {
  return request({
    url: '/merchant/admin/saleMerchant/getOrderAutoAuditSet',
    method: 'get'
  })
}


// 修改开单状态
export function updateBillStatus(data){
  return request({
    url: '/order/merchant/orderInfo/updateBillStatus',
    method: 'post',
    data:qs.stringify(data)
  })
}

// 待发货和不发货的列表
export function orderDetailList(data) {
  return request({
    url: '/order/merchant/orderInfo/orderDetailList',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 已发货列表
export function orderDetailDeliveryList(id) {
  return request({
    url: `/order/merchant/orderInfo/orderDetailDeliveryList/${id}`,
    method: 'get',
  })
}




//订单导出记录
export function exportOrderListLog(data) {
  return requestExport({
    method: 'post',
    url: '/order/merchant/orderInfo/export',
    data,
    headers: { responseType: 'blob' }
  })
}

/**
 * 导出订单列表
 */
export function exportOrderList(data) {
  return requestExport({
    method: 'post',
    url: '/order/merchant/orderInfo/pageExport',
    data,
    headers: { responseType: 'blob' }
  })
}

/**
 * 导出订单明细
 */
 export function exportOrderDetails(data) {
  return requestExport({
    method: 'post',
    url: '/order/merchant/orderInfo/detailExport',
    data,
    headers: { responseType: 'blob' }
  })
}

// 订单日志
export function getOrderDetailLog(id) {
  return request({
    url: `/order/merchant/orderInfo/orderDetailLog/${id}`,
    method: 'post',
  })
}

// ============================== 以下是代客下单的新接口 start

/**
 * 切换收货地址 --> 代客下单用户切换不同的收货地址
 * */ 
export function saveReceiveAddr(data) {
  return request({
    url: '/order/order/cart/saveReceiveAddr',
    transformRequest: [function() {
      return qs.stringify(data)
    }],
    data,
    method: 'post',
    headers:{'Content-type': 'application/x-www-form-urlencoded '}
  })
}

/**
 * 购物车商品选择--分页查询
 * */ 
export function getProductCartPage(data) {
  return request({
    url: '/product/admin/product/getProductCartPage',
    method: 'post',
    data
  })
}
/**
 * 导出发货单
 */
 export function exportShippingOrders(data) {
  return requestExport({
      url: `/order/merchant/orderDelivery/export/delivery/info`,
      method: 'post',
      data,
      headers: { responseType: 'blob' }
  })  
}

/**
 * 新增/修改物流信息
 */
 export function postOrderLogistics(data) {
  return request({
    url: '/order/merchant/orderDelivery/setLogistics/info',
    method: 'post',
    data
  })
}

//获取所有的仓库
export const getAllStore = () => {
  return requestAxios({
      method: 'get',
      url: `product/warehouseManagement/getAllWarehouse`
  })
}

/**
 * 批量加入购物车接口 ----> 批量加进货单
 * */ 
export function addCart(data) {
  return request({
    url: '/order/order/cart/batchAddItems',
    method: 'post',
    data,
  })
}

/**
 * 展示商家支付方式
 * */ 
export function showPayByPurchasing(saleMerchantId) {
  return request({
    url: `/general/frontend/paymentInterface/showPayByPurchasing/${saleMerchantId}`,
    method: 'get',
  })
}

/**
 * 获取购物车基本信息
 * */ 
export function basicInfo(purMerchantId){
  return request({
    url: `/order/order/cart/basic/info/${purMerchantId}`,
    method: 'get',
  })
}


/**
 * 获取购物车
 * */ 
export function showCart(purMerchantId) {
  return request({
    url: `/order/order/cart/showCart/${purMerchantId}`,
    method: 'get',
  })
}

/**
 * 删除购物车商品
 * */ 
export function delCartProduct(data) {
  return request({
    url: '/order/order/cart/removeItem',
    method: 'post',
    data
  })
}

/**
 * 更新购物车项数量
 * */ 
export function updateQuantity(data) {
  return request({
    url: '/order/order/cart/updateQuantity',
    method: 'post',
    data
  })
}


/**
 * 结算页价格信息
 * 该接口涉及 1.手动调价,2.使用优惠券,3.购物车的价格信息
 * */ 

export function orderFormPriceInfo(data) {
  return request({
    url: '/order/order/cart/getOrderFormPriceInfo',
    method: 'post',
    data
  })
}

/**
 * 满减活动列表
 * */ 
export function fullReduceList(data) {
  return request({
    url: '/product/wechat/fullReduce/listByProductIds',
    method: 'post',
    data: qs.stringify(data),
    headers:{
      'Content-type': 'application/x-www-form-urlencoded '
    }
  })
}

/**
 * 设置满减活动规则
 * */ 
export function setFullReductionActivity(data) {
  return request({
    url: '/order/order/cart/setFullReductionActivity',
    method: 'post',
    data
  })
}

/**
 * 赠品接口list
 * */ 
 export function activityList(data) {
  return request({
    url: '/order/order/cart/full/reduce/activity/list',
    method: "post",
    data
  })
}

/**
 * 选择赠品
 * */ 
export function itemGiveaway(data) {
  return request({
    url: '/order/order/cart/select/item/giveaway',
    method: "post",
    data
  })
}

/**
 * 切换价格
 * */ 
export function switchItenPrice(data) {
  return request({
    url: '/order/order/cart/select/item/price',
    method: 'post',
    data
  })
}

/**
 * 可领取的优惠券列表
 * */ 
export function listBySaleMerchantIdCoupon(data) {
  return request({
    url: '/order/order/cart/coupon/listBySaleMerchantId',
    method: 'post',
    data
  })
}

// 用户点击领取优惠券
export function receiveCou(data) {
  return request({
    url: '/order/order/cart/coupon/receive',
    method: 'post',
    data
  })
}

/**
 * 可使用优惠券
 * */ 
export function listByOrderConfirmCoupon(data) {
  return request({
    url: '/order/order/cart/coupon/listByOrderConfirm',
    method: 'post',
    data
  })
}

/**
 * 保存订单 (代客下单专用)
 * */ 
export function saveOrderValut(data) {
  return request({
    url: '/order/order/cart/saveOrder',
    transformRequest: [function() {
      return qs.stringify(data)
    }],
    data,
    method: 'post',
    headers:{'Content-type': 'application/x-www-form-urlencoded '}
  })
}

// ============================== 代客下单 end

/**
 * 订单改价
 * */ 
export function orderPriceChangeApi(data) {
  return request({
    url: '/order/merchant/orderInfo/orderPriceChange',
    method: "put",
    data
  })
}

// 下单渠道
export function getApplicationList(data) {
  return request({
    url: '/order/merchant/orderInfo/applicationList',
    method: "get",
    data
  })
}

// 查询退款设置
export function getRefundMethodSetting() {
  return request({
    url: '/merchant/admin/saleMerchant/getRefundMethodSetting',
    method: "get"
  })
}

// 设置退款设置
export function refundMethodSetting(refundMethod) {
  return request({
    url: '/merchant/admin/saleMerchant/refundMethodSetting?refundMethod='+refundMethod,
    method: "post"
  })
}


