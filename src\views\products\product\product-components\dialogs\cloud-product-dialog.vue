<template>
  <el-dialog
    title="选择云库产品"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose" v-loading="loading">
    <div class="search-wrapper-bar">
      <el-input v-model="query.productCode"  placeholder="请输入产品编码"></el-input>
      <el-input v-model="query.productName"  placeholder="请输入产品名称"></el-input>
      <el-input v-model="query.manufacturer"  placeholder="请输入厂家"></el-input>
      <el-button type="primary" @click="onSearch">搜索</el-button>
      <el-button  @click="onReset">重置</el-button>
    </div>

    <el-table :data="tableData" border>
      <el-table-column label="序号" type="index" width="50" fixed="left" align="center" />
      <el-table-column label="选择" width="50" fixed="left" align="center">
        <template slot-scope="scope">
          <el-radio v-model="radio" :label="scope.$index"></el-radio>
        </template>
      </el-table-column>
      <el-table-column label="产品主图" width="150" class-name="img-cell">
        <template slot-scope="scope">
          <!-- <img v-for="(item, idx) of splitString(scope.row.pictIdS)" :key="idx" :src="item" width="50px" height="50px"> -->
          <img v-if="scope.row.pictIdS.length>0" :src="splitString((scope.row.pictIdS))" width="50px" height="50px">
          <img v-else src="@/assets/img/index/product_default.png" width="50px" height="50px" />
        </template>
      </el-table-column>
      <el-table-column label="产品编码" prop="productCode" width="150" show-overflow-tooltip />
      <el-table-column label="产品名称" prop="productName" width="150" show-overflow-tooltip />
      <el-table-column label="规格" prop="spec" width="100" show-overflow-tooltip />
      <el-table-column label="单位" prop="unit" show-overflow-tooltip />
      <el-table-column label="批准文号" prop="approvalNumber" width="200" show-overflow-tooltip />
      <el-table-column label="剂型" prop="agentiaType" width="100" show-overflow-tooltip />
      <el-table-column label="生产厂家" prop="manufacturer" width="150" show-overflow-tooltip />
      <el-table-column label="品牌" prop="brandName" width="150" show-overflow-tooltip />
      <el-table-column label="经营类目" prop="businessRangeName" width="200" show-overflow-tooltip />
      <el-table-column label="产品条形码" prop="barCode" width="150" show-overflow-tooltip />
    </el-table>
    <el-pagination
      background
      @size-change="load"
      @current-change="load"
      :current-page.sync="page"
      :page-size.sync="pageSize"
      layout="->, prev, pager, next, sizes, jumper"
      :total="total"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'

export default {
  data () {
    return {
      loading: false,
      visible: false,
      search: '',
      tableData: [],
      radio: 0,
      page: 1,
      pageSize: 10,
      total: 0,
      query: {
        productCode: '', // 编码
        productName: '', // 名称
        manufacturer: '', // 厂家
      }
    }
  },
  mounted () {
    this.load()
  },
  methods: {
    onSearch () {
      this.page = 1;
      this.total = 0;
      this.load();
    },
    onReset () {
      this.query.manufacturer = '';
      this.query.productName = '';
      this.query.productCode = '';
      this.load()
    },
    async load () {
      this.loading = true
      const { data } = await request.post('product/admin/productPlatform/anno/selectProductPlatform', {
        current: this.page,
        size: this.pageSize,
        map: {},
        model: {...this.query},
        order: 'descending',
        sort: 'id'
      })
      this.tableData = data.records
      this.page = data.current
      this.total = data.total
      this.loading = false
    },
    handleClose(done) {
      done()
    },
    splitString (val) {
      return val.split(',')[0]
    },
    handleConfirm () {
      this.$emit('getData', this.tableData[this.radio])
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.search-wrapper-bar{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .el-input{
    width: 200px;
    margin-right: 10px;
  }
}
.el-table{
  margin-bottom: 20px;
  ::v-deep{
    .el-radio{
      .el-radio__label{
        display: none;
      }
    }
  }
}
</style>
