<template>
  <div class="mini">
    <section class="mini-header" :class="{'boxShadow': header.backgroundGradient == 'Y'}" :style="{background: `url(${header.picUrl})`}">
      <img src="@/assets/imgs/miniBanner.png">
      <p>公告：{{header.notice}}</p>
    </section>
    <section class="mini-couponList">
      <div class="mini-couponWrap">
        <div class="mini-couponList_flexBox" v-for="(items, i) in coupons" :key="i">
          <div class="mini-couponList_flexBoxItem0">
            <h3>{{items.price}}元</h3>
            <p>满{{items.racePrice}}元使用</p>
          </div>
          <div class="mini-couponList_flexBoxItem1">
            <p>优惠券</p>
            <span>点击领取</span>
          </div>
        </div>
      </div>
    </section>

    <section class="mini-productList">
      <div class="mini-productList__tab">
        <div
          class="mini-productList__tabItem"
          v-for="(items, i) in tabs"
          :key="i"
          :class="{'active': activeClass === i}"
          @click="changeClass(i)"
          >
          {{items.name}}
        </div>
      </div>
      <div class="mini-productList__list" v-if="details">
        <div class="mini-productList__listItem"  v-for="(items, index) in details" :key="index">
          <div class="mini-productList__listImg">
            <img :src="items.product.pictIdS|getMainPic" width="100%" v-if="items.product" />
          </div>
          <div class="mini-productList__listDetail">
            <h3>{{items.product.title}}</h3>
            <p>厂家：{{items.product.manufacturer}}</p>
            <p>规格：{{items.product.spec}} &nbsp; 单位：{{items.product.until}}</p>
            <div class="mini-productList__listPrice">
              <p>￥100</p>
              <div class="plusIcon">
                <i class="el-icon-plus" style="color: #fff;"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  props: ['header', 'detail', 'active'],
  data () {
    return {
      activeClass: 0,
      tabs: [
        {
          name: '商家推荐'
        },
        {
          name: '新品'
        },
        {
          name: '非药品'
        }
      ],
      coupons: [
        {
          price: 40,
          racePrice: 399
        },
        {
          price: 60,
          racePrice: 500
        },
        {
          price: 100,
          racePrice: 1000
        }
      ],
      details: [
      ]
    }
  },
  filters:{
    getMainPic:function(pics){
      if(pics!=null&& pics.length>0){
        var picList=pics.split(",")
        if(picList.length>0){
          return picList[0]
        }
      }else{
        return ""
      }
    }
  },
  methods: {
    changeClass (i) {
      this.activeClass = i
      this.details = this.detail[this.activeClass].itemList
    },
    init () {

      this.details = this.detail[this.activeClass].itemList
      this.tabs = this.detail.map((items) => {
        return {
          name: items.name
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .mini{
    width: 375px;
    margin-top: 48px;
    .boxShadow{
      box-shadow: 0 -50px 0 rgba(255,255,255, .7) inset;
    }
    &-header{
      width: 100%;
      padding-bottom: 15px;
      background-position: center;
      background-repeat: no-repeat;
      img{
        width: 100%;
      }
      p{
        padding: 5px 10px;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        background: #fff;
        margin: -5px 16px 0 16px;
        border-bottom-left-radius: 2px;
        border-bottom-right-radius: 2px;
      }
    }

    &-couponWrap{
      width: 100%;
      overflow:auto;
      display: -webkit-box;
    }


    &-couponList{
      background: #eeeeee;
      padding: 10px 0;
      width: 375px;

      &_flexBox{
        display: flex;
        width: 185px;
        justify-content: flex-start;
        align-items: center;
        margin-right: 5px;
        div{
          text-align: center;
          h3, p{
            margin: 0;
          }
        }
      }

      &_flexBoxItem0{
        background: #0084ff;
        padding: 10px 0;
        color: #fff;
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        width: 55%;
        p{
          padding-top: 5px;
        }
      }

      &_flexBoxItem1{
        background: #fff;
        padding: 10px 0;
        width: 45%;
        p{
          color: #0084ff;
        }
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        span{
          display: block;
          background: #0084ff;
          color: #fff;
          border-radius: 10px;
          margin: 5px 8px 0;
        }
      }
    }

    &-productList{
      background: #fff;
      &__tab{
        display: -webkit-box;
        border-bottom: 1px solid #e3e3e3;
        width: 100%;
        overflow: auto;
      }
      &__tabItem{
        padding: 10px;
        width: 100px;
      }
      .active{
        color: #0084ff;
        font-weight: bold;
      }
      &__list{
        height: 350px;
        overflow: auto;
      }
      &__listItem{
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      &__listImg{
        width: 35%;
        padding: 10px;
      }

      &__listDetail{
        width: 75%;
        padding: 0 15px;

        h3, p{
          margin: 0;

        }
        h3{
          font-size: 14px;
        }
        p{
          font-size: 12px;
          color: #999999;
        }
      }

      &__listPrice{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 5px;
        border-bottom: 1px solid #e3e3e3;
        p{
          color: rgb(236, 137, 47);
          font-size: 14px;
          font-weight: bold;
        }
        .plusIcon{
          width: 20px;
          height: 20px;
          background: #0084ff;
          text-align: center;
          border-radius: 50%;
        }
      }
    }

    &-couponList::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }
</style>
