<template>
  <div :class="$style.container">
    <el-dialog width="70%" v-bind="$attrs" v-on="$listeners">
      <!-- 搜索表单 START -->
      <im-search-pad-dialog :as-expand="false" :hasExpand="false" :model="model" @reset="handleReset"
                            @search="handleSearch"
      >
        <im-search-pad-item-dialog v-for="(field,indexField) in fields" :key="indexField" :prop="field.prop">
          <el-input v-if="field.type === 'Text' || !field.type" v-model.trim="model[field.prop]"
                    :placeholder="field.placeholder || field.text"
          />
          <el-select v-else-if="['Select', 'MultipleSelect'].includes(field.type)" v-model="model[field.prop]"
                     collapse-tags :multiple="field.type === 'MultipleSelect'"
                     :placeholder="field.placeholder || field.text" clearable
          >
            <el-option v-for="(option,optionIndex) in field.options" :key="optionIndex" :value="option.value"
                       :label="option.text"
            />
          </el-select>
          <region-picker v-else-if="field.type === 'Region'" v-model="model[field.prop]"
                         :placeholder="field.placeholder || field.text"
          >
          </region-picker>
        </im-search-pad-item-dialog>
        <template slot="button">
          <slot name="button"/>
        </template>
      </im-search-pad-dialog>
      <table-pager row-key="id" reserve-selection ref="table" :height="height" :options="computedColumns"
                   :remote-method="fetch" :data.sync="items" :selection="!enableRadio" @selectionAll="handleAllSelect"
                   @selectionChangeHandle="handleSelectChange" :page-size="size" :is-need-button="false"
                   :pagination-is-center="true" :selected-num="enableRadio ? radioValue ? 1 : 0 : selects.length || 0"
                   :is-stripe="false"
      >
        <!-- 列表 START -->
        <el-table-column show-overflow-tooltip v-for="(col, index) in computedColumns" :key="index" :slot="col.prop"
                         :label="col.label" :align="col.align" :width="col.width"
        >
          <template slot-scope="scope">
            <el-radio @change="onRadioChange(scope.row)" :class="$style.radio" v-if="col.type === 'Radio'"
                      v-model="radioValue" :label="scope.row[matchField]" :disabled="scope.row.selectable === true"
            ></el-radio>
            <div v-else-if="col.render" v-html="col.render(scope.row)" :class="$style.ellipsis"></div>
            <el-image v-else-if="col.type === 'Image'" :class="$style.image"
                      :src="scope.row[col.prop] | imgFilter"
                      :preview-src-list="scope.row[col.prop] | imageFilterPreview"
            ></el-image>
            <div :class="$style.ellipsis" v-else style="{color: col.color}" v-html="scope.row[col.prop]">
            </div>
          </template>
        </el-table-column>
        <!-- 列表 END -->
        <!-- 按钮 START -->
        <div slot="handleButton">
          <el-button :class="$style.btn" :disabled="submitting" @click="onCancel">{{ cancelText }}</el-button>
          <el-button v-if="$attrs.title !== '查看关联用户'" :class="$style.btn" v-throttle type="primary" :loading="submitting" @click="onOk">
            移除{{ selects.length > 0 ? `(${ selects.length })` : "" }}
          </el-button>
        </div>
        <!-- 按钮 END -->
      </table-pager>
    </el-dialog>
  </div>
</template>

<script>
import ImSearchPadDialog from "@/components/eyaolink/ImSearchPadDialog/index";
import ImSearchPadItemDialog from "@/components/eyaolink/ImSearchPadItemDialog/index";
import RegionPicker from "@/components/RegionPicker/RegionPicker.vue";
import { merchantType } from "@/api/archivesList";
import { patchDeleteImportCustomer } from "@/api/merchantApi/customerlabel";

export default {
  components: {
    ImSearchPadDialog,
    ImSearchPadItemDialog,
    RegionPicker
  },
  props: {
    // 启用单选
    enableRadio: {
      type: Boolean,
      default: false
    },
    // 当前选择的数据
    data: {
      type: Array,
      default: () => { }
    },
    // 限制选择的数据数量 9999 默认为不限制
    limit: {
      type: Number,
      default: 9999
    },
    // 匹配字段
    matchField: {
      type: String,
      default: "id"
    },
    formatResponse: {
      type: Function,
      default: data => data
    },
    okText: {
      type: String,
      default: `确 定`
    },
    cancelText: {
      type: String,
      default: `取 消`
    },
    checkType: {
      type: String,
      default: () => {
        return "";
      }
    },
    request: {
      type: Function
    },
    customerTagId: {
      type: String,
      default: () => {
        return "";
      }
    },
    isBySalesman: { //业务客户列表
      type: Boolean,
      default: false
    },
  },
  computed: {
    // 列 替身
    computedColumns() {
      let columns = this.columns;
      if(this.isBySalesman){
        columns.push({
          label: "所属业务员", prop: "saleManName" ,width:'100px'
        })
      }
      return columns?.map(column => {
        column = { ...column, slot: true, name: column.prop };
        if (column.type === "Image" && !column.align) {
          column.align = "center";
        }
        return column;
      }) || [];
    },
    computedOkText() {
      let currentTab = this.tabs[ this.current ].button || { okText: this.okText };
      return currentTab.okText;
    }
  },
  data() {
    return {
      radioValue: null,
      radioOther: null,
      current: 0, // 当前的tabs页索引
      height: 0, // 表格高度
      size: 10, // 表格每页条数
      model: {}, // 请求接口参数
      loading: false, // 加载中
      submitting: false, // 提交中
      items: [], // 每页数据
      selects: [], // 已选中数据
      columns: [
        { label: "客户编码", prop: "customerCode" },
        { label: "客户名称", prop: "name" },
        {
          label: "企业类型", prop: "merchantType", render(row) {
            // 这里为了兼容业务员管理没有merchantType字段而是采用merchantTypeName
            return row.merchantType || row.merchantTypeName;
          }
        },
        { label: "联系人", prop: "ceoName" },
        { label: "联系电话", prop: "ceoMobile" },
        { label: "所在仓库", prop: "warehouseNames" },
        {
          label: "所在地区", prop: "region", render(row) {
            // 这里为了兼容业务员管理没有region字段而是由provinceName、cityName、countyName
            return row.region || `${ row.provinceName }${ row.cityName }${ row.countyName }`;
          }
        }
      ],
      fields: [
        { prop: "keyword", text: "客户名称/客户编码" },
        {
          prop: "merchantTypeIds",
          text: "企业类型",
          type: "MultipleSelect",
          options: []
        },
        {
          type: "Region",
          prop: "address",
          text: "所在地区"
        }
      ],
    };
  },
  async mounted() {
    console.log(this.isBySalesman,'业务员')
    this.fetchMerchantTypes();
  },
  //方法集合
  methods: {
    async fetchMerchantTypes() {
      const { code, data } = await merchantType();
      if (code === 0) {
        this.fields.splice(1, 1, {
          ...this.fields[ 1 ],
          options: data.map(item => ({ value: item.id, text: item.name }))
        });
      }
    },
    async fetchWarehouseList() {
      let { code, data } = await getAllStore();
      if (code === 0) {
        this.fields.splice(2, 1, {
          ...this.fields[ 2 ],
          options: data.map(item => ({ value: item.id, text: item.name }))
        });
      }
    },
    formatQueryParamsBeforeFetch(params) {
      if (params.model.address) {
        let { address, ...model } = params.model;
        const [provinceId, cityId, countyId] = address;
        const district = countyId;
        params = {
          ...params,
          model: {
            ...model,
            provinceId, cityId, countyId, district
          }
        };
      }
      return params;
    },
    refresh() {
      this.$refs.table.clearSelection();
      this.initModelValues();
      console.log("refresh");
      this.$refs.table.doRefresh();
    },
    onRadioChange(data) {
      this.radioOther = data;
    },
    onCancel() {
      this.$emit("cancel");
      this.$emit("close");
      this.$emit("update:visible", false);
    },
    onOk() {
      if (!this.enableRadio && !this.selects.length) {
        this.$message.warning("至少选择一条数据");
        return;
      }
      if (this.enableRadio && !this.radioValue) {
        this.$message.warning("请先选中数据");
        return;
      }
      let values = this.enableRadio ? this.radioOther || this.radioValue : this.selects;
      let idsArr = [];
      values.forEach(item => {
        idsArr.push(item.id);
      });
      patchDeleteImportCustomer(idsArr).then(res => {
        if (res.code === 0 && res.msg === "ok") {
          this.$emit("ok", values, this.current);
          this.$emit("close");
          this.$emit("update:visible", false);
        }
      });
    },
    handleReset() {
      this.$emit("reset");
      this.initModelValues();
      console.log("handleReset");
      this.$refs.table.doRefresh();

    },
    handleSearch() {
      this.$emit("search");
      console.log("handleSearch");
      this.$refs.table.doRefresh();
    },

    handleAllSelect(selects) {
      console.log("handleAllSelect", selects);
      this.selects = selects;
      this.$emit("change", selects);
    },
    handleBeforeClose() {
      this.$emit("cancel");
      this.$emit("close");
      this.$emit("update:visible", false);
    },
    handleSelectChange(selects) {
      // 判断是否限制勾选个数
      if (this.limit !== 9999 && selects.length > this.limit) {
        this.$refs.table.clearSelection();
        selects = selects.slice(0, this.limit);
        this.$nextTick(() => {
          selects.forEach(row => {
            this.$refs.table.toggleRowSelection(row, true);
          });
        });
        this.$message.warning(`最多只能选择${ this.limit }个`);
      }
      this.selects = selects;
      this.$emit("change", selects);
    },
    initModelValues(fields = []) {
      let model = {};
      fields.forEach(field => {
        let defaultValue = "";
        if (["MultipleSelect", "Region"].includes(field.type)) {
          defaultValue = [];
        }
        model[ field.prop ] = defaultValue;
      });
      this.model = model;
    },
    async fetch(params = {}) {
      let query = {
        model: {
          ...this.model,
          customerTagId: this.customerTagId,
          // specifyType: this.checkType
        }
      };
      if (this.checkType) {
        query.model.specifyType = this.checkType;
      }
      let queryParams = this.formatQueryParamsBeforeFetch({ ...query, ...params });
      let result = {};
      if (queryParams.model && queryParams.model.merchantTypeIds && queryParams.model.merchantTypeIds.length === 0){
        delete queryParams.model.merchantTypeIds;
      }
      queryParams.model.source = this.isBySalesman ? 2 : 1
      try {
        const request = this.request;
        this.loading = true;
        result = await request(queryParams);
        result = this.formatResponse(result);
        this.selects.forEach(row => {
          this.$refs.table.toggleRowSelection(row, true);
        });
      } catch ( error ) {

      } finally {
        this.loading = false;
      }
      return result;
    },
  },
};
</script>

<style lang="scss" module>
.container {
  position: relative;

  .radio {
    :global {
      .el-radio__label {
        display: none;
      }
    }
  }

  .image {
    width: 40px;
    height: 40px;
  }

  .btn {
    border-radius: 2px;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tabs {
    user-select: none;

    .item {
      position: relative;
      display: inline-block;
      margin-right: 20px;
      cursor: pointer;
      transition: all .3s;

      &::after {
        content: "";
        position: absolute;
        bottom: -15px;
        left: 0;
        right: 0;
        width: 100%;
        height: 2px;
        border-radius: 2px;
        transform: scaleX(0);
        transition: all .3s;
      }

      &.active {
        color: #0056E5;
        font-weight: 500;

        &::after {
          transform: scaleX(1);
          background-color: #0056E5;
        }
      }
    }
  }

  :global {
    .el-dialog__header {
      font-size: 16px;
      padding: 14px;
    }

    .el-dialog__body {
      padding: 12px;
    }

    .el-dialog__headerbtn {
      top: 16px;
    }

    .el-form-item {
      margin-bottom: 14px;
    }
  }
}
</style>
