<template>
  <div class="tab_bg no-margin-top">
    <tabs-layout
      v-model="activeName"
      :tabs="tabs"
      @change="handleClick"
    >
      <template slot="button">
        <el-button v-if="checkPermission(['admin','weChat:edit']) && activeName=='2'" type="primary" @click="save('wechatForm')">保存</el-button>
      </template>
    </tabs-layout>
      <div v-show="activeName==='1' && tabs.length > 0" class="detail-items">
        <el-form :inline="true" label-width="140px" :model="query">
          <!-- 基础信息 -->
          <div class="item">
            <page-module-title title="基础信息" />
            <el-row>
              <el-col :span="6">
                <el-form-item class="formItem" label="商家编码:">
                  {{query.code}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="商家名称:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
                  {{query.name}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="商家识别码:" :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]">
                  {{query.identifyCode}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="社会统一信用代码:">
                  {{query.socialCreditCode}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="法定代表人:">
                  {{query.legalPerson}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="负责人:" :rules="[{ required: true, message: '请填写产品分类',trigger: 'blur' }]">
                  {{query.ceoName}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="负责人手机:" :rules="[{ required: true, message: '请填写产品规格',trigger: 'blur' }]">
                  {{query.ceoMobile}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="所在区域:" :rules="[{ required: true, message: '请填写产品品牌',trigger: 'blur' }]">
                  {{query.region}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="注册资金:">
                  {{query.registerCapital}} (万)
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="店铺名称:" :rules="[{ required: true, message: '请填写生产厂家',trigger: 'blur' }]">
                  {{query.shopName}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="经营开始时间:" :rules="[{ required: true, message: '请填写产品计量单位',trigger: 'blur' }]">
                  {{query.managementStartDate}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="productNumber" label="经营结束时间:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                  {{query.managementEndDate}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item class="formItem" prop="productNumber" label="注册地址:" :rules="[{ required: true, message: '请填写产品剂型',trigger: 'blur' }]">
              {{query.registerAddress}}
            </el-form-item>

          </div>
          <!-- 账户信息begin -->
          <div class="item">
            <page-module-title title="账户信息" />
              <el-row>
                <el-col :span="6">
                  <el-form-item class="formItem" prop="productNumber" label="登录账号:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                    {{query.loginAccount}}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item class="formItem" prop="productNumber" label="手机号码:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                    {{query.userMobile}}
                  </el-form-item>
                </el-col>
              </el-row>
          </div>
          <!-- 账户信息end -->
          <!-- 结款信息begin -->
          <div class="item">
            <page-module-title title="结款信息" />
            <el-row>
              <el-col :span="6">
                <el-form-item class="formItem" prop="bankAccount" label="银行账户:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                  {{query.bankAccount}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="bankNumber" label="银行账号:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                  {{query.bankNumber}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item class="formItem" prop="bankName" label="开户银行:" :rules="[{ required: true, message: '请填写产地',trigger: 'blur' }]">
                  {{query.bankName}}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- 结款信息end -->
          <!-- 经营类目begin -->
          <div class="item">
            <page-module-title title="经营类目" />
            <div style="padding: 10px 30px 30px 20px" class="cateGory">
             <el-row :gutter="20" v-if="cateGory" style="width:100%">
                <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8" :style="'width:' + getFlexNum()+'%;flex:1;display:flex;flex-wrap:wrap;'">
                  <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids" :key="ite.id">{{ite.label + '、'}}</span>
                </el-col>
              </el-row>
              <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
            </div>
          </div>
          <!-- 经营类目 -->
          <!-- 结算规则begin -->
          <div class="item">
            <page-module-title title="结算规则" />
            <div>
              <el-form-item label-width="150px" label="佣金计算规则: " :rules="[{message: '请选择佣金计算规则'}]">
                <el-radio-group v-model="query.statementRule" :disabled="true">
                  <el-radio v-if="query.statementRule == 'N'" label="N">不收取佣金</el-radio>
                  <el-radio v-if="query.statementRule == 'Y'" label="Y">按订单金额比例</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div>
              <el-form-item v-if="query.statementRule=='Y'" label-width="150px" class="formItem" prop="orderAmountRate" label="金额比例:">
                <el-input style="width: 200px" disabled v-model="query.orderAmountRate" placeholder="请填写金额比例">
                  <i slot="suffix">%</i>
                </el-input>
              </el-form-item>
            </div>
          </div>
          <!-- 结算规则end -->
          <!-- 商家资质begin -->
          <div class="item">
            <page-module-title title="商家资质" />
            <lisence-table :lisenceTableDate.sync="lisenceTableDate" :edit="['admin','business-qualification:edit']" :confirm="['admin','business-qualification:submit']" />
          </div>
          <!-- 商家资质end -->
          <!-- 发货地址begin -->
          <div class="item">
            <page-module-title title="发货地址" />
            <template>
              <el-table :data="deliveryAddressList" style="width: 100%" border>
                <el-table-column prop="name" label="发货人姓名" width="120"></el-table-column>
                <el-table-column prop="mobilPhone" label="联系手机" width="140"></el-table-column>
                <el-table-column prop="fixedPhone" label="联系电话" width="140"></el-table-column>
                <el-table-column prop="region" label="发货区域" width="200"></el-table-column>
                <el-table-column prop="detailedAddress" label="详细地址"></el-table-column>
                <el-table-column label="是否默认" width="150">
                  <template slot-scope="{ row }">
                    <span>{{ row.defaultOrNot.code=='Y'?'默认':'非默认' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="是否启用" width="150">
                  <template slot-scope="{ row }">
                    <span>{{ row.isOpen.code=='Y'?'启用':'禁用' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </div>
          <!-- 发货地址end -->
        </el-form>
      </div>
      <div v-show="activeName==='2' && tabs.length > 0" style="margin-top: 10px;height:600px">
        <el-form ref="wechatForm" :rules="rules" :model="wechatForm" label-width="120px">
          <el-form-item label="商家LOGO">
            <div style="display:flex">
              <el-upload
                action
                :show-file-list="false"
                :before-upload="handleBefore"
                :http-request="upload"
                accept=".jpg,.png,.bmp,.jpeg"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
              >
                <div>
                  <img v-if="wechatForm.logoPath" class="el-upload-list__item-thumbnail" :src="wechatForm.logoPath" alt="点击上传商家LOGO" style="width: 100px;height: 100px;border: 1px dashed #ddd;">
                  <p v-if="!wechatForm.logoPath" style="margin-top:0;width: 100px;height: 100px;border: 1px solid #ddd;background: #f5f5f5;"><span style="font-size: 26px;display: block;margin-top: 15px;">+</span>上传</p>

                </div>

              </el-upload>
              <div style="margin-left:10px">上传要求:上传的图片大小不能超过1M</div>
            </div>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
              </el-dialog>
          </el-form-item>
          <el-form-item label="电话客服" prop="phone">
            <el-col :span="8">
              <el-input v-model="wechatForm.customerService.phone" placeholder="请输入电话客服" @input="onInput()" />
            </el-col>
          </el-form-item>
          <el-form-item label="QQ在线客服" prop="serviceQq">
            <el-col :span="8">
              <el-input v-model="wechatForm.customerService.serviceQq" placeholder="请输入QQ在线客服" @input="onInput()" />
            </el-col>
          </el-form-item>
          <el-form-item label="appId" prop="appId">
            <el-col :span="8">
              <el-input v-model="wechatForm.appId" placeholder="请输入商家的appId" @input="onInput()" />
            </el-col>
          </el-form-item>
          <el-form-item label="域名" prop="host">
            <el-col :span="8">
              <el-input v-model="wechatForm.host" placeholder="请输入访问域名" @input="onInput()" />
            </el-col>
          </el-form-item>
      </el-form>
    </div>
    <el-dialog title="图片预览" width="60%" :visible.sync="imgVisible">
      <img :src="dialogImageUrl" style="margin: 0 auto;display: block;width: 100%;">
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getListByLicenseBaseType, trees, editMerchantLicense } from '@/api/group'
import { findMerchantTypeDetailsById, editSaleMerchant, addrList } from '@/api/settingCenter'
import { getUploadFileUrl, beforeFileUpload } from '@/api/upload'
import checkPermission from '@/utils/permission'
import { filter as _filter } from 'lodash'
import LisenceTable from "./lisenceTable.vue";
// import addrTable from "./addrTable";
export default {
  components: {
    LisenceTable,
    // addrTable
  },
  data() {
    const checkRtelePhone = (rule, value, callback) => {
      if (value) {
        const reg = /^0\d{2,3}-?\d{7,8}$/ // “0”表示以0开头的区号，后面跟2-3位，电话号码7-8位；“-”表示用户在符合条件处可以输入“-”；“\d”表示纯数字；“$”表示结束。
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error('请输入正确的电话客服'))
        }
      } else {
        callback()
      }
    }
    const checkPhone = (rule, value, callback) => {
      if (value) {
        const reg = /^1\d{10}$/
        if (!reg.test(value)) {
          callback(new Error('请输入11位手机号'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const checkQQ = (rule, value, callback) => {
      if (value) {
        const reg = /^[1-9][0-9]{4,9}$/gim
        if (!reg.test(value)) {
          return callback(new Error('请输入正确的qq'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      tabs: [],
      tableData: [],
      saleMerchantId: '',
      type: 'MERCHANT',
      merchantLicense: [],
      merchantLicenses: [],
      dialogImageUrl: '',
      dialogVisible: false,
      address: [],
      options: [],
      id: '',
      logoPath: '',
      editing: false,
      disabled: true,
      edit: {
        licenseNumber: '',
        licenseEndTime: '',
        filePath: '',
        id: ''
      },
      uploadingList: [],
      ids: '',
      imgVisible: false,
      activeName: '1',
      saveType: 'enterprise',
      customerServiceId: '',
      deliveryAddressList:[],
      wechatForm: {
        appId:'',
        host:"",
        customerService: {
          phone: '',
          serviceQq: ''
        }
      },
      rules: {
        phone: [{ validator: checkRtelePhone, trigger: 'blur' }],
        ceoMobile: [
          { validator: checkPhone, trigger: 'blur' }
        ],
        serviceQq: [
          { validator: checkQQ, trigger: 'blur' }
        ]
      },
      query:{},
      cateGory: false,
      lisenceTableDate: [],
    }
  },
  mounted() {
    this.getDetail()
    this.getArea()
    this.$nextTick(() => {
      this.clearValidate('wechatForm')
    });
    this.gerAddress();
    const tabs = [
      { name: '企业资料', value: '1', hide: !checkPermission(['admin', 'sale-saas-shop:enterpriseInfo', 'sale-platform-shop:enterpriseInfo']) }, 
      { name: '店铺资料', value: '2', hide: !checkPermission(['admin', 'sale-saas-shop:shopInfo', 'sale-platform-shop:shopInfo']) }
    ]
    this.tabs = tabs.filter(item =>!item.hide)
    this.$nextTick(()=>{
      if (this.tabs.length === 0) return
      this.activeName = this.tabs[0].value
    })
  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    getFlexNum(){
      let num = 0
      this.cateGory
      for (const key in this.cateGory) {
        num++
      }
      if(num == 1){
        return 100
      } else if(num ==2) {
        return 50
      } else {
        return 33
      }
    },
    onInput() {
      this.$forceUpdate()
    },
    handleClick(tab, index) {
      if (tab.value === '1') {
        this.saveType = 'enterprise'
      } else {
        this.saveType = 'shop'
      }
    },
    // 商家资质编辑
    async editHandle(row, index) {
      row.editing = true
      this.logoPath = row.filePath
    },
    previewHandle(row) {
      this.dialogImageUrl = row.filePath
      this.imgVisible = true
    },
    cancelHandle(row) {
      row.editing = false
    },
    async gerAddress() {
      let { data } = await addrList();
      this.deliveryAddressList = data;
    },
    async submitHandle(row) {
      await editMerchantLicense({
        'filePath': this.logoPath,
        'id': row.id,
        'licenseBaseId': row.licenseBaseId,
        'licenseEndTime': row.licenseEndTime,
        'licenseNumber': row.licenseNumber,
        'merchantId': row.merchantId
      })
      this.$message.success('编辑成功！')
      row.editing = false
    },
    clearValidate(formName) {
      this.$nextTick(() => {
        // 等dom渲染结束再调用，要不然是undefined
        this.$refs[formName].clearValidate()
      })
    },
    async save(formName) {
      const data = await editSaleMerchant({
        'id': this.id,
        'logoPath': this.wechatForm.logoPath,
        "appId": this.wechatForm.appId,
        'host': this.wechatForm.host,
        'customerServiceUpdateDTO': {
            'phone': this.wechatForm.customerService.phone,
            'serviceQq': this.wechatForm.customerService.serviceQq,
            'id': this.customerServiceId
          }
      })
      if (data.code === 0) {
            this.$message.success('编辑成功！')
            findMerchantTypeDetailsById().then(res => {
              if (res.code === 0) {
                sessionStorage.removeItem('merchants-detail')
                sessionStorage.setItem('merchants-detail', JSON.stringify(res.data))
                this.getDetail()
              }
            })
          } else {
            return false
          }
      // this.$refs[formName].validate(async valid => {
      //   if (valid) {
      //     const data = await editSaleMerchant({
      //       'ceoMobile': this.wechatForm.ceoMobile,
      //       'ceoName': this.wechatForm.ceoName,
      //       'cityId': this.address[1],
      //       'countyId': this.address[2],
      //       'id': this.id,
      //       'legalPerson': this.wechatForm.legalPerson,
      //       'loginAccount': this.wechatForm.loginAccount,
      //       'logoPath': this.wechatForm.logoPath,
      //       'name': this.wechatForm.name,
      //       'password': this.wechatForm.password,
      //       'provinceId': this.address[0],
      //       'registerAddress': this.wechatForm.registerAddress,
      //       'registerCapital': this.wechatForm.registerCapital,
      //       'userMobile': this.wechatForm.userMobile,
      //       'customerServiceUpdateDTO': {
      //         'phone': this.wechatForm.customerService.phone,
      //         'serviceQq': this.wechatForm.customerService.serviceQq,
      //         'id': this.customerServiceId
      //       }
      //     })

      //   }
      // })
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    getId(row, column) {
      this.ids = row.id
    },

    async upload(fileObj) {
      const { data } = await getUploadFileUrl(fileObj.file)
      this.wechatForm.logoPath = data.url
      this.dialogImageUrl = data.url
    },
    async uploadFile(fileObj) {
      const { data } = await getUploadFileUrl(fileObj.file)
      this.logoPath = data.url
      this.updateUploadingList(this.ids)
    },
    updateUploadingList(id) {
      const successIndex = this.uploadingList.indexOf(id)
      this.uploadingList.splice(successIndex, 1)
    },
    handleBefore(file) {
      return beforeFileUpload(file, this.$message)
    },
    getDetail() {
      const data = JSON.parse(sessionStorage.getItem('merchants-detail'))
      this.wechatForm = data;
      this.query = data;
      let obj = {};
      if (!data.businessCategoryDetailList) {
        this.cateGory = false;
      } else {
        data.businessCategoryDetailList.forEach((item) => {
          if (!obj[item.parentName]) {
            obj[item.parentName] = [];
            obj[item.parentName].push(item);
          } else {
            obj[item.parentName].push(item);
          }
        });
        this.cateGory = obj;
      }
      this.query.statementRule = data.statementRule.code;
      // console.log('data-------------------->',data);
      this.merchantLicense = data.merchantLicenses
      this.wechatForm.logoPath = data.logoPath
      this.getType()
      this.address = [data.provinceId, data.cityId, data.county_id]
      this.id = data.id
      if (data.customerService) {
        this.wechatForm.customerService.phone = data.customerService.phone
        this.wechatForm.customerService.serviceQq = data.customerService.serviceQq
        this.customerServiceId = data.customerService.id
      } else {
        this.customerServiceId = 0
        this.wechatForm.customerService = {
          serviceQq: '',
          phone: ''
        }
      }

    },
    async getType() {
      const { data } = await getListByLicenseBaseType(this.type)
      data.map((item, i) => {
        this.merchantLicense.map((info, j) => {
          if (item.id === info.licenseBaseId) {
            this.merchantLicenses.push({
              name: item.name,
              licenseNumber: info.licenseNumber,
              licenseEndTime: info.licenseEndTime ? info.licenseEndTime : '长期',
              filePath: info.filePath,
              id: info.id,
              editing: false,
              licenseBaseId: info.licenseBaseId,
              merchantId: info.merchantId
            })
          }
        })
      });
      // console.log('=============data==>',data);
      data.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          id: "",
          filePathList: [],
          limit: item.multiple.code == "Y" ? 5 : 1,
        };
        JSON.parse(sessionStorage.getItem('merchants-detail')).merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
            obj.isForever = ids.isForever.code === 'Y'
          }
        });
        // console.log('商家资质---->',obj);
        this.lisenceTableDate.push(obj);
      });
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    // 地区
    async getArea() {
      const { data } = await trees()
      this.options = data
    },
    parentChangeAction(val) {
      this.address = val
    },
    // 编辑商家资质
    async editTable() {
      await editMerchantLicense({
        'filePath': '',
        'id': 0,
        'isForever': '',
        'licenseBaseId': 0,
        'licenseEndTime': '',
        'licenseNumber': '',
        'licenseStartTime': '',
        'merchantId': 0,
        'reminderDateType': ''
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table_area {
  width: 100%;
  font-size: 14px;
  color: #505465;
}
.table_header {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .header_item {
    background-color: #eee;
    line-height: 40px;
    height: 40px;
    text-align: center;
    font-size: 14px;
    color: #505465;
  }
  .header1 {
    width: 100%;
    border-right: 1px solid #ddd;
  }
  .header2 {
    // width: 10%;
  }
}
.table_body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // border: 1px solid #ddd;
  margin-bottom: 10px;
  .body_item {
    height: 100px;
    text-align: center;
  }
  .area_item1 {
    width: 100%;
    border: 1px solid #ddd;
    // font-size: 14px;
    font-size: 14px;
    color: #505465;
    .noData {
      line-height: 100px;
    }
    .haveData {
      margin-top: 30px;
    }
  }
  .area_item2 {
    color: #409eff;
    cursor: pointer;
    line-height: 100px;
    width: 10%;
  }
}

.cateGory{
  display: flex;
  .el-col-8{
    word-break:break-all;
    font-size: 14px;
    line-height: 20px;
    padding-bottom: 10px;
    text-overflow: wrap;
  }
}
</style>
