<template>
<el-dialog title="协议商品" width="70%"  v-bind="$attrs" v-on="$listeners" v-dialogDrag @opened="handleOpen" :close-on-click-modal="false" @close="clearData">
  <el-table :data="list" border stripe class="items-table" v-loadig="loading">
    <el-table-column type="index" align="center" width="80">
      <template slot="header">
        <span>序号</span>
      </template>
    </el-table-column>
    <el-table-column prop="imgUrl" label="商品主图" align="center">
      <template slot-scope="scope">
        <img :src="scope.row.pictIdS | imgFilter" width="50px"/>
      </template>
    </el-table-column>
    <el-table-column prop="erpCode" label="ERP商品编码" width="180">

    </el-table-column>
    <el-table-column prop="productName" label="商品名称" width="160" show-overflow-tooltip></el-table-column>
    <el-table-column prop="spec" label="规格" width="120"></el-table-column>
    <el-table-column prop="unit" label="单位"></el-table-column>
    <el-table-column prop="manufacturer" label="生产厂家" width="180"></el-table-column>
    <el-table-column label="销售价">
      <template v-slot="scope">
        <span class="sale-price">{{ scope.row.salePrice }}</span>
      </template>
    </el-table-column>
    <el-table-column label="成本价" width="100">
      <template v-slot="scope">
        <span class="cost-price">{{ scope.row.costPrice }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="price" label="协议价" width="100">
      <template v-slot="scope">
        <el-input class="price-input"

                  v-if="scope.row.editMode"
                  v-model="scope.row.inputPrice"></el-input>
        <span v-else>{{ scope.row.price }}</span>
      </template>
    </el-table-column>
  </el-table>
	<!-- 分页 -->
	<div class="page-row">
		<el-pagination
			background
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
			:current-page="page"
			:page-sizes="[10, 20, 50, 100]"
			:page-size.sync="limit"
			layout="total, sizes, prev, pager, next, jumper"
			:total="totalCount"
		>
		</el-pagination>
	</div>
</el-dialog>
</template>

<script>
import { agreeProductDetail } from '@/api/products/agreemenPrice'
export default {
	name: 'CheckProductDialog',
	props: {
		negotiatedPriceId: {
			type: String,
			default: () => {
				return ''
			}
		}
	},
	data() {
		return {
			loading: false,
      list: [],
			totalCount: 0,
			limit: 10,
			page: 1,
			model: {
				negotiatedPriceId: ''
			}
		}
	},
	methods: {
		handleOpen() {
			this.model = {...this.model,negotiatedPriceId: this.negotiatedPriceId }
			this.initData()
		},
		initData() {
      this.loading = true;
			const query = {...this.model}
			Object.keys(query).forEach((key) => {
				if (!query[key]) delete query[key]
			})
			let params = {
        current: this.page,
        map: {},
        model: {
          ...query,
        },
        order: "descending",
        size: this.limit,
        sort: "id",
      };
      agreeProductDetail(params).then(res => {
					if(res.code === 0) {
              this.list = res.data.records || [];
              this.totalCount = res.data.total;
					}
					this.loading = false;
				})
		},
		clearData() {},
		handleSizeChange(val) {
      this.limit = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.initData();
    }
	}
}
</script>

<style lang="scss" scoped>
.items-table {
  .item-img {
    width: 40px;
    height: 40px;
    background-color: #eee;
    display: block;
  }
  .sale-price {
    color: #FF6600;
  }

  .cost-price {
    color: #339900;
  }

  .price-input {
    ::v-deep input {
      padding: 0 5px;
    }
  }
}
.page-row {
	margin-top: 10px;
	display: flex;
	justify-content: flex-end;
}
</style>