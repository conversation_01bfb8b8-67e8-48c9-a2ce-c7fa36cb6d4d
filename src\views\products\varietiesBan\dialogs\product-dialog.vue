<template>
  <el-dialog
    title="选择商品"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose"
  >
    <im-search-pad
      :has-expand="false"
      :model="search"
      @reset="reload"
      @search="load"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model="search.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="search.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model="search.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-select v-model="search.stockCondition" placeholder="库存" style="width: 110px">
          <el-option label="库存" value="ALL" />
          <el-option label="有库存" value="WITH" />
          <el-option label="没库存" value="WITHOUT" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>

    <el-table ref="productTable" :data="productList" border>
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="选择" width="50">
        <template slot-scope="scope">
          <el-radio v-model="radio" :label="scope.$index" />
        </template>
      </el-table-column>
      <el-table-column label="产品主图" width="80" class-name="img-cell">
        <template slot-scope="scope">
          <img v-if="scope.row.pictIdS" :src="splitString(scope.row.pictIdS)" width="50px" height="50px">
          <img v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''" :src="pictImg" width="50px" height="50px">
        </template>
      </el-table-column>
      <el-table-column label="商品编码" prop="productCode" width="180" show-overflow-tooltip />
      <el-table-column label="商品名称" prop="productName" width="200" show-overflow-tooltip />
      <el-table-column label="规格" prop="spec" width="150" show-overflow-tooltip />
      <el-table-column label="单位" prop="unit" width="60" />
      <el-table-column label="生产厂家" prop="manufacturer" min-width="200" show-overflow-tooltip />
      <el-table-column label="库存" prop="stockQuantity" width="100" show-overflow-tooltip />
    </el-table>
    <el-pagination
      @size-change="load"
      @current-change="load"
      :current-page.sync="page"
      :page-size.sync="pageSize"
      layout="->, prev, pager, next, sizes, jumper"
      :total="total"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import load from 'jszip/lib/load'
import {productList} from '@/api/product.js'
import productImg from "../../../../assets/product.png";
export default {
  components: {
  },
  data () {
    return {
      pictImg: productImg,
      loading: false,
      visible: false,
      stock: '',
      productList: [],
      radio: 0,
      page: 1,
      pageSize: 10,
      total: 0,
      keywordSelect: 1,
      keyword: '',
      products: [],
      model: {
        stockCondition: ''
      },
      search: {
        stockCondition: 'ALL',
        productCode: '',
        productName: '',
        manufacturer: ''
      }
    }
  },
  mounted () {
    this.load()
  },
  methods: {
    reload() {
      this.search =  {
        stockCondition: 'ALL',
          productCode: '',
          productName: '',
          manufacturer: ''
      }
      this.load()

    },
    async querySearch(queryString, cb) {
      let params = {
        model: {
          "productCode": this.search.productCode,
          "productName": this.search.productName,
          "stockCondition": this.search.stockCondition,
          "manufacturer": this.search.manufacturer
        },
        order: "descending",
        sort: "id"
      }
      const {data} = await productList(params)
      this.products = data.records
      for(let item in data.records){
        this.products.push({'value': item.approvalUserName})
      }
      // 调用 callback 返回建议列表的数据
      cb(this.products)
    },
    handleSelect(item) {
      this.listQuery.model.productId = item.id
    },
    async load () {
      this.loading = true
      const { data } = await request.post('product/admin/product/page', {
        current: this.page,
        map: {},
        model: {
          "productCode": this.search.productCode,
          "productName": this.search.productName,
          "stockCondition": this.search.stockCondition,
          "manufacturer": this.search.manufacturer
        },
        order: 'descending',
        size: this.pageSize,
        sort: 'id'
      })
      if(data) {
        this.productList = data.records
        this.page = data.current
        this.total = data.total
      } else {
        this.productList = []
        this.page = 0
        this.total = 0
      }
      this.loading = false
    },
    handleClose(done) {
      done()
    },
    handleConfirm () {
      this.$refs.productTable.clearSelection()
      this.$emit('getData', this.productList[this.radio])
      this.visible = false
    },
    splitString (val) {
      return val.split(',')[0]
    }
  }
}
</script>

<style lang="scss" scoped>
.search-wrapper{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .el-input{
    width: 200px;
    margin-right: 10px;
  }
}
.el-table{
  margin-bottom: 20px;
  ::v-deep{
    .el-radio{
      .el-radio__label{
        display: none;
      }
    }
  }
}
</style>
