<template>
  <div class="detail-wrapper">
    <page-title title="奖励详情" />
    <div class="content">
      <div class="text_wrapper">
        <span class="item" v-for="item in userData" :key="item.key">
          <span>{{ item.label }}：</span>
          <span v-if="item.key === 'settleMethod'">{{ detail[item.key] && detail[item.key].desc }}</span>
          <span v-else>{{ detail[item.key] }}</span>
        </span>
      </div>
      <div class="settlement_status">
        <img class="icon" src="../../../assets/imgs/order_icon.png" />
        <div class="right">
          <div class="title">{{ detail.settleStatus && detail.settleStatus.desc }}</div>
          <div class="text" v-if="detail.settleStatus">
            <span>当前{{ detail.settleStatus.desc }}，</span>
            <span v-if="detail.settleStatus.code === 'HAD'">已结算时间：{{ detail.settleTime || '-' }}</span>
            <span v-else-if="detail.settleStatus.code === 'AUTO'">自动结算时间：{{ detail.waitSettleTime || '-' }}</span>
            <span v-else>可结算时间：{{ detail.waitSettleTime || '-' }}</span>
          </div>
        </div>
      </div>
      <page-module-card title="奖励计算">
        <div style="margin-bottom: 10px">奖励金额 = 实际成交金额*奖励比例：{{ rewardMoney }}</div>
        <div style="margin-bottom: 20px">其他奖励：{{ detail.otherReward || '-'}}</div>
        <rewardSettingTable :tableData="detail.rewardSetting ? [detail.rewardSetting] : []" :canEdit="false" />
      </page-module-card>
      <page-module-card title="订单信息" v-if="query.model.settlementId">
        <div style="text-align: right; font-size: 16px; margin-bottom: 10px">实际成交金额：{{ detail.realAmount }}</div>
        <table-pager
          ref="table"
          :isNeedButton="false"
          :options="tableTitle"
          :data.sync="orderList"
          :operation-width="100"
          :remote-method="setRewardSettlementDetailOrderList"
        >
          <template slot="orderNo">
            <el-table-column label="订单/退款编号">
              <slot slot-scope="{row}">
                <div>编号：{{ row.orderNo }}</div>
                <div>支付：{{ row.payTime }}</div>
                <div>完成：{{ row.finishTime }}</div>
              </slot>
            </el-table-column>
          </template>
          <template slot="purName">
            <el-table-column label="客户信息">
              <slot slot-scope="{row}">
                <div>{{ row.purName }}</div>
                <div></div>
                <div>ERP客户编码：{{ row.purErpCode }}</div>
              </slot>
            </el-table-column>
          </template>
          <template slot="productName">
            <el-table-column label="商品信息">
              <slot slot-scope="{row}">
                <div>{{ row.productName }}</div>
                <div>{{ row.spec }}</div>
                <div>编码：{{ row.productErpCode }}</div>
              </slot>
            </el-table-column>
          </template>
        </table-pager>
      </page-module-card>
    </div>
  </div>
</template>

<script>
import { getRewardSettlementDetail, getRewardSettlementDetailOrderList } from "@/api/retailStore";
import rewardSettingTable from "@/views/merchant/membersFissile/rewardSettingTable.vue";

const TableColumns = [
  {
    label: "订单/退款编号",
    prop: 'orderNo',
    name: 'orderNo',
    slot: true
  },
  {
    label: '客户信息',
    prop: 'purName',
    name: 'purName',
    slot: true
  },
  {
    label: '商品信息',
    prop: 'productName',
    name: 'productName',
    slot: true
  },
  {
    label: '成交单价',
    prop: 'unitMoney',
  },
  {
    label: '购买数量',
    prop: 'totalNum',
  },
  {
    label: '成交/退款金额(元)',
    prop: 'realAmount',
  },
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}

export default {
  name: 'rewardSettlementDetail',
  components: {
    rewardSettingTable
  },
  data() {
    return {
      detail: {},
      tableTitle: TableColumnList,
      query: {
        current: 1,
        model: {
          settlementId: ''
        }
      },
      orderList: [],
      orderListTotal: 0,
      userData: [
        { label: '奖励人', key: 'objectName' },
        { label: '结算单编号', key: 'id' },
        { label: '创建时间', key: 'createTime' },
        { label: '结算方式', key: 'settleMethod' },
      ]
    }
  },
  created() {
    this.query.model.settlementId = this.$route.query.id
    this.setRewardSettlementDetail()
  },
  computed: {
    rewardMoney() {
      try {
        const { realAmount, settingRatio } = this.detail
        if (realAmount && settingRatio) {
          const result = (parseFloat(realAmount) * (parseInt(settingRatio) / 100)).toFixed(4)
          return `${realAmount} * ${settingRatio}% = ${result}`
        }
        return ''
      } catch (e) {
        console.error("计算奖励金额异常: ", e)
        return ''
      }
    }
  },
  methods: {
    setRewardSettlementDetail() {
      getRewardSettlementDetail(this.query.model.settlementId).then(res => {
        this.detail = res
      })
    },
    async setRewardSettlementDetailOrderList() {
      try {
        const res = await getRewardSettlementDetailOrderList(this.query)
        this.orderList = res.data.records
        this.orderListTotal = res.data.total
        return res
      } finally {
        this.listLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  .text_wrapper {
    .item {
      margin-right: 30px;
    }
  }
  .settlement_status {
    background-color: #f5f5f5;
    padding: 20px;
    margin: 20px 0;
    display: flex;
    .icon {
      width: 35px;
      height: 35px;
    }
    .right {
      flex: 1;
      margin-left: 15px;
      .title {
        font-size: 18px;
        margin-bottom: 6px;
        color: #0056E5;
      }
    }
  }
}
</style>
