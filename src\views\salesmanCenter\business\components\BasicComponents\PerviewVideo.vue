<template>
    <div :class='$style.container'>
        <div @click="onImageClick" :class='$style["img-warp"]'>
            <img :src="poster" alt="视频">
        </div>
        <el-dialog title="视频预览" :visible.sync="visible">
            <video :class="$style.video" controls autoplay :src="url"></video>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        poster: {
            type: String,
            default: ''
        },
        url: {
            type: String,
            required: true
        }

    },
    data() {
        return {
            visible: false
        }
    },
    methods: {
        onImageClick() {
            this.visible = true;
        }
    },
    components: {

    }
}
</script>
<style lang='scss' module>
.video {
    width: 100%;
    max-height: 60vh;
}
.container {
    width: 100%;

    .img-warp {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        position: relative;
        cursor: pointer;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 32px;
            height: 32px;
            transform: translate(-50%, -50%);
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAAXNSR0IArs4c6QAAAV9QTFRFAAAA////////////////////////////////////////////////////////////////////////////////Agob////DBQiFhorFh0rHiMzHiUzJSw8NTtJNTtLPUNQ////Q0pW////UlhkUVdj////WFxoWF5oXmRu////ZGh0ZGhzZGlzam55////cHN/cHV/b3N+////dXiBdXqBen6HeoCH////gIOMf4KLhIeQ////jZCXjZCZkpWckpWdlpqglZmfmp2knqConqKoqq2yqauxrbC0////////t7m9t7m/////u7zBu7zCvL/D////wMLGwsXJzs/TztDT////0NLV09XX////2Nnb////2tze3d7h3N3g3+Dh4ePk4ePl////////6urs6uvs////7Ozu////8vLz////8fHy8/P0////9fX39/f39/f4+fn5/////Pz8/v7+////3eXCVQAAAHR0Uk5TAAQIEBcYGxwgIyQnKDc4OzxTV1tfZmdoamptbW90dHZ3eXt9fn+AgIKDhIWFh4eJiYqLjIyOjo+QkZOTmJiampydn6GhqKmrq6+ysrO0tLe3ubzFxcfIysvPz9HT1NbY2Nvf4uLj5Ofr6+zu7/Dz8/X3+vxL+NLOAAADJUlEQVRYw52Xa1tTRxSFN6AFL1S5DAGlhKtcEqmNpkcQRAUttFCIJoqEaA6FEpIAmeP7/59+AAqEmZM5WV9n1nrmtvesJWJBR/f02vahBn2wszrb1S6R0On51MFPPXBlt436QK2wsTQz9FipgXhycaNQA/aG21zoE1UoZZIxdQ2xZ5sl0N6tBvSWvioU03Xsc7zMw1FfSxj/Xg6KSWXF1C7k7tj5PZrKvArFQgnda1v+K8gOqgboz0LKuI3WdYIV5YCVgK1WA3+LkxfKCelTg0LLFsdjyhFjx6zX7yLFiTNfqbFTvOv8XoLfVQQ8D+i5yr+jeaciYQV978oB5MiqiMiSuzyGPkoDUQUGK/Rd8G8fsaAiY57qRXF67KomUGTivIA1U80IJC6WMExeqeaWMCoiInu8NE+IPUmEP2l8EZGHlM3jI/uwH7a5WIlOEUmxaR7/ChB8CFHI4ImIz6/m4R9n7fj7iFUgiS/STs3cAdVFQ6+9tu6hRod0WR/B5Z/wxdanCnTLLH81FKBsaTUbTMsqi40F4KOxWF6zJjvMuAiYL3SGbTkg7iRgvNBfOBTNgJsAvL0x5RFaQLkK7JrmRBHIGwU0j10Fbj6ofrQcMuR4iO9vTolzINsknAT+nTQWw46sseQg8CNjvKtFVmWavxsLVNK2pzwr3Q7F9NleTF3S0bic34SU808iPsnQhvItbm0oz/BFxCNjHt4FCP6I2VvaJikR+ZmSec7IP7A/GdaWSzwQEfFJ20zZZCyMP8eeiIiMUmzuY8kzfPa1VUk2w59Cn/+uE80tofC/zWmrMh+dv8DRrUuDURmMyu8vXxqMpizOp6sWR+5rlqPxl9F3r9lsghdR+OmAOsv9itNxd/74Cal6q7vOsbPC+DFbLQazfepoVp+f2Ox64GRXlwPWW42BIwXZhoZz8BN4ttzUqyk3sJzzFXSPPTTdzUEhxFc9LULufnjsO4L8nLkDzhWhGh77ROS2p6G8+Vt98ExkSlCdcMquw3tArfDnYiLer9SjoZmls+jrj7a5xueHqZvh2+uMFuDbu2ZXdw406MPttenuDtu8/wCSOfX++zolpQAAAABJRU5ErkJggg==');
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
</style>