<template>
  <el-dialog class="product-modal" :title="title" :visible.sync="visible" width="1000px">
    <div class="product-wrap">
      <div v-loading="loading">

        <div class="detail-dispage">
          <span>{{detail.name}}</span>
          <span v-if="detail.promotionPackageState&&detail.promotionPackageState.code==='ON'" style="color: #20B02c;background-color: #e6F6e7; ">上架中</span>
          <span v-if="detail.promotionPackageState&&detail.promotionPackageState.code==='OFF'" style="background-color: #FFE9EB;color: #ff7088;">已下架</span>
        </div>
        <el-table ref="multipleTable"  :data="checkList" border>
          <el-table-column label="序号" type="index" width="80" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column label="销售状态" prop="whetherOnSale">
            <template slot-scope="{row}">
              <span v-if="row.whetherOnSale.code === 'Y'">上架中</span>
              <span class="text-primary" v-if="row.whetherOnSale.code === 'N'">已下架</span>
            </template>
          </el-table-column>
          <el-table-column label="是否主产品" prop="main" width="100">
            <template slot-scope="{row}">
              <span v-if="row.main.code === 'Y'">是</span>
              <span v-if="row.main.code === 'N'">否</span>
            </template>
          </el-table-column>
          <el-table-column label="商品编码" prop="productCode" width="180"></el-table-column>
          <el-table-column label="商品名称" prop="productName" width="200"></el-table-column>
          <el-table-column label="规格" prop="spec"></el-table-column>
          <el-table-column label="生产厂家" prop="manufacturer" width="250px"></el-table-column>
          <el-table-column label="套餐单价" prop="price">
            <template slot-scope="{row}">
              <span class="text-warning">{{row.price}}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="num"></el-table-column>
          <el-table-column label="销售价" prop="salePrice" width="100" />
          <el-table-column label="成本价" prop="costPrice" width="100"/>
          <el-table-column label="库存" prop="stockQuantity" width="100" />

        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script>

  export default {
    props: ['checkList','detail'],
    data () {
      return {
        // 获取row的key值
        getRowKeys(row) {
          return row.id;
        },
        model:{
          productCode: '',
          productName: '',
          whetherUseCoupon: '',
          whetherReturnable: '',
          ids: []
        },
        visible: false,
        list: [],
        total: 0,
        page: 1,
        pageSize: 10,
        loading: false,
        selection: [],
        isSelect: false,
        title: ''
      }
    },

    methods: {
      close() {
        this.visible = false
      },

      open() {
        this.visible = true

      },

    },
    watch: {
      checkList(){
        this.title = '查看商品（' + this.checkList.length + '）'
      }
    }
  }
</script>

<style lang="scss">
  .detail-dispage {
    position: absolute;
    top: 20px;
    left: 150px;
    span {
      display: inline-block;
      text-align: center;
      line-height: 24px;
      padding: 0 15px;
      height: 24px;
      color: #0056E5;
      margin-right: 10px;
      font-size: 12px;
      background: #e5eefc;
      border-radius: 2px;
    }
  }
</style>
