import { param } from '@/utils'
import requestAxios from '@/utils/requestAxios'

// 分页查询角色下面的成员
export function salesManList(data) {
    //submitType:  ADD 添加  DEL移除 
    return requestAxios({
        url: '/authority/merchant/orgRole/salesManList',
        method: 'post',
        data
    })
}

// 组织角色修改
export function orgRole(data) {
    return requestAxios({
        url: `/authority/merchant/orgRole?${param(data)}`,
        method: 'put',
        headers: { 'content-type': 'application/x-www-form-urlencoded' }
    })
}

// 新增角色下面的成员
export function addSalesMan(data) {
    return requestAxios({
        url: '/authority/merchant/orgRole/addSalesMan',
        method: 'post',
        data
    })
}

// 新增业务员获取业务员列表
export function pageSalesMan(data) {
    return requestAxios({
        url: '/authority/merchant/orgRole/pageSalesMan',
        method: 'post',
        data
    })
}

// 批量删除角色下面的成员
export function deleteSalesMan(data) {
    return requestAxios({
        url: '/authority/merchant/orgRole/deleteSalesMan',
        method: 'post',
        data
    })
}

// 批量修改成员所属角色
export function updateSalesMan(data) {
    return requestAxios({
        url: '/authority/merchant/orgRole/updateSalesMan',
        method: 'post',
        data
    })
}
