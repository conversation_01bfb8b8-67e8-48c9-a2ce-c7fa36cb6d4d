<template>
	<el-form :model="form" label-width="90px" label-position="right" :disable="isDetail">
		<div class="basic-information">
      <form-item-title title="基础信息"/>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="商品编码">
            <el-input v-model="form.productCode" disabled placeholder="系统自动生成产品唯一标识码" :readonly="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="经营类目" required>
            <el-select v-model="form.businessRangeId" :disabled="isDetail">
              <el-option
                v-for="(item, idx) of businessRangeList"
                :key="idx"
                :label="item.name"
                :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="平台分类" prop="platformCategoryId">
            <el-cascader
              :disabled="isDetail"
              v-model="form.platformCategoryId"
              :options="platformCategoryList"
              :props="{ checkStrictly: true, emitPath: false, label: 'label', value: 'id' }"
              style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品分类" prop="categoryId">
            <el-cascader
              :disabled="isDetail"
              v-model="form.categoryId"
              :options="categoryList"
              :props="{ checkStrictly: true, emitPath: false, label: 'label', value: 'id' }"
              style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="商品名称" required>
            <el-input v-model="form.productName" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="通用名称">
            <el-input v-model="form.drugName" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品标题">
            <el-input v-model="form.title" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌" required>
            <el-input v-model="form.brand.name" disabled>
              <template slot="append">
                <el-button plain slot="append" @click="onShowBandDialog" :disabled="isDetail">从品牌库中添加</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="规格" required>
            <el-input v-model="form.spec" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="剂型" required>
            <el-input v-model="form.agentiaType" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计量单位" :rules="{validator: validateNumber, trigger: 'change'}" prop="measurement">
            <el-input v-model="form.measurement" :disabled="isDetail" type="number">
              <el-select class="unit" v-model="formUnitSelect" slot="append" :disabled="isDetail">
                <el-option value="g" />
                <el-option value="对" />
                <el-option value="扎" />
                <el-option value="kg" />
                <el-option value="条" />
                <el-option value="只" />
                <el-option value="头" />
                <el-option value="个" />
                <el-option value="枝" />
                <el-option value="包" />
                <el-option value="粒" />
                <el-option value="张" />
                <el-option value="盒" />
                <el-option value="捆" />
                <el-option value="支" />
                <el-option value="朵" />
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="包装单位" required>
            <el-input v-model="form.unit" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="生产厂家" required>
            <el-input v-model="form.manufacturer" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="产地">
            <el-input v-model="form.area" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="批准文号">
            <el-input v-model="form.approvalNumber" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="中包装" required prop="midPackTotal" :rules="{validator: int7, trigger: 'change'}">
            <el-input v-model="form.midPackTotal" type="number" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="件包装" required prop="packTotal"  :rules="{validator: int7, trigger: 'change'}">
            <el-input v-model="form.packTotal" type="number" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="本位码">
            <el-input v-model="form.standardCode" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="条形码">
            <el-input v-model="form.barCode" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="处方类型" required>
            <el-select v-model="form.otcType" :disabled="isDetail">
              <el-option value="RX" label="RX"/>
              <el-option value="A_OTC" label="OTC_甲"/>
              <el-option value="B_OTC" label="OTC_乙"/>
              <el-option value="OTHER" label="其它"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="助记码">
            <el-input v-model="form.mnemonicCode" disabled placeholder="输入商品名称自动生成"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="ERP编码" >
            <el-input v-model="form.erpCode" :disabled="isDetail"  placeholder="请输入ERP商品id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="效期" >
            <el-date-picker
            style="width: 100%"
              :disabled="isDetail"
              v-model="form.expDate"
              type="date"
              default-time="00:00:00"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属仓库" required>
            <el-select v-model="form.warehouseId" prop="warehouseId" :disabled="isDetail">
              <el-option
                v-for="(item, idx) of storeArr"
                :key="idx"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <band-dialog @getData="onSelectBand" ref="bandDialog"/>
    </div>
	</el-form>
</template>

<script>
import _ from 'lodash'
import formItemTitle from '@/views/products/common-components/form-item-title'
import request from '@/utils/request'
import bandDialog from './dialogs/band-dialog';
import {
    getAllStore
  } from "@/api/products/store";
export default {
	components: { formItemTitle, bandDialog },
  props: {
    action: {
      type: String,
    },
    isNumber2: {
      type: Function
    },
    isInt7: {
      type: Function
    }
  },
	data () {
		return {
      businessRangeList: [],
      platformCategoryList: [],
      platformCategoryList3: [], // 第三极
      categoryList: [],
      categoryList3: [], // 第三级
			form: {
        drugName: '', // 通用名称
				productCode: '', //产品编码
				businessRangeId: '', //经营类目
				platformCategoryId: '', //平台类目
				categoryId: '', //商品分类
				productName: '', //产品名称
				title: '', //商品标题
        brandId: '10000',
				brand: {id: '10000', name: '其它'}, //品牌
				spec: '', //规格
				agentiaType: '其它', //剂型
        measurement: '', //计量单位
				unit: '', //包装单位
				manufacturer: '', //生产厂家
				area: '', //产地
				approvalNumber: '', //批准文号
				midPackTotal: 1, //中包装
				packTotal: 1, //件包装
				standardCode: '', //本位码
				barCode: '', //条形码
				otcType: 'OTHER', //是否处方
				mnemonicCode: '', //助记码
        erpCode: '', // ERP商品id，
        expDate: '',  // 效期
        warehouseId: ''
			},
      formUnitSelect: 'g',
      storeArr: []
		}
  },
  computed: {
    isDetail () {
      return this.action === 'SHOW'
    }
  },
  mounted () {
    this.getStoreList()
    this.getBusinessRangeList()
    this.getPlatformCategoryList()
    this.getCategory()
  },
  methods: {
    /**
       * @description 获取全部仓库的下拉列表
       * <AUTHOR>
       */
      getStoreList() {
        getAllStore().then(res => {
          const data = res.data;
          if(data && data.length) {
            data.forEach(item => {
              if(item.defaultWarehouse.code === 'Y') {
                this.form = {...this.form,warehouseId: item.id}
              }
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeArr.push(obj)
            })
          }
        })
      },
    int7 (rule, value, cb) {
      if (!this.isInt7(value)) {
        return cb(new Error('只能是正整数，且最多7位'));
      }
      cb();
    },
    validateNumber (rule, value, cb) {
      // 输入正数，保留小数点后两位
      if (!this.isNumber2(value)) {
        return cb(new Error('只能输入正数，且小数最多两位'));
      }
      cb();
    },
    validatePlatformCategory (rule, value, cb) {
      return true
      // if (this.platformCategoryList3.indexOf(value) > -1) {
      //   cb();
      //   return true;
      // }
      // cb(new Error('必须选到第三级'))
      // return false
    },
    validateCategory (rule, value, cb) {
      return true
      // if (this.categoryList3.indexOf(value) > -1) {
      //  cb();
      //  return true;
      // }
      // cb(new Error('必须选到第三级'));
      // return false;
    },
	  getData () {
	    if (!this.form.businessRangeId) {
	      this.$message.error('请选择经营类目');
	      return false;
      }
      // if (!this.form.categoryId) {
      //   this.$message.error('请选择商品分类');
      //   return false;
      // }
      // if (!this.validateCategory('', this.form.categoryId, () => {})) {
      //   this.$message.error('商品分类必须选择到第三级');
      //   return false;
      // }
      // if (!this.validatePlatformCategory('', this.form.platformCategoryId, () => {})) {
      //   this.$message.error('平台类目必须选择到第三级');
      //   return false;
      // }
      if (!this.form.productName) {
        this.$message.error('请填写商品名称');
        return false;
      }
      if (!this.form.spec) {
        this.$message.error('请填写规格');
        return false;
      }
      if (!this.form.manufacturer) {
        this.$message.error('请填写生产厂商');
        return false;
      }
      if (!this.form.brandId) {
        this.$message.error('请选择品牌');
        return false;
      }
      if (!this.form.agentiaType) {
        this.$message.error('请选择剂型');
        return false;
      }
      if (!this.form.unit) {
        this.$message.error('请填写包装单位');
        return false;
      }
      if (!this.form.midPackTotal) {
        this.$message.error('请填写中包装');
        return false;
      }
      if (!this.form.packTotal) {
        this.$message.error('请填写件包装');
        return false;
      }
      if (!this.form.otcType) {
        this.$message.error('请选择处方类型');
        return false;
      }

      if ((this.form.measurement + '').length && !this.isNumber2(this.form.measurement)) {
        this.$message.error('计量单位输入不正确');
        return false;
      }

      if ((this.form.midPackTotal + '').length && !this.isInt7(this.form.midPackTotal)) {
        this.$message.error('中包装输入不正确');
        return false;
      }

      if ((this.form.packTotal + '').length && !this.isInt7(this.form.packTotal)) {
        this.$message.error('件包装输入不正确');
        return false;
      }

      this.form.brandId = this.form.brand.id

      return {...this.form, measurement: JSON.stringify({val: this.form.measurement, code: this.formUnitSelect})}
    },
    async setForm (data) {
	    let form = {
        drugName: '', // 通用名称
        productCode: '', //产品编码
        businessRangeId: '', //经营类目
        platformCategoryId: '', //平台类目
        categoryId: '', //商品分类
        productName: '', //产品名称
        title: '', //商品标题
        spec: '', //规格
        agentiaType: '', //剂型
        measurement: '', //计量单位
        unit: '', //包装单位
        manufacturer: '', //生产厂家
        area: '', //产地
        approvalNumber: '', //批准文号
        midPackTotal: '', //中包装
        packTotal: '', //件包装
        standardCode: '', //本位码
        barCode: '', //条形码
        otcType: '', //是否处方
        mnemonicCode: '', //助记码
        brandId: '', // 品牌id
        erpCode: '', // erp商品id
        expDate: '',  // 效期
        warehouseId: '' //所属仓库
      }
      for (let k in form) {
        this.form[k] = data[k]
      }
      this.form.measurement = this.form.measurement || '';
	    this.form.midPackTotal = this.form.midPackTotal || '';
      this.form.packTotal = this.form.packTotal || '';

      // 特殊处理计量单位
      if (this.form.measurement) {
        try {
          let values = JSON.parse(this.form.measurement);
          this.form.measurement = values.val
          this.formUnitSelect = values.code
        } catch (e) {

        }
      }
      if (this.form.brandId) {
        const res = await request.get('/product/admin/brand/' + this.form.brandId)
        this.form.brand = {
          id: res.data.id,
          name: res.data.brandName
        }
      } else {
	      this.form.brandId = '10000'
        this.form.brand = {
          id: '10000',
          name: '其它'
        }
      }
    },
    // 经营类目
    async getBusinessRangeList () {
      const { data } = await request.post('merchant/admin/businessCategory/listBusinessCategory')
      this.businessRangeList = data
    },
    // 平台类目
    async getPlatformCategoryList () {
      const { data } = await request.post('product/admin/categoryPlatform/query', {})
      this.platformCategoryList = this.buildTree(data)
      //第三极
      this.platformCategoryList.forEach(item => {
        let children1 = item.children || [];
        children1.forEach(children => {
          let children2 = children.children || [];
          children2.forEach(c => {
            this.platformCategoryList3.push(c.id);
          })
        })
      })
    },
    // 商品分类
    async getCategory () {
      const { data } = await request.post('product/admin/category/query', {})
      this.categoryList = this.buildTree(data)
      //第三极
      this.categoryList.forEach(item => {
        let children1 = item.children || [];
        children1.forEach(children => {
          let children2 = children.children || [];
          children2.forEach(c => {
            this.categoryList3.push(c.id);
          })
        })
      })
    },
    setCloudData (data) {
      this.form.productCode = data.productCode
      this.form.businessRangeId = data.businessRangeId
      this.form.categoryId = data.categoryId
      this.form.productName = data.productName
      this.form.brand = data.brand
      this.form.brandId = data.brand ? data.brand.id : ''
      this.form.spec = data.spec
      this.form.agentiaType = data.agentiaType
      this.form.measurement = data.measurement.split(':')[0]
      this.formUnitSelect = data.measurement.split(':')[1]
      this.form.unit = data.unit
      this.form.manufacturer = data.manufacturer
      this.form.area = data.area
      this.form.approvalNumber = data.approvalNumber
      this.form.midPackTotal = data.midPackTotal
      this.form.packTotal = data.packTotal
      this.form.standardCode = data.standardCode
      this.form.barCode = data.barCode
      this.form.otcType = data.otcType.code
      this.form.mnemonicCode = data.mnemonicCode
      this.form.drugName = data.productName
    },

    buildTree (list) {
      const map = _.groupBy(list, 'parentId')

      return this._buildTree(0, 0, map)
    },

    _buildTree (parentId, level, map) {
      const list = map[parentId]

      if (!list) { return null }

      return list.map(item => ({
        ...item,
        level,
        children: this._buildTree(item.id, level + 1, map)
      })).sort((a, b) => a.sort - b.sort)
    },
    onSelectBand (item) {
      this.form.brand = {
        id: item.id,
        name: item.brandName
      }
      this.form.brandId = item.id;
    },
    onShowBandDialog () {
      this.$refs['bandDialog'].visible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-information{
	.el-row{
		margin-bottom: 20px;
		padding-right: 30px;
		.el-col{
			.el-form-item{
				margin-bottom: 0;
        .el-select{
          width: 100%;
        }
				.unit{
					width: 60px;
					::v-deep{
						.el-input{
							>input{
								padding: 0 20px 0 10px;
							}
						}
					}
				}
			}
		}
	}
}
</style>
