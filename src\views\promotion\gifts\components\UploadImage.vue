<template>
    <div :class="$style.container">
        <div :class="$style.main">
            <!-- 图片预览列表 start -->
            <div :class="[$style.images, disabled ? $style.disabled : '']">
                <!-- 图片列表 start -->
                <div v-for="(item, index) in images" :class="$style.item" :key="item.url">
                    <img :src="item.url" :alt="'image' + index" />
                    <div v-if="item.uploading" :class="$style['loading-mask']">
                        <i class="el-icon-loading"></i>
                        <span>上传中...</span>
                    </div>
                    <template v-else>
                        <div :class="$style.mask">
                            <i class="el-icon-delete" @click="onDeleteClick(index)" />
                        </div>
                        <div :class="$style.label" @click="onLabelClick(item)">设置为主图</div>
                    </template>
                </div>
                <!-- 图片列表 end -->
            </div>
            <!-- 图片预览列表 end -->

            <!-- 图片上传组件（当按钮来用，预览部分已重写）start -->
            <el-upload :show-file-list="false" :disabled="disabled" v-if="isUploadVisible"
                :on-change="handleUploadChange" list-type="picture-card" :action="uploadParams.action"
                :headers="uploadParams.headers" :limit="limit" :data="uploadParams.data" accept=".jpg,.png,.bmp,.jpeg">
                <div :class="$style.upload" slot="trigger">
                    <i class="el-icon-plus"></i>
                    <span :class="$style.tip">上传({{ images.length }}/{{ limit }})</span>
                </div>

            </el-upload>
            <!-- 图片上传组件（当按钮来用，预览部分已重写）end -->
        </div>
        <div :class="$style.tip">{{ tip }}</div>
    </div>
</template>
<script>
import { deepClone } from '@/utils'
import { getToken } from '@/utils/auth'
import { getLocalUser } from '@/utils/local-user'
export default {
    props: {
        limit: {
            type: Number,
            default: 5
        },
        disabled: {
            type: Boolean,
            default: false
        },
        tip: {
            type: String,
            default: '建议尺寸：600x600像素以上JPG或PNG格式图片，大小不超过2M'
        },
        // files: {
        //     type: Array,
        //     default: () => []
        // }
        files: {
            type: String,
            default: ''
        }
    },

    watch: {
        files: {
            handler(files) {
                this.images = files && files.split(',').map(url => {
                    let target = this.images.find(v => v.url === url) || { url, uid: `${Math.floor(Math.random() * Math.pow(10))}` }
                    return target;
                }) || []
                // this.images = data.map(item => {
                //     if (typeof item === 'string') {
                //         item = { url: item }
                //     }
                //     return { uid: `${Math.floor(Math.random() * Math.pow(10))}`, ...item }
                // })
            },
            immediate: true,
            deep: true
        }
    },

    data() {
        return {
            uploadParams: {
                action: process.env.VUE_APP_BASE_API + '/file/file/upload',
                headers: {
                    Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
                    token: `Bearer ${getToken()}`
                },
                data: {
                    pur: 0,
                    sale: 0,
                    tenant: 0,
                    userid: getLocalUser().userId,
                    folderId: 0
                }
            },
            images: [],
        }
    },
    computed: {
        /** 是否显示上传按钮 （不禁用且已上传数量不大于限制数量） */
        isUploadVisible() {
            return !this.disabled && (this.images.length < this.limit)
        }
    },
    methods: {
        onLabelClick(item) {
          const index = this.images.findIndex(it => it.url === item.url)
          const image = deepClone(this.images[index])
          this.images.splice(index, 1)
          this.images.unshift(image)
          this.ayncUpdate()
        },
        onDeleteClick(index) {
            this.images.splice(index, 1)
            this.ayncUpdate()
        },
        /**
         * 处理图片上传状态
         * [ready: 已准备 | uploading:上传中 | success: 上传成功 | fail: 上传失败]
         */
        handleUploadChange({ status, url, name, uid, response }) {
            // status [ready| uploading | success | fail]
            const index = this.images.findIndex(v => v.uid === uid)
            // 图片列表已存在
            const hasImage = index >= 0;
            const oImage = {
                url,
                name,
                uid,
                uploaded: false,
                uploading: true
            }
            switch (status) {
                case 'ready':
                    this.images.push(oImage)
                    break;
                case 'uploading':
                    !hasImage && this.images.push(oImage)
                    break;
                case 'success':
                    let { data } = response || { data: {} }
                    this.images[index] && this.images.splice(index, 1, {
                        ...oImage,
                        name: data.filename,
                        id: data.id,
                        url: data.url,
                        uploaded: true,
                        uploading: false
                    })
                    this.ayncUpdate()
                    break;
                case 'fail':
                    this.$message({
                        message: response && response.msg || '网络繁忙',
                        type: 'warning'
                    });
                    hasImage && this.splice(index, 1)
                    break;

                default:
                    console.log('---> default <---')
            }

        },
        // 同步更新
        ayncUpdate() {
            let files = this.images.map(v => v.url).join(',')
            this.$emit('update:files', files)
        }
    }
}
</script>
<style lang="scss" module>
.container {


    .main {
        display: flex;
        align-items: stretch;

        .images {
            flex: 0 0 auto;

            .item {
                position: relative;
                overflow: hidden;
                background-color: #fff;
                border: 1px solid #c0ccda;
                border-radius: 6px;
                box-sizing: border-box;
                width: 148px;
                height: 148px;
                margin: 0 8px 8px 0;
                display: inline-block;

                .loading-mask {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, .6);
                    color: #fff;

                    i {
                        font-size: 20px;
                    }
                }


                img {
                    width: 100%;
                }

                .mask {

                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    top: 0;
                    background-color: rgba(0, 0, 0, .3);
                    text-align: center;
                    transform: translateY(-100%);
                    opacity: 0;
                    transition: all .2s;

                    i {
                        margin-top: 48px;
                        color: #fff;
                        font-size: 30px;
                        cursor: pointer;
                    }

                }

                .label {
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    text-align: center;
                    color: #FFF;
                    line-height: 36px;
                    background: #000;
                    cursor: pointer;
                    transform: translateY(100%);
                    opacity: 0;
                    transition: all .3s;
                }

            }

            &:not(.disabled) {
                .item:not(.uploading):hover {
                    .mask {
                        opacity: 1;
                        transform: translateY(0)
                    }

                    .label {
                        opacity: 1;
                        transform: translateY(0)
                    }
                }

            }
        }

        .upload {

            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            height: 100%;
            line-height: 1;
            color: #888;

            .tip {
                margin-top: 10px;

            }
        }
    }

    .tip {
        color: #aaa;
    }
}
</style>
