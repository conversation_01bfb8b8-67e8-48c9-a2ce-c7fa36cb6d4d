{"succeed": true, "data": [{"appId": 1, "name": "应用数据", "description": "", "permission": "appdata", "id": 6, "check": false, "type": "menu", "seq": 1, "child": [{"appId": 1, "name": "业务配置", "description": "", "permission": "appdata:bizConfig", "id": 7, "check": false, "type": "page", "seq": 1, "child": [{"appId": 7, "name": "提交", "description": "", "permission": "bizConfigAction:submit", "id": 101, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "更新", "description": "", "permission": "bizConfigAction:update", "id": 102, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "删除", "description": "", "permission": "bizConfigAction:update", "id": 103, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "详情", "description": "", "permission": "bizConfigAction:update", "id": 104, "check": false, "type": "action", "seq": 1, "child": []}]}, {"appId": 1, "name": "行政区管理", "description": "", "permission": "appdata:district", "id": 8, "check": false, "type": "page", "seq": 2, "child": []}, {"appId": 1, "name": "异常配置组", "description": "", "permission": "appdata:alertGroup", "id": 9, "check": false, "type": "page", "seq": 3, "child": []}, {"appId": 1, "name": "异常通知用户", "description": "", "permission": "appdata:alertUser", "id": 10, "check": false, "type": "page", "seq": 4, "child": []}]}, {"appId": 1, "name": "权限管理", "description": "", "permission": "auth", "id": 1, "check": false, "type": "menu", "seq": 2, "child": [{"appId": 1, "name": "管理员列表", "description": "", "permission": "auth:admin", "id": 2, "check": false, "type": "page", "seq": 1, "child": [{"appId": 7, "name": "提交", "description": "", "permission": "adminAction:submit", "id": 101, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "更新", "description": "", "permission": "adminAction:update", "id": 102, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "删除", "description": "", "permission": "adminAction:update", "id": 103, "check": false, "type": "action", "seq": 1, "child": []}, {"appId": 7, "name": "详情", "description": "", "permission": "adminAction:update", "id": 104, "check": false, "type": "action", "seq": 1, "child": []}]}, {"appId": 1, "name": "角色列表", "description": "", "permission": "auth:role", "id": 3, "check": false, "type": "page", "seq": 2, "child": [{"appId": 7, "name": "新增", "description": "", "permission": "roleAction:submit", "id": 101, "check": false, "type": "action", "seq": 1, "child": []}]}, {"appId": 1, "name": "应用列表", "description": "", "permission": "auth:app", "id": 4, "check": false, "type": "page", "seq": 3, "child": []}, {"appId": 1, "name": "功能列表", "description": "", "permission": "auth:function", "id": 5, "check": false, "type": "page", "seq": 4, "child": []}]}], "error": null}