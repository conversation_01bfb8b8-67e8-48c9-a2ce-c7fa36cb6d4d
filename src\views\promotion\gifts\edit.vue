<template>
    <div class="detail-wrapper" v-loading="loading">
        <!-- 头部标题 start -->
        <page-title :title="title">
            <template>
                <el-button @click="onSave" type="primary">保存</el-button>
            </template>
        </page-title>
        <!-- 头部标题 end -->

        <el-form ref="form" :model="form" label-width="130px" :rules="rules">
            <page-module-card title="赠品类型">
                <div :class="$style.types">
                    <div @click="onTypeClick(item.value)" v-for="item in types" :key="item.value"
                        :class="createTypeClass(item.value)">
                        <h4>{{ item.label }}</h4>
                        <p>{{ item.content }}</p>
                    </div>
                </div>
            </page-module-card>


            <page-module-card title="基本信息">
                <template v-if="isProduct">
                    <select-product :disabled="!isAdd" :data.sync="products"></select-product>
                </template>
                <!-- 非商品礼品表单 start -->
                <template v-else>
                    <el-row>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="赠品名称：" prop="productName">
                                <el-input v-model="form.productName" placeholder="请输入赠品名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="生产厂家：" prop="manufacturer">
                                <el-input v-model="form.manufacturer" placeholder="请输入生产厂家"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="规格：" prop="spec">
                                <el-input v-model="form.spec" placeholder="请输入规格"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="包装单位：" prop="unit">
                                <el-input v-model="form.unit" placeholder="请输入包装单位"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="品牌：" prop="brandName">
                                <el-button @click="visibleSelectBrand = true" style="width: 100%;">{{ form.brandName ?
                                        form.brandName : '从品牌库中添加'
                                }}</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="6" :md="8" :sm="12">
                            <el-form-item label="ERP商品编码：" prop="erpCode">
                                <el-input v-model="form.erpCode" placeholder="请输入ERP商品编码"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="产品图片：" prop="pictIdS">
                        <upload-image :files.sync="form.pictIdS"></upload-image>
                    </el-form-item>
                </template>
                <!-- 非商品礼品表单 end -->
            </page-module-card>

            <page-module-card title="价格库存">
                <el-row>
                    <el-col :xl="6" :md="8" :sm="12">
                        <el-form-item label="换购价：" prop="salePrice">
                            <el-input v-model="form.salePrice" placeholder="请输入换购价"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :xl="6" :md="8" :sm="12">
                        <el-form-item label="库存：" prop="stockQuantity">
                            <template v-if="!isProduct">
                                <el-input v-model="form.stockQuantity" placeholder="请输入库存"></el-input>
                            </template>
                            <template v-else>默认共享原品库存</template>
                        </el-form-item>
                    </el-col>
                </el-row>
            </page-module-card>
        </el-form>
        <select-brand v-model="brand" @change="onSelectBrandChange" :visible.sync="visibleSelectBrand"></select-brand>
    </div>
</template>
<script>

import UploadImage from './components/UploadImage.vue'
import SelectProduct from './components/SelectProduct.vue'
import SelectBrand from './components/SelectBrand.vue'
import DelButton from '@/components/eyaolink/delElButton/index.vue'

import { postGifts, fetchGifts } from '@/api/gifts'

// 页面状态 查看|编辑|新增
const STATUS = {
    VIEW: 'VIEW',
    EDIT: 'EDIT',
    ADD: 'ADD'
}
export default {
    data() {
        return {
            visibleSelectBrand: false,
            loading: false,
            // 商品类型数据
            products: [],
            // 品牌
            brand: {},
            // 非商品类型表单
            form: {
                productName: '',
                manufacturer: '',
                spec: '',
                unit: '',
                brandName: '',
                erpCode: '',
                pictIdS: '',
                salePrice: '',
                stockQuantity: ''
            },
            // 赠品表单验证
            rules: {
                productName: [
                    { required: true, message: '请输入商品名称' }
                ],
                manufacturer: [
                    { required: true, message: '请输入生产厂家' }
                ],
                spec: [
                    { required: true, message: '请输入规格' }
                ],
                unit: [
                    { required: true, message: '请输入包装单位' }
                ],
                pictIdS: [
                    { required: true, message: '请上传赠品图片' }
                ],
                salePrice: [
                    { required: true, message: '请输入换购价' }
                ],
                stockQuantity: [
                    { required: true, message: '请输入库存数量' }
                ]

            },
            types: [
                {
                    label: '商品',
                    content: '以在售商品等原品作为赠品',
                    value: 'PRODUCT'
                }, {
                    label: '非商品',
                    content: '以礼品，如抽纸、大米等作为赠品',
                    value: 'UN_PRODUCT'
                }
            ],
            currentType: 'PRODUCT',
            timer: null
        }
    },
    created() {
        // 判断是不是新增
        if (!this.isAdd) {
            this.fetch();
        }

    },
    computed: {
        isProduct() {
            return this.currentType === 'PRODUCT';
        },
        isDisabled() {
            return !this.isAdd;
        },
        isAdd() {
            return !this.$route.query.id
        },
        title() {
            return this.isAdd ? '新增赠品' : '编辑赠品';
        },

    },
    methods: {
        onSelectBrandChange(val) {
            this.form.brandName = val.brandName;
            this.form.brandId = val.id;

        },
        async onSave() {
            if (this.isProduct) {
                this.form.stockQuantity = 999999
            }
            let valid = await this.$refs.form.validate()
            if (!valid) return;

            let isAdd = this.isAdd
            let id = isAdd ? '' : this.$route.query.id
            let params = [];
            // 判断格式化参数
            if (this.isProduct) {
                // 商品类型
                params = this.products.map(item => {
                    return {
                        brandId: item.brandId,
                        brandName: item.brandName,
                        erpCode: item.erpCode,
                        giveawayType: this.currentType,
                        id: id,
                        manufacturer: item.manufacturer,
                        pictIdS: item.pictIdS,
                        productId: item.id,
                        productName: item.productName,
                        saleMerchantId: item.saleMerchantId,
                        salePrice: this.form.salePrice,
                        spec: item.spec,
                        stockQuantity: item.stockQuantity,
                        unit: item.unit
                    }
                })

            } else {
                // 非商品类型
                params = [
                    {
                        ...this.form,
                        giveawayType: this.currentType,
                        id: id
                    }
                ]
            }
            try {
                this.loading = true;
                // 发送请求
                const { code } = await postGifts(params)
                if (code !== 0) return;
                this.$message.success('保存成功')
                this.timer = setTimeout(() => {
                    this.$store.dispatch("tagsView/delView", this.$route);
                    this.$router.replace({
                        path: '/promotion/gifts'
                    })
                }, 500)
            } catch (error) {

            } finally {
                this.loading = false;
            }

        },
        /**
         * 赠品类型点击
         * @param {number} type 赠品类型id
         */
        onTypeClick(type) {
            if (this.isDisabled) return
            this.currentType = type;
        },
        /**
         * 生成类型卡片Class样式类
         */
        createTypeClass(type) {
            let _class = [this.$style.item];
            let isActive = this.currentType === type;
            isActive && _class.push(this.$style.active);
            this.isDisabled && _class.push(this.$style.disabled);
            return _class;
        },
        async fetch() {
            try {
                this.loading = true;
                let { data } = await fetchGifts(this.$route.query.id)
                if (data) {
                    this.currentType = data.giveawayType.code
                    if (this.isProduct) {
                        data.id = data.productId
                        this.products = [data]
                        this.form.salePrice = data.salePrice
                    } else {
                        let {
                            productId,
                            productName,
                            manufacturer,
                            spec,
                            unit,
                            brandId,
                            brandName,
                            erpCode,
                            pictIdS,
                            salePrice,
                            stockQuantity
                        } = data;
                        this.brand = {
                            id: brandId, 
                            brandName: brandName
                        }
                        this.form = { productId, productName, manufacturer, spec, unit, brandId, brandName, erpCode, pictIdS, salePrice, stockQuantity }
                    }
                }
            } catch (error) {

            } finally {
                this.loading = false;
            }
        }

    },
    components: {
        DelButton,
        UploadImage,
        SelectProduct,
        SelectBrand
    },
    beforeDestroy() {
        clearTimeout(this.timer)
    }
}
</script>
<style lang="scss" module>
.types {
    display: flex;
    padding: 15px 0;

    .item {
        position: relative;
        padding: 15px 10px;
        border: 1px solid #e3e3e3;
        width: 200px;
        margin-right: 20px;
        cursor: pointer;

        &::after {
            display: none;
            content: '';
            text-align: center;
            position: absolute;
            bottom: 0px;
            right: -1px;
            width: 20px;
            height: 20px;
            color: #fff;
            background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
            background-size: cover;
            background-position: center;
        }

        &.active {
            border: 1px solid #0056E5;
            position: relative;

            &:not(.disabled) {
                h4 {
                    color: #0056e5;
                }
            }

            &::after {
                display: block;
            }
        }

        &.disabled {
            border: 1px solid #999999;
            position: relative;
            cursor: not-allowed;

            &::after {
                filter: grayscale(100%);
            }
        }

        h4 {
            font-size: 16px;
            margin: 0;
        }

        p {
            font-size: 13px;
            color: #999999;
            padding-top: 10px;
            margin: 0;
        }

    }
}

.upload-wrap {
    position: relative;
    width: 100%;
    height: 100%;

    .upload {
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        line-height: 1;
        color: #888;

        .tip {
            margin-top: 10px;

        }
    }


}

.upload-tip {
    color: #aaa;
}
</style>