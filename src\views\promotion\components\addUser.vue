<template>
  <div class="app-container">
    <el-dialog
      title="选择客户"
      :visible.sync="addUservisible"
      width="85%"
      @close="handleClose"
      v-dialogDrag
    >
      <im-search-pad
        :has-expand="false"
        :model="listQuery"
        @reset="reset"
        @search="search"
      >
        <im-search-pad-item prop="orderNo">
          <el-input v-model.trim="listQuery.model.purMerchantCode" placeholder="请输入客户编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="name">
          <el-input v-model.trim="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="ceoName">
          <el-input v-model.trim="listQuery.model.ceoName" placeholder="请输入负责人姓名" />
        </im-search-pad-item>
        <im-search-pad-item prop="address">
          <el-cascader
            placeholder="请选择所在区域"
            v-model="address"
            :options="categoryOpts"
            :props="{ expandTrigger: 'hover',
              value: 'id',
              label: 'label',
              children: 'children'}"
            @change="parentChangeAction"
            clearable></el-cascader>
        </im-search-pad-item>
      </im-search-pad>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load" :selection="true" @selection-change="handleSelectionChange" :isNeedButton="false">
      </table-pager>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="adds">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { merchantPurSaleRelList,trees } from "@/api/group";

const TableColumns = [
  { label: "客户编码", prop: "code" },
  { label: "客户名称", prop: "name" },
  { label: "企业类型", prop: "merchantType" },
  { label: "负责人", prop: "ceoName" },
  { label: "联系电话", prop: "ceoMobile" },
  { label: "所在区域", prop: "region" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export default {
  name: "DragTable",
  props: ["visible",'saleMerchantId','row'],
  data() {
    return {
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      sortable: null,
      tableData: [],
      categoryOpts: [],
      ids: [],
      address: '',
      listQuery: {
        model:{
          "code": '',
          "name": '',
          "saleMerchantId": '',
          'ceoName': '',
          'cityId': '',
          'purMerchantName':'',
          'purMerchantCode':'',
          'countyId': ''
        }
      },
      addUservisible: false,
      merchantGroupId: ''
    };
  },
  created() {
    this.getTrees()
  },
  methods: {
     // 获取地区
    async getTrees () {
      const { data } = await trees()
      this.categoryOpts = data
    },
     parentChangeAction(val) {
      this.listQuery.model.countyId = val[0]
      this.listQuery.model.cityId = val[1]
      this.listQuery.model.countyId = val[2]
    },

    handleClose () {
      this.$parent.showAdd = false;
    },

    search() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.load()
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.listQuery.model = {}
      this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listLoading = true;
      Object.assign(this.listQuery, params)
      return await merchantPurSaleRelList(this.listQuery);
    },

    async adds() {
      if(this.ids.length>0){
        this.$emit('changeShow', this.ids);
        this.$parent.showCoupon = true;
      } else {
        this.$message.warning('请选择需要赠送的客户');
      }
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item,index) {
        return item.purMerchantId
      })

    },
  },
  watch: {
    visible() {

      this.addUservisible = this.visible
    },
    row() {
      this.merchantGroupId = this.row.id
    }
  }
};
</script>

<style lang="scss" scoped>
  ::v-deep {
    .page-row{
      margin-top: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ddd;
    }
    .el-dialog__body{
      padding-bottom: 0;
    }
    .el-dialog__footer{
      padding-top: 16px;
    }
  }
</style>
