<template>
  <el-form ref="form" :model="form" label-width="80px" label-position="right" :disabled="action === 'SHOW'">
    <div class="price-information">
      <form-item-title title="价格信息" descript="未指定地区得价格启用统一销售价结算" />
      <el-row :gutter="80">
        <el-col :span="6">
          <el-form-item label-width="100px" label="分销供货价" :required="isShare == 'Y'" prop="supplyPrice"
            :rules="{validator: validateNumber,trigger: 'change'}">
            <el-input v-model="form.supplyPrice" :disabled="isDetail" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="100px" label="建议批发价" prop="wholesalePrice"
            :rules="{validator: validateNumber,trigger: 'change'}">
            <el-input v-model="form.wholesalePrice" :disabled="isDetail" />
          </el-form-item>
        </el-col>

        <!--        -->
        <el-col :span="6">
          <el-form-item label-width="100px" label="统一销售价" prop="salePrice"
            :rules="{validator: validateNumber, required: true, trigger: 'change'}">
            <el-input v-model="form.salePrice" :disabled="isDetail" @change="salePriceChange" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="100px" label="成本价" prop="costPrice"
            :rules="{validator: validateNumber,trigger: 'change'}">
            <el-input v-model="form.costPrice" :disabled="isDetail" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="100px" label="医保价" prop="medicarePrice"
            :rules="{validator: validateNumber,trigger: 'change'}">
            <el-input v-model="form.medicarePrice" :disabled="isDetail" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label-width="100px" label="零售价" prop="retailPrice"
            :rules="{validator: validateNumber,trigger: 'change'}">
            <el-input v-model="form.retailPrice" :disabled="isDetail" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col :span="11">
              <div class="user_problem">
                <el-tooltip placement="right" effect="light">
                  <div slot="content">
                    1、当单品已设单品会员价系数
                    <br/>
                    会员价=统一销售价*单品会员价系数
                    <br/>
                    2、当单品未设会员价系数
                    <br/>
                    会员价=统一销售价*基础会员价系数
                  </div>
                  <i style="margin-left:10px;position: absolute;left: 18px; top:11px" class="el-icon-question"></i>
                </el-tooltip>
                <el-form-item label-width="100px" label="会员价" prop="memberPrice"
                  :rules="{validator: validateNumber,trigger: 'change'}">
                  <el-input v-model="form.memberPrice" :disabled="isDetail || !memberCheckd" @change="memberPriceChange" />
                </el-form-item>
            
              </div>
            </el-col>
            <el-col :span="5">
              <div class="memberCheck">
                <el-checkbox @change="memberCheckdChange" v-model="memberCheckd">单品会员价系数</el-checkbox>
              </div>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="memberCheckd" label-width="0px" label="" prop="productMemberCoefficient"
                :rules="[memberCheckd?{validator:coefficientValidator,required: true,trigger: 'blur'}:{}]">
                <el-input v-model="form.productMemberCoefficient" placeholder="请输入会员价系数" @change="productMemberCoefficientChange"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          
        </el-col>
      </el-row>

      <el-table :data="form.areaPriceList" :class="{ 'beyond-hidden': form.areaPriceList.length > 10 }"  border>
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="省份" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button v-if="scope.row.edit" class="area" :disabled="!scope.row.edit" type="text"
              @click="openAreaDialog(scope.row)">
              {{
              scope.row.cache.province
              ? scope.row.cache.province.text
              : "请选择省份"
              }}
            </el-button>
            <el-button v-else class="area" :disabled="!scope.row.edit" type="text" @click="openAreaDialog(scope.row)">
              {{
              scope.row.real.province
              ? scope.row.real.province.text
              : "请选择省份"
              }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="城市" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button v-if="scope.row.edit" class="area" :disabled="!scope.row.edit" type="text"
              @click="openAreaDialog(scope.row)">
              {{
              scope.row.cache.city ? scope.row.cache.city.text : "请选择城市"
              }}
            </el-button>
            <el-button v-else class="area" :disabled="!scope.row.edit" type="text" @click="openAreaDialog(scope.row)">
              {{
              scope.row.real.city ? scope.row.real.city.text : "请选择城市"
              }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="区" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button v-if="scope.row.edit" class="area" :disabled="!scope.row.edit" type="text"
              @click="openAreaDialog(scope.row)">
              {{
              scope.row.cache.district
              ? scope.row.cache.district.text
              : "请选择区"
              }}
            </el-button>
            <el-button v-else class="area" :disabled="!scope.row.edit" type="text" @click="openAreaDialog(scope.row)">
              {{
              scope.row.real.district
              ? scope.row.real.district.text
              : "请选择区"
              }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="客户类型" width="200">
          <template slot-scope="scope">
            <el-select v-if="scope.row.edit" v-model="scope.row.cache.customerType" class="multiple-select" clearable
              multiple collapse-tags placeholder="请选择" @change="onTypeChange(scope.row.cache, 'customerType')">
              <el-option v-for="item in tGroups" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-select v-else v-model="scope.row.real.customerType" class="multiple-select" clearable multiple
              collapse-tags placeholder="请选择" disabled @change="onTypeChange(scope.row.cache, 'customerType')">
              <el-option v-for="item in tGroups" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="客户分组" width="200">
          <template slot-scope="scope">
            <el-select v-if="scope.row.edit" v-model="scope.row.cache.customerGroup" class="multiple-select" multiple
              collapse-tags placeholder="请选择" clearable @change="onTypeChange(scope.row.cache, 'customerGroup')">
              <el-option v-for="item in cGroups" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-select v-else v-model="scope.row.real.customerGroup" class="multiple-select" multiple collapse-tags
              placeholder="请选择" clearable disabled @change="onTypeChange(scope.row.real, 'customerGroup')">
              <el-option v-for="item in cGroups" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="销售价">
          <template slot-scope="scope">
            <div v-if="!scope.row.edit">{{ scope.row.real.salePrice }}</div>
            <el-input v-else v-model="scope.row.cache.salePrice" />
          </template>
        </el-table-column>
        <el-table-column v-if="!isDetail" label="操作" width="100">
          <template slot-scope="scope">
            <el-button v-if="!scope.row.edit" type="text" @click="handleEdit(scope.row)">编辑
            </el-button>
            <el-button v-else type="text" style="color: #7f7f7f" @click="handleCancel(scope.row, scope.$index)">取消
            </el-button>
            <el-button v-if="!scope.row.edit" type="text" @click="handleDelete(scope)">删除
            </el-button>
            <el-button v-else type="text" @click="handleConfirm(scope.row)">确定
            </el-button>
          </template>
        </el-table-column>
        <el-button v-if="!isDetail" slot="append" type="text" @click="handleAdd">+ 添加区域价格
        </el-button>
      </el-table>
      <area-tree-dialog ref="areaDialog" @on-confirm="getEreaData" :regionList="areaList" />
    </div>
  </el-form>
</template>

<script>
  import {
    map as _map,
    reduce as _reduce,
    cloneDeep as _cloneDeep,
  } from "lodash";
  import formItemTitle from "@/views/products/common-components/form-item-title";
  import request from "@/utils/request";
  import {
    getLocalUser
  } from "@/utils/local-user";
  import areaTreeDialog from "@/components/areaTreeDialog";
  import {
    getManagementRelBySaleMerchantId,
  } from "@/api/merchantApi/allianceMember"

  export default {
    name: 'priceInformation',
    components: {
      formItemTitle,
      areaTreeDialog
    },
    props: {
      areaData: {
        type: Array,
        required: true,
      },
      action: {
        type: String,
      },
      isNumber2: {
        type: Function,
      },
      isInt7: {
        type: Function,
      },
      isShare: {
        type: String,
      },
    },
    data() {

      return {
        form: {
          salePrice: "",
          costPrice: "",
          supplyPrice: "",
          wholesalePrice: "",
          medicarePrice: "",
          retailPrice: "",
          memberPrice: "",
          productMemberCoefficient:'', // 单品会员系数
          areaPriceList: [],
        },
        memberPriceCoefficient:1,
        memberCheckd:false,
        tGroups: [],
        cGroups: [],
        currentItem: null,
        areaList: [],
      };

    },
    computed: {
      isDetail() {
        return this.action === "SHOW";
      },
    },

    async created() {
      const res = await request.get("merchant/admin/merchantType");
      this.tGroups = [{
        id: "NONE",
        name: "不限客户类型"
      }, {
        id: "ALL",
        name: "全部类型"
      }, ...res.data];
      const groups = await request.post("merchant/admin/merchantGroup/page", {
        current: 1,
        size: 9999,
        model: { groupType: 'CLIENT'},
      });
      this.cGroups = [{
        id: "NONE",
        name: "不限客户分组"
      }, {
        id: "ALL",
        name: "全部分组"
      }, ...groups.data.records];
      this.getmember();
    },

    methods: {
      getmember(){
        getManagementRelBySaleMerchantId().then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.memberPriceCoefficient = Number(res.data.memberPriceCoefficient);
          }
        })
      },
      // 销售价的改变
      salePriceChange(e){
        if(!this.memberCheckd){
          // 没有勾选了单品会员价系数
          this.form.memberPrice = (e * this.memberPriceCoefficient).toFixed(2) == 0 ? null : (e * this.memberPriceCoefficient).toFixed(2);
        } else {
          if(this.form.memberPrice == '' || this.form.memberPrice == null) {
            // 会员价为空
            if(this.form.productMemberCoefficient == '' || this.form.productMemberCoefficient == null) {
              // 会员价系数为空
              this.form.memberPrice = (e * 1).toFixed(2) == 0 ? 0.01 : (e * 1).toFixed(2);
            } else {
              // 会员价系数不为空
              this.form.memberPrice = (e * this.form.productMemberCoefficient).toFixed(2) == 0 ? 0.01 : (e * this.form.productMemberCoefficient).toFixed(2);
            }
          } else {
            // 会员价不为空
            // this.form.productMemberCoefficient = (Number(this.form.memberPrice)/Number(e)).toFixed(6);
            if(this.form.productMemberCoefficient == '' || this.form.productMemberCoefficient == null) {
              // 会员价系数为空
              this.form.memberPrice = (e * 1).toFixed() == 0 ? 0.01 : (e * 1).toFixed(2);
            } else {
              // 会员价系数不为空
              this.form.memberPrice = (Number(e) * this.form.productMemberCoefficient).toFixed(2) == 0 ? 0.01 : (Number(e) * this.form.productMemberCoefficient).toFixed(2);
            }
          }
        }
      },
      // 会员价的改变
      memberPriceChange(e){
        if(this.form.salePrice == null || this.form.salePrice.length == 0 || this.form.salePrice == 0) {
          this.$message.warning('请先填写的统一销售价');
          return
        }
        this.form.productMemberCoefficient = (Number(e)/Number(this.form.salePrice)).toFixed(6);
      },
      // 单品会员价系数的改变
      productMemberCoefficientChange(e){
        if(this.form.salePrice == null || this.form.salePrice.length == 0 || this.form.salePrice == 0) {
          this.$message.warning('请先填写的统一销售价');
          return
        }
        // console.log('e---->',e);
        if(e == '' || e == null) {
          this.form.memberPrice = (Number(this.form.salePrice) * 1).toFixed(2);
        } else {
          this.form.memberPrice = (Number(e) * Number(this.form.salePrice)).toFixed(2) == 0 ? 0.01 : (Number(e) * Number(this.form.salePrice)).toFixed(2);
        }
      },
      // 会员价勾选的改变
      memberCheckdChange(){
        if(this.form.salePrice == null || this.form.salePrice.length == 0 || this.form.salePrice == 0) {
          this.$message.warning('请先填写的统一销售价');
          return
        }
        if(!this.memberCheckd) {
          console.log('this.memberPriceCoefficient',this.memberPriceCoefficient);
          this.form.memberPrice = (Number(this.form.salePrice) * this.memberPriceCoefficient).toFixed(2) == 0 ? null : (Number(this.form.salePrice) * this.memberPriceCoefficient).toFixed(2);
        } else {
          if(this.form.productMemberCoefficient != '' && this.form.productMemberCoefficient != null && this.form.productMemberCoefficient != 0){
            this.form.memberPrice = (Number(this.form.salePrice) * Number(this.form.productMemberCoefficient)).toFixed(2) == 0 ? 0.01 : (Number(this.form.salePrice) * Number(this.form.productMemberCoefficient)).toFixed(2);
          }
        }
      },
      coefficientValidator(rule, value, callback) {
        if (!value) {
          return callback(new Error('会员价系数不能为空'));
        } else {
          if (isNaN(value)) {
            return callback(new Error('请输入数字'));
          } else if (!isNaN(value) && Number(value) <= 0) {
            return callback(new Error('会员价系数不能小于或等于0'));
          } else if (!isNaN(value) && Number(value) > 1) {
            return callback(new Error('会员价系数不能大于1'));
          } else if(value.indexOf('.') != -1){
            if(value.split('.')[1].length > 6){
              return callback(new Error('会员价系数不能超过六位小数'))
            } else {
              return callback();
            }
          } else {
            return callback();
          }
        }
      },
      validateNumber(rule, value, cb) {
        console.log('rule---->',value);
        // 输入正数，保留小数点后两位
        if (!value) {
          return cb()
        }
        if (!this.isNumber2(value)) {
          return cb(new Error('只能输入正数，且小数最多两位'))
        }
        cb()
        console.log(" cb()=======", rule)

      },
      initPrice() {
        if (this.form.supplyPrice == "0.00") {
          this.form.supplyPrice = ""
        }
        if (this.form.wholesalePrice == "0.00") {
          this.form.wholesalePrice = ""
        }
        if (this.form.salePrice == "0.00") {
          this.form.salePrice = ""
        }
        if (this.form.costPrice == "0.00") {
          this.form.costPrice = ""
        }
        if (this.form.medicarePrice == "0.00") {
          this.form.medicarePrice = ""
        }
        if (this.form.retailPrice == "0.00") {
          this.form.retailPrice = ""
        }
        if(this.form.memberPrice == "0.00"){
          this.form.memberPrice = ""
        }
      },

      handleBuyNumberChange(value) {
        if (value) {}
      },
      int7(rule, value, cb) {
        if (!this.isInt7(value)) {
          return cb(new Error("只能是正整数，且最多7位"));
        }
        cb();
      },

      onTypeChange(obj, key) {
        console.log(obj,key)
        this.$nextTick(() => {
          if (obj[key][obj[key].length - 1] === "ALL") {
            obj[key] = ["ALL"];
          }else if(obj[key][obj[key].length - 1] === "NONE") {
            obj[key] = ["NONE"];
          } else {
            obj[key] = obj[key].filter((item) => {
              return !(item === "ALL" || item === "NONE");
            });
          }
        });
      },
      handleAdd() {
        this.form.areaPriceList.push({
          real: {
            province: null,
            city: null,
            distrct: null,
            customerType: ["NONE"],
            customerGroup: ["NONE"],
            salePrice: "",
          },
          cache: {
            province: null,
            city: null,
            distrct: null,
            customerType: ["NONE"],
            customerGroup: ["NONE"],
            salePrice: "",
          },
          newItem: true,
          edit: true,
        });
      },
      handleEdit(item) {
        item.edit = true;
        item.cache = _cloneDeep(item.real);
      },
      handleCancel(item, idx) {
        if (item.newItem) {
          this.form.areaPriceList.splice(idx, 1);
          return;
        }
        item.cache = {
          province: null,
          city: null,
          district: null,
          customerType: [],
          customerGroup: [],
          salePrice: "",
        };
        item.edit = false;
      },
      handleDelete(item) {
        this.form.areaPriceList.splice(item.$index, 1);
      },
      handleConfirm(item) {
        const { city, salePrice } = item.cache;
        if(!city) {
          this.$message.warning('请选择区域')
          return
        } else if(!salePrice || !this.isNumber2(salePrice)) {
          this.$message.warning('请输入正确的销售价')
          return
        }
        item.edit = false;
        item.newItem = false;
        item.real = _cloneDeep(item.cache);
      },
      openAreaDialog(item) {
        if (item.cache.city != null && item.cache.district != null && item.cache.province != null) {
          console.log('0000');
          let flag = true; // 是否所有的区都是all flase：不是全部都是all，true:是全部的都是all
          let distAll = false; // 区里面的是否有all，有的就是true,没有的就是false
          if (item.cache && item.cache.district) {
            let checkList = [];
            item.cache.district.value.split(',').forEach(_ => {
              checkList.push(_.split(':')[1]);
            });
            let allList = [];
            if (checkList.includes('ALL')) {
              checkList.forEach(i => {
                if (i == "ALL") {
                  allList.push(i);
                }
              });
              if (allList.length == checkList.length) {
                // 区全部都是all
                flag = true;
              } else {
                flag = false;
                // 区里面。有all，有不是all的
                distAll = true;
              }
            } else {
              // 去里面没有一个all
              flag = false;
            }
          }
          if (item.cache && item.cache.district && flag) {
            // 区全部都是all
            if (item.cache && item.cache.city && item.cache.city.value == 'ALL') {
              // 市是all
              // 赋值省的id
              let valPro = item.cache.province.value == "ALL" ? '0' : item.cache.province.value
              this.areaList = [valPro];
            } else {
              // 区是all 市不是all
              // 赋值所有的选中的市
              this.areaList = item.cache.city.value.split(',');
            }
          } else {
            if (distAll) {
              // 区里面。有all，有不是all的
              if (item.cache.city && item.cache.city.value == 'ALL') {
                let valPro = item.cache.province.value == "ALL" ? '0' : item.cache.province.value
                this.areaList = [valPro];
                // this.areaList = [item.cache.province.value];
              } else {
                // 区是all 市不是all
                // 赋值所有的选中的市
                this.areaList = item.cache.city.value.split(',');
              }
            }
            // 区不是全部
            let arr = item.cache.district.value.split(',');
            let list = []
            arr.forEach(_ => {
              if (_.split(':')[1] == 'ALL') {
                list.push(_.split(':')[0]);
              } else {
                list.push(_.split(':')[1]);
              }
            });
            this.areaList = [...list];
          }
        }
        this.currentItem = item;
        this.$refs.areaDialog.show();
      },
      formatAreaData(data) {
        // 省
        const currentItem = {};
        currentItem.cache = {
          province: {
            text: data.province.label,
            value: data.province ? data.province.id : "",
          },
          city: {
            text: "",
            value: "",
          },
          district: {
            text: "",
            value: "",
          },
        };
        if (data.province.id == 0 && data.province.label == '全国') {
          currentItem.cache.city.text = "全部";
          currentItem.cache.city.value = "ALL";

          currentItem.cache.district.text = "全部";
          currentItem.cache.district.value = "ALL:ALL";
        } else {
          // 市
          if (data.province.children.length !== data.citys.length) {
            currentItem.cache.city.text = _map(
              data.citys,
              (item) => item.label
            ).join(",");
          } else {
            // 全部
            if (data.citys[0].label === "市辖区") {
              currentItem.cache.city.text = "直辖区";
            } else {
              currentItem.cache.city.text = "全部";
            }
          }

          currentItem.cache.city.value = data.citys
            .map((item) => {
              return item.id;
            })
            .join(",");

          // 处理区id
          const allCity = data.province.children || [];
          let total = 0; // 总区数量
          allCity.forEach((item) => {
            const d = item.children || [];
            total += parseInt(d.length);
          });
          const isAll = total === data.districts.length;
          if (isAll) {
            // 选了全部
            currentItem.cache.city.value = "ALL";
            currentItem.cache.district.value = "ALL:ALL";
          } else {
            const districts = data.districts || [];
            const citys = _cloneDeep(data.citys || []);
            // 遍历归档
            citys.forEach((city) => {
              city.selected = [];
              districts.forEach((d) => {
                if (d.id.indexOf(city.id) === 0) {
                  city.selected.push(d);
                }
              });
            });
            const dValue = [];
            // 区id
            citys.forEach((city) => {
              if (city.selected.length === city.children.length) {
                // 该城市所有的区
                dValue.push(`${city.id}:ALL`);
              } else {
                districts.forEach((d) => {
                  dValue.push(`${city.id}:${d.id}`);
                });
              }
            });
            currentItem.cache.district.value = dValue.join(",");
          }
          // 区
          const districtLength = _reduce(
            data.citys,
            (sum, item) => {
              return sum + item.children.length;
            },
            0
          );
          if (districtLength !== data.districts.length) {
            currentItem.cache.district.text = _map(
              data.districts,
              (item) => item.label
            ).join(",");
          } else {
            currentItem.cache.district.text = "全部";
          }
        }


        return currentItem;
      },
      getEreaData(data) {
        if (data.checkedKeys && data.checkedKeys.length == 1 && data.checkedKeys[0] == 0) {
          this.currentItem.cache = {
            ...this.currentItem.cache,
            province: {
              text: "全国",
              value: "ALL",
            },
            city: {
              text: "全部",
              value: "ALL",
            },
            district: {
              text: "全部",
              value: "ALL:ALL",
            },
          };
        } else {
          this.currentItem.cache = {
            ...this.currentItem.cache,
            province: {
              text: data.provinceName || "",
              value: data.provinceId || "",
            },
            city: {
              text: data.cityNames || "",
              value: data.cityIdS || "",
            },
            district: {
              text: data.districtNames || "",
              value: data.districtIdS || "",
            },
          };
        }
      },

      getData() {
        if(this.form.supplyPrice != '' && this.form.supplyPrice != null){
          console.log('------>',!this.isNumber2(this.form.supplyPrice));
          if(!this.isNumber2(this.form.supplyPrice)){
            this.$message.error('分销供货价输入不正确');
            return false
          }
        }
        
        if(this.form.wholesalePrice != '' && this.form.wholesalePrice != null){
          if(!this.isNumber2(this.form.wholesalePrice)){
            this.$message.error('建议批发价输入不正确');
            return false
          }
        }
        if(this.form.costPrice != '' && this.form.costPrice != null){
          if(!this.isNumber2(this.form.costPrice)){
            this.$message.error('成本价输入不正确');
            return false
          }
        }
        if(this.form.medicarePrice != '' && this.form.medicarePrice != null){
          if(!this.isNumber2(this.form.medicarePrice)){
            this.$message.error('医保价输入不正确');
            return false
          }
        }
        if(this.form.retailPrice != '' && this.form.retailPrice != null){
          if(!this.isNumber2(this.form.retailPrice)){
            this.$message.error('零售价输入不正确');
            return false
          }
        }
        if (!this.form.salePrice) {
          this.$message.error('请填写统一售价')
          return false
        }
        if (this.form.salePrice && !this.isNumber2(this.form.salePrice)) {
          this.$message.error('统一售价输入不正确')
          return false
        }

        // 单品会员价系数
        if(this.memberCheckd) {
          let value = this.form.productMemberCoefficient;
          if (!value) {
            // return callback(new Error('会员价系数不能为空'));
            this.$message.error('会员价系数不能为空');
            return false
          } else {
            if (isNaN(value)) {
              this.$message.error('请输入数字');
              return false
            } else if (!isNaN(value) && Number(value) <= 0) {
              this.$message.error('会员价系数不能小于或等于0');
              return false
            } else if (!isNaN(value) && Number(value) > 1) {
              this.$message.error('会员价系数不能大于1');
              return false
            } else if(value.indexOf('.') != -1){
              if(value.split('.')[1].length > 6){
                this.$message.error('会员价系数不能超过六位小数');
                return false;
              }
            }
          }
        }
        // 单品会员价系数
        console.log(this.form)
        const ret = {
          skuList: [{
            //lookType =2 当前成本价需要为供货价 supplyPrice
            costPrice: this.form.costPrice,
            medicarePrice: this.form.medicarePrice,
            retailPrice: this.form.retailPrice,
            salePrice: this.form.salePrice,
            supplyPrice: this.form.supplyPrice,
            wholesalePrice: this.form.wholesalePrice,
            memberPrice:this.form.memberPrice,
            // productMemberCoefficient: this.memberCheckd ? this.form.productMemberCoefficient : '',
            // whetherProductMemberCoefficient: this.memberCheckd ? "Y" : "N",
            ...this.form,
            productMemberCoefficient: this.memberCheckd ? this.form.productMemberCoefficient : '',
            whetherProductMemberCoefficient: this.memberCheckd ? "Y" : "N",
          }],
         
          productPricePolicyList: this.form.areaPriceList.map(item => {
            const data = item.real

            const cityIdS = data.city.value

            const districtIdS = data.district.value

            const enterpriseTypeIdS = data.customerType ? data.customerType.join(',') : ''
            const merchantGroupIdS = data.customerGroup ? data.customerGroup.join(',') : ''
            const id = data.id

            const ret = {
              cityIdS,
              districtIdS,
              enterpriseTypeIdS,
              merchantGroupIdS,
              'operation': '',
              'price': data.salePrice,
              'productId': this.productId,
              'provinceId': data.province.value == 0 ? 'ALL' : data.province.value,
              'saleMerchantId': getLocalUser().saleMerchantId,
              'skuId': 0,
              'streetIdS': ''
            }
            if (id) {
              ret.id = id
            }
            return ret
          })
        }
        return ret
      },
      setForm(data) {
        const skuList = data.skuList || [];
        this.productId = data.id
        
        if (skuList.length) {
          const obj = skuList[0];
          this.form = {
            ...this.form,
            ...obj
          };
          this.memberCheckd = obj.whetherProductMemberCoefficient && obj.whetherProductMemberCoefficient.code == 'Y' ? true : false;
          // this.form.costPrice = obj.costPrice || ''
          // this.form.wholesalePrice=obj.wholesalePrice||""
          // this.form.medicarePrice = obj.medicarePrice || ''
          // this.form.retailPrice = obj.retailPrice || ''
          // this.form.salePrice = obj.salePrice || '2222222'
          // this.form.supplyPrice=obj.supplyPrice||""
          console.log("this.form====11111", this.form);
          this.initPrice();
        }
        const productPricePolicyList = data.productPricePolicyList || [];
        this.form.areaPriceList = productPricePolicyList.map((item) => {
          const areaObj = {
            province: "",
            citys: [],
            districts: [],
          };
          this.areaData.forEach((p) => {
            if (p.id === item.provinceId) {
              areaObj.province = p;
              const citys = p.children || [];
              let cityIds = item.cityIdS;
              if (cityIds === "ALL") {
                areaObj.citys = citys;
                citys.forEach((c) => {
                  const d = c.children || [];
                  areaObj.districts = [...areaObj.districts, ...d];
                });
              } else {
                cityIds = cityIds.split(",");
                let districtIdS = item.districtIdS.split(",");
                districtIdS = districtIdS.map((dItem) => {
                  dItem = dItem.split(":");
                  return dItem;
                });
                citys.forEach((city) => {
                  if (cityIds.indexOf(city.id) > -1) {
                    areaObj.citys.push(city);
                    // 查找看是否有区
                    districtIdS.forEach((dItem) => {
                      if (dItem[0] === city.id) {
                        if (dItem[1] === "ALL") {
                          // 所有
                          areaObj.districts = [
                            ...areaObj.districts,
                            ...city.children,
                          ];
                        } else {
                          // 查找具体的区
                          const cChildren = city.children || [];
                          for (let k = 0; k < cChildren.length; k++) {
                            if (dItem[1] === cChildren[k].id) {
                              areaObj.districts.push(cChildren[k]);
                              break;
                            }
                          }
                        }
                      }
                    });
                  }
                });
              }
            }
          });
          let row = this.formatAreaData(_cloneDeep(areaObj)).cache;
          row = {
            id: item.id,
            ...row,
            customerType: (item.enterpriseTypeIdS || "").split(","),
            customerGroup: (item.merchantGroupIdS || "").split(","),
            salePrice: item.price,
          };
          const ret = {
            real: _cloneDeep(row),
            cache: _cloneDeep(row),
            edit: false,
          };
          return ret;
        });
      },
    },
  };

</script>

<style lang="scss" scoped>
  .price-information {
    .el-row {
      margin-bottom: 20px;
    }

    .el-table {
      width: 100%;
      
      ::v-deep {
        .el-table__body-wrapper {
          .el-table__append-wrapper {
            text-align: center;
          }
        }

        .area {
          color: #505465;
        }

        .area.is-disabled {
          color: #505465;
          cursor: text;
        }

        .multiple-select {
          width: 100%;
        }

        .multiple-text {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  ::v-deep .beyond-hidden.el-table {
    width: 100%;
    height: 500px;
    overflow-y: auto;
    .el-table::before {
      height: 0px !important;
    }
  }

  ::v-deep .beyond-hidden.el-table::before {
      height: 0px !important;
    }

.memberCheck {
  display: flex;
  align-items: center;
  // flex-direction: column;
  margin-left: 10px;
  line-height: 35px;
  height: 35px;
}
.user_problem {
  position: relative;
}
</style>
