<template>
  <div class="product-list">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="queryParams"
      @reset="resetFilter"
      @search="load"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="queryParams.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>

      <im-search-pad-item prop="productName">
        <el-input v-model.trim="queryParams.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model.trim="queryParams.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item prop="saleMerchantName">
        <el-input v-model.trim="queryParams.saleMerchantName" placeholder="请输入分销商名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="whetherOnSale">
        <el-select v-model="queryParams.whetherOnSale" placeholder="商品共享状态" clearable>
          <el-option value="Y" label="共享" />
          <el-option value="N" label="不共享" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="product-list-container">
      <div class="product-list-tabs-wrapper">
        <div class="product-list-tabs">
          <template v-for="(tab, index) in tabs">
            <div
              v-if="!tab.hide"
              :key="tab.value"
              class="tab"
              :class="{'active': currentTab === index}"
              @click="handleChangeTab(index)"
            >
              <span>{{ tab.name }}</span>
            </div>
          </template>
        </div>
        <div class="operations">
          <el-button
            v-if="checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:batchAccept','sale-platform-manufacturer-distributor-apply:batchAccept']) &&currentTab==0"
            size="small"
            :disabled="!selects.length"
            @click="handleBatchPass"
          >批量通过
          </el-button>
          <el-button
            v-if="checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:batchReject','sale-platform-manufacturer-distributor-apply:batchReject'])&&currentTab==0"
            size="small"
            :disabled="!selects.length"
            @click="handleBatchReject"
          >批量驳回
          </el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:export','sale-platform-manufacturer-distributor-apply:export'])" size="small">导出</el-button>
          <el-button size="small" @click="load">刷新</el-button>
        </div>
      </div>

      <transfer-table
        :table-options="allColumn"
        :table-data="tableData"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >

        <el-table-column slot="pictIdS" label="产品主图" align="center" class-name="img-cell">
          <template slot-scope="scope">
            <img :src="splitString(scope.row.productListAdminDto.pictIdS)[0]" width="50px" height="50px">
          </template>
        </el-table-column>
        <el-table-column slot="whetherOnSale" label="商品供应状态" width="110" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.whetherOnSale.code === 'Y'?'已上架':'已下架' }}</span>
          </template>
        </el-table-column>
<!--        待审核需要展示操作-->
        <el-table-column v-if="currentTab==0" slot="operator" fixed="right" label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-row class="table-edit-row">
              <span v-if="checkPermission(['admin','sale-saas-manufacturer-distributor-apply:accept', 'sale-platform-manufacturer-distributor-apply:accept'])" class="table-edit-row-item">
                <el-button
                  type="text"
                  size="small"
                  @click="enter(scope.row.id,1)"
                >通过</el-button>
                <el-button
                  v-if="checkPermission(['admin','sale-saas-manufacturer-distributor-apply:reject', 'sale-platform-manufacturer-distributor-apply:reject'])"
                  type="text"

                  size="small"
                  @click="enter(scope.row.id,2)"
                >驳回</el-button>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </transfer-table>
      <div class="pagination">
        <div class="pagination-total">共{{ total }}条记录&nbsp;&nbsp;第{{ page }}/{{ totalPage }}页</div>
        <el-pagination
          background
          :total="total"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          layout="prev, pager, next, sizes, jumper"
          @current-change="load"
          @size-change="load"
        />
      </div>
      <el-dialog v-dialogDrag :title="isAllAdoptTile" :visible.sync="changeVisible" width="500px">
        <el-form ref="changeForm" :model="changeForm">
          <el-form-item :label="changeVisibleType === 2 ? '确定驳回吗?':'确定通过吗?'" />
          <el-form-item :label="changeVisibleType === 2 ?'审核驳回后，分销商不可销售该商品，但可重新申请。':'审核通过后，分销商可销售该商品。'" />
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="changeVisible=false">取 消</el-button>
          <el-button type="primary" @click="examine()">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog v-dialogDrag :title="allAdoptTitle" :visible.sync="batchChange" width="500px">
        <el-form ref="batchForm" :model="batchForm">
          <el-form-item :label="batchForm.isBatchNumber === 2 ? '确定驳回吗?':'确定通过吗?'" />
          <el-form-item :label="batchForm.isBatchNumber === 2 ?'审核驳回后，分销商不可销售该商品，但可重新申请。':'审核通过后，分销商可销售该商品。'" />
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="batchChange=false">取 消</el-button>
          <el-button type="primary" @click="handleBatchPending">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import request from '@/utils/request'
import transferTable from '@/components/TransferTable/index'
import checkPermission from '@/utils/permission'

export default {
  components: {
    transferTable
  },
  data() {
    return {
      changeForm: {
        approvalStatus: 'PENDING',
        rejectReason: '默认值',
        ids: ''
      },
      batchChange: false,
      batchForm: {
        rejectReason: '默认值',
        isBatchNumber: 1 // 1批量通过  2批量拒绝
      },
      isExpand: false,
      allColumn: [
        {
          prop: 'saleMerchantName',
          label: '分销商名称',
          align: 'left',
          width: 250
        },
        {
          prop: 'whetherOnSale',
          label: '商品供应状态',
          align: 'left',
          width: 250,
          slot: true
        },

        {
          prop: 'pictIdS',
          label: '商品主图',
          align: 'left',
          slot: true

        },
        {
          prop: 'productListAdminDto.productCode',
          label: '商品编码',
          width: 180,
          align: 'left'
        },

        {
          prop: 'productListAdminDto.productName',
          label: '商品名称',
          width: 200,
          align: 'left'
        },
        {
          prop: 'productListAdminDto.drugName',
          label: '通用名称',
          width: 120,
          align: 'left'
        },

        {
          prop: 'productListAdminDto.spec',
          label: '规格',
          width: 120,
          align: 'left'
        },

        {
          prop: 'productListAdminDto.agentiaType',
          label: '剂型',
          align: 'left'
        },
        {
          prop: 'productListAdminDto.manufacturer',
          label: '生产厂家',
          width: 260,
          align: 'left'
        },
        {
          prop: 'productListAdminDto.unit',
          label: '单位',
          width: 80,
          align: 'left'
        },
        {
          prop: 'productListAdminDto.wholesalePrice',
          label: '建议批发价',
          width: 120,
          align: 'left'
        },
        {
          prop: 'productListAdminDto.retailPrice',
          label: '建议零售价',
          width: 120,
          align: 'left'
        },
        {
          prop: 'productListAdminDto.stockQuantity',
          label: '可卖库存',
          align: 'left'
        },
        {
          prop: 'productListAdminDto.supplyPrice',
          label: '供货价',
          className: 'salePrice',
          align: 'left'
        },
        {
          prop: 'updateTime',
          label: '操作时间',
          width: 155,
          align: 'left'
        },
        {
          prop: 'operator',
          label: '操作',
          slot: true,
          show: true

        }
      ], // 所有表列
      columns: {}, // 要显示的列
      loading: false,
      busOption: '',
      changeVisible: false,
      applyProductId: '', // 审核id
      queryParams: {
        productCode: '',
        productName: '',
        erpCode: '',
        saleMerchantName: '',
        whetherOnSale: '', manufacturer: ''
      },
      categoryOpts: [],
      currentTab: 0,
      tabs: [
        { name: '待审核', value: 'PENDING', hide: !checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:pendingAuditView','sale-platform-manufacturer-distributor-apply:pendingAuditView']) },
        { name: '已通过', value: 'ACCEPTED', hide: !checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:acceptedView','sale-platform-distributor-apply:acceptedView']) },
        { name: '已驳回', value: 'REJECTED', hide: !checkPermission(['admin', 'sale-saas-manufacturer-distributor-apply:rejectedView','sale-platform-manufacturer-distributor-apply:rejectedView']) }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      totalPage: 1,
      total: 0,
      selects: [],
      busOptions: [
        {
          label: '可现金交易',
          value: 'whetherCashTransaction:Y'
        },
        {
          label: '不可现金交易',
          value: 'whetherCashTransaction:N'
        },
        {
          label: '可退货',
          value: 'whetherReturnable:Y'
        },
        {
          label: '不可退货',
          value: 'whetherReturnable:N'
        }
      ],
      isAllAdoptTile: '审核',
      allAdoptTitle: '审核', // 批量审核
      changeVisibleType: 1

    }
  },
  watch: {
    currentTab() {
      this.$nextTick(() => {
        // 重置
        this.page = 1
        this.pageSize = 10
        this.totalPage = 1
        this.total = 0
        // 重新请求
        this.load()
      })
    }
  },
  mounted() {
    this.load()
    this.loadCategories()
  },
  methods: {
    checkPermission,
    resetData() {
      this.page = 1
      this.pageSize = 10
      this.totalPage = 1
      this.total = 0
      this.selects = []
      this.tableData = []
      this.queryParams.drugName = ''
      this.queryParams.productCode = ''
      this.queryParams.productName = ''
      this.queryParams.categoryCode = ''
      this.queryParams.erpCode = ''
      this.queryParams.stockCondition = 'ALL'
      this.categoryId = ''
      this.busOption = ''
    },
    handleBatchPass() {
      this.batchChange = true
      this.allAdoptTitle = '审核通过'
      this.batchForm.isBatchNumber = 1
    },
    handleBatchReject() {
      this.batchChange = true
      this.allAdoptTitle = '审核驳回'
      this.batchForm.isBatchNumber = 2
    },
    async examine() {
      const { data } = await request.post('product/admin/productShareApply/batchApproval', {
        approvalStatus: this.changeForm.approvalStatus,
        ids: [this.applyProductId],
        rejectReason: this.changeForm.rejectReason || ''
      })
      this.applyProductId = ''
      this.changeForm.rejectReason = ''
      this.changeForm.approvalStatus = ''
      this.changeVisible = false
      this.$message.success('操作成功！')
      this.load()
    },
    enter(id, type) {
      this.changeVisibleType = type
      this.isAllAdoptTile = type == 1 ? '审核通过' : '审核驳回'
      this.changeForm.approvalStatus = type == 1 ? 'ACCEPTED' : 'REJECTED'
      this.applyProductId = id
      this.changeVisible = true
    },
    async load() {
      this.loading = true
      const model = {
        ...this.queryParams
      }
      if (this.tabs[this.currentTab].value === 'ONSALE') {
        model.whetherOnSale = 'Y'
      } else if (this.tabs[this.currentTab].value === 'NOTONSALE') {
        model.whetherOnSale = 'N'
      } else {
        model.approvalStatus = this.tabs[this.currentTab].value
      }
      if (this.busOption) {
        const arr = this.busOption.split(':')
        model[arr[0]] = arr[1]
      }
      try {
        const { data } = await request.post('product/admin/productShareApply/supplierPage', {
          current: this.page,
          size: this.pageSize,
          map: {},
          model,
          order: 'descending',
          sort: 'createTime'
        })
        this.tableData = data.records
        this.page = data.current
        this.totalPage = data.pages
        this.total = data.total
      } catch (e) {

      }
      this.loading = false
    },

    handleChangeTab(index) {
      this.currentTab = index
    },

    splitString(val) {
      if (!val) {
        return ''
      }
      return val.split(',')
    },

    async loadCategories() {
      try {
        const { data } = await request.post('product/admin/category/query', {})
        const map = _.groupBy(data, 'parentId')

        this.categoryOpts = this._buildTree(0, 0, map)
      } catch (e) {

      }
    },

    _buildTree(parentId, level, map) {
      const list = map[parentId]

      if (!list) {
        return null
      }

      return list.map(item => ({
        id: item.id,
        code: item.categoryCode,
        name: item.label,
        sort: item.sortValue,
        frontShow: _.get(item, 'whetherShowFrontend.code') === 'Y',
        parentId: item.parentId,
        level,
        children: this._buildTree(item.id, level + 1, map)
      })).sort((a, b) => a.sort - b.sort)
    },

    resetFilter() {
      this.resetData()
      this.load()
    },

    handleSelectionChange(selects) {
      this.selects = selects
    },

    async handleBatchPending() {
      this.loading = true
      try {
        await request.post('product/admin/productShareApply/batchApproval', {
          ids: this.selects.map((item) => {
            return item.id
          }),
          rejectReason: this.batchForm.rejectReason || '',
          approvalStatus: this.batchForm.isBatchNumber == 1 ? 'ACCEPTED' : 'REJECTED'
        })
        this.$message.success('操作成功！')
        this.batchForm.rejectReason = ''
        this.batchForm.isBatchNumber = 1
        this.allAdoptTitle = ''
        this.batchChange = false
      } catch (e) {
        this.batchChange = false
      }
      this.selects = []
      this.loading = false
      this.load()
    }

  }
}
</script>

<style lang="scss" scoped>
  .product-list {
    &-filter {
      padding: 16px 16px 16px 12px;
      background-color: #ffffff;
      border-left: 4px solid #1890ff;

      .el-input {
        margin-right: 16px;
        width: 200px;
      }

      .el-select {
        margin-right: 16px;
        width: 130px;
      }

      .el-button + .el-button {
        margin-left: 8px;
      }
    }

    &-container {
      background-color: #ffffff;
      padding: 16px;
      margin-top: 16px;

      .product-list-tabs-wrapper {
        position: relative;

        .product-list-tabs {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #dcdde0;
          font-size: 14px;
          color: #303133;
          margin-bottom: 16px;

          .tab {
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            margin-right: 9px;
            position: relative;
            cursor: pointer;

            &:after {
              content: '';
              display: block;
              width: 100%;
              height: 2px;
              background-color: #1890ff;
              position: absolute;
              bottom: -1px;
              left: 0;
              display: none;
            }

            &.active:after {
              display: block;
            }
          }
        }

        .operations {
          position: absolute;
          right: 0;
          bottom: 8px;
        }
      }
    }

    .el-table {
      ::v-deep {
        td.salePrice {
          color: #FF6600;
        }

        td.costPrice {
          color: #339900;
        }

        td.grossProfit {
          color: #339900;
        }
      }
    }

    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #505465;
      font-size: 14px;
      margin-top: 20px;
    }
  }
</style>

<style lang="scss">
  td.salePrice {
    color: #FF6600;
  }

  td.costPrice {
    color: #339900;
  }

  td.grossProfit {
    color: #339900;
  }
</style>

