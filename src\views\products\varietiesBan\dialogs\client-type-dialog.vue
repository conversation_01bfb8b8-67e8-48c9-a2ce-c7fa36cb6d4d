<template>
  <el-dialog
    title="选择客户类型"
    :visible.sync="visible"
    width="50%"
    @open="getList"
    :before-close="handleClose">
    <el-table border :data="tableData" @selection-change="handleSelectionChange" ref="clientTypeTable">
      <el-table-column type="index" label="序号" width="50"/>
      <el-table-column type="selection" width="50" :selectable="selectable"/>
      <el-table-column label="客户类型名称" prop="name"/>
    </el-table>
    <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination"></pagination>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { merchantTypeList} from '@/api/group'
export default {
  components: {
    Pagination
  },
  props: ['checkedId'],
  data () {
    return {
      total: 0,
      page: 0,
      visible: false,
      params: {
        current:1,
        size: 10,
        model: {}
      },
      tableData: [
      ],
      selectData: [],
      selected: []
    }
  },
  mounted() {
    //this.getList()
  },
  methods: {
    handleConfirm() {
      this.$emit('getData', this.selectData)
      this.$refs.clientTypeTable.clearSelection()
      this.visible = false
    },
    pagination(val) {
      this.params.current = val.page
      this.params.size = val.limit
      this.getList()
    },
    handleClose(done) {
      done()
    },
    handleSelectionChange(val) {
      this.selectData = val
    },
    selectable(row, index) {
      if(row.selectable === true) {
        return false
      } else {
        return true
      }
    },
    async getList() {
      const { data } = await merchantTypeList(this.params)
      this.tableData = data.records
      this.tableData.forEach((item,index)=>{
        this.$set(item,'selectable',false)
        this.selected.forEach(itx=>{
          if (itx.id === item.id) {
            this.tableData[index].selectable = true
          }
        })
      })
      this.total = data.total;
      this.page = data.current

    }
  },
  watch: {
    checkedId: {
      immediate: true,
      handler(newVal,oldVal) {
        this.selected = newVal
      }
    }
  }
}
</script>
