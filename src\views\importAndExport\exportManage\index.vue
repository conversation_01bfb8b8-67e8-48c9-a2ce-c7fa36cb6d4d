<template>
  <div class="tab_bg">
    <tabs-layout ref="tabs-layout" :tabs="[{ name: '导出管理' }]">
      <template slot="button">
        <el-button @click="reload" v-throttle>刷新</el-button>
      </template>
    </tabs-layout>
    <!--分页table-->
    <table-pager ref="pager-table" :options="tableColumns" :selection="false" :remote-method="load" :data.sync="tableData" :pageSize="pageSize" :operation-width="120">
      <el-table-column slot="uploadStatus" label="导出状态">
        <template slot-scope="{row}">
          <!-- <span v-if="scope.row.uploadStatus">{{ scope.row.uploadStatus.code === 'SUCCESS' ? '成功' : (scope.row.uploadStatus.code == 'FAILURE' ? '失败' : '处理中') }}</span> -->
          <el-tag v-if="row['uploadStatus'].code === 'SUCCESS'" type="success">{{ row.uploadStatus.desc }}</el-tag>
          <el-tag v-if="row['uploadStatus'].code === 'FAILURE'" type="danger">{{ row.uploadStatus.desc }}</el-tag>
          <el-tag v-if="row['uploadStatus'].code === 'PROCESSING'">{{ row.uploadStatus.desc }}</el-tag>
        </template>
      </el-table-column>
      <div slot-scope="{ row }">
        <el-row class="table-edit-row">
          <span class="table-edit-row-item tableItemBtn" style="display: flex">
            <el-link type="primary" style="margin-right: 5px" v-throttle v-if="checkPermission(['admin','sale-saas-batchProcessing-export:download', 'sale-platform-batchProcessing-export:download'])" :href="row.filePath" target="_blank" :underline="false">下载</el-link>
            <del-el-button v-if="checkPermission(['admin','sale-saas-batchProcessing-export:del', 'sale-platform-batchProcessing-export:del'])" :targetId="row.id" :text="delText" @handleDel="handleDelete"></del-el-button>
          </span>
        </el-row>
      </div>
    </table-pager>
  </div>
</template>


<script>
import delElButton from '@/components/eyaolink/delElButton'
import { exportRecodeList, deleteExportRecode, recordImport } from '@/api/product'
import checkPermission from '@/utils/permission'
const TableColumns = [
  { label: "导出时间", name: "updateTime",prop: "updateTime",width: "180" },
  { label: "导出类型", name: "exportType.desc", prop:"exportType.desc", width: "150"},
  { label: "文件名称", name: "fileName",prop: 'fileName', width: "200" },
  { label: "导出状态", name: "uploadStatus", prop:"uploadStatus", width: "150", slot: true },
  { label: "操作人", name: "createUserName", prop:'createUserName', width: "150" }
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export default {
  //import引入的组件
  components: {
    delElButton
  },

  data() {
    return {
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
      delText: "您确定删除此导出记录吗？"
    }
  },
  //方法集合
  methods: {
    checkPermission,
    async load(params) {
      let listQuery = {
        model:{},
        sort: "id",
        order: "descending"
      };
      Object.assign(listQuery,params);
      return await exportRecodeList(listQuery);
    },
    // 删除
    handleDelete(id) {
      deleteExportRecode(id).then(res => {
        if (res.code === 0 && res.msg === 'ok') {
          this.$message.success('删除成功')
          this.reload()
        }
      })
    },
    //  刷新
    reload() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
  }

}
</script>


<style lang="scss" scoped>
.tableItemBtn ::v-deep .el-link {
  font-weight: 400 !important;
}
</style>
