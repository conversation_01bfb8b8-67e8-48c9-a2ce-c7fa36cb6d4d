<template>
  <el-dialog title="签到设置" :visible.sync="visible" :close-on-click-modal="false" width="400px">
    <div class="setting_wrapper" v-loading="loading">
      <div class="setting_item">
        <span style="width: 110px;">打卡范围：</span>
        <el-input-number style="width: 100%" :min="0" :controls="false" v-model="form.outerScope" placeholder="请输入内容" />
        <span style="padding-left: 10px;">米</span>
      </div>
      <div class="setting_item">
        <span style="width: 90px;">超打卡范围：</span>
        <el-radio-group style="display: flex;flex-direction: column;margin-top: 20px;" v-model="form.superableScope">
          <el-radio :label="item.label" style="margin-bottom: 10px;" v-for="(item, index) in settingData" :key="index">
            <span style="margin-right: 10px;">{{item.name}}</span>
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCheckInSetting, setCheckInSetting } from '@/api/salemanCenter/index';

export default {
  name: 'checkInSettingsDialog',
  data() { 
    return {
      visible: false,
      loading: false,
      form: {
        outerScope: '',
        superableScope: '',
        id: '',
      },
      settingData: [
        {
          name: '不可打卡',
          label: 2,
        },
        {
          name: '可打卡，但需补充说明',
          label: 1,
        }
      ]
    }
  },
  methods: {
    show() {
      this.visible = true
      this.loading = true
      getCheckInSetting().then(res => {
        if (res.code !== 0) return
        const data = res.data
        Object.keys(this.form).forEach(key => {
          this.$set(this.form, key, data[key])
        })
      }).finally(() => {
        this.loading = false
      })
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.loading = true
      setCheckInSetting(this.form).then(res => {
        if (res.code !== 0) return
        this.$message.success("设置成功")
        this.$emit('submitSuccess')
        this.hide()
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.setting_wrapper {
  .setting_item {
    display: flex;
    align-items: center;
  }
}
</style>