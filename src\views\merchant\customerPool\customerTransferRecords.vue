<template>
  <div id="customerTransferRecords">
    <div class="search_box">
      <el-input
        v-model.trim="searchForm.purMerchantNameOrErpCode"
        placeholder="请输入客户名称/编码"
        style="width: 180px; margin-right: 10px"
        clearable
        @clear='getData()'
        @blur="getData()"
        @keydown.enter.native="getData()"
      ></el-input>
      <el-select
        style="width: 180px; margin-right: 10px"
        v-model="searchForm.oldSaleManId"
        clearable
        filterable
        remote
        placeholder="原所属业务员"
        :remote-method="(t) => remoteMethod(2, t)"
        :loading="saleManLoading"
        @change="getData()"
      >
        <el-option
          v-for="item in saleManOptionsL"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        v-model.trim="searchForm.oldPoolName"
        placeholder="原所属客户池"
        style="width: 180px; margin-right: 10px"
        @keydown.enter.native="getData()"
        clearable
        @clear='getData()'
        @blur="getData()"
      ></el-input>
      <el-select
        style="width: 180px; margin-right: 10px"
        v-model="searchForm.saleManId"
        clearable
        filterable
        remote
        placeholder="现所属业务员"
        :remote-method="(t) => remoteMethod(1, t)"
        :loading="saleManLoading"
        @change="getData()"
      >
        <el-option
          v-for="item in saleManOptionsZ"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        v-model.trim="searchForm.poolName"
        placeholder="现所属客户池"
        style="width: 180px; margin-right: 10px"
        @keydown.enter.native="getData()"
        clearable
        @clear='getData()'
        @blur="getData()"
      ></el-input>
      <el-date-picker
        v-model="searchForm.date"
        type="daterange"
        style="width: 240px; margin-right: 10px"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        value-format="yyyy-MM-dd HH:mm:ss"
        :clearable="false"
        @change="getMonthDiff"
      >
      </el-date-picker>
      <el-button type="primary" @click="getData()">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
    <div class="main_box">
      <table-pager
        ref="pager-table"
        :pageSize="10"
        :options="tableTitle"
        :data.sync="tableData"
        :remote-method="load"
        :isNeedButton="false"
      >
      </table-pager>
    </div>
  </div>
</template>

<script>
import { getSaleManList, getMerchantPoolCirculationLogList } from "@/api/merchantApi/customerPool.js";
import Pagination from "@/components/Pagination/index.vue";
import dayjs from "dayjs";
export default {
  name: "customerTransferRecords",
  components: {
    Pagination,
  },
  props: {},
  data() {
    return {
      searchForm: {
        purMerchantNameOrErpCode: "",
        oldSaleManId: "",
        oldPoolName: "",
        saleManId: "",
        poolName: "",
        date: [],
      },
      saleManOptionsL: [], // 包含离职的业务员
      saleManOptionsZ: [], // 在职业务员
      saleManLoading: false,
      tableTitle: [
        {
          key: 1,
          prop: "erpCode",
          name: "erpCode",
          label: "ERP编码",
          width: "",
          disabled: true,
          align: "center",
        },
        {
          key: 2,
          prop: "purMerchantName",
          name: "purMerchantName",
          label: "客户名称",
          width: "",
          disabled: true,
          align: "center",
        },
        {
          key: 3,
          prop: "purMerchantTime",
          name: "purMerchantTime",
          label: "创建时间",
          width: "",
          align: "center",
        },
        {
          key: 4,
          prop: "oldSaleManName",
          name: "oldSaleManName",
          label: "原所属业务员",
          width: "",
          align: "center",
        },
        {
          key: 5,
          prop: "oldPoolName",
          name: "oldPoolName",
          label: "原所属客户池",
          width: "",
          align: "center",
        },
        {
          key: 6,
          prop: "saleManName",
          name: "saleManName",
          label: "现所属业务员",
          width: "",
          align: "center",
        },
        {
          key: 7,
          prop: "poolName",
          name: "poolName",
          label: "现所属客户池",
          width: "",
          align: "center",
        },
        {
          key: 8,
          prop: "createName",
          name: "createName",
          label: "操作人",
          width: "",
          align: "center",
        },
        {
          key: 9,
          prop: "createTime",
          name: "createTime",
          label: "流转时间",
          width: "",
          align: "center",
        },
        {
          key: 10,
          prop: "description",
          name: "description",
          label: "流转原因",
          width: "",
          align: "center",
        },
      ],
      tableData: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    let end = dayjs().format("YYYY-MM-DD 23:59:59");
    let start = dayjs().subtract(3, "month").format("YYYY-MM-DD 00:00:00");
    this.searchForm = {
      date: [start, end],
    };
  },
  mounted() {},
  methods: {
    getData() {
      this.$refs["pager-table"].doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    reset() {
      let end = dayjs().format("YYYY-MM-DD 23:59:59");
      let start = dayjs().subtract(3, "month").format("YYYY-MM-DD 00:00:00");
      this.searchForm = {
        date: [start, end],
      };
      this.getData();
    },
    async load(params) {
      try {
        let temp = {
          ...(params ?? {}),
          model: {
            ...this.searchForm,
            startTime: this.searchForm.date?.[0] ?? "",
            endTime: this.searchForm.date?.[1] ?? "",
          },
        };
        this.loading = true;
        let res = await getMerchantPoolCirculationLogList(temp);
        this.loading = false;
        return res || { data: {} };
        console.log('search',this.searchForm);
        return {
          data: {
            records: [{}, {}],
          },
        };
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    // 获取业务员列表
    remoteMethod(f, query) {
      if (query !== "") {
        this.saleManLoading = true;
        getSaleManList({
          name: query,
          whetherOnJob: f === 1 ? "1" : "",
        }).then((res) => {
          this.saleManLoading = false;
          f === 1
            ? (this.saleManOptionsZ = res.data ?? [])
            : (this.saleManOptionsL = res.data ?? []);
        });
      } else {
        this.saleManOptionsL = [];
        this.saleManOptionsZ = [];
      }
    },
    // 计算两个日期之间的月份差
    getMonthDiff() {
      if (!this.searchForm?.date?.length) {
        this.getData();
        return;
      };
      let start = this.searchForm.date[0];
      let end = this.searchForm.date[1];
      let months = dayjs(start).add(3, "month");
      let f = dayjs(end).isAfter(months);
      if (f) {
        this.searchForm.date = [start, months.format("YYYY-MM-DD 23:59:59")];
        this.$message.error("所选日期范围不能超过3个月");
      }
      this.getData();
    },
  },
};
</script>

<style scoped lang="scss">
#customerTransferRecords {
  // height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  > div {
    background: #fff;
    padding: 16px;
  }
  > div + div {
    margin-top: 20px;
  }
}
.search_box {
  height: 68px;
  border-left: 4px solid #0056e5;
  display: flex;
  align-items: center;
  > .el-input + .el-button {
    margin-left: 10px;
  }
}
.main_box {
  flex: 1;
  display: flex;
  flex-direction: column;
  .main_content {
    flex: 1;
  }
  .pagination-container {
    justify-content: right;
  }
}
</style>
