import { formatDataTime } from '@/utils/index';
import { MessageBox } from "element-ui";
import router from "@/router/index";
/**
 * 下载方法
 * @param JSONObject
 */
 export const downloadFile = (exportObj) => {
  import('@/vendor/Export2Excel').then(excel => {
      excel.export_json_to_excel({
          header: exportObj.tHeader, //表头 必填
          data: exportObj.exportData, //具体数据 必填
          filename: exportObj.fileName||'name', //导出名称 非必填
          autoWidth: true, //非必填
          bookType: 'xlsx' //非必填
      })
  })
}


// 导出处理文件流
export function exoprtToExcel(data,fileName){
    const name = fileName || `${formatDataTime('yyyyMMDDHHmmss')}.xlsx`
    const blob = new Blob([data], { // 取响应回来的数据
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'
    });
    const href = window.URL.createObjectURL(blob); // 创建下载的链接
    const downloadElement = document.createElement('a');
    downloadElement.href = href;
    downloadElement.download = name;
    document.body.appendChild(downloadElement);
    downloadElement.click(); // 点击下载
    document.body.removeChild(downloadElement); // 下载完成移除元素
    window.URL.revokeObjectURL(href);
}

// 根据url下载文件
export function downloadFile2Url(url, fileName) {
  const name = fileName || `${formatDataTime('yyyyMMDDHHmmss')}`
  const downloadElement = document.createElement('a');
  downloadElement.href = url;
  downloadElement.download = name;
  document.body.appendChild(downloadElement);
  console.log(downloadElement)
  downloadElement.click(); // 点击下载
  document.body.removeChild(downloadElement); // 下载完成移除元素
}

export function exoprtZipByURL(data,fileName) {
    let link = document.createElement("a");
    //创建一个a标签
    link.style.display = "none";
    link.target="_blank"
    //将a标签隐藏
    link.href = data;
    //给a标签添加下载链接  "域名+接口"  safe是一个动态的域名  后面的接口替换成你自己的下载接口
    // link.setAttribute("download", fileName);
    // 此处注意，要给a标签添加一个download属性，属性值就是文件名称 否则下载出来的文件是没有属性的，空白白
    document.body.appendChild(link);
    //将上面创建的a标签加入到body的尾部
    link.click();
    document.body.removeChild(link);
}



export function exoprtZipByBlob(data,fileName) {
    console.info(data)
    let blob = new Blob([data], { type: 'application/zip' })
    let url = window.URL.createObjectURL(blob)
    const link = document.createElement('a') // 创建a标签
    link.href = url
    link.download = fileName // 重命名文件
    link.click()
    URL.revokeObjectURL(url) // 释放内存

}


