<template>
<el-card class="product-create page-card" shadow="never" v-loading="loading">
  <template slot="header">
    <div class="title">控销设置</div>
  </template>

  <varieties-ban-form :data="detail" :isAdd="isAdd" :client="client"/>
</el-card>
</template>

<script>
  import { productControlSaleById } from '@/api/product.js'
  import VarietiesBanForm from './form'
export default {
  components: { VarietiesBanForm },
  data() {
    return {
      loading: false,
      detail: '',
      isAdd: true,
      client: ''
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.getDetail()
  },
  methods: {
    async getDetail() {
      if (this.id) {
        this.isAdd = false
        const { data } = await productControlSaleById(this.id)
        this.detail = data
        this.client = data.selectCustomer.code
      }
    }
  }
}
</script>
