<template>
  <div class="im-table-page">
    <!-- table -->
    <el-table
      ref="table"
      v-loading="loading"
      v-bind="$attrs"
      :data="data"
      :default-expand-all="defaultExpandAll"
      :row-class-name="rowClassName"
      :row-key="rowKey"
      :span-method="spanMethod"
      :tree-props="treeProps"
      border
      @select="select"
      @selection-change="moreCheckBox"
      @select-all="selectAll"
      @filter-change="filterTagTable"
    >
      <el-table-column v-if="index" align="center" fixed type="index" width="48">
        <template slot="header">
          <i class="el-icon-menu" />
        </template>
      </el-table-column>
      <slot />
    </el-table>
    <im-pagination
      v-if="showPagination"
      :current-page.sync="currentPage"
      :page-btn="pageBtn"
      :page-size.sync="pageSize"
      :total="total"
      class="table-page"
      @pagination="handlePagination"
    />
    <div v-else class="main-info">
      共{{ total }}条记录
    </div>
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  props: {
    data: {
      type: Array,
      required: true
    },
    index: {
      type: Boolean,
      default: false
    },
    remoteMethod: {
      type: Function,
      default: () => {}
    },
    rowClassName: {
      type: Function,
      default: () => {}
    },
    spanMethod: {
      type: Function,
      default: () => {}
    },
    moreCheckBox: {
      type: Function,
      default: () => {}
    },
    selectAll: {
      type: Function,
      default: () => {}
    },
    select: {
      type: Function,
      default: () => {}
    },
    filterTagTable: {
      type: Function,
      default: () => {}
    },
    treeProps: {
      type: Object,
      default: function() {
        return {}
      }
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    rowKey: {
      type: String,
      default: ''
    },
    pageBtn: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      total: 0,
      pageSize: 10,
      currentPage: 0,
      condition: {}
    }
  },
  mounted() {
    this.currentPage = 0
    window.addEventListener('resize', this.doLayout)
    this.updateData()
  },
  activated() {
    this.$refs.table.doLayout()
  },
  destroyed() {
    window.removeEventListener('resize', this.doLayout)
  },
  methods: {
    doRefresh(pageParam) {
      if (pageParam && pageParam instanceof Object) {
        this.currentPage = pageParam.currentPage
        this.pageSize = pageParam.pageSize
      }
      this.updateData()
    },
    updateData() {
      this.loading = true
      const pageNum = this.currentPage < 1 ? 1 : this.currentPage
      return this.remoteMethod({
        pageNum,
        pageSize: this.pageSize
      }).then(response => {
        const { data } = response
        if (data && data.records) {
          this.$emit('update:data', data.records)
          this.total = data.total
          this.$emit('updated', data.records)
        }
        this.loading = false
      }).catch(() => {
        this.$emit('update:data', [])
        this.loading = false
      })
    },
    handlePagination(data) {
      this.currentPage = Math.min(this.currentPage, Math.ceil(this.total / data.pageSize))
      this.updateData()
    },
    doLayout() {
      this.$refs.table && this.$refs.table.doLayout()
    },
    toggleRowSelection(row, selected) {
      this.$refs.table.toggleRowSelection(row, selected)
    },
    clearSelection() {
      this.$refs.table.clearSelection()
    },
    toggleRowExpansion(row, expanded) {
      this.$refs.table.toggleRowExpansion(row, expanded)
    }
    // select(val, row, sss){
    //   this.$emit('chang-select', { val, row, sss })
    // }
  }
}
</script>
<style lang="scss">
  .im-table-page {
    .el-table__header th {
      padding: 11px 0;
    }
  }</style>
<style lang="scss" scoped>
@import '~@/styles/variables';

  .im-table-page {
    .table-page {
      margin-top: 15px;
    }

    .im-pagination {
      padding-top: 16px;
      border-top: $tableBorder;
    }
  }
</style>
