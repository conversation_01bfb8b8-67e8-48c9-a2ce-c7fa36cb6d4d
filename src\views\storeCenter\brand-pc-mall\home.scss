ol, ul {
  list-style: none;
}

.main {
  ol, ul {
    list-style: none;
  }
  background-color: #f5f5f5;

  .content {
    position: relative;
  }
}

.content-title{
  background-color: #fff;
  height: 56px;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #EBECEE;
  .lt{
    font-size: 18px;
    font-weight: 500;
    color: #1e2439;
  }
  .el-button--primary{
    background-color: #0056E5;
    border-color: #0056E5;
  }
}

.header-container {
  margin: 0 auto;
  position: relative;
  height: 480px;

  .img {
    display: inline-block;
    height: 400px;
    width: calc((100vw - 1200px) / 2);
    background-color: #f8f8f8;
    position: absolute;
    right: calc((100vw - 1210px) / 2 + 1200px);
    background-position-x: right;
    background-repeat: no-repeat;
  }

  .img-r {
    top: 0;
    left: calc((100vw - 1217px) / 2 + 1200px);
    background-position-x: left;
    width: calc((100vw - 1217px) / 2);
  }

  .nav-body {
    width: 1200px;
    margin: 0 auto;
    text-align: left;

    .nav-content {
      margin-left: 218px;
      margin-top: 10px;
      text-align: right;

      .nav-carousel {
        width: 740px;
        overflow: hidden;
        float: left;
        background-color: #fff;

        .nav-show {
          display: flex;
          img {
            width: 380px;
            height: 100px;

            &:hover {
              opacity: .8;
            }
          }
        }
      }

      .nav-person {
        width: 230px;
        height: 164px;
        display: inline-block;
        background-color: #fff;

        .nav-person-img {
          text-align: center;
          margin-top: 16px;
          img{
            width: 60px;
            height: 60px;
          }

          p {
            font-size: 14px;
            padding-top: 12px;
          }
        }

        .nav-person-img2 {
          margin: 17px 12px 0;
          display: flex;

          img {
            width: 56px;
            height: 56px;
          }

          p {
            color: #fff;
            font-size: 14px;
            padding: 5px 0 0 3px;
            line-height: 1.3em;
          }

          .last {
            font-size: 12px;
            padding-top: 7px;
          }
        }

        .nav-person-button {
          text-align: center;
          margin-top: 16px;

          .el-button--small.is-round {
            width: 96px;
            height: 32px;
            background: #0056e5;
            color: #fff;
            font-size: 12px;
            border-radius: 0;
            border: 0;
          }

          .last {
            .el-button--small.is-round {
              background: #e5eefc;
              margin-left: 10px;
              color: #0056e5;
            }
          }
        }

        .nav-person-list {
          width: 210px;
          height: 100px;
          margin: 8px 0 0 10px;
          background: #fff;

          .list-button {
            width: 50%;
            height: 32px;
            float: left;
            text-align: center;
            margin-top: 10px;
            border-right: 1px solid #E5E5E5;
            cursor: pointer;

            .num {
              color: #3A6BED;
              font-size: 16px;
              margin-bottom: 5px;
            }

            .text {
              color: #666;
              font-size: 12px;
              border-bottom: 1px solid #E5E5E5;
              width: 80%;
              margin: 0 auto;
              padding-bottom: 7px;
            }

            &:nth-child(2), &:nth-child(4) {
              border-right: none;
            }

            &:nth-child(3), &:nth-child(4) {
              margin-top: 16px;

              .text {
                border-bottom: none;
              }
            }
          }
        }
      }

      .nav-notice {
        width: 230px;
        height: 306px;
        display: inline-block;
        background-color: #fff;
        text-align: left;

        .notice-list{
          border-top: 1px solid #f5f6f7;
          &.quick{
            padding: 16px;
            .quick-title{
              font-size: 12px;
              font-weight: 500;
              color: #1e2439;
              padding-bottom: 16px;
            }
            .quick-content{
              display: flex;
              justify-content: space-between;
              .quick-list{
                display: flex;
                width: 40px;
                justify-content: center;
                flex-wrap: wrap;
                cursor: pointer;
                p{
                  font-size: 12px;
                  font-weight: 400;
                  color: #1e2439;
                  padding-top: 5px;
                }
                img{
                  width: 24px;
                  height: 24px;
                }
              }
            }
          }
          &.notice{
            padding: 16px;
            li{
              font-size: 12px;
              color: #505465;
              margin-top: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
            }
          }
          &.qual{
            padding: 12px 16px 14px;
            display: flex;
            justify-content: space-between;
            .qual-list{
              display: flex;
              width: 50px;
              justify-content: center;
              flex-wrap: wrap;
              p{
                font-size: 12px;
                font-weight: 400;
                color: #1e2439;
                padding-top: 6px;
              }
              img{
                width: 40px;
                height: 40px;
              }
            }
          }
        }
      }
    }
  }
}

/* 主要架构 */
.commodity {
  width: 1200px;
  margin: 64px auto 0;

  .commodity-head {

    .commodity-text {
      text-align: left;

      .commodity-title {
        font-size: 22px;
        font-weight: bold;
        color: #1e2d46;
      }

      .commodity-remarks {
        margin: 0 32px 0 10px;
        font-size: 16px;
        color: #828591;
      }
    }

    .count-down {
      margin-right: 30px;
      line-height: 68px;
      display: inline-block;
      font-size: 19px;
      font-weight: bold;

      .count-down-text {
        color: #666666;
        padding-right: 8px;
        font-size: 15px;
      }

      .count-down-num {
        padding: 6px 4px;
        margin-right: 2px;
        background-color: #222;
        color: #fff;
      }

      .count-down-point {
        color: #000;
        margin-right: 2px;
      }
    }

    .commodity-more {
      float: right;
      line-height: 60px;
      margin-right: 5px;
      font-size: 14px;
      cursor: pointer;

      i {
        font-size: 20px;
        line-height: 60px;
        vertical-align: bottom;
      }

      &:hover {
        color: #3A6BED;
      }
    }
  }

  .commodity-content {
    width: 100%;
    position: relative;
    margin-top: 30px;
    .discount{
      display: flex;
      .discount-lt{
        width: 596px;
        display: flex;
        justify-content: space-between;
        height: 410px;
        img{
          width: 293px;
          height: 410px;
        }
      }
      .discount-rt{
        flex: 1;
        margin-left: 10px;
        .discount-rt-content{
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
        }
      }
    }
    .discount-list{
      width: 292px;
      height: 200px;
      background: #ffffff;
      margin-top: 10px;
      padding: 25px 30px 0;
      &:nth-child(1), &:nth-child(2){
        margin-top: 0;
      }
      .discount-title{
        font-size: 22px;
        font-weight: 600;
        color: #09172f;
        text-align: left;
        span{
          font-size: 14px;
          font-weight: 400;
          color: #9b9ea7;
          padding-left: 8px;
        }
      }
      .discount-content{
        margin-top: 25px;
        display: flex;
        justify-content: space-between;
        img{
          width: 100px;
          height: 100px;
        }
      }
    }
    .reco-title{
      height: 64px;
      background-color: #fff;
      display: flex;
      .title-list{
        text-align: center;
        width: 200px;
        height: 64px;
        p{
          font-size: 16px;
          font-weight: 500;
          color: #1e2439;
          margin-top: 16px;
          cursor: pointer;
        }
        span{
          font-size: 12px;
          font-weight: 400;
          color: #828591;
          padding-top: 10px;
          display: block;
        }
        &.active{
          p{
            margin-top: 10px;
            padding: 6px 12px;
            opacity: 1;
            background: #0556fe;
            border-radius: 14px;
            color: #ffffff;
            display: inline-block;
          }
          span{
            padding-top: 4px;
          }
        }
      }
    }
    .reco-content{
      margin-bottom: 10px;
      display: flex;
      flex-wrap: wrap;
      .reco-list{
        width: 232px;
        height: 370px;
        background: #ffffff;
        margin-top: 10px;
        margin-left: 10px;
        padding: 20px 20px 0;
        text-align: left;
        &:nth-child(5n-4){
          margin-left: 0;
        }
        .reco-logo{
          width: 192px;
          height: 192px;
        }
        .name{
          font-size: 14px;
          color: #1e2439;
          margin-top: 20px;
          overflow: hidden;
          line-height: 18px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .spec{
          margin-top: 8px;
          font-size: 12px;
          color: #828591;
          line-height: 14px;
          height: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .price{
          font-size: 18px;
          font-weight: 500;
          color: #ff5c00;
          margin-top: 8px;
          span{
            font-size: 14px;
            font-weight: 600;
          }
          p{
            font-size: 14px;
          }
        }
        .shop{
          cursor: pointer;
          margin-top: 24px;
          font-size: 12px;
          color: #505465;
          display: flex;
          align-items: center;
          .icon-shop{
            width: 12px;
            height: 12px;
            margin-right: 4px;
          }
          .icon-arrow{
            margin-left: 4px;
            width: 3px;
            height: 6px;
          }
        }
      }
    }
  }

}

.no-login{
  width: 100%;
  background-color: #fff;
  height: 80px;
  .w-1200{
    width: 1200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    margin: 0 auto;
    height: 80px;
    .lt{
      display: flex;
      align-items: center;
      img{
        width: 50px;
        height: 50px;
        margin-right: 10px;
      }
      p{
        font-size: 18px;
        font-weight: 500;
        color: #1e2439;
      }
      span{
        font-size: 14px;
        color: #9b9ea7;
        padding-top: 6px;
        display: inline-block;
      }
    }
    .immediately-button{
      width: 96px;
      height: 32px;
      background: #0056e5;
      font-size: 12px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
}

.upload-demo-table{
  .el-list-leave-active{
    display: none;
  }
}
