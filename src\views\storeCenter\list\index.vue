<template>
  <div  class="tab_bg">
    <div>
      <tabs-layout
        v-model="activeName"
        :tabs="[{ name: '商家服务中心', value: 'first' }]"
      >
        <template slot="button">
          <el-button @click="getList">刷新</el-button>
        </template>
      </tabs-layout>
      <el-table
        ref="dragTable"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" :render-header="renderHeader" width="60">
          <template slot-scope="{ row }">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :width="item.width"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column width="140" label="操作">
          <template slot-scope="{ row }">
            <span v-if="row.type === 0">
              <e-link v-if="checkPermission(['admin','storeCenter:recharge'])" @click="goDetail(row)">充值缴纳</e-link>
            </span>
            <span v-else>
              <el-link v-if="checkPermission(['admin','storeCenter:apply'])" type="primary" @click.native="showApply = true">申请退保证金</el-link>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="total" :page="page" @pagination="pagination" />
    </div>
    <!-- 申请退保证金 -->
    <el-dialog
      :visible.sync="showApply"
      title="申请退保证金"
    >
      <el-form ref="applyForm" model="applyForm" label-width="95px" label-position="left">
        <el-form-item label="申请金额：">
          6000.00元
        </el-form-item>
        <el-form-item label="到款账户：">
          基药云科技有限公司
        </el-form-item>
        <el-form-item label="到款账号：">
          43535345353534534534
        </el-form-item>
        <el-form-item label="验证码：">
          <el-input v-model="applyForm.msgCode" placeholder="请输入内容" size="middle" style="width: 300px;">
            <template slot="append">
              <el-button type="primary" @click="isSend = true">发送验证码</el-button>
            </template>
          </el-input>
          <el-button v-if="isSend" type="text">验证码已发送至您的手机：139×××2665</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="showApply = false">取消</el-button>
          <el-button type="primary" @click="onSubmit">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { fetchList } from '@/api/article'
import Sortable from 'sortablejs'
import Pagination from '@/components/Pagination/index.vue'
import checkPermission from '../../../utils/permission'
import TabsLayout from "../../../components/TabsLayout/index";
const TableColumns = [
  { label: '费用名称', name: 'costName' },
  { label: '所需缴纳费用', name: 'cost' },
  { label: '已缴金额', name: 'acount' },
  { label: '有效期至', name: 'validityDate' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}

export { TableColumnList }

export default {
  name: 'DragTable',
  components: {
    TabsLayout,
    Pagination
  },
  data() {
    return {
      rules: {
        msgCode: [
          { required: true, message: '请填写活动形式', trigger: 'blur' }
        ]
      },
      isSend: false,
      // Form
      applyForm: {
        msgCode: ''
      },
      // 申请退保证金弹窗
      showApply: false,
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      list: [{
        id: 0,
        costName: '平台技术服务费',
        cost: '￥6000.00',
        acount: '￥6000.00',
        validityDate: '2021年12月30日',
        type: 0
      }, {
        id: 1,
        costName: '平台通用保证金',
        cost: '￥10000.00',
        acount: '￥10000.00',
        validityDate: '长期',
        type: 1
      }],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first'
    }
  },
  methods: {
    checkPermission,
    onSubmit() {
      this.$alert('申请退保证金提交成功，一般预计3-5个工作日到帐，实际到帐以渠道打处理为准。', '申请退保证金操作成功', {
        confirmButtonText: '确定',
        callback: action => {
          this.showApply = false
        }
      })
    },
    showModal() {
    },
    goDetail(row) {
      this.$router.push({
        name: 'storeCenter_payment'
      })
    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (
        <div style='position:relative'>
          <div onClick={this.setHeaer}>
            <i class='el-icon-menu' />
          </div>
          <el-dialog
            title='设置显示列表'
            showClose={false}
            visible={this.showSelectTitle}
            width='640px'
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style='margin-top: 25px;text-align: center;'>
              <el-button type='primary' onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },

    setHeaer: function() {
      this.showSelectTitle = !this.showSelectTitle
    },
    async getList() {
      this.listLoading = true
      const { data } = await fetchList(this.listQuery)
      this.list = data.items
      this.total = data.total
      this.listLoading = false
      this.oldList = this.list.map((v) => v.id)
      this.newList = this.oldList.slice()
      this.$nextTick(() => {
        this.setSort()
      })
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        '.el-table__body-wrapper > table > tbody'
      )[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function(dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0]
          this.list.splice(evt.newIndex, 0, targetRow)

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0]
          this.newList.splice(evt.newIndex, 0, tempIndex)
        }
      })
    }
  }
}
</script>
