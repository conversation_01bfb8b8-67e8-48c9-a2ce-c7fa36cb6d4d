<template>
  <el-form :model="form" label-width="80px" label-position="right" :disable="action==='SHOW'">
    <div class="gps-quality">
      <form-item-title title="GPS质量"/>
      <el-row :gutter="40">
        <el-col :span="6">
          <el-form-item label="养护类型" label-width="100px">
            <el-select v-model="form.curingType" :disabled="isDetail">
              <el-option value="STRONG" label="重点养护" />
              <el-option value="GENERAL" label="常规养护" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="养护周期" label-width="100px">
            <el-input v-model="form.curingCycle" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="储存方式" label-width="100px">
            <el-select v-model="form.keepWay" :disabled="isDetail">
              <el-option value="NORMAL" label="常温" />
              <el-option value="SHADE" label="阴冷" />
              <el-option value="COOL" label="冷藏" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="储存条件" label-width="120px">
            <el-input v-model="form.keepCondition" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="6">
          <el-form-item label="是否医保" label-width="100px">
            <el-radio-group v-model="form.whetherMedicareVariety" size="mini" :disabled="isDetail">
              <el-radio-button label="Y">医保</el-radio-button>
              <el-radio-button label="N">非医保</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医保号" label-width="100px">
            <el-input v-model="form.medicareNum" :disabled="isDetail || form.whetherMedicareVariety === 'N'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="特殊管理药品" label-width="100px">
            <el-select v-model="form.specialMngMedicinal" :disabled="isDetail">
              <el-option value="NONSPECIFIC" label="非特殊药品" />
              <el-option value="SPIRIT" label="精神药物" />
              <el-option value="ANESTHESIA" label="麻醉药品" />
              <el-option value="MEDICINAL_TOXIC" label="医疗用毒性药品" />
              <el-option value="RADIOACTIVITY" label="放射性药品" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否冷藏冷链" label-width="120px" required>
            <el-radio-group v-model="form.whetherColdChain" size="mini" :disabled="isDetail">
              <el-radio-button label="Y">是</el-radio-button>
              <el-radio-button label="N">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="性能" label-width="100px">
            <el-input v-model="form.performance" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用途" label-width="100px">
            <el-input v-model="form.purpose" :disabled="isDetail"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script>
import formItemTitle from '@/views/products/common-components/form-item-title'
export default {
  components: { formItemTitle },
  props: {
    action: {
      type: String
    },
    isNumber2: {
      type: Function
    },
    isInt7: {
      type: Function
    }
  },
  data () {
    return {
      form: {
        curingType: '',
        curingCycle: '',
        keepWay: '',
        keepCondition: '',
        whetherMedicareVariety: 'N',
        medicareNum: '',
        specialMngMedicinal: '',
        whetherColdChain: 'N',
        performance: '',
        purpose: ''
      }
    }
  },
  computed: {
    isDetail () {
      return this.action === 'SHOW'
    }
  },

  methods: {
    getData () {
      return this.form
    },
    setForm (data) {
      for (let k in this.form) {
        this.form[k] = data[k]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.gps-quality{
	.el-row{
		margin-bottom: 10px;
		padding-right: 30px;
		.el-col{
			.el-form-item{
				margin-bottom: 0;
			}
		}
	}
}
</style>
