<template>
    <el-dialog title="添加配送地区" :visible.sync="dialogVisible" @open="getTree" @close="handleClose" width="950px">
      <default-form ref="AreForm" :defaultForm="defaultForm" :defaultDelivery="defaultDelivery" style="border-bottom: 1px solid #ddd;width: 900px;position: relative;min-height: 450px;padding-left: 200px;">
        <el-form-item style="position: absolute;left: 0; top: 0;border-right: 1px solid #ddd;width: 200px; height: 440px; overflow: scroll">
          <el-tree
            v-loading="loading"
            :data="areaData"
            show-checkbox
            node-key="id"
            ref="tree"
            accordion
            @check="curCheck"
            :default-expanded-keys="curNodes"
            highlight-current>
          </el-tree>
        </el-form-item>
        <br/>
        <el-form-item label="是否启动：">
          <el-radio-group v-model="whetherEnabled">
            <el-radio label="是" value="Y"></el-radio>
            <el-radio label="否" value="N"></el-radio>
          </el-radio-group>
        </el-form-item>
      </default-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="onSure">确 定</el-button>
      </div>
      </el-dialog>
</template>

<script>

  import DefaultForm  from "./defaultForm";
  import {trees} from '@/api/group'
  import {filter as _filter, map as _map} from "lodash";
  import { analysisByCheck } from '@/api/settingCenter'
    export default {
      name: "areaDialogs",
      components: {
        DefaultForm
      },
      props: ['visible','rowData','index','checkedIds'],
      data() {
        return {
          props: { multiple: true },
          defaultProps: {
            children: 'children',
            label: 'label'
          },
          formatAreaData: [],
          whetherEnabled: '是',
          areaData: [],
          dialogVisible: false,
          defaultDelivery: '',
          curNodes: [],
          tableIndex: '',
          loading: true,
          grandFather: '',
          defaultForm: {
            checked: false,
            startAmount: '',//起配金额
            freeAmount: '',//包邮金额,
            whetherFreeDelivery: 'N',//0否1是
            freightAmount: ''
          },
          checkedIdS: []
        }
      },
      created() {

      },
      methods: {
        async getTree() {
          const { data } = await trees()
          this.areaData = data
          this.areaData.forEach((item,index)=>{
            this.checkedIdS.map(v => {
              if (v.provinceId === item.id) {
                if (v.cityIdS === 'ALL' && v.districtIdS === 'ALL:ALL') {
                  this.$set(item, 'disabled', true)
                  item.children.forEach(itm => {
                    this.$set(itm, 'disabled', true)
                    itm.children.forEach(itms => {
                      this.$set(itms, 'disabled', true)
                    })
                  })
                } else {
                  const districtIdS = v.districtIdS.split(',')
                  districtIdS.forEach(j=>{
                    item.children.forEach(itm => {
                      const ind = item.children.findIndex(itm=>itm.id===j.substring(0, 6))
                      if(j.substring(7) ==='ALL') {
                        item.children[ind].disabled = true
                        itm.children.forEach(itms => {
                          this.$set(itms, 'disabled', true)
                        })
                      } else {
                        item.children[ind].children.forEach(itms=>{
                          const idx = item.children[ind].children.findIndex(itm=>itm.id===j.substring(7))
                          item.children[ind].children[idx].disabled = true
                        })
                      }
                    })
                  })
                }
              }
            })
          })
          this.loading = false
        },
        handleClose(){
          // this.curNodes = []
          this.$refs.tree.setCheckedKeys([])
          this.whetherEnabled = '是'
          this.$refs.AreForm.defaultForm.startAmount =''
          this.$refs.AreForm.defaultForm.freeAmount=''//包邮金额,
          this.$refs.AreForm.defaultForm.checked = false//0否1是
          this.$refs.AreForm.defaultForm.freightAmount = ''//运费
          // 子组件调用父组件方法，并传递参数
          this.$emit('changeShow','false')
        },
        onSure() {
          let flag = this.$refs['AreForm'].validateForm()
          if(flag) {
           const checkData = (this.$refs.tree).getCheckedNodes(false, true)
           if(checkData.length <= 0) {
             this.$message.error('请选择区域！')
             return
           }
           const params = {
             checkedKeys: this.$refs.tree.getCheckedKeys(),
             checkedKeysTrue: this.$refs.tree.getCheckedKeys(true),
             halfCheckedKeys: this.$refs.tree.getHalfCheckedKeys()
           }
            analysisByCheck(params).then(res=>{
              const checkData = res.data
              this.formatAreaData.push({
                checkedKeys: checkData.checkedKeys,
                cityIdS: checkData.cityIdS,
                cityNames: checkData.cityNames,
                districtIdS: checkData.districtIdS,
                districtNames: checkData.districtNames,
                provinceId: checkData.provinceId,
                provinceName: checkData.provinceName,
                startAmount: this.$refs.AreForm.defaultForm.startAmount,
                freeAmount: this.$refs.AreForm.defaultForm.freeAmount,//包邮金额,
                whetherFreeDelivery: {
                  code: this.$refs.AreForm.defaultForm.checked ? 'Y' : 'N',
                  desc: this.$refs.AreForm.defaultForm.checked ? '是' : '否'
                },//0否1是
                freightAmount: this.$refs.AreForm.defaultForm.freightAmount,//运费
                whetherEnabled: this.whetherEnabled === '是'
              })
              this.$emit('setData', this.formatAreaData, this.tableIndex)
              this.formatAreaData = []
              this.dialogVisible = false
            })
           /*const [_provinces, _cities, _districts] = [[], [], []]
           checkData.forEach((item) => {
             switch(item.level) {
              case 1:
                 _provinces.push(item);
                 break;
              case 2:
                _cities.push(item);
                break;
              default:
              _districts.push(item)
             }
           })
           this.formatAreaData = _map(_provinces, (item, idx) => {
             const currentTree = item.id.slice(0, 2)
             const citys = _filter(_cities, item1 => currentTree === item1.id.slice(0, 2))
             const districts = _districts.filter((dist) => {
               return currentTree === dist.id.slice(0, 2)
             })
             return {
               province: item,
               citys: citys,
               districts: districts.map(obj => Object.assign({}, obj)),
               startAmount: this.$refs.AreForm.defaultForm.startAmount,
               freeAmount: this.$refs.AreForm.defaultForm.freeAmount,//包邮金额,
               whetherFreeDelivery: this.$refs.AreForm.defaultForm.checked ? 'Y' : 'N',//0否1是
               freightAmount: this.$refs.AreForm.defaultForm.freightAmount,//运费
               whetherEnabled: this.whetherEnabled === '是'
             }
           })*/
         }
        },
        curCheck(data,state) {
          const curNode = this.$refs.tree.getNode(data)
           if (curNode.level === 1) {
             if(state.checkedKeys.length===0) {
               this.$refs.tree.setCheckedKeys([])
             } else {
               this.$refs.tree.setCheckedKeys([curNode.data.id])
               this.grandFather = curNode.parent.data.id
             }
           } else if (curNode.level === 2) {

             if(this.grandFather !== curNode.parent.data.id) {
               //this.$refs.tree.setCheckedKeys([curNode.data.id])
               this.grandFather = curNode.parent.data.id
             }
           } else {
             if(this.grandFather !== curNode.parent.parent.data.id) {
               //this.$refs.tree.setCheckedKeys([curNode.data.id])
               this.grandFather = curNode.parent.parent.data.id
             }
           }
        }
      },
      watch: {
        checkedIds: {
          immediate: true,
          handler(newVal,oldVal){
            this.checkedIdS = newVal
          }

        },
        index() {
          this.tableIndex = this.index
        },
        visible() {
          this.dialogVisible = this.visible
          if (!this.rowData) {
            //this.areaData = this.areaArr
            this.$nextTick(() => {
              this.$refs.tree && this.$refs.tree.setCheckedKeys([])
            })
            return
          }
          //this.areaData = this.areaArr.filter(({ id }) => id === this.rowData.provinceId) || []
          this.defaultDelivery = this.rowData
          this.whetherEnabled = this.rowData.whetherEnabledDesc
          /*const _curNodes = new Set()
            const _arr = this.rowData.district.districtIdS.split(',');
            for(let i = 0; i < _arr.length; i++) {
              const _item = _arr[i]
              if(!_item) {
                continue
              }
              const [city, distict] = _item.split(':')
              if (distict === 'ALL') {
                if (city === 'ALL') {
                  _curNodes.add(this.rowData.province.provinceId)
                  break;
                } else {
                  _curNodes.add(city)
                }
              } else {
                _curNodes.add(distict)
              }
            }
            this.curNodes = Array.from(_curNodes)*/
          this.$nextTick(() => {
              this.$refs.tree && this.$refs.tree.setCheckedKeys(this.rowData.checkData)
          })
        }
      }
    }
</script>

<style lang="scss">
  .el-dialog__body {
    .isFree {
      margin-left: 145px;
    }
  }
</style>
