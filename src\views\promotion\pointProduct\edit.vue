<template>
  <div class="detail-wrapper" v-loading="loading">
    <!-- 头部标题 start -->
    <page-title :title="title">
      <el-button @click="onSave" type="primary">保存</el-button>
    </page-title>
    <!-- 头部标题 end -->
    <el-form ref="form" :model="form" label-width="130px" :rules="rules">
      <page-module-card title="基本信息">
        <el-row>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="商品名称：" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="生产厂家：" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入生产厂家" />
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="品牌：" prop="brandName">
              <el-button @click="visibleSelectBrand = true">
                {{ form.brandId ? form.brandName : '从品牌库中添加' }}
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="包装单位：" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入包装单位" />
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="规格：" prop="spec">
              <el-input v-model="form.spec" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="商品分组" prop="groupingId">
              <el-select v-model="form.groupingId" clearable placeholder="请选择商品分组">
                <el-option v-for="item in groupList" :key="item.id" :label="item.groupingName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="ERP商品编码：" prop="erpCode">
              <el-input v-model="form.erpCode" placeholder="请输入ERP商品编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="商品图片：" prop="pictIdS">
          <upload-image :files.sync="form.pictIdS"></upload-image>
        </el-form-item>
      </page-module-card>
      <page-module-card title="价格库存">
        <el-row>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="积分值：" prop="points">
              <el-input-number v-model="form.points" :min="1" :step-strictly="true" label="请输入积分数" :step="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xl="6" :md="8" :sm="12">
            <el-form-item label="库存：" prop="stockQuantity">
              <el-input-number v-model="form.stockQuantity" :min="0" :step-strictly="true" label="请输入库存" :step="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </page-module-card>
      <page-module-card title="详情说明">
        <tinymce :height="200" v-model="form.productDescription" />
      </page-module-card>
      <selectBrand v-model="brand" @change="onSelectBrandChange" :visible.sync="visibleSelectBrand" />
    </el-form>
  </div>
</template>
<script>
import DelButton from '@/components/eyaolink/delElButton/index.vue'
import { postGifts, fetchGifts } from '@/api/gifts'
import { getProductGroupList } from '@/api/retailStore'
import UploadImage from '../gifts/components/UploadImage.vue';
import tinymce from '@/components/Tinymce'
import SelectBrand from '../gifts/components/SelectBrand.vue';

export default {
  name: 'editpointProduct',
  components: {
    DelButton,
    UploadImage,
    SelectBrand,
    tinymce
  },
  data() {
    return {
      loading: false,
      form: {
        productName: '',
        manufacturer: '',
        brandName: '',
        brandId: '',
        unit: '',
        spec: '',
        erpCode: '',
        pictIdS: '',
        points: '',
        stockQuantity: '',
        productDescription: ''
      },
      visibleSelectBrand: false,
      groupList: [],
      // 商品表单验证
      rules: {
        productName: [{ required: true, message: '请输入商品名称' }],
        manufacturer: [{ required: true, message: '请输入生产厂家' }],
        spec: [{ required: true, message: '请输入规格' }],
        unit: [{ required: true, message: '请输入包装单位' }],
        pictIdS: [{ required: true, message: '请上传商品图片' }],
        points: [{ required: true, message: '请输入积分值' }],
        stockQuantity: [{ required: true, message: '请输入库存数量' }]
      }
    }
  },
  created() {
    this.fetch()
    this.setProductGroupList()
  },
  computed: {
    productId() {
      return this.$route.query.id
    },
    title() {
      return !this.productId ? '新增商品' : '编辑商品';
    },
    brand() {
      return { id: this.form.brandId, brandName: this.form.brandName }
    }
  },
  methods: {
    onSelectBrandChange(val) {
      this.form.brandName = val.brandName
      this.form.brandId = val.id
    },
    // 设置分组列表
    setProductGroupList() {
      getProductGroupList().then(res => {
        this.groupList = res.records
      })
    },
    async onSave() {
      let valid = await this.$refs.form.validate()
      if (!valid) return
      try {
        this.loading = true;
        const { code } = await postGifts([
          {
            id: this.productId,
            giveawayType: 'UN_PRODUCT',
            unProductType: 'POINTS_PRODUCT',
            ...this.form
          }
        ])
        if (code !== 0) return
        this.$message.success('保存成功')
        setTimeout(() => {
          this.$store.dispatch("tagsView/delView", this.$route)
          this.$router.replace({
            path: '/promotion/pointProduct'
          })
        }, 500)
      } finally {
        this.loading = false;
      }
    },
    async fetch() {
      if (!this.productId) return
      try {
        this.loading = true;
        let { data } = await fetchGifts(this.productId)
        this.form = data
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>