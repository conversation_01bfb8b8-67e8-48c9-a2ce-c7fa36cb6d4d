<template>
  <el-dialog
    modal
    lock-scroll
    destroy-on-close
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="600px"
    :visible.sync="dialogVisible"
  >
  <template #title>
    重置密码 <span class="tips">【为保障您的账户安全，请设置账号密码】</span>
  </template>
    <div>
      <!-- <div class="tips tips_marLeft tips_marBottom">
        <span>为保障您的账户安全，请设置账号密码</span>
      </div> -->
      <el-form label-width="110px" :model="formData" ref="form" :rules="rules">
        <el-form-item label="登录账号：" prop="account">
          <el-input
            placeholder="请设置登录账号"
            v-model="formData.account"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="旧密码：" prop="oldPassWord">
          <el-input
            type="password"
            placeholder="请输入旧密码"
            v-model="formData.oldPassWord"
            maxlength="13"
          ></el-input>
        </el-form-item>
        <el-form-item label="设置密码：" prop="password">
          <el-input
            type="password"
            placeholder="请设置为6-13位密码"
            v-model="formData.password"
            maxlength="13"
            @input="(e) => onQDHandle(e)"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码：" prop="rePassword">
          <el-input
            type="password"
            placeholder="请重新输入密码"
            v-model="formData.rePassword"
            maxlength="13"
          ></el-input>
        </el-form-item>
        <div
          class="pass_qd"
          v-if="formData.password.length"
        >
          <span>{{ pass_qd }}</span>
          <div
            class="pass_color"
            v-for="index in 3"
            :key="index"
            :style="
              index <= pass_index
                ? `background:${pass_color}`
                : 'background:#CBCBCB'
            "
          ></div>
        </div>
        <p class="tips tips_marLeft tips_marTop">
          密码由英文+数字或+其它符号~!@#$%^&*_的组成，不允许纯数字或纯字母
        </p>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="onOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { forgotPasswordByAccount } from "@/api/user";
export default {
  name: "",
  components: {},
  props: {},
  data() {
    var validatePassword = (rule, value, callback) => {
      let reg =
      /^(?:(?=.*\d)(?=.*[a-zA-Z])|(?=.*\d)(?=.*[~!@#$%^&*_])|(?=.*[a-zA-Z])(?=.*[~!@#$%^&*_]))[\da-zA-Z~!@#$%^&*_]{6,13}$/;
      if (!reg.test(value)) {
        callback(new Error("密码格式错误"));
      } else {
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value !== this.formData.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      formData: {
        account: "",
        oldPassWord: "",
        password: "",
        rePassword: "",
      },
      rules: {
        oldPassWord: [
          { required: true, trigger: "blur", message: "请输入旧密码" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入密码" },
          { validator: validatePassword, trigger: "blur" },
        ],
        rePassword: [
          { required: true, trigger: "blur", message: "请再次输入密码" },
          { validator: validatePass, trigger: "blur" },
        ],
      },
      pass_qd: "",
      pass_color: "",
      pass_index: 0,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    onQDHandle(value) {
      if (value.length) {
        let code = [0, 0, 0];
        let th_pass = value;
        code[0] = /[0-9]/.test(th_pass) ? 1 : 0;
        code[1] = /[a-zA-Z]/.test(th_pass) ? 1 : 0;
        code[2] = /[^\w\s]/.test(th_pass) ? 1 : 0;
        let code_num = code.join("").match(/1/g)?.length ?? 1;
        this.pass_qd = code_num <= 1 ? "弱" : code_num === 2 ? "中" : "强";
        this.pass_color =
          code_num <= 1 ? "red" : code_num === 2 ? "blue" : "green";
        this.pass_index = code_num;
      } else {
        this.pass_color = "";
        this.pass_index = 4;
        this.pass_qd = "";
      }
    },
    initData(obj) {
      this.dialogVisible = true;
      this.formData.account = obj.account;
      // this.formData.oldPassWord = obj.oldPassWord;
    },
    onOk() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return;
        if (this.formData.oldPassWord === this.formData.password) {
          this.$message.error("新密码不能与旧密码相同");
          return;
        }
        forgotPasswordByAccount({
          account: this.formData.account,
          oldPassword: this.formData.oldPassWord,
          password: this.formData.password,
        }).then((res) => {
          if (res.data) {
            this.dialogVisible = false;
            this.$emit("onOk");
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.tips {
  color: #b4b6bd;
  font-size: 12px;
}
.tips_marBottom{
  margin-bottom: 20px;
}

.tips_marLeft{
  margin-left: 110px;
}
.tips_marTop{
  margin-top: 20px;
}
.pass_qd {
  display: flex;
  align-items: center;
  margin-left: 110px;
  margin-top: 28px;
  > span {
    font-size: 12px;
    margin-right: 12px;
  }
}

.pass_color {
  width: 40px;
  height: 6px;
  border-radius: 4px;
  margin-right: 4px;
}
</style>
