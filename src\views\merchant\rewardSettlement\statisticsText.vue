<template>
  <div class="statistics">奖励积分（个）：{{ statisticsData.points }}，奖励现金（元）：{{ statisticsData.amount }}</div>
</template>

<script>
export default {
  name: 'statisticsText',
  props: {
    statisticsData: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  padding: 14px 0px;
  text-align: right;
  margin-bottom: 3px;
  font-size: 16px;
  color: rgb(130, 133, 145);
  font-weight: 600;
}
</style>