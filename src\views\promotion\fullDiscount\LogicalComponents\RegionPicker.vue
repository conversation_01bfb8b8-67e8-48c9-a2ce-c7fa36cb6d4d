<template>
    <el-cascader  placeholder="请选择所在区域" :options="options" :props="{
        checkStrictly: true, expandTrigger: 'hover',
        value: 'id',
        label: 'label',
        children: 'children'
    }" clearable style="width: 240px;" @change="onChange" />
</template> 

<script>
import { trees } from '@/api/group'
const MODEL = 'UPDATE_MODEL';
export default {
    prop: {
        value: {
            type: Object,
            default: () => {
                return {
                    provinceId: '',
                    cityId: '',
                    countyId: ''
                }
            }
        }
    },
    model: {
        prop: 'value',
        event: MODEL
    },
    data() {
        return {
            options: []
        }
    },
    mounted() {
        this.fetch()
    },
    methods: {
        onChange([provinceId, cityId, countyId]) {
            this.$emit(MODEL, {
                ...this.value,
                provinceId,
                cityId,
                countyId
            })
        },
        async fetch() {
            const { data } = await trees()
            this.options = data
        }

    },

}
</script>
