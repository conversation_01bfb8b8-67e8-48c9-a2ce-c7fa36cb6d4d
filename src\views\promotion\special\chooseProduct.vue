<template>
  <div class="dia_content">
    <el-dialog title="提示" :visible.sync="visible" width="85%" v-dialogDrag lock-scroll :close-on-click-modal="false" top="4.5vh" :before-close="closeBox">
      <div slot="title" class="title"></div>
      <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="model.recommendState" @change="chageTabsFun"></tabs-layout>
      <div class="content_box">
        <im-search-pad-dialog :has-expand="false" :model="model" @reset="reload" @search="onSubmit">
          <im-search-pad-item-dialog prop="erpCode">
            <el-input v-model.trim="model.erpCode" placeholder="请输入ERP编码2" />
          </im-search-pad-item-dialog>
          <im-search-pad-item-dialog prop="productName">
            <el-input v-model.trim="model.productName" placeholder="请输入商品名称" />
          </im-search-pad-item-dialog>
          <im-search-pad-item-dialog prop="manufacturer">
            <el-input v-model.trim="model.manufacturer" placeholder="请输入生产厂家" />
          </im-search-pad-item-dialog>
          <im-search-pad-item-dialog prop="warehouseIds" v-if="storeList.length">
            <el-select  v-model="model.warehouseIds" class="width160" collapse-tags multiple placeholder="所在仓库" clearable>
              <el-option v-for="item in storeList" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </im-search-pad-item-dialog>
        </im-search-pad-dialog>
        <table-pager :rowKey="rowKey" :reserveSelection="true" ref="productTable" :height="tableHeight" :options="allColumn"
                     :remote-method="productList" :data.sync="tableData" :selection="true" @selection-change="onSelect"
                     @selection-all="onAllSelect" :pageSize="pageSize" :isNeedButton="false" :paginationIsCenter="true" :selectedNum="multipleSelection.length || 0">
          <el-table-column slot="pictIdS" label="主图" align="center" width="80">
            <template slot-scope="scope">
              <el-image style="width:40px;height:40px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
            </template>
          </el-table-column>
          <el-table-column slot="erpCode" width="180" label="商品编码/仓库">
            <template v-slot="{row}">
              <div class="omit_one" style="width:160px">{{ row.erpCode || '无' }}</div>
              <div v-if="row.warehouseName == null || row.warehouseName && row.warehouseName.length < 10">{{ row.warehouseName || "无" }}</div>
              <el-tooltip v-else class="item" effect="dark" :content="row.warehouseName" placement="top">
                <div class="omit_one" style="width:160px">{{ row.warehouseName || '无'}}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column slot="productName" width="220" label="商品名称">
            <template v-slot="{row}">
              <div v-if="row.productName && row.productName.length < 24">{{ row.productName }}</div>
              <el-tooltip v-else class="item" effect="dark" :content="row.productName" placement="top">
                <div class="omit_two" style="width:200px">{{ row.productName || '无'}}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column slot="manufacturer" width="220" label="生产厂家">
            <template v-slot="{row}">
              <div v-if="row.manufacturer && row.manufacturer.length < 24">{{ row.manufacturer }}</div>
              <el-tooltip v-else class="item" effect="dark" :content="row.manufacturer" placement="top">
                <div class="omit_two" style="width:200px">{{ row.manufacturer || '无'}}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column slot="salePrice" width="120" label="销售价">
            <template v-slot="{row}">
              <span style="color:#E6A23C">{{ row.salePrice }}</span>
            </template>
          </el-table-column>
          <el-table-column slot="costPrice" width="120" label="成本价">
            <template v-slot="{row}">
              <span style="color:green">{{ row.costPrice }}</span>
            </template>
          </el-table-column>
          <div slot="handleButton">
            <el-button class="btn_class" @click="closeBox">取消</el-button>
            <el-button class="btn_class" type="primary" @click="submitSelectFun">
              {{ model.recommendState == "Y" ? "移除" : "添加"}}{{multipleSelection.length == 0 ? "": `(${multipleSelection.length})`}}
            </el-button>
          </div>
        </table-pager>
      </div>

    </el-dialog>
  </div>
</template>


<script>
import {
  getProductsBySpecialId,
  edtiFeaturedPage,
  featuredPageItemDel,
} from "@/api/featured";
import ImSearchPadDialog from "@/components/eyaolink/ImSearchPadDialog/index"
import ImSearchPadItemDialog from "@/components/eyaolink/ImSearchPadItemDialog/index"
import {
  getAllStore
} from "@/api/products/store";
import checkPermission from "../../../utils/permission";

const TableColumns = [
  {  prop: 'pictIdS',  name: 'pictIdS',  label: '主图',  slot: true},
  {  prop: 'erpCode', name: 'erpCode', label: 'ERP商品编码/仓库', width: 120, slot: true},
  {  prop: 'productName',  name: 'productName',  label: '商品名称',  width: 200, slot: true},
  {  prop: 'manufacturer',  name: 'manufacturer',  label: '生产厂家',  width: 200, slot: true},
  {  prop: 'spec',  name: 'spec',  label: '规格',  width: 100},
  {  prop: 'unit',  name: 'unit',  label: '单位',  width: 100},
  {  prop: 'salePrice',  name: 'salePrice',  label: '销售价',  width: 100, slot: true},
  {  prop: 'costPrice',  name: 'costPrice',  label: '成本价',  width: 100, slot: true},
  {  prop: 'stockQuantity',  name: 'stockQuantity',  label: '可卖库存',  width: 100},
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  });
}
export {
  TableColumnList
};
export default {
  //import引入的组件
  components: {
    ImSearchPadDialog,
    ImSearchPadItemDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    pageId: {
      type: String,
      default: "",
      required: true,
    },
  },
  data() {
    return {
      rowKey: "id",
      multipleSelection: [],
      tabs: [{
        name: "已推荐商品",
        value: "Y",
        count: 0,
        // permission: 'groupOrder-all:view',
        countName: "recommended",
      },
        {
          name: "未推荐商品",
          value: "N",
          count: 0,
          // permission: 'groupOrder-waitpay:view',
          countName: "notRecommended",
        },
      ],
      tableHeight: 0,
      pageSize: 10,
      delText: '您确定移除选中商品？',
      reserveSelection: true,
      allColumn: TableColumnList,
      tableVal: [],
      tableData: [],
      model: {
        specialId: "",
        erpCode: "",
        productName: "",
        manufacturer: "",
        warehouseIds: [],
        recommendState: "Y",
      },
      storeList: []
    };
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  async mounted() {
    try {
      const res = await getAllStore()
      const data = res.data;
      if(data && data.length) {
        data.forEach(item => {
          const obj = {};
          obj.label = item.name;
          obj.value = item.id;
          this.storeList.push(obj)
        })
      }
    } catch (error) {
      console.log(error)
    }
  },

  computed: {
    _visible: {
      get() {
        this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },

    created() {
      this.tableHeight = Number((window.screen.height * 0.52).toFixed(0)) || 500;
    },
  //方法集合
  methods: {
    checkPermission,
    async productList(params) {
      const model = this.model;
      const listQuery = {
        model: {
          ...model,
          whetherOnSale: model.recommendState === 'Y' ? '' : 'Y'
        }
      }
      Object.assign(listQuery, params)
      listQuery.model.specialId = this.pageId;
      return await getProductsBySpecialId(listQuery)
    },
    onAllSelect(selection) {
      this.onSelect(selection);
    },
    onSelect(val) {
      this.multipleSelection = val;
    },
    chageTabsFun() {
      this.tableData = [];
      this.$refs.productTable.clearSelection();
      this.multipleSelection = [];
      this.$refs.productTable.doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    onSubmit() {
      this.$refs.productTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    reload() {
      this.model = {
        ...this.model,
        ...{
          "erpCode": "",
          "productName": "",
          "manufacturer": "",
          "warehouseIds": [],
          "recommendState": this.model.recommendState
        }
      }
      this.$refs.productTable.doRefresh()
    },
    submitSelectFun() {
      console.log("submitSelectFun", this.multipleSelection);
      let list = [];
      if (this.multipleSelection.length > 0) {
        this.multipleSelection.forEach(element => {
          list.push(element.id);
        });
      } else {
        this.$message.warning('请选择商品');
        return
      }

      if (this.model.recommendState == "Y") {
        // 移除
        featuredPageItemDel(list.toString()).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('移除成功');
          }
        })
      } else {
        // 添加
        let queryList = [];
        this.multipleSelection.forEach(element => {
          queryList.push(Object.assign({}, {
            pageId: this.pageId,
            productId: element.id
          }))
        });
        edtiFeaturedPage(queryList).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('添加成功');
          }
        })
      };
      this.reload();
      this.$refs.productTable.clearSelection();
      this.multipleSelection = [];
    },
    closeBox: function () {
      console.log("closeBox")
      this._visible = false
    },
  },
};

</script>

<style lang="scss" scoped>
.dia_content {
  position: relative;
    ::v-deep .el-dialog__headerbtn {
        z-index: 99999;
    }
    
    ::v-deep .el-dialog__header {
        padding: 0 !important;
        border-bottom: none !important;
    }

    ::v-deep .el-dialog__body {
        padding: 10px 0 10px !important;
				.im-search-pad {
					margin-left:  20px !important;
				}
				.bottom_btn {
					margin-right: 20px !important;
				}
    }
		::v-deep .varietiesBan-list-container .varietiesBan-list-tabs-wrapper .varietiesBan-list-tabs {
			padding-left: 20px
		}
    .content_box {
      padding: 0 14px;
    }
    .bottom_box {
      position: absolute;
      left: 15px;
      bottom: 18px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 98%;
    }
}
.btn_class {
  border-radius: 2px;
}
</style>

<style lang="less" scoped>
  // /deep/ .el-dialog__header {
  //   border: none;
  //   padding: 0;
  // }
  // .title_box {
  //     padding-right: 10px;
  //   //   .operations {
  //   //       bottom: 0px;
  //   //   }
  // }
  // .title_box .varietiesBan-list-container .varietiesBan-list-tabs-wrapper .operations {
  //     bottom: 0px;
  // }
</style>
