import qs from 'qs'
import request from '@/utils/request'
// 获取优列表
export function getList(data) {
  return request({
    url: '/product/merchant/limitTimeDiscount/page',
    method: 'post',
    data
  })
}
//删除
export function handleDel(ids) {
  return request({
    url: '/product/merchant/limitTimeDiscount',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//查看详情
export function detail(id) {
  return request({
    url: `/product/merchant/limitTimeDiscount/${id}`,
    method: 'get'
  })
}
//修改活动状态：作废
export function updatePromotionPackageState(data) {
  return request({
    url: `/product/merchant/limitTimeDiscount/updateSetCouponStatusEnum`,
    method: 'post',
    transformRequest: [function() {
      return qs.stringify(data)
    }],
    data,
    headers:{'Content-type': 'application/x-www-form-urlencoded '}
  })
}
//根据商品id搜素是否与其他活动冲突

export function searchRepeatProduct(data) {
  return request({
    url: `/product/merchant/limitTimeDiscount/searchRepeatProduct`,
    method: 'post',
    transformRequest: [function() {
      return qs.stringify(data,{ arrayFormat: 'brackets' })
    }],
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;'
    }
  })
}
export function getCheckList(data) {
  return request({
    url: '/product/merchant/discountProductRel/page',
    method: 'post',
    data
  })
}
// 根据ids获取商品列表
export function postGoodsList (data) {
  return request({
    url: '/product/admin/product/anno/productListByIds',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;'
    }
  })
}
//删除活动中已保存的商品
export function discountProductRel(ids) {
  return request({
    url: '/product/merchant/discountProductRel',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}

