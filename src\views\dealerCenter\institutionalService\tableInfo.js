import { deepClone } from '@/utils'

const initTableInfo = [
  {
    key: 4,
    label: '机构名称',
    name: 'name',
  },
  {
    key: 5,
    label: '所在地区',
    name: 'area',
  },
  {
    key: 13,
    label: '联系人',
    name: 'contacts',
    width: '100px'
  },
  {
    key: 14,
    label: '联系人手机',
    name: 'phone',
    width: '110px'
  },
  {
    key: 9,
    label: '服务状态',
    name: 'status',
    width: '90px'
  },
  {
    key: 10,
    label: '服务期限',
    name: 'beginTime', // endTime
  },
  {
    key: 11,
    label: '服务内容',
    name: 'note',
    width: '300px'
  },
  {
    key: 12,
    label: '开通账号数',
    name: 'accountTotal',
    width: '90px'
  }
]

export default {
  ALL: deepClone(initTableInfo)
}
