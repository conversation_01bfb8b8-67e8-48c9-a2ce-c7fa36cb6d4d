<template>
  <div class="fission_setting" v-loading="loading">
    <div class="item">
      <page-module-title title="裂变设置">
        <el-button v-if="!isEdit" type="primary" @click="isEdit = true">编辑</el-button>
        <div v-if="isEdit">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </div>
      </page-module-title>
      <el-form :model="fissionSetting" :disabled="!isEdit" v-if="fissionSetting">
        <div>
          <div class="tips">调整裂变设置后不影响历史数据。</div>
          <div class="form_item" style="align-items: flex-start;">
            <span class="label">推广员门槛：</span>
            <span>
              <el-radio-group v-model="fissionSetting.associationType.code">
                <el-radio label="ALL" style="margin: 5px 0 15px;">所有客户</el-radio><br />
                <el-radio label="MERCHANT_TYPE">指定客户</el-radio>
              </el-radio-group>
              <el-checkbox-group
                v-model="fissionSetting.associationIds"
                :disabled="!isEdit || isEdit && fissionSetting.associationType.code === 'ALL'"
                style="margin: 10px 0 0 25px;"
              >
                <el-checkbox v-for="item in merchantTypeList" :label="item.id">{{ item.name }}</el-checkbox>
              </el-checkbox-group>
            </span>
          </div>
          <div class="form_item">
            <span class="label">合伙人门槛：</span>
            <span>
              本人成交金额 >=<el-input-number :min="1" :step="1" step-strictly v-model="fissionSetting.transactionMoney" style="width: 200px;margin: 0 10px;" />元，
              团队成员 >=<el-input-number :min="1" :step="1" step-strictly v-model="fissionSetting.teamMember" style="width: 200px;margin: 0 10px;" />人
            </span>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { merchantType } from "@/api/archivesList";
import { getFissionSetting, saveFissionSetting } from "@/api/retailStore";
import { deepClone } from "@/utils";

export default {
  name: 'fissionSetting',
  components: {
  },
  data() {
    return {
      fissionSetting: null,
      fissionSettingTemp: null,
      merchantTypeList: [],
      isEdit: false,
      loading: false
    }
  },
  created() {
    this.setMerchantType()
    this.setFissionSetting()
  },
  methods: {
    setMerchantType() {
      merchantType().then(res => {
        this.merchantTypeList = res.data || []
      })
    },
    setFissionSetting() {
      this.loading = true
      getFissionSetting().then(res => {
        this.fissionSetting = res
        this.fissionSettingTemp = deepClone(res)
      }).finally(() => {
        this.loading = false
      })
    },
    cancel() {
      this.fissionSetting = deepClone(this.fissionSettingTemp)
      this.isEdit = false
    },
    save() {
      saveFissionSetting(this.fissionSetting).then(() => {
        this.$message.success('保存成功')
        this.isEdit = false
        this.setFissionSetting()
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.fission_setting {
  background-color: #fff;
  .tips {
    background-color: #eee;
    padding: 10px;
  }
  .form_item {
    display: flex;
    align-items: center;
    margin: 20px 0;
    .label {
      margin-right: 10px;
    }
  }
}
</style>
