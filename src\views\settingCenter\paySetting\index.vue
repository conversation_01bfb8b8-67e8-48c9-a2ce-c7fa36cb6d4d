<template>
  <div class="tab_bg">
    <tabs-layout :tabs="[{ name: '配送服务设置', value: '' }]">
      <template slot="button">
        <el-button v-throttle @click="getTree">刷新</el-button>
        <el-button v-throttle v-if="checkPermission(['admin','paySetting:edit'])" type="primary" @click="save">保存</el-button>
      </template>
    </tabs-layout>
    <div class="detail-items">
      <page-module-card title="默认起配" tip="未指定地区范围内时启用默认起配金额">
        <default-form ref="defaultArea" :default-delivery="defaultDelivery" :default-form="defaultForm" />
      </page-module-card>
      <page-module-card title="地区设置">
        <el-table ref="table" v-loading="loading" border :data="tableData">
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column label="省份" prop="provinceName" />
          <el-table-column label="城市" prop="cityNames" />
          <el-table-column label="区" prop="districtNames" />
          <el-table-column label="起配金额（元）" prop="startAmount" />
          <el-table-column label="运费（元）" prop="freightAmount" />
          <el-table-column label="满额包邮（元）" prop="freeAmount" />
          <el-table-column label="是否启用" prop="whetherEnabledDesc" width="100" />
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button v-if="checkPermission(['admin','sale-saas-setting-manage-delivery:edit', 'sale-platform-setting-manage-delivery:edit'])" type="text" @click="handleEdit(scope.row,scope.$index)">编辑</el-button>

                </span>
                <span class="table-edit-row-item">
                  <el-button v-if="checkPermission(['admin','sale-saas-setting-manage-delivery:del', 'sale-platform-setting-manage-delivery:del'])" type="text" @click="handleDelete(scope.row,scope.$index)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <el-button v-if="checkPermission(['admin','sale-saas-setting-manage-delivery:add', 'sale-platform-setting-manage-delivery:add'])" type="primary" style="margin-top: 20px;margin-bottom: 60px;" @click="addHandle">+新增配送地区</el-button>
      </page-module-card>
    </div>
    <area-dialog :visible="isShow"  @setData="setData" @changeShow="changeAddUser" :rowData="rowData" :index="index" v-bind:checkedIds="checkedIds"></area-dialog>
  </div>
</template>
<script>
  import DetailItem from '@/views/merchant/list/detail-item'
  import DefaultForm from './defaultForm'
  import AreaDialog from "./areaDialogs"
  import {getDeatils,delArea,batchSave,analysisByDb} from '@/api/settingCenter'
  import {trees} from '@/api/group'
  import { map as _map, reduce as _reduce,filter as _filter } from 'lodash'
  import checkPermission from '../../../utils/permission';
  export default {
    components: {
      AreaDialog,
      DetailItem,
      DefaultForm
    },
    data() {
      return {
        tableData:[],
        isShow: false,
        defaultDelivery: '',
        areaDeliveryList: [],
        areaData: [],
        itemData: {},
        rowData: '',
        index: '',
        loading: true,
        defaultForm: {
          checked: false,
          startAmount: '',//起配金额
          freeAmount: '',//包邮金额,
          whetherFreeDelivery: 'N',//0否1是
          freightAmount: ''
        },
        checkedIds: []
      };
    },
    props: {},
    methods: {
      checkPermission,
      changeAddUser(data) {
        if (data === 'false') {
          this.isShow = false
        } else {
          this.isShow = true
        }
      },
      traversalTree(tree, level) {
        return tree.map(item => Object.assign({}, item, {
          level,
          children: item.children ? this.traversalTree(item.children, level + 1): null
        }))
      },
      //获取当前页面已保存的设置
      async getData() {
        this.tableData = []
        this.loading = true
        const {data} = await getDeatils()
        this.defaultDelivery = data.defaultDelivery
        this.areaDeliveryList = data.areaDeliveryList
        if (this.areaDeliveryList && this.areaDeliveryList.length > 0) {
          _map(this.areaDeliveryList, (item, index) => {
            analysisByDb({
              provinceId: item.provinceId,
              cityIdS: item.cityIdS,
              districtIdS: item.districtIdS
            }).then(res => {
              const curData = res.data
              this.tableData.push({
                checkData: curData.checkedKeys,
                provinceName: curData.provinceName,
                provinceId: item.provinceId,
                cityNames: curData.cityNames,
                cityIdS: item.cityIdS,
                districtNames: curData.districtNames,
                districtIdS: item.districtIdS,
                startAmount: item.startAmount,
                freightAmount: item.freightAmount,
                whetherEnabledDesc: item.whetherEnabled.desc,
                freeAmount: item.freeAmount,
                id: item.id,
                whetherFreeDelivery: item.whetherFreeDelivery
              })
              this.loading = false
            })
          })
        } {
          this.loading = false
        }
      },

          /*this.tableData = _map(this.areaDeliveryList,(item,idx)=>{
            const province = {
              text: '',
              value: [],
              provinceId: '',
            }
            const city = {
              text: '',
              value: [],
              cityIdS: ''
            }
            const district = {
              text: '',
              value: [],
              districtIdS: ''
            }
            //省
            province.provinceId = item.provinceId
            city.cityIdS = item.cityIdS
            district.districtIdS = item.districtIdS
            let i = _map(this.areaData,(info,j) => info.id).indexOf(item.provinceId);
            province.value = this.areaData[i].province
            province.text = this.areaData[i].label
            if(item.cityIdS === 'ALL') {
              city.text = '全部'
              city.cityIdS = 'ALL'
              district.text = '全部'
            } else {
              let o, w,t
              _map(item.cityIdS.split(','),itm => {
                o = _map(this.areaData[i].children, (info, j) => info.id).indexOf(itm)
                city.text +=('，' + this.areaData[i].children[o].label)
              })
              city.value = this.areaData[i].children[o]
              // hack
              item.districtIdS = item.districtIdS.replace(/，/g, ',')
              _map(item.districtIdS.split(','),(its,index) => {
                if(its) {
                  let itsLast = its.substr(5, its.length)
                  let itsFist = its.substr(0, 4)
                  if(itsLast === 'ALL') {
                    district.text += '，全部'
                  } else {
                    t = _map(this.areaData[i].children, (info, j) => info.id).indexOf(itsFist)
                    w = _map(this.areaData[i].children[t].children, (info, j) => info.id).indexOf(itsLast)
                    district.text += ('，' + this.areaData[i].children[t].children[w].label)
                  }
                }
              })
            }
            district.text.substr(1)
            return {
              province: province,
              city: city,
              district: district,
              startAmount: item.startAmount,
              freightAmount: item.freightAmount,
              whetherEnabledDesc: item.whetherEnabled.desc,
              freeAmount: item.freeAmount,
              id: item.id,
              whetherFreeDelivery: item.whetherFreeDelivery
            }
          })*/
      async getTree() {
        const { data } = await trees()
        this.areaData = this.traversalTree(data, 1)
        this.getData()
      },
      addHandle() {
        this.rowData = null
        this.index = ''
        this.$nextTick(() => {
          this.isShow = true
          let tableData = this.$refs.table.tableData
          this.checkedIds = _map(tableData, (item, idx) => {
            const provinceId = item.provinceId
            const cityIdS = item.cityIdS
            const districtIdS = item.districtIdS
            return {
              provinceId:provinceId,
              cityIdS:cityIdS,
              districtIdS: districtIdS
            }
          })
        })
      },
      handleEdit(row,index) {
        this.rowData = row
        this.index = index
        this.$nextTick(() => {
          this.isShow = true
          let tableData = this.$refs.table.tableData
          this.checkedIds = _map(tableData, (item, idx) => {
            if (row.provinceId !== item.provinceId) {
              const provinceId = item.provinceId
              const cityIdS = item.cityIdS
              const districtIdS = item.districtIdS
              return {
                provinceId: provinceId,
                cityIdS: cityIdS,
                districtIdS: districtIdS
              }
            } else {
              return {}
            }
          })
        })
      },
      //修改按钮
      save() {
        let flag = this.$refs['defaultArea'].validateForm();
        if(flag){
          let tableData = this.$refs.table.tableData
          this.areaDeliveryList = _map(tableData, item => {
            const provinceId = item.provinceId
            const cityIdS = item.cityIdS
            const districtIdS = item.districtIdS
            return {
              provinceId: provinceId,
              cityIdS: cityIdS,
              districtIdS: districtIdS,
              freeAmount: item.freeAmount,
              freightAmount: item.freightAmount,
              startAmount: item.startAmount,
              whetherEnabled: item.whetherEnabledDesc === '是'?'Y':'N',
              id: item.id ? item.id : 0,
              whetherFreeDelivery: item.whetherFreeDelivery.code
            }
          })
          let params = {
            defaultDelivery: {
              startAmount: this.$refs.defaultArea.defaultForm.startAmount,
              freeAmount: this.$refs.defaultArea.defaultForm.freeAmount,//包邮金额,
              whetherFreeDelivery: this.$refs.defaultArea.defaultForm.checked ? 'Y' : 'N',//0否1是
              freightAmount: this.$refs.defaultArea.defaultForm.freightAmount,//运费,
              id: this.$refs.defaultArea.id
            },
            areaDeliveryList: this.areaDeliveryList
          }
          if (!this.$refs.defaultArea.id) {
            delete params.defaultDelivery.id
          }
          batchSave(params).then(res=>{
            this.$message.success('修改成功！')
          }).catch(errorLog=>{
          })
        }
      },
      handleDelete(row,index) {
        this.$confirm('是否确定删除？').then(_ => {
          this.tableData.splice(index, 1)
          }).catch(_ => {});
      },
      //添加地区赋值给table
      setData(data,tableInx) {
        let curData = data[0]
        if(tableInx === '') {
          this.tableData.push({
            provinceName: curData.provinceName,
            provinceId: curData.provinceId,
            cityNames: curData.cityNames,
            cityIdS: curData.cityIdS,
            districtNames: curData.districtNames,
            districtIdS: curData.districtIdS,
            startAmount: curData.startAmount,
            freightAmount: curData.freightAmount,
            whetherEnabledDesc: curData.whetherEnabled === true ? '是' : '否',
            freeAmount: curData.freeAmount,
            whetherFreeDelivery: curData.whetherFreeDelivery,
            checkData: curData.checkedKeys
          })
        } else {
          this.tableData.splice(tableInx,1, {
            provinceName: curData.provinceName,
            provinceId: curData.provinceId,
            cityNames: curData.cityNames,
            cityIdS: curData.cityIdS,
            districtNames: curData.districtNames,
            districtIdS: curData.districtIdS,
            startAmount: curData.startAmount,
            freightAmount: curData.freightAmount,
            whetherEnabledDesc: curData.whetherEnabled === true ? '是' : '否',
            freeAmount: curData.freeAmount,
            whetherFreeDelivery: curData.whetherFreeDelivery,
            checkData: curData.checkedKeys
          })

        }
      }
    },
    mounted() {
      this.getData()
    },
  };
</script>
<style lang="less" scoped></style>
