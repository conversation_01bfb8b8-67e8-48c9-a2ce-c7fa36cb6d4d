import request from '@/utils/request';
import qs from 'qs';

// 团购活动-分页列表查询
export function groupBuyPage(data) {
  return request({
    url: `/product/admin/groupBuy/page`,
    method: 'post',
    data
  })
}

// 团购活动查询详情
export function getGroupBuyInfo(id) {
    return request({
        url:`/product/admin/groupBuy/${id}`,
        method:'get'
    })
}

// 团购活动--作废
export function setVoid(data) {
    return request({
        url:`/product/admin/groupBuy/cancel`,
        method:'put',
        // data
        transformRequest: [function() {
            return qs.stringify(data)
        }],
        data,
        headers:{'Content-type': 'application/x-www-form-urlencoded '}
    })
}

// 新增团购活动
export function addGroupBuy(data){
    return request({
        url:'/product/admin/groupBuy',
        method:'post',
        data
    })
}

// 修改团购活动
export function editGroupBuy(data){
    return request({
        url:'/product/admin/groupBuy',
        method:'put',
        data
    })
}

// 根据商品id搜素是否与其他活动冲突
export function searchRepeatProductGroup(data){
    return request({
        url:`/product/admin/groupBuy/searchRepeatProduct`,
        method:'post',
        transformRequest: [function() {
            return qs.stringify(data)
        }],
        data,
        headers:{'Content-type': 'application/x-www-form-urlencoded '}
    })
}

// 删除团购活动
export function delGroupBuy(id) {
    return request({
        url:'/product/admin/groupBuy?ids[]=' + id,
        method:'delete',
        headers:{'Content-type': 'application/x-www-form-urlencoded '}
    })
}


 
 