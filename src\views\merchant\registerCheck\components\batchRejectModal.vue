<template>
  <el-dialog title="提示" :visible.sync="visible" top="15%" destroy-on-close width="500px">
    <div class="tip">此操作将批量驳回采购商申请，是否继续？</div>
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-form-item label="驳回理由" prop="rejectReason">
        <el-input type="textarea" v-model="form.rejectReason" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updatePurMerchantApplyRejected } from '@/api/registercheck';

export default {
  name: 'batchRejectModal',
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        rejectReason: '',
        'ids[]': ''
      },
      rules: {
        rejectReason: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' },
        ]
      }
    }
  },
  methods: {
    show(ids) {
      this.form['ids[]'] = ids
      this.form.rejectReason = ''
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        updatePurMerchantApplyRejected(this.form).then(res => {
          if (!res.data) return
          this.hide()
          this.$message.success('批量驳回成功！')
          this.$emit('submitSuccess')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tip {
  margin-bottom: 20px;
}
</style>