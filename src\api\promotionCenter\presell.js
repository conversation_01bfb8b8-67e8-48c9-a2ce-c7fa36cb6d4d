import request from '@/utils/request';
import qs from 'qs';

// 预售活动-分页列表查询
export function getPreSellPage(data) {
  return request({
    url: `/product/merchant/productPresell/page`,
    method: 'post',
    data
  })
}

// 预售活动查询详情
export function getPreSellInfo(id) {
    return request({
        url:`/product/merchant/productPresell/${id}`,
        method:'get'
    })
}

// 新增预售活动
export function addPreSell(data){
    return request({
        url:'/product/merchant/productPresell',
        method:'post',
        data
    })
}

// 修改预售活动
export function editPreSell(data){
    return request({
        url:'/product/merchant/productPresell',
        method:'put',
        data
    })
}

// 根据商品id搜素是否与其他活动冲突
export function searchRepeatProductPreSell(data){
    return request({
        url:`/product/merchant/productPresell/searchRepeatProduct`,
        method:'post',
        transformRequest: [function() {
            return qs.stringify(data)
        }],
        data,
        headers:{'Content-type': 'application/x-www-form-urlencoded '}
    })
}

// 删除预售活动
export function delPreSell(id) {
    return request({
        url:'/product/merchant/productPresell?ids[]=' + id,
        method:'delete',
        headers:{'Content-type': 'application/x-www-form-urlencoded '}
    })
}

// 更新到货状态
export function batchUpdateArriveGoodsStatus(params){
  return request({
    url:'/order/merchant/orderInfo/batchUpdateArriveGoodsStatus',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;'
    }
  })
}

// 更新活动状态
export function updateSetPreSellStatus(data){
  return request({
    url:'/product/merchant/productPresell/updateSetPresellStatusEnum',
    method:'post',
    data
  })
}
