<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" class="decor-footer-dialog" @confirm="confirm">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="自定义链接" name="first">
        <el-table
          ref="tableData"
          :data="tableData"
          border
        >
          <el-table-column label="序号" type="index" width="54" align="center" />
          <el-table-column label="模块名称" prop="moduleName">
            <template slot-scope="scope">
              <el-input v-model="scope.row.moduleName" placeholder="请输入模块名称" class="el-input-none-border" />
            </template>
          </el-table-column>
          <el-table-column label="模块显示" prop="publishStatus">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.publishStatus" true-label="Y" false-label="N" />
              <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="scope.row.id" class="table-edit-row-item">
                  <el-button type="text" @click="addUrl(scope.row, scope.$index)">添加链接</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
                </span>
                <span class="table-edit-row-item">
                  <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="tableData.length < 5" class="add-button" @click="add"><span>添加模块</span></div>
        <im-dialog :title="titleUrl" :visible.sync="visible" width="1000px" append-to-body class="decor-footer-dialog-link" @confirm="confirmLinkBUtton">
          <el-table
            ref="tableData"
            :data="tableDataUrl"
            border
          >
            <el-table-column label="序号" type="index" width="54" align="center" />
            <el-table-column label="链接名称" prop="linkName" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.linkName" placeholder="请输入链接名称" class="el-input-none-border" />
              </template>
            </el-table-column>
            <el-table-column label="链接类型" prop="linkType" width="180">
              <template slot-scope="scope">
                <el-select v-model="scope.row.linkType" placeholder="请选择链接类型" @change="changeLinkFun($event, scope.$index, scope.row)">
                  <el-option
                    v-for="itemInfo in selectList"
                    :key="itemInfo.code"
                    :label="itemInfo.name"
                    :value="itemInfo.code"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="链接目标" prop="linkUrl" min-width="300">
              <template slot-scope="scope">
                <el-input v-model="scope.row.linkUrl" placeholder="链接目标">
                  <ProductItemTable v-if="scope.row.linkType==='PRODUCT_DETAIL'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
                  <StoreListTable v-if="scope.row.linkType==='STORE_INDEX'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
                  <ProductTypeItemTable v-if="scope.row.linkType==='CATEGORYTYPE_LIST'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
                  <HelpCenter v-if="scope.row.linkType==='HELP_CENTER'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
                </el-input>
                <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template slot-scope="scope">
                <el-row class="table-edit-row">
                  <span class="table-edit-row-item">
                    <el-button type="text" @click="handleUpUrl(scope.$index)">上移</el-button>
                  </span>
                  <span class="table-edit-row-item">
                    <el-button type="text" @click="handleDownUrl(scope.$index)">下移</el-button>
                  </span>
                  <span class="table-edit-row-item">
                    <el-button type="text" @click="handleDelUrl(scope.$index)">删除</el-button>
                  </span>
                </el-row>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="tableDataUrl.length < 4" class="add-button" @click="addUrlButton"><span>添加链接</span></div>
        </im-dialog>
      </el-tab-pane>
      <el-tab-pane label="客服中心" name="second">
        <div class="service-content">
          <div class="service-list">
            <div class="label">客服QQ：</div>
            <div class="value"><el-input v-model="serviceQQ"  /></div>
          </div>
          <div class="service-list">
            <div class="label">客服电话：</div>
            <div class="value"><el-input v-model="servicePhone"  /></div>
          </div>
          <div class="service-list">
            <div class="label">工作时间：</div>
            <div class="value">

              <div v-for="(item, index) in workTime" class="time" :key="index">
                <el-select v-model="item.dataStart" placeholder="请选择"  style="width: 90px;">
                  <el-option
                    v-for="item in TIME_LIST"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <span class="to-time">至</span>
                <el-select v-model="item.dataEnd" placeholder="请选择"  style="width: 90px;">
                  <el-option
                    v-for="item in TIME_LIST"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-time-picker
                  v-model="item.time"
                  is-range
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 200px;margin: 0 10px;"

                />
                <el-checkbox v-model="item.timeChecked">显示</el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="小程序码" name="third">
        <div class="decor-footer-import-image">
          <el-upload
            :action="'/api/file/file/upload'"
            list-type="picture-card"
            accept=".jpg,.gif,.png"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-change="handleChange"
            :headers="headersProgram"
            :data="insertProgram"
            :limit="1"
          >
            <el-button >从电脑上传图片</el-button>
            <div slot="tip" class="el-upload__tip">
              <span>建议尺寸：不小于100*100px，支持JPG、PNG格式图片，大小不超过2M</span>
            </div>
          </el-upload>
        </div>
      </el-tab-pane>
      <el-tab-pane label="版权信息" name="fourth">
        <tinymce
          v-if="visibleDialog"
          id="inforContent"
          ref="Information"
          v-model="information"
          :height="380"
          :width="686"
        />
      </el-tab-pane>
    </el-tabs>

  </im-dialog>
</template>

<script>
import { TIME_LIST } from '@/utils/const'
import Tinymce from '@/components/Tinymce/index'
import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import HelpCenter from '@/components/eyaolink/help'
import {
  batchSaveUpdate,
  advUpdate,
  copyrightUpdate,
  copyrightAdd,
  addWorkTime,
  updateWorkTime,
  getWorkTime,
  listByParentId
} from './index'
import { getToken } from '@/utils/auth'
export default {
  name: 'MallNavigation',
  components: {
    Tinymce,
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    HelpCenter
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      headersProgram: {
        token: `Bearer ${getToken()}`,
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      },
      visibleDialog: false,
      visible: false,
      activeName: 'first',
      TIME_LIST,
      tableData: [],
      tableDataUrl: [],
      titleUrl: '',
      serviceQQ:'',
      servicePhone: '',
      fileList: [],
      time1: '',
      time2: '',
      timeOnechecked: true,
      information: '',
      oldContent: '',
      id: '',
      linkIndex: 0,
      workTime: [
        {
          dataStart: '',
          dataEnd: '',
          time: '',
          timeChecked: false
        },
        {
          dataStart: '',
          dataEnd: '',
          time: '',
          timeChecked: false
        }
      ],
      linkId: '',
      timeBol: false,
      workTimeId: ''
    }
  },
  methods: {
    init(data) {
      this.visibleDialog = true
      this.information = data.content
      this.oldContent = data.content
      this.id = data.id
      if (data.PC_SERVICE_CENTER && data.PC_SERVICE_CENTER.length > 0) {
        console.info("data.PC_SERVICE_CENTER",data.PC_SERVICE_CENTER)
        let {servicePhone,serviceQQ} = data.PC_SERVICE_CENTER[0] && data.PC_SERVICE_CENTER[0].linkUrl ?JSON.parse(data.PC_SERVICE_CENTER[0].linkUrl):{servicePhone:"",serviceQQ:""}
        this.servicePhone = servicePhone || "";
        this.serviceQQ = serviceQQ || "";
        this.fileList = [{ name: data.PC_SERVICE_CENTER[0].name, url: data.PC_SERVICE_CENTER[0].picUrl, response: { data: { url: data.PC_SERVICE_CENTER[0].picUrl }}}]
      } else {
        this.servicePhone = ''
        this.serviceQQ='',
        this.fileList = []
      }
      getWorkTime().then(res => {
        if (res.data && res.data.ext1 && JSON.parse(res.data.ext1).length > 0) {
          this.timeBol = true
          this.workTimeId = res.data.id
          const data = JSON.parse(res.data.ext1)
          this.workTime = JSON.parse(JSON.stringify(data))
        }
      }).then(() => {
        listByParentId().then(res => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            this.tableData = []
            res.data.map(val => {
              this.tableData.push({
                ...val,
                publishStatus: val.publishStatus.code
              })
            })
          }
        })
      })
      this.$refs.Information && this.$refs.Information.initTinymce()
    },
    confirmLink(row) {
      let index = 0
      if (row.linkUrl.lastIndexOf('=') === -1) {
        index = row.linkUrl.lastIndexOf('/')
      } else {
        index = row.linkUrl.lastIndexOf('=')
      }
      const str = row.linkUrl.substring(index + 1, row.linkUrl.length)
      row.linkUrl = row.linkUrl.replace(str, row.linkParam)
    },
    changeLinkFun(val, index, row) {
      row.linkParam = ''
      this.selectList.map(item => {
        if (item.code === val) {
          row.linkUrl = item.describe
        }
      })
    },
    confirm() {
      const PC_SERVICE_CENTER = {
        linkUrl:JSON.stringify({servicePhone:this.servicePhone,serviceQQ:this.serviceQQ}),
        picUrl: this.fileList && this.fileList.length > 0 ? this.fileList[0].response.data.url : '',
        type: 'PC_SERVICE_CENTER',
        sortValue: 1
      }
      advUpdate([PC_SERVICE_CENTER]).then(res => {
        if (this.oldContent !== this.information) {
          if (this.id) {
            copyrightUpdate({
              content: this.information,
              id: this.id
            }).then(res => {
              this.$emit('confirm')
              this.visibleDialog = false
            })
          } else {
            copyrightAdd({
              content: this.information
            }).then(res => {
              this.$emit('confirm')
              this.visibleDialog = false
            })
          }
        } else {
          this.$emit('confirm')
          this.visibleDialog = false
        }
      }).then(res => {
        if (this.timeBol) {
          updateWorkTime({
            id: this.workTimeId,
            ext1: JSON.stringify(this.workTime),
          })
        } else {
          addWorkTime({
            ext1: JSON.stringify(this.workTime)
          })
        }
      }).then(res => {
        if (this.tableData && this.tableData.length > 0) {
          batchSaveUpdate(this.tableData, 0).then(res => {
          })
        }
      })
    },
    confirmLinkBUtton() {
      let data = []
      this.tableDataUrl.map(val => {
        data.push({
          ...val,
          id: ''
        })
      })
      batchSaveUpdate(data, this.linkId).then(res => {
        listByParentId().then(res => {
          this.tableData = res.data
        }).then(() => {
          this.$message.success('添加链接成功')
          this.visible = false
        })
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    add() {
      this.tableData.push({
        moduleName: '',
        publishStatus: 'Y',
        linkNameList: []
      })
    },
    addUrlButton() {
      this.tableDataUrl.push({
        linkName: '',
        linkUrl: ''
      })
    },
    handleClick() {
    },
    addUrl(row, index) {
      this.titleUrl = row.name
      this.linkIndex = index
      this.linkId = row.id
      this.tableDataUrl = []
      if (this.tableData[index].linkNameList && this.tableData[index].linkNameList.length > 0) {
        this.tableData[index].linkNameList.map(val => {
          for (let key in val.linkNameList) {
            val.linkName = key
            val.linkUrl = val.linkNameList[key]
          }
          this.tableDataUrl.push({
            ...val
          })
        })
      }
      this.visible = true
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 2MB!',
          type: 'warning'
        })
        return false
      }
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    },
    handleUpUrl(index) {
      if (index !== 0) {
        this.tableDataUrl[index] = this.tableDataUrl.splice(index - 1, 1, this.tableDataUrl[index])[0]
      }
    },
    handleDownUrl(index) {
      if (index !== this.tableDataUrl.length - 1) {
        this.tableDataUrl[index] = this.tableDataUrl.splice(index + 1, 1, this.tableDataUrl[index])[0]
      }
    },
    handleDelUrl(index) {
      this.tableDataUrl.splice(index, 1)
    }
  }
}
</script>

<style lang="less">
  .add-button{
    border: 1px solid #EBECEE;
    border-top: 0;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 14px;
      color: #0056e5;
      position: relative;
      &:after{
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        background-color: #0056E5;
        width: 7px;
        height: 1px;
        margin-top: 0;
      }
      &:before{
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -3px;
        background-color: #0056E5;
        width: 1px;
        height: 7px;
      }
    }
  }
  .decor-footer-dialog{
    .el-dialog__body{
      padding-top: 0;
    }
    .service-content{
      padding: 14px;
      .service-list{
        margin-top: 24px;
        display: flex;
        &:first-child{
          margin-top: 0;
        }
        .label{
          font-size: 14px;
          color: #1e2439;
          line-height: 32px;
        }
        .value{
          margin-left: 10px;
          .time{
            margin-top: 16px;
            &:first-child{
              margin-top: 0;
            }
            .to-time{
              padding: 0 10px;
            }
          }
        }
      }
    }
    .mce-menubtn.mce-fixed-width span{
      width: 60px;
    }
  }
  .decor-footer-dialog-link{
    .el-dialog__headerbtn{
      top: 8px;
    }
  }
  .decor-footer-import-image{
    text-align: left;
    padding: 14px;
    min-height: 138px;
      .el-upload-list--picture-card .el-upload-list__item{
        width: 100px;
        height: 100px;
        background-color: #EBECEE;
      }
    .el-progress-circle{
      width: 100px !important;
      height: 100px !important;
    }
    .el-upload-list--picture-card .el-progress{
      width: 100px;
      height: 100px;
    }
      .el-upload--picture-card{
        width: 130px;
        height: 32px;
        line-height: 0;
        border: 0;
      }
      .el-upload-list{
        min-width: 140px;
        display: inline-block;
      }
      .el-upload__tip {
        left: 155px;
        position: absolute;
        top: 58px;
      }
    }
  .el-dialog__body{
      padding: 40px;
    }
</style>
