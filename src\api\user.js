import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/oauth/anno/token',
    method: 'post',
    data
  })
}
export function sendPhoneValidateCode(data) {
  return request({
    url: '/oauth/anno/sendPhoneValidateCode',
    method: 'post',
    data
  })
}
/**
 * @description 根据账号设置密码
 */
export function forgotPasswordByAccount(data) {
  return request({
    url: `/authority/wechat/user/anno/forgotPasswordByAccount`,
    method: "POST",
    data,
  });
}
export function secondaryVerificationEnabled(data) {
  return request({
    url: '/oauth/anno/secondaryVerificationEnabled',
    method: 'get',
    data
  })
}
export function getInfo() {
  return request({
    url: '/oauth/user/getLoginUserInfo',
    method: 'get'
  })
}

export function logout() {
  return Promise.resolve()
  return request({
    url: '/vue-element-admin/user/logout',
    method: 'post'
  })
}
//修改当前用户密码
export function editPassword(query) {
  return request({
    url: '/authority/user/password/current',
    method: 'PUT',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })

}
export function getUserMenuRouter() {
  return request({
    url: '/oauth/menu/router',
    method: 'get'
  })
}
export function getUserResourceVisible() {
  return request({
    url: '/oauth/resource/visible',
    method: 'get',
  })
}
