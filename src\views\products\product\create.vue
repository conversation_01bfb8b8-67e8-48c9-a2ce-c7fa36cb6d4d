<template>
<el-card class="product-create page-card" shadow="never" v-loading="loading">
  <template slot="header">
    <div class="title">新增商品档案</div>
    <div>
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="createProduct">生成档案</el-button>
    </div>
  </template>
  <product-form ref="form" action="CREATE" :areaData="areaData"/>
</el-card>
</template>

<script>
import ProductForm from './form'
import request from '@/utils/request'
export default {
  components: { ProductForm },
  data () {
    return {
      loading: false,
      areaData: []
    }
  },
  created () {
    this.load()
  },
  methods: {
    async load () {
      try {
        const areaData = await request.get('authority/area/anno/tree')
        this.areaData = areaData.data
      } catch (e) {

      }
    },
    onCancel () {
      this.$confirm('确定取消编辑，取消后已编辑内容将不被保存', '取消提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.back();
      })
    },
    async createProduct () {
      if (this.loading) {
        return;
      }
      const formData = this.$refs.form.getData()
      // 特殊处理skuList
      if (formData.skuList) {
        let keys = {
          "costPrice": 0,
          "limitQuantity": 0,
          "medicarePrice": 0,
          "retailPrice": 0,
          "salePrice": 0,
          "stockQuantity": 0,
          "supplyPrice":0,
          "wholesalePrice":"",
          "memberPrice":"",
          "productMemberCoefficient":'',
          "whetherProductMemberCoefficient":'N'
        }
        formData.skuList = formData.skuList.map(item => {
          let ret = {};
          for (let key in keys) {
            ret[key] = item[key]
          }
          return ret;
        })
      }

      if (!formData) {
        this.loading = false;
        return
      }

      try {
        //加上商品id，不然报错  新增商品要传，没选默认0
        formData.platformProductId = formData.platformProductId ?formData.platformProductId:0
        formData.skuList[0].expDate =formData.expDate?  formData.expDate + ' 00:00:00':'';
        let data =  await request.post('product/admin/product', formData)
        if(data.code==0){
          this.$message({
            type: 'success',
            message: '生成档案成功'
          })
          sessionStorage.setItem('productList', '123')
          this.$router.push({ path: '/productCenter/productsManagement/list' })
        }else{
          this.$message.error('生成档案失败')
        }

      } catch (e) {

      }

      this.loading = false
    }
  },
}
</script>
