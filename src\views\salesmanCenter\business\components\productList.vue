<template>
  <div>
    <el-table border :data="tableList" style="width: 100%">
      <el-table-column type="index" align='center' width="50">
      </el-table-column>
      <el-table-column prop="productCode" label="商品编码" width="220">
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" width="220">
      </el-table-column>
      <el-table-column prop="spec" label="规格" width="220">
      </el-table-column>
      <el-table-column prop="manufacturer" label="生产厂家" width="220">
      </el-table-column>
      <el-table-column label="陈列图片">
        <template slot-scope="{row}">
          <div class="img-cell">
              <div v-if="row.picts.split(',').length < 2">
                <img style="width:50px;height:50px;margin-right:5px" v-for="(i,indexs) in row.picts.split(',')"
                  :key="indexs" :src="i" alt="">
              </div>
              <div class="img_box" v-else>
                <img style="width:50px;height:50px;margin-right:5px" :src="row.picts.split(',')[0]" alt="">
                <div class="img-posi">
                  <img style="width:50px;height:50px;margin-right:5px" :src="row.picts.split(',')[1]" alt="">
                  <div class="img_mask"> +{{ row.picts.split(',').length - 1 }}</div>
                </div>
              </div>
            </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="{ row }">
          <el-button type="text" @click="handleBigImage(row)">预览图片</el-button>
          <el-image :ref="`refs${row.productId}`" style="width:0px;height:0px;margin-right:5px" :src="previewDetail[0]" :preview-src-list="previewDetail"></el-image>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>


<script>
import {
    productShowingDetail
  } from '@/api/salemanCenter/index'
  export default {
    //import引入的组件
    components: {},

    props:{
        currentId:{
            type:String,
            default:''
        }
    },

    data() {
      return {
          tableList:[],
          previewDetail:[],
      }
    },
    created() {
        this.initData();
    },
    //方法集合
    methods: {
        initData(){
            productShowingDetail(this.currentId).then(res=>{
                if(res.code == 0 && res.msg == 'ok') {
                    this.tableList = res.data.productArray || []
                }
            })
        },
        handleBigImage(row){
          this.previewDetail = [];
            if(row.picts!='' && row.picts !=null && row.picts != undefined) {
                row.picts.split(',').forEach(item=>{
                    this.previewDetail.push(item);
                });
                this.$refs[`refs${row.productId}`].showViewer = true;
            }
        }
    },

  }

</script>


<style lang='scss' scoped>
.img-cell {
    display: flex;
    align-items: center;
    .img_box {
      display: flex;
      align-items: center;
      position: relative;
      .img-posi {
        width: 50px;
        height: 50px;
      }
      .img_mask {
        position: absolute;
        width: 50px;
        height: 50px;
        top: 0;
        left: 55px;
        line-height: 50px;
        text-align: center;
        background-color: rgba($color: #000000, $alpha: 0.6);
        color: #fff;
      }
    }
  }
</style>
