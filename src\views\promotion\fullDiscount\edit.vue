<template>
    <div class="detail-wrapper" :class="$style.container" v-loading="loading">
        <!-- 头部标题 start -->
        <page-title :title="title">
            <template slot="icon">
                <span :class="[$style[this.form.activityStatus]]"></span>
            </template>

            <template v-if="!isDisabled">
                <el-button @click="onSave" type="primary">保存</el-button>
            </template>
        </page-title>
        <!-- 头部标题 end -->

        <el-form ref="form" :model="form" label-width="130px" :rules="rules">
            <page-module-card title="满减送类型">
                <el-form-item label-width="0">
                    <card-radio v-model="form.fullReduceType" :disabled="isDisabled" :options="fullReduceTypes">
                    </card-radio>
                </el-form-item>
            </page-module-card>


            <page-module-card title="基础信息">
                <el-form-item label="活动名称：" prop="activityName">
                    <el-input style="width: 400px" :disabled="isDisabled" v-model="form.activityName"
                        placeholder="请输入活动名称"></el-input>
                </el-form-item>

                <el-form-item label="活动时间：" prop="activityTime">
                    <el-date-picker :disabled="isDisabled" v-model="form.activityTime" type="datetimerange"
                        range-separator="至" value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始日期"
                        end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
                </el-form-item>

                <el-form-item label="优惠叠加：" prop="whetherOverlayDiscount">
                    <el-radio-group :disabled="isDisabled" v-model="form.whetherOverlayDiscount">
                        <el-radio label="N">不叠加其他营销活动</el-radio>
                        <el-radio label="Y">叠加其他营销活动</el-radio>
                    </el-radio-group>
                </el-form-item>


                <el-form-item v-if="form.whetherOverlayDiscount === 'Y'">
                    <el-checkbox-group :disabled="isDisabled" v-model="form.superpositionRule">
                        <el-checkbox v-for="item in superpositionRules" :key="item.value" :label="item.value"
                            name="superpositionRule">
                            {{ item.text }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item label="活动商品：" prop="productRangeType">
                    <el-radio-group :disabled="isDisabled" v-model="form.productRangeType">
                        <el-radio v-for="item in productTypes" :label="item.value" :key="item.value">{{ item.text }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="裂变奖励：" prop="productRangeType">
                    <el-radio-group :disabled="isDisabled" v-model="form.whetherReward.code">
                        <el-radio v-for="item in whetherRewardData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
                    </el-radio-group>
                    <FissionRewardTip />
                </el-form-item>
                <el-form-item label="积分抵扣：" prop="productRangeType">
                    <el-radio-group :disabled="isDisabled" v-model="form.whetherPointsDeduction.code">
                        <el-radio v-for="item in pointDeductionData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
                    </el-radio-group>
                    <PointDeductionTip />
                </el-form-item>

                <el-form-item v-if="['LABEL', 'UN_LABEL'].includes(form.productRangeType)" prop="productLabelIds">
                    <el-checkbox-group :disabled="isDisabled" v-model="form.productLabelIds">
                        <el-checkbox v-for="item in productLabels" :key="item.value" :label="item.value"
                            name="productLabelIds">{{ item.text }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item v-else-if="['PART', 'UN_PART'].includes(form.productRangeType)"
                    prop="fullReduceProductRelSaveDTOList">
                    <select-product :disabled="isDisabled" v-model="form.fullReduceProductRelSaveDTOList">
                    </select-product>
                </el-form-item>

            </page-module-card>


            <page-module-card title="活动规则">
                <el-form-item label="优惠设置：" prop="discountSetUp">
                    <el-radio-group :disabled="isDisabled" @change="onDiscountSetupChange" v-model="form.discountSetUp">
                        <el-radio v-for="item in discountSetupOptions" :key="item.value" :label="item.value">
                            {{ item.text }}</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item v-for="(item, index) in form.fullReduceActivityRuleRelDTOList" :key="index">
                    <!-- fullReduceActivityRuleRelDTOList -->
                    <div :class="$style.title" v-if="form.discountSetUp === 'LADDER'">
                        <el-row>
                            <el-col :span="12"><span>{{ convertNumbersToChinese(index) }}级优惠</span></el-col>
                            <el-col v-if="index && !isDisabled" style="text-align: right;" :span="12">
                                <el-button @click="onDeleteStep(index)" type="text">删除</el-button>
                            </el-col>
                        </el-row>
                    </div>

                    <el-form-item :class="$style['mb-10']" label="优惠门槛：" required>
                        <el-form-item style="display: inline-block;" :class="$style['mr-50']"
                            :prop="`fullReduceActivityRuleRelDTOList[${index}].couponThreshold`">
                            <span>{{ form.discountSetUp === 'CYCLE' ? '每' : '' }}满</span>
                            <el-input-number :disabled="isDisabled" :class="[$style['mx-10'], $style['w-100']]"
                                :controls="false" v-model="item.couponThreshold" placeholder="请输入数值">
                            </el-input-number>
                            <span>{{ isFullMinus ? '元' : '个' }}</span>
                        </el-form-item>
                        <el-form-item style="display: inline-block;"
                            :prop="`fullReduceActivityRuleRelDTOList[${index}].cycleNumber`"
                            v-if="form.discountSetUp === 'CYCLE'">
                            <span>循环</span>
                            <el-input-number :disabled="isDisabled" :class="[$style['mx-10'], $style['w-100']]"
                                :controls="false" v-model="item.cycleNumber">
                            </el-input-number>
                            <span>次 </span>
                            <el-popover placement="right" width="350" trigger="hover">
                                <ul>
                                  <li>例如每满减1000元送一袋米，且循环10次，则：</li>
                                  <li>第1次循环：满1000元送一袋米；</li>
                                  <li>第2次循环：满2000元送两袋米；</li>
                                  <li>第3-10次循环：以此类推，循环至满10000元，送10袋米。超过满10000元，不再循环，即最大送10袋米。</li>
                                </ul>
                                <i slot="reference" style="color: #76838f" class="el-icon-question"></i>
                                </el-popover>
                        </el-form-item>
                    </el-form-item>

                    <el-form-item :class="$style['mb-20']" label="优惠内容："
                        :prop="`fullReduceActivityRuleRelDTOList[${index}]._offerType`">
                        <el-checkbox-group :disabled="isDisabled" v-model="item._offerType">
                            <el-checkbox label="money" name="content">订单金额优惠</el-checkbox>
                            <el-checkbox label="gifts" name="content">送赠品</el-checkbox>
                        </el-checkbox-group>

                        <el-form-item :class="$style['mb-20']" v-if="item._offerType.includes('money')"
                            :prop="`fullReduceActivityRuleRelDTOList[${index}].discount`">
                            <span>减</span>
                            <el-input-number :disabled="isDisabled" :class="[$style['mx-10'], $style['w-100']]"
                                :controls="false" v-model="item.discount" placeholder="请输入数值"></el-input-number>
                            <span>元</span>
                        </el-form-item>
                        <template v-if="item._offerType.includes('gifts')">

                            <el-form-item :class="[$style['mb-10'], $style['mt-10']]"
                                :prop="`fullReduceActivityRuleRelDTOList[${index}].giveawayList`">
                                <select-gifts :update="!isAdd" :disabled="isDisabled" v-model="item.giveawayList" placeholder="请输入数值">
                                </select-gifts>
                            </el-form-item>


                            <el-form-item label="可选种数："
                                :prop="`fullReduceActivityRuleRelDTOList[${index}].giveawaySpeciesNumber`">
                                <span>最多可选</span>
                                <el-input-number :disabled="isDisabled" :class="[$style['mx-10'], $style['w-100']]"
                                    :controls="false"
                                    :max="form.fullReduceActivityRuleRelDTOList[index].giveawayList.length"
                                    v-model="item.giveawaySpeciesNumber" placeholder="数值">
                                </el-input-number>
                                <span>种赠品</span>
                            </el-form-item>
                        </template>
                    </el-form-item>

                </el-form-item>
                <el-form-item v-if="form.discountSetUp === 'LADDER'">
                    <el-button v-if="form.fullReduceActivityRuleRelDTOList.length < 5 && !isDisabled" @click="onAddStep"
                        type="primary">添加阶梯优惠</el-button>
                    <p>提醒：每级优惠不叠加，如满足二级优惠条件后则不再享有一级优惠。最多支持五级优惠。</p>
                </el-form-item>


                <el-form-item label="参与人：" prop="limitObjectType">
                    <mixed-radio :disabled="isDisabled" :options='customerTypes' v-model="form.limitObjectType">
                    </mixed-radio>
                </el-form-item>

                <el-form-item
                    v-if="form.limitObjectType === 'CUSTOMER_TAG' || form.limitObjectType === 'UN_CUSTOMER_TAG'">
                    <el-checkbox-group :disabled="isDisabled" v-model="form.merchantLabelIds">
                        <el-checkbox v-for="item in merchantLabels" :key="item.value" :label="item.value"
                            name="merchantLabelIds">{{ item.text }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item
                    v-if="form.limitObjectType === 'CUSTOMER_TYPE' || form.limitObjectType === 'UN_CUSTOMER_TYPE'">
                    <el-checkbox-group :disabled="isDisabled" v-model="form.merchantTypeIds">
                        <el-checkbox v-for="item in merchantTypes" :key="item.value" :label="item.value"
                            name="merchantTypeIds">{{ item.text }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item v-if="form.limitObjectType === 'CUSTOMER' || form.limitObjectType === 'UN_CUSTOMER'">
                    <select-client :disabled="isDisabled" v-model="form.merchantIds"></select-client>
                </el-form-item>

                <el-form-item label="每人限购次数：" prop="limitTimesType">
                    <el-radio-group :disabled="isDisabled" v-model="form.limitTimesType"
                        @change="onLimitTimesTypeChange">
                        <el-radio label="N">不限次数</el-radio>
                        <el-radio label="Y">
                            <el-form-item style="display: inline-block" prop="limitNumber">
                                <el-input-number :class="[$style['mx-10'], $style['w-100']]" :controls="false"
                                    :disabled="form.limitTimesType !== 'Y' || isDisabled" v-model="form.limitNumber"
                                    placeholder="请输入数值">
                                </el-input-number>
                                <span>次</span>
                            </el-form-item>

                        </el-radio>
                    </el-radio-group>
                </el-form-item>

            </page-module-card>
        </el-form>
    </div>
</template>
<script>

import _ from 'lodash';

import UploadImage from '@/views/promotion/gifts/components/UploadImage.vue'
import DelButton from '@/components/eyaolink/delElButton/index.vue'

import SelectProduct from './BusinessComponents/SelectProduct.vue'
import SelectClient from './BusinessComponents/SelectClient.vue'
import SelectGifts from './BusinessComponents/SelectGifts.vue'

import MixedRadio from './LogicalComponents/MixedRadio.vue'
import CardRadio from './LogicalComponents/CardRadio.vue'

import { addActivity, updateActivity, fetchActivity } from "@/api/fullReduction"
import { page } from "@/api/merchantApi/customerlabel";
import { merchantType } from "@/api/archivesList";
import { getWhetherRewardData, getPointDeductionData } from "@/views/products/product/components/data";
import FissionRewardTip from '@/views/promotion/components/fissionRewardTip.vue'
import PointDeductionTip from '@/views/promotion/components/pointDeductionTip.vue'

// 页面状态 查看|编辑|新增
const STATUS = {
    VIEW: 'VIEW',
    EDIT: 'EDIT',
    ADD: 'ADD'
}
const RuleItem = {
    _offerType: [], // 优惠类型（只在前端展示用）
    couponThreshold: '', // 优惠门槛
    cycleNumber: '1', // 循环次数（循环满减可用）
    discount: '', // 优惠金额
    giveawayList: [], // 礼品列表
    giveawaySpeciesNumber: '', // 赠品总数
    whetherGiveaway: '' // 是否为满送
}

const Rule = {
    _offerType: [
        { required: true, message: '请选择优惠类型' }
    ],
    couponThreshold: [
        { required: true, message: '请输入优惠门槛' },
        { type: 'number', min: 1, message: '优惠门槛不能小于1' }
    ],
    discount: [
        { required: true, message: '请输入优惠金额' },
        // { type: 'number', message: '请输入正确的优惠金额' }
        {
            validator: (rule, value, callback) => {
                console.log(rule)
                if (value <= 0) {
                    callback(new Error('请输入正确的优惠金额'))
                }
                callback()
            }
        }
    ],
    cycleNumber: [
        { required: true, message: '请输入循环次数' },
        { type: 'number', min: 1, max: 1000, message: '循环次数必须在1-1000之间' }
    ],
    giveawaySpeciesNumber: [
        { required: true, type: 'number', message: '请输入可选种数' },
        { type: 'number', min: 1, message: '可选种数不能小于1' }
    ]
}

export default {
    data() {
        return {
            pointDeductionData: getPointDeductionData(),
            whetherRewardData: getWhetherRewardData(),
            // 优惠设置类型
            discountSetupOptions: [
                { text: '阶梯满减送', value: 'LADDER' },
                { text: '循环满减送', value: 'CYCLE' },
            ],

            // 活动类型
            fullReduceTypes: [
                {
                    text: '满N元减/送',
                    value: 'MONEY',
                    tip: '例：满1000减200（可帮商家提升客单价）',
                }, {
                    text: '满N个减/送',
                    value: 'NUMBER',
                    tip: '例：满10个送1个（可帮商家清理库存）'
                }
            ],
            // 活动商品类型选项
            productTypes: [
                {
                    text: '全部商品参加',
                    value: 'ALL'
                },
                {
                    text: '指定商品参加',
                    value: 'PART',
                    children: [
                        {
                            text: '指定具体商品',
                            value: 'PART'
                        }
                    ]
                },
                {
                    text: '指定商品不参加',
                    value: 'UN_PART',
                    children: [
                        {
                            text: '指定具体商品',
                            value: 'UN_PART'
                        }
                    ]
                }
            ],
            // 活动商品标签选项
            productLabels: [
                {
                    value: 10,
                    text: '活跃客户'
                },
                {
                    value: 11,
                    text: '活跃客户'
                },
                {
                    value: 12,
                    text: '控销客户'
                },
            ],
          superpositionRules: [
            {
                text: '优惠券',
                value: 'discount'
            },
            {
                text: '限时特价',
                value: 'timedSpecials'
            },
            {
                text: '会员价',
                value: 'vipPrice'
            }],
            merchantLabels: [],
            // 商户类型选项
            merchantTypes: [],

            // 参与客户类型选项
            customerTypes: [
                {
                    text: '全部客户参加',
                    value: 'NONE'
                },
                {
                    text: '指定客户参加',
                    value: 'CUSTOMER',
                    children: [
                        {
                            text: '按客户类型',
                            value: 'CUSTOMER_TYPE'
                        },
                        {
                            text: '按客户标签',
                            value: 'CUSTOMER_TAG'
                        },

                        // {
                        //     text: '指定具体客户',
                        //     value: 'CUSTOMER'
                        // }
                    ]
                },
                // {
                //     text: '指定客户不参加',
                //     value: 'UN_CUSTOMER',
                //     children: [
                //         {
                //             text: '按客户标签',
                //             value: 'UN_CUSTOMER_TAG'
                //         },
                //         {
                //             text: '按客户类型',
                //             value: 'UN_CUSTOMER_TYPE'
                //         },
                //         {
                //             text: '指定具体客户',
                //             value: 'UN_CUSTOMER'
                //         }
                //     ]
                // }
            ],
            loading: false,
            // 商品类型数据
            products: [],
            // 非商品类型表单
            form: {
                fullReduceType: 'MONEY',
                activityName: '', // 活动名称
                activityTime: '', // 活动时间 开始时间-结束时间
                whetherOverlayDiscount: 'N', // 是否叠加优惠
                whetherReward: { code: 'N' }, // 裂变奖励
                whetherPointsDeduction: { code: 'N' }, // 积分抵扣
                fullReduceProductRelSaveDTOList: [], // 活动商品列表
                superpositionRule: [], //叠加的优惠类型
                productRangeType: 'ALL', //  活动商品类型
                productLabelIds: [], // 商品标签
                discountSetUp: 'LADDER', // 优惠类型
                limitObjectType: 'NONE', // 参与人客户类型
                limitTimesType: 'N', // 限购数量

                fullReduceActivityRuleRelDTOList: [
                    {
                        _offerType: [], // 优惠类型（只在前端展示用）
                        couponThreshold: '',
                        cycleNumber: '', // 循环次数（循环满减可用）
                        discount: '', // 优惠金额
                        giveawayList: [], // 礼品列表
                        giveawaySpeciesNumber: '', // 赠品总数
                        whetherGiveaway: '' // 是否为满送
                    }
                ], // 规则列表

                merchantLabelIds: [], // 客户标签
                merchantTypeIds: [], // 客户类型
                limitNumber: '' // 限购数量
            },
            // 表单验证
            rules: {
                activityName: [
                    { required: true, message: '请输入活动名称' }
                ],
                activityTime: [
                    { required: true, message: '请选择活动时间' }
                ],
                whetherOverlayDiscount: [
                    { required: true, message: '请选择优惠叠加类型' }
                ],
                productRangeType: [
                    { required: true, message: '请选择活动商品类型' }
                ],
                discountSetUp: [
                    { required: true, message: '请选择活动规则类型' }
                ],

                limitObjectType: [
                    { required: true, message: '请选择领取客户类型' }
                ],
                limitTimesType: [
                    { required: true, message: '请选择每人限购类型' }
                ],
                fullReduceActivityRuleRelDTOList: [Rule],
                limitNumber: [
                    { required: true, message: '请输入限购数量' },
                    {
                        validator: (rule, value, callback) => {
                            if (value <= 0 && this.form.limitTimesType === 'Y') {
                                callback(new Error('限购数量不能小于1'))
                            }
                            callback()
                        }
                    }
                ]

            },
            timer: null
        }
    },
    created() {
        if (!this.isAdd) {
            this.fetch();
        }
        this.fetchMerchantLabels();
        this.fetchMerchantTypes()

    },
    computed: {
        /**
         * 是否为满减
         */
        isFullMinus() {
            return this.form.fullReduceType === 'MONEY';
        },

        isDisabled() {
            return ['check', STATUS.VIEW, STATUS.EDIT].includes(this.$route.query.type);
        },

        isAdd() {
            return !this.$route.query.id
        },
        title() {
            return this.isAdd ? '新增满减送' : this.$route.query.type === STATUS.VIEW ? '查看满减送' : '编辑满减送';
        },

    },
    methods: {
        convertNumbersToChinese(num) {
            return ['一', '二', '三', '四', '五', '六', '七', '八', '九'][num]
        },
        onAddStep() {
            this.form.fullReduceActivityRuleRelDTOList.push({ ...RuleItem })
            this.rules.fullReduceActivityRuleRelDTOList.push({ ...Rule })
        },
        onDeleteStep(index) {
            this.form.fullReduceActivityRuleRelDTOList.splice(index, 1)
            this.rules.fullReduceActivityRuleRelDTOList.splice(index, 1)
        },
        onLimitTimesTypeChange(val) {
            if (val === 'N') {
                this.$refs.form.clearValidate('limitNumber')
            }
        },
        /**
         * 监听优惠设置状态改变
         * 切换状态之前保存，另外一种状态的数据，用于切换回来的时候赋值
         * 如果判断没有当前缓存的数据则创建一条数据
         */
        onDiscountSetupChange(val) {
            if (val === 'CYCLE') {
                this._ladder = _.cloneDeep(this.form.fullReduceActivityRuleRelDTOList)
                if (!this._cycle || !this._cycle.length) {
                    this.form.fullReduceActivityRuleRelDTOList = [{ ...RuleItem }]
                    this.rules.fullReduceActivityRuleRelDTOList = [{ ...Rule }]
                } else {
                    this.form.fullReduceActivityRuleRelDTOList = this._cycle
                    this.rules.fullReduceActivityRuleRelDTOList = this._cycle.map(v => ({ ...Rule }))
                }
            } else if (val === 'LADDER') {
                this._cycle = _.cloneDeep(this.form.fullReduceActivityRuleRelDTOList)
                if (!this._ladder || !this._ladder.length) {
                    this.form.fullReduceActivityRuleRelDTOList = [{ ...RuleItem }]
                    this.rules.fullReduceActivityRuleRelDTOList = [{ ...Rule }]
                } else {
                    this.form.fullReduceActivityRuleRelDTOList = this._ladder
                    this.rules.fullReduceActivityRuleRelDTOList = this._ladder.map(v => ({ ...Rule }))
                }
            }

        },
        /**
         * 获取客户标签列表
         */
        async fetchMerchantLabels() {
            let { code, data } = await page({ current: 1, page: 200, model: {} });
            if (code === 0) {
                this.merchantLabels = data.records.map(v => ({
                    text: v.tagName,
                    value: v.id
                }))
            }

        },

        /**
         * 获取客户类型列表
         */
        async fetchMerchantTypes() {
            let { code, data } = await merchantType()
            if (code === 0) {
                this.merchantTypes = data.map(v => ({
                    text: v.name,
                    value: v.id
                }))
            }
        },
        /**
         * 新增/修改活动
         */
        async onSave() {
            let valid = await this.$refs.form.validate()
            if (!valid) return;
            let { activityTime, fullReduceActivityRuleRelDTOList, fullReduceProductRelSaveDTOList, superpositionRule, productRangeType, merchantIds, ...form } = this.form;
            superpositionRule = superpositionRule.length && superpositionRule.reduce((obj, cur) => {
                obj[cur] = 'Y'
                return obj;
            }, { discount: 'N', timedSpecials: 'N', vipPrice: 'N' }) || {}
            fullReduceActivityRuleRelDTOList = fullReduceActivityRuleRelDTOList.map(({ _offerType, ...item }, index) => {
                item.whetherGiveaway = _offerType.includes('gifts') ? 'Y' : 'N'
                item.discount = _offerType.includes('money') ? item.discount : ''
                item.giveawayList = item.whetherGiveaway === 'Y' && item.giveawayList && item.giveawayList.map(v => {
                    if (!v.giveawayQuantity || v.giveawayQuantity < 1 || Number(v.stockQuantity) < v.giveawayQuantity) {
                        this.$message.warning('赠送数量不可小于1或大于库存')
                        throw new Error('赠送数量不可小于1或大于库存')
                    }
                    return {
                        giveawayId: v.id,
                        stockQuantity: v.stockQuantity,
                        giveawayQuantity: v.giveawayQuantity
                    };
                }) || []
                if (form.discountSetUp === 'LADDER') {
                    item.discountLevel = index + 1;
                }
                return item;
            })
            // 只有商品类型才需要商品列表
            fullReduceProductRelSaveDTOList = ['PART', 'UN_PART'].includes(productRangeType) && fullReduceProductRelSaveDTOList && fullReduceProductRelSaveDTOList.map(v => {
                return { productId: v.id }
            }) || []
            merchantIds = merchantIds && merchantIds.map(v => v.id)
            let [activityStartTime, activityEndTime] = activityTime
            let params = {
                ...form,
                productRangeType,
                fullReduceProductRelSaveDTOList,
                fullReduceActivityRuleRelDTOList,
                activityStartTime,
                activityEndTime,
                merchantIds,
                superpositionRule
            }
            try {
                this.loading = true;
                const request = this.isAdd ? addActivity : updateActivity;
                const { code, data, ...result } = await request(params);
                console.log('result', result, code, data)
                if (code === 0) {
                    this.$message.success('保存成功')
                    this.timer = setTimeout(() => {
                        this.$store.dispatch("tagsView/delView", this.$route);
                        this.$router.replace({
                            path: '/promotion/fullDiscount'
                        })
                    }, 500)
                }
            } catch (error) {
                console.error('error', error)
            } finally {
                this.loading = false;
            }

        },

        /**
         * 获取活动详情
         */
        async fetch() {
            try {
                this.loading = true;
                let { data } = await fetchActivity(this.$route.query.id)
                if (data) {
                    let superpositionRule = []
                    if (data.superpositionRule) {
                        let keys = Object.keys(data.superpositionRule);
                        keys.forEach(key => {
                            let item = data.superpositionRule[key];
                            if (item && item.code === 'Y') {
                                superpositionRule.push(key)
                            }
                        })
                    }
                    let fullReduceActivityRuleRelDTOList = []
                    if (data.fullReduceActivityRuleRelDTOList) {
                        data.fullReduceActivityRuleRelDTOList.forEach((item, index) => {
                            if (index !== 0) {
                                this.rules.fullReduceActivityRuleRelDTOList.push({ ...Rule })
                            }
                            let _offerType = [];
                            if (item.discount) {
                                _offerType.push('money')
                            }
                            if (item.whetherGiveaway && item.whetherGiveaway.code === 'Y') {
                                _offerType.push('gifts')
                            }
                            let giveawayList = item.giveawayList && item.giveawayList.map(v => {
                                v.id_backup = v.id;
                                v.id = v.giveawayId;
                                return v
                            }) || []
                            fullReduceActivityRuleRelDTOList.push({
                                ...item,
                                giveawayList,
                                _offerType
                            })
                        })
                    }

                    let activityTime = [data.activityStartTime, data.activityEndTime]
                    let fullReduceType = data.fullReduceType && data.fullReduceType.code || '';
                    let whetherOverlayDiscount = data.whetherOverlayDiscount && data.whetherOverlayDiscount.code || '';
                    let fullReduceProductRelSaveDTOList = data.fullReduceProductRelSaveDTOList && data.fullReduceProductRelSaveDTOList.map(item => {
                        item.id_backup = item.id
                        item.id = item.productId
                        return item;
                    }) || []
                    this.form = {
                        ...data,
                        id: data.id,
                        whetherReward: data.whetherReward && data.whetherReward.code ? data.whetherReward : { code: 'N' },
                        fullReduceType: fullReduceType,
                        activityTime: activityTime, // 活动时间 开始时间-结束时间
                        whetherOverlayDiscount: whetherOverlayDiscount, // 是否叠加优惠
                        fullReduceProductRelSaveDTOList: fullReduceProductRelSaveDTOList, // 活动商品列表
                        superpositionRule: superpositionRule, //叠加的优惠类型
                        productRangeType: data.productRangeType && data.productRangeType.code || '', //  活动商品类型
                        productLabelIds: data.productLabelIds || [], // 商品标签
                        discountSetUp: data.discountSetUp && data.discountSetUp.code || '', // 优惠类型
                        limitObjectType: data.limitObjectType && data.limitObjectType.code || '', // 参与人客户类型
                        limitTimesType: data.limitTimesType && data.limitTimesType.code || '', // 限购数量
                        fullReduceActivityRuleRelDTOList: fullReduceActivityRuleRelDTOList,
                        merchantIds: data.merchantVoList || [], // 参与商户
                        merchantLabelIds: data.merchantLabelIds || [], // 商户标签
                        merchantTypeIds: data.merchantTypeIds || [], // 商户类型
                    }
                }
            } catch (error) {
                console.error(error)
            } finally {
                this.loading = false;
            }
        }

    },
    components: {
        PointDeductionTip,
        FissionRewardTip,
        DelButton,
        UploadImage,
        SelectProduct,
        SelectClient,
        SelectGifts,
        MixedRadio,
        CardRadio
    },
    beforeDestroy() {
        clearTimeout(this.timer)
    }
}
</script>
<style lang="scss" module>
.container {
    width: 100%;

    ::v-deep .el-form-item ::v-deep .el-form-item {
        margin-bottom: 22px;
    }

    .w-100 {
        width: 100px;
    }

    .m-10 {
        margin: 10px;
    }

    .mx-10 {
        margin-left: 10px;
        margin-right: 10px;
    }

    .my-10 {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .mt-10 {
        margin-top: 10px;
    }

    .mb-10 {
        margin-bottom: 10px;
    }

    .mb-20 {
        margin-bottom: 20px;
    }

    .mr-50 {
        margin-right: 50px;
    }
}


.title {
    padding: 2px 10px;
    margin-bottom: 10px;
    background-color: rgba(242, 242, 242, 1);

}

.tip {
    color: #aaa;
}

.status {
    width: 64px;
    height: 32px;
    display: inline-block;
    background-size: cover;
    background-repeat: no-repeat;
    margin-left: 12px;

    &.NOT_START {
        background-image: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    }

    &.PROCEED {
        background-image: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    }

    &.OBSOLETE {
        background-image: url('../../../assets/imgs/coupon/Icon_Revok.png')
    }

    &.FINISHED {
        background-image: url('../../../assets/imgs/coupon/<EMAIL>');
    }
}

.status-box1 {
    width: 64px;
    height: 32px;
    display: inline-block;

    background-size: cover;
    margin-left: 12px;
}
</style>
