@import './_reset.scss';
@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './order.scss';


$--temp-searchBox-border-color:#0056E5;
$--input-border-radius:'2px';

body {
  font-size: 14px;
  line-height: 1.43em;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 500;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}


.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


.temp_searchBox {
  border-left: 2px solid $--temp-searchBox-border-color;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
}

.temp_pageContent {
  background: #fff;
}


.flex_center_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_end_center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}


.flex_start_center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex_between_center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex_between_start {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.flex_between_end {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}


.flex_center_start {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.flex_start_start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.textOverEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  white-space: nowrap;
}

.textOverEllipsisLine {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.flex_center_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_end_center {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}


.flex_start_center {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex_between_center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex_between_start {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.flex_between_end {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}


.flex_center_start {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.flex_start_start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.textOverEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  white-space: nowrap;
}

.textOverEllipsisLine {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.page-card {
  border: none;
  .el-card__header {
    display: flex; justify-content: space-between; align-items: center;
    padding-top: 15px; padding-bottom: 10px;
    line-height: 30px;
    .title { font-size: 18px; font-weight: 400; }
  }
}

.section-label {
  border-left: 6px solid #409eff;
  padding-left: 10px;
}
.alert-info {
  background: #e6f7ff;
  border: 1px solid #298dbb;
  padding: 15px;
  i { display: inline-block; color: #0e77d1; margin-right: 5px; }
}
.el-dialog__header {
  border-bottom: 1px solid #ddd;
}
.el-button--medium {
  border-radius: 0;
}
.search-wrapper {
  background-color: #fff;
  padding: 15px;
  border-left: 4px solid $--temp-searchBox-border-color;
  margin-bottom: 15px;
}
.tab_bg {
  background-color: #fff;
  padding: 8px 20px 20px;
  //height:  calc(100vh - 84px);
  position: relative;
  margin-top: 15px;
  &.no-margin-top {
    margin-top: 0
  }
}
.tab_btns {
  position: absolute;
  top: 10px;
  right: 15px;
}
.detail-wrapper {
  padding: 0 20px 20px;
  background: #fff;
 }
.detail-item {
  ul {
    padding: 0;
  }
  li {
    list-style: none;
    // width: 25%;
    display: inline-block;
    line-height: 30px;
  }
}
.detail-title {
  line-height: 50px;
  font-size: 18px;
  padding: 0 20px;
border-bottom: 1px solid #ddd;
}
.detail-head {
  border-left: 4px solid $--temp-searchBox-border-color;
  padding-left: 5px;
  font-size: 16px;
}


// input number 移除上下箭头
input[type=number] {
  -moz-appearance:textfield;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.temptop_title {
  height: 56px;
  line-height: 56px;
  font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
  font-size: 18px;
  text-align: left;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 20px;

  .el-button {
    margin-left: 10px;
  }
}
[class^="el-icon-fa"], [class*=" el-icon-fa"] {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome !important;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}


::-webkit-scrollbar {
  width: 10px;
  height: 9px;
  background-color: #d1d1d1;
  -webkit-transition: background-color 0.3s ease-in-out;
  transition: background-color 0.3s ease-in-out;
}

::-webkit-scrollbar:hover {
  background-color: #d1d1d1;
}

::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  height: 50px;
  outline-offset: -1px;
  outline: 1px solid #fff;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border-right: 1px solid #fff;
  border-left: 1px solid #fff;
  -webkit-transition: background-color 0.3s ease-in-out;
  transition: background-color 0.3s ease-in-out;
}

::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:active {
  background-color: #9c9c9c;
  border-right: 1px solid #f1f1f1;
  border-left: 1px solid #f1f1f1;
}

::-webkit-scrollbar-track {
  background-color: #fff;
}

::-webkit-scrollbar-track:hover {
  background-color: #f1f1f1;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.15);
}

