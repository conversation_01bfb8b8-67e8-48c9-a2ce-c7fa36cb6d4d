<template>
  <div class="archivesEditContent" v-if="listLoading">
    <div class="top_title flex_between_center">
      <span>{{ $route.query.id ? "编辑" : "新增" }}采购商档案</span>
      <div>
        <el-popover
          v-model="rejectFlag"
          placement="bottom-end"
          title="取消提醒"
          width="300"
          trigger="click"
        >
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0; padding-top: 14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="back">确定</el-button>
          </div>
        </el-popover>
        <el-button
          :loading="submitLoading"
          @click="edit('editForm')"
          type="primary"
          >提交</el-button
        >
      </div>
    </div>

    <div class="item" v-if="!$route.query.id">
      <div class="title"><span>营业执照识别</span></div>
      <div class="imgRead">
        <el-upload
          class="avatar-uploader"
          :action="uploadParams.action"
          :data="uploadParams.data"
          :headers="uploadParams.headers"
          :show-file-list="false"
          :on-success="businessLicense"
          :before-upload="beforeUpload"
        >
          <img
            v-if="businessLicenseImg"
            :src="businessLicenseImg"
            class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <el-button
          type="primary"
          :disabled="!businessLicenseImg"
          @click="readImg"
          >识别并填入</el-button
        >
        <div style="color: rgb(249 85 85); margin-top: 20px; font-size: 14px">
          识别填入后，需要检查核对
        </div>
      </div>
    </div>
    <el-form
      :inline="true"
      label-width="140px"
      :model="query"
      ref="editForm"
      :rules="rules"
    >
      <div class="item">
        <div class="title"><span>基础信息</span></div>
        <div>
          <el-form-item class="formItem" prop="code" label="客户编码:">
            <el-input
              :disabled="true"
              v-model="customerCode"
              clearable
              style="width: 200px"
              :value="'系统根据规则自动生成'"
              placeholder="请填写客户编码"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="name"
            label="客户名称:"
            :rules="[
              { required: true, message: '请填写客户名称', trigger: 'blur' },
            ]"
          >
            <el-input
              clearable
              style="width: 200px"
              @change="nameInput"
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              v-model.trim="query.name"
              placeholder="请填写客户名称"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="identifyCode"
            label="客户识别码:"
          >
            <el-input
              clearable
              style="width: 200px"
              v-model.trim="query.identifyCode"
              placeholder="请输入4位客户识别码，如： ZKYY"
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="socialCreditCode"
            label="社会统一信用代码:"
          >
            <el-input
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              clearable
              style="width: 200px"
              @change="socialInput"
              v-model.trim="query.socialCreditCode"
              placeholder="请输入社会统一信用代码"
            ></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="legalPerson" label="法定代表人:">
            <el-input
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              clearable
              style="width: 200px"
              v-model.trim="query.legalPerson"
              placeholder="请填写法定代表人姓名"
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="ceoName"
            label="负责人:"
            :rules="[
              { required: true, message: '请填写负责人姓名', trigger: 'blur' },
            ]"
          >
            <el-input
              clearable
              style="width: 200px"
              v-model.trim="query.ceoName"
              placeholder="请填写负责人姓名"
            ></el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="ceoMobile" label="负责人手机:">
            <el-input
              clearable
              style="width: 200px"
              v-model.trim="query.ceoMobile"
              placeholder="请填写负责人手机"
            ></el-input>
          </el-form-item>

          <el-form-item
            class="formItem"
            prop="qualityPersonInCharge"
            label="质量负责人:"
            :rules="[
              {
                required: false,
                message: '请输入质量负责人姓名',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              clearable
              style="width: 200px"
              v-model.trim="query.qualityPersonInCharge"
              placeholder="请输入质量负责人姓名"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="regionId"
            label="所在区域:"
            :rules="[
              { required: true, message: '请选择所在区域', trigger: 'blur' },
            ]"
          >
            <el-cascader
              ref="city"
              v-model="query.regionId"
              style="width: 200px"
              placeholder="请选择所在区域"
              :props="{ value: 'id', label: 'label' }"
              @change="cityChange"
              :options="areasTree"
              clearable
            >
            </el-cascader>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="registerAddress"
            label="注册地址:"
          >
            <el-input
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              clearable
              style="width: 200px"
              v-model.trim="query.registerAddress"
              placeholder="请填写注册地址"
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="formItem"
            prop="registerCapital"
            label="注册资金:"
            :rules="[
              {
                required: false,
                message: '请填写注册资金',
                trigger: 'blur',
              },
            ]"
          >
            <el-input
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              clearable
              style="width: 200px"
              type="number"
              v-model.trim="query.registerCapital"
              placeholder="请填写注册资金"
              ><b slot="suffix">万</b></el-input
            >
          </el-form-item>
          <el-form-item class="formItem" prop="enterpriseId" label="企业编码:">
            <el-input
              :disabled="enterpriseDisable || ($route.query.id ? true : false)"
              clearable
              style="width: 200px"
              v-model.trim="query.enterpriseId"
              placeholder="请填写企业编码"
            >
            </el-input>
          </el-form-item>
          <div class="address-selector">
            <el-form-item prop="detailLocation" label="企业地址">
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.detailLocation"
                placeholder="可手动填写地址或者地图点选"
              />
            </el-form-item>
            <el-form-item class="formItem" prop="latitude" label="">
              -
              <el-input
                readonly
                clearable
                style="width: 380px"
                :value="coordinate"
                placeholder="经纬度坐标"
                @click.native="handleShowMap"
              >
                <template slot="append">
                  <span style="cursor: pointer">地图选择</span>
                </template>
              </el-input>
              <map-location-select
                v-if="mapVisible"
                class="location-select"
                :center="currentPosition"
                @click="handleGetPosition"
                @close="handleClose"
              />
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 首营状态 -->
      <div class="item">
        <div class="title"><span>首营状态</span></div>
        <div>
          <el-form-item
            class="formItem"
            label="首营状态:"
            prop="firstCampStatus"
          >
            <el-select
              style="width: 200px"
              v-model="query.firstCampStatus"
              placeholder="请选择"
            >
              <el-option label="未建立" value="NOT_ESTABLISH" />
              <el-option label="已建立" value="ESTABLISH" />
            </el-select>
          </el-form-item>
          <el-form-item class="formItem" label="变更备注:" prop="remark">
            <el-input
              style="width: 200px"
              v-model="query.remark"
              placeholder="请填写变更备注"
              autocomplete="off"
              maxlength="32"
              show-word-limit
            />
          </el-form-item>
          <el-form-item class="formItem" label="ERP客户编码:">
            <el-input
              style="width: 200px"
              v-model="query.customerCode"
              autocomplete="off"
              maxlength="32"
              show-word-limit
            />
          </el-form-item>
        </div>
      </div>

      <div class="item">
        <div class="title"><span>账户信息</span></div>
        <div>
          <el-form-item
            class="formItem"
            prop="loginAccount"
            label="登录账号:"
            :rules="[
              { required: true, message: '请填写登录账号', trigger: 'blur' },
            ]"
          >
            <!-- <el-input :disabled="!!$route.query.id" clearable style="width: 200px" v-model.trim="query.loginAccount" placeholder="请填写登录账号"></el-input> -->
            <template>
              <el-select
                v-model.trim="query.loginAccount"
                filterable
                remote
                reserve-keyword
                placeholder="请输入关键词"
                :remote-method="remoteMethod"
                @change="accountChange"
                :loading="loading"
              >
                <el-option style="" :value="''" v-if="!$route.query.id">
                  <span
                    style="
                      color: #0056e5;
                      display: inline-block;
                      width: 100%;
                      height: 100%;
                    "
                    @click="showNewAccount = true"
                    >新增用户</span
                  >
                </el-option>
                <el-option
                  v-for="item in accountList"
                  :key="item.id"
                  :label="item.account"
                  :value="item.account"
                >
                </el-option>
              </el-select>
            </template>
          </el-form-item>
          <el-form-item class="formItem" prop="userMobile" label="手机号码:">
            <el-input
              clearable
              style="width: 200px"
              :disabled="!!query.userId"
              v-model.trim="query.userMobile"
              placeholder="请填写手机号码"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="!$route.query.id && isAccount"
            class="formItem"
            prop="password"
            label="登录密码:"
          >
            <el-input
              disabled
              style="width: 200px"
              placeholder="初始密码为手机号后6位"
              v-model="query.password"
              show-password
            ></el-input>
          </el-form-item>
          <!-- <el-form-item v-if="!$route.query.id && isAccount" class="formItem" prop="confirmPassword" label="确认密码:">
            <el-input disabled style="width: 200px" placeholder="" v-model="query.confirmPassword" show-password></el-input>
          </el-form-item> -->
        </div>
      </div>

      <div class="item">
        <div class="title"><span>收货地址</span></div>
        <template>
          <addrTable
            :addrtableDate.sync="addrtableDate"
            :id="$route.query.id"
            :defaultAddr="query.regionId"
          />
        </template>
      </div>

      <div class="item">
        <div class="title"><span>发票信息</span></div>
        <template>
          <div>
            <el-form-item
              class="formItem"
              prop="fpinvoiceType"
              label="发票类型:"
              :rules="[
                { required: true, message: '请选择发票类型', trigger: 'blur' },
              ]"
            >
              <el-radio-group
                v-model.trim="query.fpinvoiceType"
                @input="changeFpinvoiceType"
              >
                <!-- <el-radio label="VATINVOICE">纸质普通发票</el-radio>
                <el-radio label="SPECIALINVOICE">纸质专用发票</el-radio> -->
                <el-radio label="ELECTRON_INVOICE">电子普通发票</el-radio>
                <el-radio label="ELECTRON_SPECIALINVOICE"
                  >电子专用发票</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fpname" label="发票抬头:">
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fpname"
                placeholder="请填写发票抬头"
              ></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item
              class="formItem"
              prop="fptaxNumber"
              label="税号:"
              :rules="
                isSpecialInvoice
                  ? [{ required: true, message: '请填写税号', trigger: 'blur' }]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fptaxNumber"
                placeholder="请填写税号"
              ></el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item
              class="formItem"
              prop="fpregisterAddress"
              label="注册地址:"
              :rules="
                isSpecialInvoice
                  ? [
                      {
                        required: true,
                        message: '请填写注册地址',
                        trigger: 'blur',
                      },
                    ]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fpregisterAddress"
                placeholder="请填写注册地址"
              >
              </el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item
              class="formItem"
              prop="fpregisterMobile"
              label="注册电话:"
              :rules="
                isSpecialInvoice
                  ? [
                      {
                        required: true,
                        message: '请填写注册电话',
                        trigger: 'blur',
                      },
                    ]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fpregisterMobile"
                placeholder="请填写注册电话"
              >
              </el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item
              class="formItem"
              prop="fpbankNumber"
              label="银行账号:"
              :rules="
                isSpecialInvoice
                  ? [
                      {
                        required: true,
                        message: '请填写银行账号',
                        trigger: 'blur',
                      },
                    ]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fpbankNumber"
                placeholder="请填写银行账号"
              >
              </el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item
              class="formItem"
              prop="fpdepositBank"
              label="开户银行:"
              :rules="
                isSpecialInvoice
                  ? [
                      {
                        required: true,
                        message: '请填写开户银行',
                        trigger: 'blur',
                      },
                    ]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.fpdepositBank"
                placeholder="请填写开户银行"
              >
              </el-input>
            </el-form-item>
          </div>
          <div>
            <!-- 电子普通发票和电子专用发票中邮箱必填 -->
            <el-form-item
              class="formItem"
              prop="email"
              label="邮箱:"
              :rules="
                ['ELECTRON_INVOICE', 'ELECTRON_SPECIALINVOICE'].includes(
                  query.fpinvoiceType
                )
                  ? [{ required: true, message: '请填写邮箱', trigger: 'blur' }]
                  : []
              "
            >
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.email"
                placeholder="用于接收电子发票的邮箱"
              />
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="mobile" label="手机号码:">
              <el-input
                clearable
                style="width: 400px"
                v-model.trim="query.mobile"
                placeholder="请输入手机号码"
              />
            </el-form-item>
          </div>
        </template>
      </div>

      <div class="item">
        <div class="title">
          <span style="margin-right: 42px">经营类目</span>
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选
          </el-checkbox>
        </div>
        <div>
          <el-checkbox-group v-model="checkList" @change="checkListFun">
            <div
              v-for="item in businessScope"
              :key="item.id"
              class="findCategory"
            >
              <div
                style="
                  width: 120px;
                  font-size: 14px;
                  text-align: right;
                  line-height: 23px;
                  padding-right: 10px;
                "
              >
                {{ item.label }} :
              </div>
              <div style="flex: 1; line-height: 25px">
                <el-checkbox
                  v-for="ids in item.children"
                  :key="ids.id"
                  :label="ids.id"
                >
                  <span>{{ ids.label }}</span>
                </el-checkbox>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <div class="item">
        <div class="title"><span>客户资质</span></div>
        <div>
          <template>
            <el-form-item
              class="formItem"
              prop="merchantTypeId"
              label="企业类型:"
              :rules="[
                {
                  required: true,
                  message: '请选择企业类型',
                  trigger: 'blur',
                },
              ]"
            >
              <el-select
                v-model="query.merchantTypeId"
                placeholder="请选择企业类型"
                @change="getMerchantTypeDetailsById"
              >
                <el-option
                  v-for="item in listmerchantType"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <div class="graytext">
              上传材料为复印件加盖企业原印公章且均在有效期内，支持JPG、JPEG、PNG、BMP格式，大小不超过2M
            </div>
            <template>
              <lisence-table
                v-if="lisenceTableDate.length != 0"
                :lisenceTableDate.sync="lisenceTableDate"
              />
            </template>
          </template>
        </div>
      </div>
    </el-form>

    <el-dialog
      title="新增用户"
      :visible.sync="showNewAccount"
      style="min-width: 500px"
      width="500px"
    >
      <el-form ref="newAccount" :model="newAccountForm" label-width="100px">
        <el-form-item
          prop="account"
          label="登录账号:"
          :rules="[
            { required: true, message: '请填写登录账号', trigger: 'blur' },
          ]"
        >
          <el-input
            clearable
            style="width: 300px"
            v-model.trim="newAccountForm.account"
            placeholder="请填写登录账号"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelAccount">取 消</el-button>
        <el-button type="primary" @click="newAccountFun">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { setContextData } from "@/utils/auth";
import LisenceTable from "./lisenceTable";
import addrTable from "./addrTable";
import { getToken } from "@/utils/auth";
import { checkNumPot2 } from "@/utils/rules";
import rule from "@/utils/rules";
import { getLocalUser } from "@/utils/local-user";
import {
  add,
  getitems,
  getAccount,
  readImg,
  edititem,
  merchantType,
  MerchantTypeDetailsById,
} from "@/api/archivesList";
import { findSaleScope, areas } from "@/api/businessList";
import { getByCreditName } from "@/api/group";
import MapLocationSelect from "@/components/MapLocationSelect";
export default {
  data() {
    let that = this;
    var checkNumPot3 = (rule, value, callback) => {
      const reg =
        /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
      if (!value) {
        return callback(new Error("请填写数字"));
      } else if (!reg.test(value)) {
        return callback(new Error("请填写数字,最多2位小数"));
      } else if (value <= 0 || value > 100) {
        return callback(new Error("请填写0-100以内的数"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.query.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    var ceoMobile = (rule, value, callback) => {
      const reg = /^1\d{10}$/;
      if (!value) {
        return callback(new Error("请输入负责人手机号"));
      } else if (!reg.test(value)) {
        return callback(new Error("请输入正确的手机号"));
      } else {
        callback();
      }
    };
    return {
      mapVisible: false,
      submitLoading: false,
      enterpriseDisable: false,
      isIndeterminate: false,
      checkAll: false,
      allOptions: [],
      customerCode: "",
      rules: {
        // password: [{
        //     required: true,
        //     message: "密码不能为空",
        //     trigger: "blur"
        //   },
        //   {
        //     required: true,
        //     min: 6,
        //     max: 20,
        //     message: "长度在 6 到 20 个字符",
        //     trigger: "blur",
        //   },
        // ],
        // confirmPassword: [{
        //     required: true,
        //     message: "确认密码不能为空",
        //     trigger: "blur"
        //   },
        //   {
        //     required: true,
        //     min: 6,
        //     max: 20,
        //     message: "长度在 6 到 20 个字符",
        //     trigger: "blur",
        //   },
        //   {
        //     validator: validatePass2,
        //     trigger: "blur"
        //   },
        // ],
        registerCapital: [
          {
            validator: checkNumPot2,
            trigger: "blur",
            required: true,
          },
        ],
        ceoMobile: rule.phone1,
        userMobile: rule.phone,
        orderAmountRate: [
          {
            validator: checkNumPot3,
            trigger: "blur",
            required: true,
          },
        ],
        // socialCreditCode: [
        //   { validator: socialCreditCode, trigger: "blur", required: true },
        // ],
        // socialCreditCode: [{
        //   trigger: "blur",
        //   required: true,
        //   min: 5,
        //   max: 18,
        //   message: '社会信用代码有误'
        // }, ],
        fpregisterMobile: rule.phone1,
        fpname: { required: true, message: "请填写发票抬头", trigger: "blur" },
      },
      checkList: [],
      businessScope: {},
      listLoading: false,
      regionId: "",
      query: {
        fpinvoiceType: "ELECTRON_INVOICE",
        name: "",
        socialCreditCode: "",
        firstCampStatus: "NOT_ESTABLISH",
        remark: "",
        customerCode: "",
        mobile: "",
        email: "",
      },
      listmerchantType: [],
      rejectText: {},
      rejectFlag: false,
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + "/file/file/upload",
        headers: {
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
          token: `Bearer ${getToken()}`,
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0,
        },
      },
      // insertProgram: {
      //   folderId: 0,
      // },
      // headersProgram: {
      //   token: `Basic ${getToken()}`,
      //   Authorization: process.env.VUE_APP_AUTHORIZATION_CODE,
      // },
      lisenceTableDate: [],
      areasTree: [],
      addrtableDate: [],
      businessLicenseImg: "",
      businessLicenseImgTime: "",
      loading: false,
      merchantAccount: "",
      accountList: [],
      showNewAccount: false,
      newAccountForm: {},
      isAccount: true,
    };
  },
  computed: {
    isSpecialInvoice() {
      return ["SPECIALINVOICE", "ELECTRON_SPECIALINVOICE"].includes(
        this.query.fpinvoiceType
      );
    },
    currentPosition() {
      return [this.query.longitude, this.query.latitude];
    },
    coordinate() {
      if (this.query.latitude && this.query.longitude) {
        return this.query.latitude + "," + this.query.longitude;
      }
      return "";
    },
  },
  methods: {
    handleGetPosition({ lat, lng, address }) {
      this.$set(this.query, "latitude", lat);
      this.$set(this.query, "longitude", lng);
      this.$set(this.query, "detailLocation", address);
      this.$refs["editForm"].clearValidate(["latitude"]);
      this.$refs["editForm"].clearValidate(["detailLocation"]);
    },
    handleShowMap() {
      this.mapVisible = true;
    },
    handleClose() {
      this.mapVisible = false;
    },
    // 改变发票类型=>普通发票需要去掉部分字段校验
    changeFpinvoiceType(e) {
      if (e !== "VATINVOICE") return;
      this.$refs.editForm.clearValidate([
        "fptaxNumber",
        "fpregisterAddress",
        "fpregisterMobile",
        "fpbankNumber",
        "fpdepositBank",
      ]);
    },
    nameInput(e) {
      this.$set(this.query, "fpname", e);
    },
    socialInput(e) {
      this.$set(this.query, "fptaxNumber", e);
    },
    // 全选按钮
    handleCheckAllChange(val) {
      console.log("全选--->", val);
      this.query.businessCategoryIds = val ? this.allOptions : [];
      this.checkList = val ? this.allOptions : [];
      this.isIndeterminate = false;
    },
    accountChange(e) {
      if (e !== "") {
        this.isAccount = false;
        this.accountList.find((item) => {
          if (item.account == e) {
            this.query.userMobile = item.mobile;
            this.query.userId = item.id;
          }
        });
      }
    },
    cancelAccount() {
      this.showNewAccount = false;
      this.newAccountForm = {};
    },
    async newAccountFun() {
      this.$refs.newAccount.validate(async (valid) => {
        if (valid) {
          let { data } = await getAccount({
            model: {
              account: this.newAccountForm.account,
            },
          });
          if (data.total == 0) {
            this.showNewAccount = false;
            this.query.loginAccount = this.newAccountForm.account;
            this.newAccountForm = {};
            this.isAccount = true;
          } else {
            this.$message.error("账号已存在");
          }
        }
      });
    },
    async remoteMethod(query) {
      if (query === "") {
        this.accountList = [];
        return;
      }
      let { data } = await getAccount({
        model: {
          account: query,
        },
      });
      this.accountList = data.records;
    },
    async readImg() {
      let { data } = await readImg(this.businessLicenseImg);
      let obj = {
        socialCreditCode:
          data.socialCreditCode == "无" ? "" : data.socialCreditCode,
        legalPerson: data.legalPerson == "无" ? "" : data.legalPerson,
        registerAddress: data.address == "无" ? "" : data.address,
        registerCapital: parseInt(data.registeredCapital),
        name: data.name == "无" ? "" : data.name,
        merchantLicenses: [],
      };
      this.query = obj;
      this.businessLicenseImgTime =
        data.periodValidity == "长期" || data.periodValidity == "永久"
          ? "Y"
          : data.periodValidity;
    },
    businessLicense(res, file) {
      this.businessLicenseImg = res.data.url;
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      //F:\eyaolink\project\prodAliGit\eyaolink-admin-ui\src\views\purchasingAgent\archivesList\editItem.vue
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 1;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 1MB!");
      }
      return isJPG && isLt2M;
    },
    async getMerchantTypeDetailsById() {
      this.lisenceTableDate = [];
      let { data } = await MerchantTypeDetailsById(this.query.merchantTypeId);
      let tableDate = data.licenseBases;
      tableDate.forEach((item) => {
        let obj = {
          licenseBaseId: item.id,
          licenseEndTime: "",
          filePath: "",
          isForever: "",
          licenseNumber: "",
          label: item.name,
          isEdit: false,
          limit: item.multiple.code == "Y" ? 5 : 1,
          idIsRequired: item.idIsRequired.code,
          photoIsRequired: item.photoIsRequired.code,
          expireIsRequired: item.expireIsRequired.code,
        };
        this.query.merchantLicenses.find((ids) => {
          if (item.id == ids.licenseBaseId) {
            obj.isForever = ids.isForever.code === "Y";
            obj.licenseEndTime =
              ids.isForever.code === "Y" ? "" : ids.licenseEndTime;
            obj.filePath = ids.filePath;
            obj.filePathList = this.getsrc(ids.filePath);
            obj.licenseNumber = ids.licenseNumber;
            obj.label = item.name;
            obj.merchantId = ids.merchantId;
            obj.id = ids.id;
          }
        });
        if (obj.label == "营业执照" && !this.$route.query.id) {
          obj.filePath = this.businessLicenseImg;
          obj.filePathList = this.getsrc(this.businessLicenseImg);
          obj.licenseNumber = this.query.socialCreditCode;
          obj.isForever = this.businessLicenseImgTime;
        }
        this.lisenceTableDate.push(obj);
      });
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      if (this.$route.query.from === "registerCheck") {
        this.$router.push("/merchant/registerCheck");
      } else {
        this.$router.push("/merchant/merchantManage/list");
      }
    },
    async getmerchantType() {
      let { data } = await merchantType();
      this.listmerchantType = data;
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    cityChange(e) {
      this.query.provinceId = e[0];
      this.query.cityId = e[1];
      this.query.countyId = e[2];
      this.regionId = e;
    },
    checkListFun(e) {
      console.log("eee---->", e);
      this.query.businessCategoryIds = e;
      this.checkAll = e.length === this.allOptions.length;
      this.isIndeterminate = e.length > 0 && e.length < this.allOptions.length;
    },
    // 获取经营范围
    async getjyfw() {
      let { data } = await findSaleScope();
      console.log("获取经营范围", data);
      let list = [];
      data.forEach((item) => {
        if (Array.isArray(item.children) && item.children.length > 0) {
          item.children.forEach((i) => {
            list.push(i.id);
          });
        }
      });
      console.log("list", list);
      this.allOptions = list;
      this.businessScope = data;
    },
    // 获取详情
    async getitem() {
      if (!this.$route.query.id) {
        this.listLoading = true;
        this.query.merchantLicenses = [];
        return;
      }
      let { data } = await getitems(this.$route.query.id);
      this.listLoading = true;
      if (!data.deliveryAddressDetailDTOList) {
        data.deliveryAddressSaveDTOList = [];
      }
      this.query = data;
      this.customerCode = data.code;
      this.query.firstCampStatus =
        data.firstCampStatus && data.firstCampStatus.code;
      this.query.customerCode = data.customerCode || "";
      this.query.remark = data.remark || "";
      this.query.regionId = [data.provinceId, data.cityId, data.countyId];
      this.$set(this.query, "fpinvoiceType", data.invoiceInfo.invoiceType.code);
      this.$set(this.query, "fpname", data.invoiceInfo.name);
      this.$set(this.query, "fpdepositBank", data.invoiceInfo.depositBank);
      this.$set(this.query, "fptaxNumber", data.invoiceInfo.taxNumber);
      this.$set(
        this.query,
        "fpregisterMobile",
        data.invoiceInfo.registerMobile
      );
      this.$set(this.query, "fpbankNumber", data.invoiceInfo.bankNumber);
      this.$set(
        this.query,
        "fpregisterAddress",
        data.invoiceInfo.registerAddress
      );
      this.$set(this.query, "email", data.invoiceInfo.email);
      this.$set(this.query, "mobile", data.invoiceInfo.mobile);

      delete this.query.invoiceInfo;
      if (data.businessCategoryDetailList) {
        data.businessCategoryDetailList.forEach((item) => {
          if (item.parentId != "0") {
            this.checkList.push(item.id);
          }
        });
        // 19 5 5 1 2 4
        console.log(
          "this.checkList.length == this.allOptions.length",
          this.checkList.length,
          this.allOptions.length,
          this.checkList
        );
        this.isIndeterminate =
          this.checkList.length > 0 &&
          this.checkList.length < this.allOptions.length;
        this.checkAll = this.checkList.length == this.allOptions.length;
      }
      this.$set(this.query, "merchantTypeId", data.merchantTypeId);
      this.getMerchantTypeDetailsById();
    },
    edit(content) {
      this.$refs[content].validate((valid) => {
        if (valid) {
          if (!this.checkList.length) {
            this.$message.error("请填写选择经营类目");
            return;
          }
          let str = `${this.$route.query.id ? "编辑" : "新增"}采购商档案`;
          this.$confirm("请确认采购商信息完善无误", str, {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          }).then(async () => {
            const invoiceInfoDTO = {
              invoiceType: this.query.fpinvoiceType,
              name: this.query.fpname,
              taxNumber: this.query.fptaxNumber,
              registerMobile: this.query.fpregisterMobile,
              bankNumber: this.query.fpbankNumber,
              depositBank: this.query.fpdepositBank,
              registerAddress: this.query.fpregisterAddress,
              email: this.query.email,
              mobile: this.query.mobile,
            };
            if (!this.$route.query.id) {
              3;
              this.query.invoiceInfoSaveDTO = invoiceInfoDTO;
              this.query.merchantLicenses = [];
              this.lisenceTableDate.forEach((item) => {
                // if (item.filePath != "") {
                //   this.query.merchantLicenses.push(item);
                // }
                this.query.merchantLicenses.push(item);
              });
              let arr = [];
              this.query.deliveryAddressSaveDTOList = [];
              this.addrtableDate.forEach((item) => {
                this.query.deliveryAddressSaveDTOList.push({
                  cityId: item.address[1],
                  provinceId: item.address[0],
                  countyId: item.address[2],
                  fixedPhone: item.fixedPhone,
                  mobilPhone: item.mobilPhone,
                  name: item.name,
                  detailedAddress: item.detailedAddress,
                });
              });
              delete this.query.regionId;
              this.submitLoading = true;
              // 手机号后六位
              let temp = {
                ...this.query,
                password: this.query.userMobile.slice(-6),
                confirmPassword: this.query.userMobile.slice(-6),
              };
              add(temp)
                .then((res) => {
                  if (res.code !== 0 || !res.data.id) return;
                  this.query = {};
                  this.$message.success("创建采购商成功");
                  let listQuery = {
                    current: 1,
                    size: 10,
                    model: {
                      approvalStatus: {
                        code: "PENDING",
                      },
                    },
                  };
                  this.$store.dispatch("tagsView/delView", this.$route);
                  setContextData("purchasingAgentlist_detail", listQuery);
                  this.$router.push("/merchant/merchantManage/list");
                })
                .finally(() => {
                  this.submitLoading = false;
                });
            } else {
              if (!this.checkList.length) {
                this.$message.error("请选择经营类目");
                return;
              }
              this.query.invoiceInfoUpdateDTO = invoiceInfoDTO;
              this.query.merchantLicenses = [];
              this.lisenceTableDate.forEach((item) => {
                if (item.filePath != "") {
                  this.query.merchantLicenses.push(item);
                }
              });
              let row = JSON.parse(JSON.stringify(this.query));
              row.businessCategoryIds = this.checkList;
              delete row.deliveryAddressDetailDTOList;
              delete row.businessCategoryDetailList;
              // delete row.merchantLicenses;
              delete row.region;
              delete row.deliveryAddressSaveDTOList;
              delete row.regionId;
              this.submitLoading = true;
              edititem(row)
                .then((res) => {
                  if (res.code !== 0 || !res.data.id) return;
                  this.$message.success("修改采购商成功");
                  let listQuery = {
                    current: 1,
                    size: 10,
                    model: {
                      approvalStatus: {
                        code: "PENDING",
                      },
                    },
                  };
                  this.$store.dispatch("tagsView/delView", this.$route);
                  setContextData("purchasingAgentlist_detail", listQuery);
                  this.$router.push("/merchant/registerCheck");
                })
                .finally(() => {
                  this.submitLoading = false;
                });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getEnterpriseInfo() {
      if (this.$route.query && this.$route.query.purMerchantName) {
        console.log(
          "this.$router.query.purMerchantName",
          decodeURIComponent(this.$route.query.purMerchantName)
        );
        getByCreditName(
          decodeURIComponent(this.$route.query.purMerchantName)
        ).then((res) => {
          if (res.data.length == 0) {
            this.enterpriseDisable = false;
            // 没有查到
            this.query.name = decodeURIComponent(
              this.$route.query.purMerchantName
            );
          } else {
            // 查到了
            res.data.forEach((item) => {
              if (
                item.name ==
                decodeURIComponent(this.$route.query.purMerchantName)
              ) {
                this.enterpriseDisable = true;
                // 客户名称，信用代码，法定代表人，所在区域，注册地址，注册资金，企业编码
                this.query.name = item.name;
                this.query.socialCreditCode = item.creditCode;
                this.query.legalPerson = item.legalPerson;
                this.query.registerAddress = item.registerAddress;
                this.query.registerCapital = item.registerCapital;
                this.query.enterpriseId = item.id;
                this.query.regionId = [
                  item.provinceId,
                  item.cityId,
                  item.countyId,
                ];
                this.query.provinceId = item.provinceId;
                this.query.cityId = item.cityId;
                this.query.countyId = item.cityId;
              }
            });
          }
        });
      }
    },
  },
  components: {
    LisenceTable,
    addrTable,
    MapLocationSelect,
  },
  created() {
    this.getmerchantType();
    this.getareas();
    this.getitem();
    this.getjyfw();
    // console.log('---query-->', this.$route.query);
    // this.query.name = this.$route.query.name;
    // this.query.socialCreditCode = this.$route.query.socialCreditCode;
  },
  mounted() {
    this.getEnterpriseInfo();
  },
};
</script>
<style lang="less" scoped>
.location-select {
  position: absolute;
  top: 40px;
  background: #fff;
  left: 0;
  width: 500px;
  z-index: 999;
  box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.1);
}

.archivesEditContent {
  // margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 0px 20px;
  background-color: #fff;

  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      padding: 0 0 15px;

      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }

    .imgRead {
      margin: 0 auto;
      text-align: center;
      padding-bottom: 20px;

      /deep/ .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        width: 150px;
        height: 150px;
        position: relative;
        overflow: hidden;
        margin-bottom: 20px;
      }

      /deep/ .avatar-uploader .el-upload:hover {
        border-color: #409eff;
      }

      /deep/ .avatar-uploader-icon {
        font-size: 24px;
        color: #8c939d;
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
      }

      /deep/ .avatar {
        width: 150px;
        height: 150px;
        display: block;
      }
    }
  }

  .findCategory {
    padding-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
  }

  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    text-align: left;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;

    .el-button {
      margin-left: 10px;
    }
  }

  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;

    > div {
      min-width: 100%;
      height: 25px;
    }
  }

  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }

  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;

    font-size: 13px;
  }

  .graytext {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
    padding-bottom: 20px;
  }
}
</style>
