<template>
  <div class="product-show detail-wrapper" v-loading="loading">
    <page-title title="商品档案详情">
      <template v-if="detail">
        <el-button @click="onChangeSale" v-if="checkPermission(['admin','sale-saas-self-product:pullOffShelves','sale-platform-self-product:pullOffShelves']) && detail.whetherOnSale === 'Y'">{{ '下架' }}</el-button>
        <el-button @click="onChangeSale" v-if="checkPermission(['admin','sale-saas-self-product:putOnSale',,'sale-platform-self-product:putOnSale']) && detail.whetherOnSale !== 'Y'">{{ '上架' }}</el-button>
        <el-button @click="$router.push({ path: '/productsManagement/edit', query: { id: $route.query.id,marketing:  $route.query.marketing} })" v-if="checkPermission(['admin','sale-saas-self-product:edit',,'sale-platform-self-product:edit'])">编辑</el-button>
      </template>
    </page-title>
    <product-form ref="form" action="SHOW" :areaData="areaData" />
  </div>
</template>

<script>
import request from '@/utils/request'
import ProductForm from './form'
import checkPermission from "../../../utils/permission";

export default {
  components: {
    ProductForm
  },
  data () {
    return {
      loading: false,
      detail: null,
      areaData: [],
      marketing:""
    }
  },

  mounted () {
    this.load();
  //  判断跳转回首营还是分销页面 FRANCHISES分销  DIRECT自营
    this.marketing=this.$route.query.marketing
  },

  methods: {
    checkPermission,
    async load () {
      this.loading = true
      try {
        const areaData = await request.get('authority/area/anno/tree')
        this.areaData = areaData.data
        const { data } = await request.get(`product/admin/product/detail/${this.$route.query.id}`)
        data.expDate = data.skuList[0].expDate
        this.detail = data
        this.$refs.form.restore(data)
      } catch (e) {

      }
      this.loading = false
    },
   async onChangeSale () {
      this.loading = true
     let ids = [this.detail.id];
      let url = this.detail.whetherOnSale === 'Y' ? 'product/admin/product/batchPullOffShelves' : 'product/admin/product/batchPutOnSale'
      try {
        let data = await request.post(url, null, {params: {ids}})
        if(data.code==0 && data.msg == 'ok'){
          this.$message.success('操作成功')
        }
      } catch (e) {

      };
      
     this.loading = false
     this.load()
    }
  }
}
</script>
