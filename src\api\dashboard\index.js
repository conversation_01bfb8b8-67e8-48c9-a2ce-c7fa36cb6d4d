import request from '@/utils/request'

export function index() {
  return request({
    url: "/authority/common/generateId",
    method: 'get',
  })
}

// 待办事项统计
export function getBacklog() {
  return request({
    url: "/product/admin/home/<USER>",
    method: 'get',
  })
}

// 数据概况统计
export function dataProfile(data) {
  return request({
    url: `/product/admin/home/<USER>/${data}`,
    method: 'get',
  })
}

// 商品看板统计
export function goodsCard(data) {
  return request({
    url: `/product/admin/home/<USER>/info`,
    method: 'get',
  })
}

// 订单看板图
export function spectaculars(data) {
  return request({
    url: `/order/admin/orderHome/spectaculars`,
    method: 'post',
    data: {
      endTime: data.endTime + ' 23:59:59',
      naturalWeek: "N",
      orderStatus: data.orderStatus,
      startTime: data.startTime + ' 00:00:00',
    }
  })
}

// 商品看板列表
export function productList(data) {
  return request({
    url: `/order/admin/orderHome/product/list`,
    method: 'post',
    data
  })
}

// 客户看板列表
export function merchantData(data) {
  return request({
    url: `/merchant/admin/merchantHome/spectaculars`,
    method: 'post',
    data
  })
}
// 客户消费排行
export function merchantList(data) {
  return request({
    url: `/merchant/admin/merchantHome/consumerRankings`,
    method: 'post',
    data
  })
}

