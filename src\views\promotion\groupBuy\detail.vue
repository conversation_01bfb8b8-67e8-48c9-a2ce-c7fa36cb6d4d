<template>
  <div class="detail-wrapper">
    <page-title
      :title="id ? (form.activityStatus&&form.activityStatus.code == 'NOT_START' ? '编辑团购活动':'查看团购活动') : '新增团购活动'" :newBack="true">
      <template slot="icon">
        <span
          :class="{'status-box0': form.activityStatus.code == 'NOT_START', 'status-box1': form.activityStatus.code == 'PROCEED', 'status-box2': form.activityStatus.code == 'OBSOLETE', 'status-box3': form.activityStatus.code == 'FINISHED'}"
          v-if="this.form.activityStatus"></span>
      </template>
      <template>
        <template v-if="!id || (form.activityStatus&&form.activityStatus.code == 'NOT_START')">
          <el-button type="primary" @click="submit">保存</el-button>
        </template>
        <template v-if="form.activityStatus&&form.activityStatus.code == 'PROCEED'">
          <el-button type="primary" @click="handleState">作废</el-button>
        </template>
      </template>
    </page-title>
    <el-form label-width="120px" ref="ruleForm" :model="form" :rules="rules">
      <div class="detail-items" v-loading="loading">
        <page-module-card title="基础信息">
          <el-form-item label="活动名称：" prop="activityName" :rules="[{ required: true, message: '请输入活动名称' }]"
            style="width:370px;">
            <el-input v-model="form.activityName" placeholder="请输入活动名称，最多20个字"
              :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'"></el-input>
          </el-form-item>
          <el-form-item label="活动时间：" prop="during" :rules="[{ required: true, message: '请选择活动时间', trigger: 'blur' }]">
            <el-date-picker type="datetimerange" @input="dataPickerChange" range-separator="至" v-model="form.during"
              value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动商品：" prop="productList">
            <div v-if="!id || (form.activityStatus&&form.activityStatus.code == 'NOT_START')">
              <el-button type="primary" @click="openProducts()">选择商品</el-button>
            </div>
            <div v-if="indexResultsTable.length > 0" v-loading="loading">
              <el-table style="margin-top:15px;" :data="indexResultsTable" border class="product-table">
                <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
                <el-table-column label="主图" align="center" width="80" class-name="img-cell">
                  <template slot-scope="{row}">
                    <img :src="splitString(row.pictIdS)" width="50px" v-if="row.pictIdS">
                    <img :src="pictImg" width="50px" v-if="row.pictIdS == null || row.pictIdS == ''">
                  </template>
                </el-table-column>
                <el-table-column label="ERP商品编码/仓库" prop="productCode" width="200px">
                  <template v-slot="{row}">
                    <span>编码：{{row.erpCode || '无'}}</span> <br />
                    <span>仓库：{{ row.warehouseName || '无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="productName" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="规格" prop="spec" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="生产厂家" prop="manufacturer" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="销售价" prop="salePrice" width="100" />
                <el-table-column label="最低起订量" prop="minBuyQuantity" width="100" />
                <el-table-column label="成本价" prop="costPrice" width="100" />
                <el-table-column label="库存" prop="stockQuantity" width="100" />
                <el-table-column label="操作" prop="stockQuantity" width="100" fixed="right" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="deleteRowGoods(scope)"
                      :disabled="(form.activityStatus && form.activityStatus.code !== 'NOT_START')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item label="团购价：" prop="groupBuyPrice" :rules="[{ required: true, message: '请输入团购价' }]"
            style="width:370px;">
            <el-input v-model="form.groupBuyPrice" placeholder="请输入团购价"
              :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'"></el-input>
          </el-form-item>
          <el-form-item label="目标采购量：" prop="startBuyNumber" :rules="[{ required: true, message: '目标采购量' }]"
            style="width:370px;">
            <el-input v-model="form.startBuyNumber" placeholder="目标采购量"
              :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'"></el-input>
          </el-form-item>
        </page-module-card>
        <page-module-card title="活动规则">
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{  required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType"
              :disabled="form.activityStatus && form.activityStatus.code !== 'NOT_START'">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                </el-radio>
                <el-checkbox-group style="margin-top: 10px;" v-model="form.merchantTypeIds"
                  v-if="merchantList.length > 0 && form.limitObjectType === 'CUSTOMER_TYPE' ">
                  <el-checkbox v-for="(item) in merchantList" :key="item.id" :label="item.id">
                    {{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
<!--              <div class="block-radio">-->
<!--                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>-->
<!--                <div style="margin: 15px 0;" v-if="form.limitObjectType == 'CUSTOMER_GROUP'">-->
<!--                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>-->
<!--                </div>-->
<!--                <el-table v-if="groupTableData && form.limitObjectType == 'CUSTOMER_GROUP' " :data="groupTableData"-->
<!--                  border>-->
<!--                  <el-table-column prop="name" label="客户分组" width="234" />-->
<!--                  <el-table-column prop="customerNumber" label="客户数量" width="120" />-->
<!--                  <el-table-column label="操作" width="52">-->
<!--                    <template slot-scope="scope">-->
<!--                      <el-button @click="deleteRow(scope.$index)" type="text">删除</el-button>-->
<!--                    </template>-->
<!--                  </el-table-column>-->
<!--                </el-table>-->
<!--              </div>-->
              <div class="block-radio">
                <el-radio label="CUSTOMER_TAG">指定客户标签可参与</el-radio>
                <div v-if="form.limitObjectType == 'CUSTOMER_TAG'" style="margin: 15px 0">
                  <el-checkbox-group v-model="form.customerLabelIds">
                    <el-checkbox v-for="item in customerTag" :key="item.value" :label="item.value">{{ item.text }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
      <div v-if="productVisible">
        <products-modal :productVisible="productVisible" ref="products-modal"  @closeProductDia="closeProductDia" @change="handleAddProducts" :checkList="goodsList" :limit="1"></products-modal>
      </div>
    <add-group :visible="showAdd" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser" :select-data="groupTableData" />
  </div>
</template>

<script>
  import request from '@/utils/request'
  import addGroup from '../components/addGroup.vue'
  import ProductsModal from "@/components/eyaolink/productModel/index"
  import {
    merchantGroupListNew
  } from "@/api/group";
  import {
    postGoodsList,
    detail,
    updatePromotionPackageState,
    getCheckList
  } from "@/api/limitTime"
  import productImg from "@/assets/product.png";

  import {
    setVoid,
    getGroupBuyInfo,
    addGroupBuy,
    editGroupBuy,
    searchRepeatProductGroup
  } from '@/api/promotionCenter/index'
  import { page } from "@/api/merchantApi/customerlabel";

  export default {
    components: {
      ProductsModal,
      addGroup,
    },
    data() {
      var validateList = (rule, value, callback) => {
        if(value.length==0){
          return callback(new Error('商品列表不能为空'));
        } else {
          callback();
        }
      };
      return {
        quitShow:false,
        pictImg: productImg,
        activeType:'groupBuy',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        },
        id: '',
        loading: false,
        showAdd: false,
        saleMerchantId: '', //经销商id
        active: 0,
        merchantList: [], // 客户类型列表
        groupTableData: [], // 已选分组数据
        form: {
          during: [],
          activityName: '',
          limitBuyNum: '',
          limitBuyType: 'N',
          groupBuyPrice: '', //团购价
          startBuyNumber: '', // 目标采购量
          limitObjectType: 'NONE', //指定人类型 NONE,CUSTOMER_TYPE,CUSTOMER_GROUP,CUSTOMER,UN_CUSTOMER
          merchantTypeIds: [], //客户类型
          productRelList: [],
          merchantGroupIds: [], //分组集合
          productList:[],
          customerLabelIds: []
        },
        rules: {
          productList: [{required: true, validator: validateList, trigger: 'change'}]
        },
        groupBuyProductRelSaveDTOList: [],
        total: 0,
        goodsList: [],
        currentPage: 1, //初始页
        pagesize: 10,
        indexResultsTable: [],
        selectionArr: [],
        notSearchId: '',
        checkedArr: [],
        productVisible: false, // 选择商品弹窗是否显示
        timer: null,
        customerTag: [],
        confirmBackStatus: false
      }
    },
    created() {
      this.id = this.$route.query.id;
      this.getMerchantType()
      if (this.id) {
        this.getDetail()
      };
      this.fetchMerchantLabels();
    },
    watch:{
      indexResultsTable(val){
        if(val.length>0){
          this.form.productList = this.indexResultsTable;
        } else {
          this.form.productList = [];
        }
      }
    },
    methods: {
      async fetchMerchantLabels() {
        let { code, data } = await page({ current: 1, page: 200, model: {} });
        if (code === 0) {
          this.customerTag = data.records.map(v => ({
            text: v.tagName,
            value: v.id
          }))
        }

      },
      //前端自己分页
      getResultsTable: function () {
        // es6过滤得到满足搜索条件的展示数据list
        let self = this
        let list = this.goodsList
        //表格渲染的数据  indexResultsTable:[],
        this.indexResultsTable = list.filter((item, index) =>
          index < this.currentPage * this.pagesize && index >= this.pagesize * (this.currentPage - 1)
        ) //根据页数显示相应的内容
        this.total = list.length;

      },
      dataPickerChange(e){

        this.$set(this.form, 'during', e);
        this.$forceUpdate();
      },
      //作废
      async handleState() {
        let param = {
          id: this.id
        }
        const data = await setVoid(param)
        if (data.code === 0) {
          this.$message.success('作废操作成功！')
          this.getDetail()
        }
      },
      //打开商品
      openProducts() {
        if(this.form.during.length < 2) {
          this.$message.warning('请先选择活动时间');
          return
        }
        this.productVisible = true;
        this.form.productRelList = this.goodsList
        if (this.id) {

          this.notSearchId = this.id
        } else {
          this.notSearchId = ''
        }

      },
      closeProductDia(){
        this.productVisible = false;
      },
      checkBoxChange(row) {
        if (row.main) {
          row.main = true
        } else {
          row.main = false
        }
      },
      // 获取详情
      async getDetail() {
        this.loading = true
        if (this.id) {
          let {
            data
          } = await getGroupBuyInfo(this.id);
          this.form = {...data};
          this.loading = false;
          this.$nextTick(() => {
            this.$set(this.form, 'during', [data.activityStartTime, data.activityEndTime]);
          });
          this.form.limitObjectType = data.limitObjectType.code;
          this.form.during = [data.activityStartTime, data.activityEndTime];
          this.form.productRelList = [data.product];
          this.goodsList = [data.product];
          if (data.merchantTypeIds === null) {
            this.form.merchantTypeIds = [];
          }
          if (data.merchantGroupIds === null) {
            this.form.merchantGroupIds = [];
          }
          
          if (!Boolean(this.form.customerLabelIds)) {
            this.form.customerLabelIds = [];
          }
          if (data.merchantGroupIds) {
            this.getGroupList();
          }
          if (this.goodsList.length > 0) {
            this.getCheckGoodsList()
          }
          if (data.merchantTypeIds) {

          }
        }
      },
      // 获取已有客户分组列表
      async getGroupList() {
        this.loading = true;
        const query = {
          model: {}
        }
        const {
          data
        } = await merchantGroupListNew(query);
        this.loading = false;
        this.groupTableData = data.records.filter((items) => {
          if (this.form.merchantGroupIds.includes(items.id)) {
            return {
              items
            };
          }
        })
      },
      // 获取已有的产品列表
      async getCheckGoodsList(ids) {
        const formData = new FormData()
        let idArr = ids ? ids : this.form.productRelList.map((items) => items.productId)
        formData.append('ids', idArr.toString())
        let {
          data
        } = await postGoodsList(formData)
        this.goodsList = []
        this.goodsList = data.filter((items, index) => {
          if (idArr.includes(items.id)) {
            if (this.form.productRelList.length === 0) {
              this.$set(items, 'promotionPrice', undefined)
              this.$set(items, 'reduced', undefined)
              this.$set(items, 'discount', undefined)
              this.$set(items, 'selected', false)
            } else {
              if (this.form.productRelList.length - 1 < index) {
                this.$set(items, 'reduced', '-')
                this.$set(items, 'discount', '-')
                this.$set(items, 'promotionPrice', undefined)
                this.$set(items, 'selected', false)
              } else {
                this.$set(items, 'promotionPrice', this.form.productRelList[index].promotionPrice)
                this.$set(items, 'reduced', this.form.productRelList[index].reduced)
                this.$set(items, 'discount', this.form.productRelList[index].discount)
              }

            }
            return items;
          }
        })
        this.loading = false
        this.getResultsTable()
      },
      // 清空表单
      resetForm() {
        this.$refs['ruleForm'].resetFields();
      },
      conflictTest() {
        return new Promise((resovle, reject) => {
          let params = {
            'endTime': this.form.during[1],
            'startTime': this.form.during[0],
            'productIds[]': this.goodsList[0].id
          };
          searchRepeatProductGroup(params).then(res => {
            console.log('res');
            if (res.code == 0 && res.msg == 'ok') {
              if (res.data) {
                resovle(true)
              } else {
                resovle(false)
              }
            } else {
              resovle(false)
            }
          }).catch(rej => {
            console.log('rej');
            resovle(false)
          }).finally(reason=>{
            resovle(false)
            console.log('reason');
          })
        })
      },
      // 提交表单
      submit() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            if (this.goodsList.length === 0) {
              this.$message.warning('请选择活动商品！')
              return false
            }

            if (this.goodsList.length > 0) {
              this.groupBuyProductRelSaveDTOList = this.goodsList.map((item) => {
                return {
                  productName: item.productName,
                  productId: item.id
                }
              })
            }
            // 选择指定客户类型
            if (this.form.limitObjectType === 'CUSTOMER_TYPE') {
              this.form.merchantGroupIds = []
            }
            if (this.form.limitObjectType === 'CUSTOMER_GROUP' || this.form.limitObjectType === 'NONE') {
              this.form.merchantTypeIds = []
            }
            if (this.form.limitBuyType === 'N') {
              this.form.limitBuyNum = ''
            }
            // 是否有id
            if (this.id) {
              this.form.id = this.id
            } else {
              delete this.form.id
            }
            this.loading = true;
            let param = {
              ...this.form,
              groupBuyProductRelSaveDTOList: this.groupBuyProductRelSaveDTOList,
              activityStartTime: this.form.during[0],
              activityEndTime: this.form.during[1],
            }
            delete param.during
            delete param.productRelList
            this.quitShow = true;
            this.conflictTest().then(res => {
              if(res){
                let method = this.id ? editGroupBuy(param) : addGroupBuy(param)
                method.then((res) => {
                  if (res.code == 0) {
                    this.loading = false;
                    this.$message.success('保存成功')
                    if(!this.id) {
                      sessionStorage.setItem('groupBuy','123')
                    }
                    this.timer = setTimeout(() => {
                      this.$router.push({
                        path: '/promotion/groupBuy'
                      })
                    }, 500)
                  } else {
                    this.loading = false;
                    this.$message.error(res.msg)
                  }
                }).catch(() => {
                  this.loading = false;
                })
              } else {
                this.loading = false;
              }
            })

          } else {
            this.loading = false;
          }
        });
      },
      // 删除选中客户组
      deleteRow(i) {
        this.groupTableData.splice(i, 1)
      },
      async deleteRowGoods(scope) {
        let index = this.goodsList.findIndex((item) => item.id === scope.row.id)
        this.goodsList.splice(index, 1)
        if (JSON.parse(JSON.stringify(this.indexResultsTable)).length === 1 && JSON.parse(JSON.stringify(this
            .goodsList)).length > 1) {
          this.currentPage = 1
        }
        this.getResultsTable()
        this.$refs['products-modal'].isSelect = true;
      },
      // 改变客户分组回调
      changeAddUser(data) {
        this.groupTableData = data;
        this.form.merchantGroupIds = data.map((items) => items.id)
        this.showAdd = false
      },
      // 选择商品后回调
      async handleAddProducts(list) {
        let query = {
          'productIds[]':list.toString(),
          'endTime': this.form.during[0],
          'startTime': this.form.during[1],
        };
        searchRepeatProductGroup(query).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.getCheckGoodsList(list)
          }
        })
      },
      async postCouponList(params) {
        let listQuery = {
          model: {
            limitTimeDiscountId: this.id
          }

        }
        Object.assign(listQuery, params)
        const data = await getCheckList(listQuery)
        this.goodsList = data.records

        return data
      },
      // 获取客户类型
      async getMerchantType() {
        const {
          data
        } = await request.get('merchant/admin/merchantType', {})
        this.merchantList = data
      },
      splitString(val) {
        return String(val).split(',')[0]
      },
      // 增加一条历史记录
      pushHistory() {
        window.history.pushState(null, '')
      },
      // 退出路由
      backRoute() {
        const visitedViews = this.$store.getters.visitedViews
        if (!Array.isArray(visitedViews) || Array.isArray(visitedViews) && visitedViews.length === 0) {
          this.$router.replace({ path: '/' })
          return
        } 
        let pushRouteIndex = visitedViews.length - 1
        if (visitedViews[pushRouteIndex].fullPath === this.$route.fullPath) pushRouteIndex -= 1
        this.$router.replace({ path: visitedViews[pushRouteIndex].fullPath })
      },
      // 监听返回
      confirmBack() {
        if (this.quitShow) { this.backRoute(); return; }
        this.$confirm('确定退出吗？如果退出，您输入的内容将不会被保存', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.confirmBackStatus = true
          this.backRoute()
        }).catch(() => {
          this.pushHistory()
        });
      },
      // 移除返回监听
      removeEventListenerPopstate() {
        window.removeEventListener('popstate', this.confirmBack)
      },
      // 监听返回
      addEventListenerPopstate() {
        this.pushHistory()
        window.addEventListener('popstate', this.confirmBack)
      }
    },
    beforeRouteLeave(to, from, next) {
      if(this.quitShow || this.confirmBackStatus) {
        next();
      } else {
        this.$confirm('确定退出吗？如果退出，您输入的内容将不会被保存', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          next()
        }).catch(() => {});
      }
    },
    mounted() {
      this.addEventListenerPopstate()
      this.$once('hook:beforeDestroy', () => {
          this.removeEventListenerPopstate()
      })
    },
    beforeDestroy() {
      clearTimeout(this.timer);
    }
  }
</script>

<style lang="scss">
  .product-table {
    .el-input-number.is-controls-right .el-input__inner {
      padding-right: 15px;
    }
  }

  .limit-form .el-form-item {
    margin-bottom: 0;
  }

  .detail-wrapper {
    .el-pager li {
      width: 0;
    }

    .page-row-left {
      float: left;
    }

    .page-row-leftBlock {
      display: flex;

      .selectBlock {
        display: block;
        width: 14px;
        height: 14px;
        margin-right: 8px;

        img {
          width: 14px;
        }
      }
    }

    .page-row-right {
      float: right;
    }

    .status-box0 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box1 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box2 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/Icon_Revok.png') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box3 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    background: #f7f7f8;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 20px;
    color: #8c939d;
    line-height: 100px;
    text-align: center;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .detail-item {
    border: none !important;
  }

  .detail-tit {
    display: flex;
    align-items: center;
    margin: 0;
  }

  .inline-input {
    width: 80px;
    margin: 0 10px;
  }

  .inline-item {
    display: inline-block;
  }

  .block-radio {
    margin-bottom: 16px;

    &-top {
      margin-top: 11px;
    }

    &-none {
      margin: 0;
    }
  }

  .detail {
    &-header {
      // width: 100%;
      margin: 0 12px;
      padding: 19px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ddd;

      p {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  .tips {
    color: #999999;
    margin: 0;

    &-btn {
      border: none;
      margin: 0;
      padding: 0;
    }
  }

  .no-button {

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
  }
</style>
