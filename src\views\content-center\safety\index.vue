<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="title">
        <el-input v-model.trim="model.title" @keyup.enter.native="searchLoad" placeholder="请输入标题" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="tabs">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'sale-saas-content-manage-safety:add','sale-platform-content-manage-safety:add'])" type="primary" @click="toInfoPageAdd()">+ 新增安全协议</el-button>
        </template>
      </tabs-layout>
      <!--分页table-->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="false" :pageSize="pageSize" :operation-width="120">

        <el-table-column label="协议类型" width="150" slot="protocolType">
          <slot slot-scope="{row}">
            {{ row.protocolType && row.protocolType.desc }}
          </slot>
        </el-table-column>
        <el-table-column label="应用类型" width="150" slot="terminalType">
          <slot slot-scope="{row}">
            {{ row.terminalType && row.terminalType.desc }}
          </slot>
        </el-table-column>

        <el-table-column label="是否发布" width="200" slot="publishStatus">
          <slot slot-scope="{row}">
            <el-switch v-model="row.publishStatus" active-text="是" inactive-text="否" @change="switchChange(row)">
            </el-switch>
          </slot>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'sale-saas-content-manage-safety:edit','sale-platform-content-manage-safety:edit'])" type="text" @click="toInfoPageFun(row.id)">编辑</el-button>
              <del-el-button v-if="checkPermission(['admin', 'sale-saas-content-manage-safety:del','sale-platform-content-manage-safety:del'])" style="margin-left:5px" :targetId="row.id" :text="delText" @handleDel="delSalesman">
              </del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>


<script>
import delElButton from "@/components/eyaolink/delElButton";
  const TableColumns = [{
      label: "协议类型",
      name: "protocolType",
      prop: "protocolType",
      width: "150",
      slot: true
    },
    {
        label: "应用类型",
      name: "terminalType",
      prop: "terminalType",
      width: "150",
      slot: true
    },
    {
      label: "标题",
      name: "title",
      prop: "title",
    },
    {
      label: "是否发布",
      name: "publishStatus",
      prop: 'publishStatus',
      width: "200",
      slot: true
    },
    {
      label: "操作人",
      name: "userName",
      prop: 'userName',
      width: "150"
    },
    {
      label: "操作时间",
      name: "updateTime",
      prop: 'updateTime',
      width: "200"
    }
  ]
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import {
    securityProtocolPage,
    batchPublishStatus,
    securityProtocolDelete
  } from "@/api/content-center/safety/index"
  import checkPermission from '@/utils/permission';
  export default {
    //import引入的组件
    components: {
        delElButton
    },

    data() {
      return {
        isExpand: false,
        tableData: [],
        pageSize: 10,
        delText: '您确定删除该安全协议吗？',
        tableColumns: TableColumnList,
        model: {
          title: '',
        }
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},

    computed: {
      tabs() {
        return [{
          name: '安全协议'
        }]
      }
    },
    //方法集合
    methods: {
      checkPermission,
      // 获取数据
      async load(params) {
        let listQuery = {
          model: this.model
        }
        Object.assign(listQuery, params)
        // TODO 替换成对应用的列表api
        let detail = await securityProtocolPage(listQuery);
        let list = JSON.parse(JSON.stringify(detail.data.records));
        list = list.map(item=>{
            return Object.assign(
                {},
                item,
                {
                    publishStatus:(item.publishStatus && item.publishStatus.code == 'Y') ? true : false
                }
            )
        });
        console.log('list----->',list);
        detail.data.records = list;
        return detail
      },
    //   是否发布
      switchChange(row){
          let params = {
              'ids[]': row.id,
              publishStatus: row.publishStatus ? 'Y' : "N"
          };
          batchPublishStatus(params).then(res=>{
              if(res.code == 0 && res.msg == 'ok') {
                  this.$message.success('修改状态成功');
                  this.reload();
              }
          })
      },
      // 新增安全协议
      toInfoPageAdd() {
          this.$router.push('/content-center/safety/edit');
      },
      //   编辑
      toInfoPageFun(id) {
          this.$router.push({
              path:'/content-center/safety/edit',
              query:{
                  id
              }
          })
      },
      // 删除
      delSalesman(id) {
        securityProtocolDelete(id).then(res=>{
          if(res.code == 0 && res.msg == "ok") {
            this.$message.success('删除成功');
            this.reload();
          }
        })
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
    },

  }

</script>


<style lang='scss' scoped>

</style>
