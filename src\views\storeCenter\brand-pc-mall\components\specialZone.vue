<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" @confirm="confirm">
    <div class="dialog-tips">
      <span v-if="type === 'advert-lt'">建议尺寸：293*410px，支持JPG或PNG格式图片，大小不超过2M</span>
      <span v-else>建议尺寸：292*200px，支持JPG或PNG格式图片，大小不超过2M</span>
    </div>
    <el-table
      ref="tableData"
      :data="tableData"
      border
    >
      <el-table-column label="序号" type="index" width="54" align="center" />
      <el-table-column label="图片" prop="name" width="200">
        <template slot-scope="scope">
          <div class="file-list" :class="{ 'file-length': scope.row.fileList.length>=1 }">
            <el-upload
              class="upload-demo-table"
              :action="$uploadUrl"
              :limit="1"
              :file-list="scope.row.fileList"
              :headers="headersProgram"
              :data="insertProgram"
              :on-change="handleChange"
              :on-success="uploadSuccess"
              accept=".jpg,.png"
              :before-upload="beforeUpload"
            >
              <el-button type="text" @click="fileIndex(scope.$index)">上传图片</el-button>
            </el-upload>
            <div class="upload-delete" @click="handleRemove(scope.row)"><i class="el-icon-close" /></div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="链接类型" prop="linkType" width="180">
        <template slot-scope="scope">
          <el-select v-model="scope.row.linkType" placeholder="请选择链接类型" @change="changeLinkFun($event, scope.$index, scope.row)">
            <el-option
              v-for="itemInfo in selectList"
              :key="itemInfo.code"
              :label="itemInfo.name"
              :value="itemInfo.code"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="链接目标" prop="linkUrl" min-width="300">
        <template slot-scope="scope">
          <el-input v-model="scope.row.linkUrl" placeholder="链接目标">
            <ProductItemTable v-if="scope.row.linkType==='PRODUCT_DETAIL'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <StoreListTable v-if="scope.row.linkType==='STORE_INDEX'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <ProductTypeItemTable v-if="scope.row.linkType==='CATEGORYTYPE_LIST'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <HelpCenter v-if="scope.row.linkType==='HELP_CENTER'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
          </el-input>
          <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
            </span>
            <!-- <span class="table-edit-row-item">
              <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
            </span> -->
          </el-row>
        </template>
      </el-table-column>
    </el-table>
  </im-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { advUpdate } from './index'
import { query } from '@/api/setting/data/dictionaryItem'

import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import HelpCenter from '@/components/eyaolink/help'

export default {
  name: 'MallNavigation',
  components: {
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    HelpCenter
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '1000px'
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visibleDialog: false,
      tableData: [],
      fileList: [],
      headersProgram: {
        token: `Bearer ${getToken()}`,
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0'
      },
      insertProgram: {
        folderId: 0
      },
      scopeIndex: ''
    }
  },
  methods: {
    init(data) {
      this.tableData = []
      const _that = this
      setTimeout(function() {
        if (_that.type === 'advert-lt') {
          if (!data.PC_ADV_DISCOUNT_LT || data.PC_ADV_DISCOUNT_LT.length === 0) {
            _that.tableData.push({
              linkUrl: '',
              linkType: '',
              sortValue: 1,
              fileList: []
            },
            {
              linkUrl: '',
              linkType: '',
              sortValue: 2,
              fileList: []
            })
          } else {
            for (let i = 0; i < 2; i++) {
              if (data.PC_ADV_DISCOUNT_LT[i]) {
                _that.tableData.push({
                  fileList: [{ name: data.PC_ADV_DISCOUNT_LT[i].name, response: { data: { url: data.PC_ADV_DISCOUNT_LT[i].picUrl }}}],
                  linkType: data.PC_ADV_DISCOUNT_LT[i].linkType,
                  sortValue: data.PC_ADV_DISCOUNT_LT[i].sortValue,
                  linkUrl: data.PC_ADV_DISCOUNT_LT[i].linkUrl
                })
              } else {
                _that.tableData.push({
                  linkUrl: '',
                  sortValue: i + 1,
                  fileList: []
                })
              }
            }
            // _that.tableData = data.PC_ADV_DISCOUNT_LT.map(val => {
            //   val.fileList = [{ name: val.name, response: { data: { url: val.picUrl }}}]
            //   return val
            // })
          }
        } else {
          if (!data.PC_ADV_DISCOUNT_RT || data.PC_ADV_DISCOUNT_RT.length === 0) {
            for (let i = 0; i < 4; i++) {
              _that.tableData.push({
                linkUrl: '',
                sortValue: i + 1,
                fileList: []
              })
            }
          } else {
            for (let i = 0; i < 4; i++) {
              if (data.PC_ADV_DISCOUNT_RT[i]) {
                _that.tableData.push({
                  fileList: [{ name: data.PC_ADV_DISCOUNT_RT[i].name, response: { data: { url: data.PC_ADV_DISCOUNT_RT[i].picUrl }}}],
                  linkType: data.PC_ADV_DISCOUNT_RT[i].linkType,
                  sortValue: data.PC_ADV_DISCOUNT_RT[i].sortValue,
                  linkUrl: data.PC_ADV_DISCOUNT_RT[i].linkUrl
                })
              } else {
                _that.tableData.push({
                  linkUrl: '',
                  linkType: '',
                  sortValue: i + 1,
                  fileList: []
                })
              }
            }
            // _that.tableData = data.PC_ADV_DISCOUNT_RT.map(val => {
            //   val.fileList = [{ name: val.name, response: { data: { url: val.picUrl }}}]
            //   return val
            // })
          }
        }
        _that.visibleDialog = true
      }, 10)
    },
    change(e) {
      this.$forceUpdate()
    },
    confirmLink(row) {
      let index = 0
      if (row.linkUrl.lastIndexOf('=') === -1) {
        index = row.linkUrl.lastIndexOf('/')
      } else {
        index = row.linkUrl.lastIndexOf('=')
      }
      const str = row.linkUrl.substring(index + 1, row.linkUrl.length)
      row.linkUrl = row.linkUrl.replace(str, row.linkParam)
    },
    changeLinkFun(val, index, row) {
      row.linkParam = ''
      this.selectList.map(item => {
        if (item.code === val) {
          row.linkUrl = item.describe
        }
      })
    },
    confirm() {
      let valid = true
      const data = []
      this.tableData.map(val => {
        if (!val.fileList[0]) {
          valid = false
        } else {
          data.push({
            linkUrl: val.linkUrl,
            linkType: val.linkType,
            linkParam: val.linkParam,
            sortValue: val.sortValue,
            picUrl: val.fileList[0].response.data.url,
            name: val.fileList[0].name,
            type: this.type === 'advert-lt' ? 'PC_ADV_DISCOUNT_LT' : 'PC_ADV_DISCOUNT_RT'
          })
        }
      })
      if (valid) {
        advUpdate(data).then(res => {
          this.$message.success('编辑广告成功')
          this.$emit('confirm')
          this.visibleDialog = false
        })
      } else {
        this.$message.error('请先上传图片！')
      }
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    },
    handleRemove(row) {
      const rowIndex = this.tableData.indexOf(row)
      const data = []
      this.tableData.map((val, index) => {
        if (index === rowIndex) {
          val.fileList = []
        }
        data.push(val)
      })
      this.tableData = data
    },
    handleChange(file, fileList) {
      this.tableData[this.scopeIndex].fileList = fileList
    },
    fileIndex(index) {
      this.scopeIndex = index
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 2MB!',
          type: 'warning'
        })
        return false
      }
    },
    uploadSuccess(res, file) {
      const data = []
      this.tableData.map((val, index) => {
        data.push(val)
      })
      this.tableData = data
    }
  }
}
</script>

<style lang="less">
  .upload-delete{
    display: none
  }

  .file-length{
    position: relative;
    .el-upload--text{
      display: none;
    }
    .el-icon-document{
      display: none;
    }
    .upload-delete{
      position: absolute;
      right: 5px;
      top: 4px;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: #979797;
      color: rgba(235,236,238,1);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      font-size: 12px;
    }
    .el-progress__text{
      display: none;
    }
  }

</style>
