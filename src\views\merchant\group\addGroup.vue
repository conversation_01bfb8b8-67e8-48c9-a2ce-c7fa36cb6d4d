<template>
<div>
  <el-dialog v-dialogDrag :title="title" :visible.sync="addVisible" width="25%" @close="resetForm('groupForm')">
    <el-form ref="groupForm" :model="groupForm" :rules="rules"  label-width="80px">
      <el-form-item label="分组名称" prop="name">
        <el-input v-model="groupForm.name" placeholder="请输入分组名称"></el-input>
      </el-form-item>
      <el-form-item label="分组编码">
        <el-input v-model="groupForm.code" placeholder="请输入分组编码"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="resetForm('groupForm')">取 消</el-button>
      <el-button type="primary" @click="onSubmit('groupForm')">确定</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import { addGroup, editGroup } from '@/api/group'

export default {
  name: "addGroup",
  props:['groupVisible','title','detail','groupType'],
  data() {
    return {
      addVisible: false,
      id: '',
      groupForm: {
        code: '',
        name: '',
      },
      rules: {
        name: [
          {required: true, message: '请输入分组名称', trigger: 'blur'},
          {min: 1,max: 20,message: '分组名称不超过20个字符',trigger: 'blur'}
        ],
        /*priceModulus: [{
          required: true, message: '请输入价格系数', trigger: 'blur'
        }]*/
      },
    }
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.addVisible = false
      this.$emit('changeShow','false')
    },
    onSubmit(formName) {
      
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.id) {
            let query = {
              code: this.groupForm.code,
              groupType: this.groupType,
              name: this.groupForm.name,
              id: this.id
            }
            editGroup(query).then(res=>{
              this.$message.success(this.title + '分组成功!')
              this.addVisible = false
              this.$emit('load',false)
            })
          } else {
            let query = {
              code: this.groupForm.code,
              groupType: this.groupType,
              name: this.groupForm.name,
            }
            addGroup(query).then(res => {
              this.$message.success(this.title + '分组成功!')
              this.addVisible = false
              this.$emit('load', false)
            })
          }
        } else {
          return false;
        }
      });
    }
  },
  watch: {
    groupVisible(val) {
      this.addVisible = this.groupVisible;
      if(val){
        this.groupForm = {...this.detail};
        this.id = this.detail.id
      } else {
        this.groupForm = {
          code: '',
          name: '',
        };
      }
    }
  }
}
</script>

<style scoped>

</style>
