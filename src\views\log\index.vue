<template>
  <div>
    <div class="cards">
      <div class="item" v-for="(item, index) in logFailData" :key="index">
        <p><span>{{ item.dockProject.desc }}推送失败</span></p>
        <div class="data">
          <div class="text">{{ item.failCount }}</div>
          <el-button size="mini" :type="getScreenListIncludesItemIndex(item) === -1 ? 'primary': 'danger'" @click="filterFailData(item)">
            {{ getScreenListIncludesItemIndex(item) === -1 ? '筛选' : '取消' }}
          </el-button>
        </div>
      </div>
    </div>
    <im-search-pad :model="listQuery" :hasExpand="false" @reset="onReset" @search="onSearch">
      <im-search-pad-item prop="timeDuring">
        <el-date-picker
          v-model="timeDuring"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="businessNoOrErpNo">
        <el-input v-model.trim="listQuery.businessNoOrErpNo" placeholder="ERP编码/电商编码" clearable />
      </im-search-pad-item>
      <im-search-pad-item prop="dockStatus">
        <el-select v-model="listQuery.dockStatus" placeholder="对接状态" clearable>
          <el-option v-for="item in dockStatusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="tabList" v-model="listQuery.dockProject" @change="onSearch">
        <template slot="button">
          <el-button icon="el-icon-refresh" @click="onReset">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :isNeedButton="false" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <template slot="dockProject">
          <el-table-column label="对接项目">
            <slot slot-scope="{row}">
              <span v-text="row.dockProject.desc" />
            </slot>
          </el-table-column>
        </template>
        <template slot="dockStatus">
          <el-table-column label="对接状态">
            <slot slot-scope="{row}">
              <span :style="{color: ['PUSH_SUCCESS', 'RECEIVE_SUCCESS'].includes(row.dockStatus.code) ? '#04CE90' : '#D9001B'}" v-text="row.dockStatus.desc" />
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { getLogList, getLogFailData } from '@/api/dockLog';

const TableColumns = [
  { label: "对接项目", prop: 'dockProject', name: 'dockProject', slot: true },
  { label: '电商编码', prop: 'businessNo' },
  { label: 'ERP编码', prop: 'erpNo' },
  { label: '对接状态', prop: 'dockStatus', name: 'dockStatus', slot: true },
  { label: '对接时间', prop: 'createTime' },
  { label: '失败原因', width: 300, prop: 'failReason' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
const initListQueryModel = () => {
  return {
    businessNoOrErpNo: undefined,
    dockProject: undefined,
    dockStatus: undefined,
    screenList: [],
    startTime: undefined,
    endTime: undefined,
  }
}
export default {
  name: 'Log',
  data() {
    return {
      dockStatusList: [
        { label: '推送成功', value: 'PUSH_SUCCESS' },
        { label: '推送失败', value: 'PUSH_FAIL' },
        { label: '接收成功', value: 'RECEIVE_SUCCESS' },
        { label: '接收失败', value: 'RECEIVE_FAIL' }
      ],
      remoteLoading: false,
      tableTitle: TableColumnList,
      tabList: [
        { name: '全部', value: undefined },
        { name: '订单', value: 'ORDER' },
        { name: "采购商", value: 'PUR_MERCHANT' },
        { name: '业务员', value: 'SALESMAN' },
        { name: '商品', value: 'PRODUCT' },
        { name: '库存', value: 'SKU' },
        { name: '发货单', value: 'DELIVERY' },
        { name: '退货单', value: 'SALES_RETURN' }
      ],
      logFailData: [],
      tableData: [],
      listLoading: false,
      timeDuring: [],
      listQuery: initListQueryModel()
    }
  },
  methods: {
    // 获取列表和失败数据列表
    async load(params) {
      this.listLoading = true
      if (Array.isArray(this.timeDuring) && this.timeDuring.length === 2) {
        this.listQuery.startTime = this.timeDuring[0] + ' 00:00:00'
        this.listQuery.endTime = this.timeDuring[1] + ' 23:59:59'
      } else {
        this.listQuery.startTime = undefined
        this.listQuery.endTime = undefined
      }
      const queryData = { model: this.listQuery }
      Object.assign(queryData, params);
      getLogFailData(queryData).then(res => {
        this.logFailData = res.data
      })
      return await getLogList(queryData)
    },
    // 当前搜索条件是否包含失败的item
    getScreenListIncludesItemIndex(it) {
      const { screenList } = this.listQuery
      const index = screenList.findIndex(item => item.dockProject.code === it.dockProject.code)
      return index
    },
    // 过滤失败数据集
    filterFailData(it) {
      const { screenList } = this.listQuery
      const index = this.getScreenListIncludesItemIndex(it)
      index === -1 ? screenList.push(it) : screenList.splice(index, 1)
      this.onSearch()
    },
    // 搜索
    onSearch() {
      this.onRefresh({ page: 1, pageSize: 10 })
    },
    // 重置
    onReset() {
      this.timeDuring = []
      this.listQuery = initListQueryModel()
      this.onRefresh({ page: 1, pageSize: 10 });
    },
    onRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
  },
}
</script>
<style lang="less" scoped>
.cards {
  margin-bottom: 8px;
  .item {
    display: inline-block;
    width: 186px;
    padding: 16px 20px;
    margin-bottom: 8px;
    background-color: #fff;
    .data {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        font-weight: bold;
        font-size: 16px;
      }
    }
    &:not(:last-child) {
      margin-right: 8px;
    }
    p {
      margin-bottom: 16px;
      font-size: 14px;
      color: #4E5766;
    }
  }
}
</style>