<template>
  <div class="detail-wrapper">
    <page-title :title="title">
      <template slot="icon">
        <span
          :class="{ 'status-box0': form.activityStatus.code == 'NOT_START', 'status-box1': form.activityStatus.code == 'PROCEED', 'status-box2': form.activityStatus.code == 'OBSOLETE', 'status-box3': form.activityStatus.code == 'FINISHED' }"
          v-if="this.form.activityStatus"></span>
      </template>
      <template>

        <template v-if="form.activityStatus && form.activityStatus.code == 'PROCEED'">
          <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-coupon:abolish','sale-platform-promotion-coupon:abolish'])" @click="handleState">作废</el-button>
        </template>
        <template v-if="isEditable">
          <el-button type="primary" @click="submit">保存</el-button>
        </template>
      </template>
    </page-title>
    <el-form label-width="120px" ref="ruleForm" :model="form">
      <div class="detail-items" v-loading="loading">
        <page-module-card title="基础信息">
          <el-form-item label="活动名称：" prop="activityName" :rules="[{ required: true, message: '请输入活动名称' }]"
            style="width:370px;">
            <el-input v-model="form.activityName" placeholder="请输入活动名称，最多20个字"
              :disabled="isDisabled"></el-input>
          </el-form-item>
          <el-form-item label="活动开始时间：" prop="startTime"
            :rules="[{ required: true, message: '请选择活动开始时间', trigger: 'blur' }]">
            <el-date-picker :disabled="isDisabled"
              v-model="form.startTime" @change="startTimeChange" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
              placeholder="选择开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动结束时间：" prop="endTime"
            :rules="[{ required: true, message: '请选择活动结束时间', trigger: 'blur' }]">
            <el-date-picker :disabled="isDisabled"
              @change="endTimeChange" v-model="form.endTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择结束时间" default-time="23:59:59">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动商品：">
            <div v-if="isEditable" class="choose_btn">
              <div class="btn_left">
                <el-button type="primary" @click="openProducts()">选择商品</el-button>
                <el-button @click="handlePatchDelete" :disabled="indexResultsTable.length === 0">批量删除</el-button>
              </div>
              <div class="btn_right">
                <el-input style="width:200px" v-model.trim="modelQuery.keyWord" placeholder="ERP商品编码/商品名称"></el-input>
                <el-input style="width:200px;margin:0 10px;" v-model.trim="modelQuery.manufacturer" placeholder="生产厂家">
                </el-input>
                <el-button type="primary" @click="submitSearch">搜索</el-button>
                <el-button @click="handleReset">重置</el-button>
              </div>
            </div>
            <div v-if="indexResultsTable.length > 0" v-loading="loading">
              <el-table style="margin-top:15px;" :data="indexResultsTable" border @select="handleSelect"
                @selection-change="handleSelectionChange" @select-all="handleSelectAll" class="product-table">
                <el-table-column type="selection" />
                <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
                <el-table-column label="主图" align="center" width="80" class-name="img-cell">
                  <template slot-scope="{row}">
                    <img :src="splitString(row.pictIdS)" width="50px" v-if="row.pictIdS">
                    <img :src="pictImg" width="50px" v-if="row.pictIdS == null || row.pictIdS == ''">
                  </template>
                </el-table-column>
                <el-table-column label="ERP商品编码/仓库" prop="productCode" width="200px">
                  <template v-slot="{row}">
                    <span>编码：{{row.erpCode || '无'}}</span> <br />
                    <span>仓库：{{ row.warehouseName || '无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="productName" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="规格" prop="spec" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="生产厂家" prop="manufacturer" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column width="200px">
                  <template slot="header">
                    <span>促销信息</span>
                    <el-tooltip placement="top">
                      <div slot="content" style="max-width: 300px">
                        <p>1、特价：选择“特价”后，需设置单品特价。如特价为10.00元，则活动采购价=10.00元，所有客户享受相同价格。</p>
                        <p>2、打折：选择“打折”后，需设置折扣。如折扣为9.5折，则该商品采购价=原采购价*0.95。客户的原采购价不同，则活动采购价不同。</p>
                      </div>
                      <el-icon style="color: #999;" class="el-icon-question"></el-icon>
                    </el-tooltip>
                  </template>

                  <slot slot-scope="{row}">
                    <template>
                      <el-select v-model="row.discountType" placeholder="类型" @change="changeRowDiscountType($event, row)" style="width: 74px;" >
                        <el-option label="特价" value="AMOUNT"></el-option>
                        <el-option label="打折" value="DISCOUNT"></el-option>
                      </el-select>

                      <el-input-number v-model="row.promotionPrice" :controls="false" style="width: 78px; margin: 0 6px;" @blur="blurPromotionPrice(row)" placeholder="请输入"></el-input-number>
                      <!-- <el-input v-model="row.promotionPrice" style="width: 78px; margin: 0 6px;" placeholder="请输入内容" @input="changePromotionPrice(row)"></el-input> -->
                      <span>{{row.discountType === 'AMOUNT' ? '元' : '折'}}</span>
                    </template>
                  </slot>
                </el-table-column>
                <el-table-column label="销售价" prop="salePrice" width="100" />
                <!-- todo -->
                <el-table-column label="每人限购" prop="limitBuy" width="210">
                  <slot slot-scope="{row}">
                    <template v-if="isEditable">
                      <el-checkbox v-model="row.whetherLimitBuy">开启限购</el-checkbox>
                      <span style="padding-left: 5px"></span>
                      <el-input-number :max="row.activityQuantity" :min="Number(row.minBuyQuantity)"
                        v-if="row.whetherLimitBuy" v-model="row.peopleLimitQuantity" class="no-button" placeholder="请输入"
                        controls-position="right" style="width:100px;"></el-input-number>
                    </template>
                    <template v-else>
                      <span>{{ row.peopleLimitQuantity || '-' }}</span>
                    </template>


                  </slot>
                </el-table-column>
                <el-table-column label="活动数量" prop="activityQuantity" width="122">

                  <slot slot-scope="{row}">
                    <el-input-number v-if="isEditable" :max="isAdd ? Number(row.stockQuantity) : 999999"
                      v-model="row.activityQuantity" class="no-button" placeholder="请输入" controls-position="right"
                      style="width:100px;">
                    </el-input-number>
                    <span v-else>{{ row.activityQuantity
                    }}</span>

                  </slot>
                </el-table-column>

                <el-table-column label="活动起订量" prop="activityMinBuyQuantity" width="122">

                  <slot slot-scope="{row}">
                    <el-input-number v-if="isEditable" :min="Number(row.minBuyQuantity)"
                      v-model="row.activityMinBuyQuantity" class="no-button" placeholder="请输入" controls-position="right"
                      style="width:100px;">
                    </el-input-number>
                    <span v-else>{{row.activityMinBuyQuantity || '-' }}</span>

                  </slot>
                </el-table-column>

                <el-table-column label="最低起订量" prop="minBuyQuantity" width="100" />
                <el-table-column label="成本价" prop="costPrice" width="100" />
                <el-table-column label="库存" prop="stockQuantity" width="100" />
                <el-table-column label="操作" prop="stockQuantity" width="100" fixed="right" align="center">
                  <template slot-scope="{row}">
                    <el-button type="text" @click="deleteRowGoods(row)" :disabled="!isEditable">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <div class="page-row-left"
                  v-if="($route.query.id && (form.activityStatus && form.activityStatus.code == 'NOT_START')) || !$route.query.id">
                  <div class="page-row-leftBlock">
                    <span class="selectBlock">
                      <img src="@/assets/imgs/coupon/<EMAIL>" />
                    </span>
                    当页已选择 {{ selectionArr.length }}
                    <el-popover placement="bottom" width="382" trigger="click">
                      <el-form :inline="true" class="limit-form">
                        <el-form-item>
                          <el-input v-model="discounts" placeholder="请输入0.1~9.9" :min="0.1" :max="9.9"></el-input>
                        </el-form-item>
                        <el-form-item>
                          <el-button @click="discounts = ''">取消</el-button>
                        </el-form-item>

                        <el-form-item>
                          <el-button type="primary" @click="setDiscount">确定</el-button>
                        </el-form-item>
                      </el-form>
                      <el-button slot="reference" style="margin-left: 10px;">设置折扣</el-button>
                    </el-popover>
                  </div>
                </div>
                <div class="page-row-right">
                  <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-size="pagesize" background layout="prev, pager, next, jumper"
                    :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="裂变奖励：" prop="productRangeType">
            <el-radio-group v-model="form.whetherReward.code" :disabled="isDisabled">
              <el-radio v-for="item in whetherRewardData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <FissionRewardTip />
          </el-form-item>
          <el-form-item label="积分抵扣：" prop="productRangeType">
            <el-radio-group :disabled="isDisabled" v-model="form.whetherPointsDeduction.code">
              <el-radio v-for="item in pointDeductionData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <PointDeductionTip />
          </el-form-item>
        </page-module-card>
        <page-module-card title="活动规则">
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{ required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType"
              :disabled="isDisabled">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                </el-radio>
                <el-checkbox-group style="margin-top: 10px;" v-model="form.merchantTypeIds"
                  v-if="merchantList.length > 0 && form.limitObjectType === 'CUSTOMER_TYPE'">
                  <el-checkbox v-for="(item) in merchantList" :key="item.id" :label="item.id">
                    {{ item.name }}</el-checkbox>
                </el-checkbox-group>
              </div>
<!--              <div class="block-radio">-->
<!--                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>-->
<!--                <div style="margin: 15px 0;" v-if="form.limitObjectType == 'CUSTOMER_GROUP'">-->
<!--                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>-->
<!--                </div>-->
<!--                <el-table v-if="groupTableData && form.limitObjectType == 'CUSTOMER_GROUP'" :data="groupTableData"-->
<!--                  border>-->
<!--                  <el-table-column prop="name" label="客户分组" width="234" />-->
<!--                  <el-table-column prop="customerNumber" label="客户数量" width="120" />-->
<!--                  <el-table-column label="操作" width="52">-->
<!--                    <template slot-scope="scope">-->
<!--                      <el-button @click="deleteRow(scope.$index)" type="text">删除</el-button>-->
<!--                    </template>-->
<!--                  </el-table-column>-->
<!--                </el-table>-->
<!--              </div>-->
              <div class="block-radio">
                <el-radio label="CUSTOMER_TAG">指定客户标签可参与</el-radio>
                <div v-if="form.limitObjectType == 'CUSTOMER_TAG'" style="margin: 15px 0">
                  <el-checkbox-group v-model="form.customerLabelIds">
                    <el-checkbox v-for="item in customerTag" :key="item.value" :label="item.value">{{ item.text }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
    <!-- <products-modal ref="products-modal" @change="handleAddProducts" :checkList="goodsList" :isActivity="true"
      :during="[form.startTime, form.endTime]" :notSearchId="notSearchId" /> -->
    <div v-if="productVisible">
      <products-modal :productVisible="productVisible" ref="products-modal" @closeProductDia="closeProductDia"
        @change="handleAddProducts" :checkList="goodsList"></products-modal>
    </div>
    <add-group :visible="showAdd" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser"
      :select-data="groupTableData" />
  </div>
</template>

<script>
import request from '@/utils/request'
import addGroup from '@/views/promotion/components/addGroup.vue'
// import ProductsModal from '@/views/promotion/components/productList.vue'
import ProductsModal from "@/components/eyaolink/productModel/index"
import { merchantGroupListNew } from "@/api/group";
import { postGoodsList, detail, updatePromotionPackageState, getCheckList } from "@/api/limitTime"
import productImg from "@/assets/product.png";
import { searchRepeatProduct } from '@/api/limitTime'
import _ from "lodash";
import { page } from "@/api/merchantApi/customerlabel";
import checkPermission from '@/utils/permission';
import { getPointDeductionData, getWhetherRewardData } from '@/views/products/product/components/data'
import FissionRewardTip from '@/views/promotion/components/fissionRewardTip.vue'
import PointDeductionTip from '@/views/promotion/components/pointDeductionTip.vue'

export default {
  components: {
    PointDeductionTip,
    FissionRewardTip,
    ProductsModal,
    addGroup,
  },
  data() {
    var validateImage = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择套餐商品'));
      } else {
        callback()
      }
    }
    return {
      pointDeductionData: getPointDeductionData(),
      whetherRewardData: getWhetherRewardData(),
      pictImg: productImg,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 1000 * 24;
        }
      },
      id: '',
      loading: false,
      showAdd: false,
      saleMerchantId: '', //经销商id
      active: 0,
      merchantList: [],   // 客户类型列表
      groupTableData: [], // 已选分组数据
      form: {
        startTime: '', // 活动开始时间
        endTime: '', // 活动结束时间
        // during: '',
        activityName: '',
        limitBuyNum: '',
        limitBuyType: 'N',
        limitObjectType: 'NONE',//指定人类型 NONE,CUSTOMER_TYPE,CUSTOMER_GROUP,CUSTOMER,UN_CUSTOMER
        customerLabelIds: [], // 选中的客户标签id集合
        merchantTypeIds: [], //客户类型
        productRelList: [],
        merchantGroupIds: [],//分组集合
        whetherReward: { code: 'N' },
        whetherPointsDeduction: { code: 'N' }, // 积分抵扣
      },
      discountProductRelList: [],
      total: 0,
      goodsList: [],
      currentPage: 1, //初始页
      pagesize: 10,
      indexResultsTable: [],
      selectionArr: [],
      discounts: '',
      subtrahend: '',
      notSearchId: '',
      checkedArr: [],
      productVisible: false, // 选择商品弹窗是否显示
      modelQuery: {

      },
      timer: null,
      customerTag: [],
      testValue: 'DISCOUNT'
    }
  },
  computed: {
    isAdd() {
      return !this.id;
    },
    // 是否可编辑
    isEditable() {
      // 可编辑状态
      const EDITABLE_STATUS = ['NOT_START', 'PROCEED'];
      const form = this.form;
      return !!(!this.id || (form.activityStatus && EDITABLE_STATUS.includes(form.activityStatus.code)))
    },
    isDisabled() {
      return this.form.activityStatus && this.form.activityStatus.code !== 'NOT_START'
    },
    // 标题
    title() {
      return this.id ? this.isEditable ? '编辑限时抢购' : '查看限时抢购' : '新增限时抢购'
    }
  },
  created() {
    this.id = this.$route.query.id;
    this.getMerchantType()
    if (this.id) {
      this.getDetail()
    };
    this.fetchMerchantLabels();
  },
  methods: {
    checkPermission,
    async fetchMerchantLabels() {
      let { code, data } = await page({ current: 1, page: 200, model: {} });
      if (code === 0) {
        this.customerTag = data.records.map(v => ({
          text: v.tagName,
          value: v.id
        }))
      }
    },
    handleSelect(selection, row) {
      let selected = selection.length && selection.indexOf(row) !== -1
      let index = this.indexResultsTable.findIndex((item) => item.id === row.id)
      let indx = this.goodsList.findIndex((item) => item.id === row.id)
      if (selected) {
        this.indexResultsTable[index].selected = true
        this.goodsList[indx].selected = true
      } else {
        this.indexResultsTable[index].selected = false
        this.goodsList[indx].selected = false
      }
    },
    handleSelectionChange(val) {
      console.log('handleSelectionChange');
      this.selectionArr = val
    },
    handleSelectAll(val) {
      // console.log('handleSelectAll');
      // 判断 选中状态的商品  是否>0  并把未选中商品的选中状态设置为false
      if (JSON.parse(JSON.stringify(val)).length > 0) {
        this.indexResultsTable.map(item => { item.selected = true })
        let idArr = val.map((items) => items.productId)
        this.goodsList.map((items, index) => {
          if (idArr.includes(items.id)) {
            items.selected = false
          }
        })
      } else {
        this.goodsList.map((items, index) => {
          items.selected = false
        })
      }
    },
    // 活动开始时间
    startTimeChange(e) {
      console.log('e', e);
      if (this.form.endTime != '' && this.form.endTime != null) {
        let start = new Date(e).getTime();
        let end = new Date(this.form.endTime).getTime();
        if (start > end) {
          this.$message.warning('活动开始时间不能大于结束时间');
          this.form.startTime = '';
          // this.$Set(this.form,startTime,'');
        }
      }
    },
    // 活动结束时间
    endTimeChange(e) {
      console.log('e', e);
      if (this.form.startTime != '' && this.form.startTime != null) {
        let start = new Date(this.form.startTime).getTime();
        let end = new Date(e).getTime();
        if (start > end) {
          this.$message.warning('活动开始时间不能大于结束时间');
          this.form.endTime = '';
          // this.$Set(this.form,startTime,'');
        }
      }
    },
    //分页
    handleSizeChange: function (size) {
      this.pagesize = size;
      this.getResultsTable();
    },
    handleCurrentChange: function (page) {
      this.currentPage = page;
      this.getResultsTable();
    },
    //前端自己分页
    getResultsTable: function (arrList = null) {
      // es6过滤得到满足搜索条件的展示数据list
      let list = arrList == null ? this.goodsList : arrList;
      //表格渲染的数据  indexResultsTable:[],
      this.indexResultsTable = list.filter((item, index) =>
        index < this.currentPage * this.pagesize && index >= this.pagesize * (this.currentPage - 1)
      )//根据页数显示相应的内容
      this.total = list.length;

    },
    // 批量删除商品
    handlePatchDelete() {
      let list = _.cloneDeep(this.selectionArr);
      list.forEach(element => {
        this.deleteRowGoods(element)
      })
    },
    // 搜索商品
    submitSearch() {

      this.indexResultsTable = [];
      let list = this.goodsList
      let { keyWord, manufacturer } = this.modelQuery
      if (keyWord || manufacturer) {
        list = this.goodsList.filter(({ erpCode, productName, manufacturer: _manufacturer }) => (keyWord && (erpCode.includes(keyWord) || productName.includes(keyWord))) || (manufacturer && _manufacturer.includes(manufacturer)))
        // list = this.goodsList.filter(item=>{
        //   return (item.erpCode && item.erpCode.indexOf(keyWord) > -1) || (item.productName && item.productName.indexOf(this.modelQuery.keyWord) > -1) || (item.manufacturer && item.manufacturer.indexOf(this.modelQuery.manufacturer) > -1)
        // });

      }
      this.getResultsTable(list);
    },
    // 重置
    handleReset() {
      this.modelQuery = {

      };
      this.getResultsTable();
    },
    // 获取小数点后n位数
    fomatFloat(val, num) { // val是原数字,num是取小数点后几位
      if (typeof num !== "number") return Number(val);
      let valFormat = val + '';
      const valFormatLen = valFormat.length;
      if (!valFormat || !valFormat.includes('.')) return Number(val);
      const pointIndex = valFormat.indexOf('.')
      let numFormat = num+pointIndex+1;
      if (valFormatLen <= numFormat) return Number(val);
      if (numFormat >= valFormatLen) numFormat = valFormatLen+1
      return Number(valFormat.slice(0, numFormat))
    },
    //设置打折
    setDiscount() {
      if (JSON.parse(JSON.stringify(this.selectionArr)).length === 0) {
        this.$message.warning('请选择商品进行设置！')
      } else {
        if (Number(this.discounts) <= 10 && Number(this.discounts) >= 0.1) {
          const promotionPrice = this.fomatFloat(this.discounts,1) >= 10 ? 9.9 : this.fomatFloat(this.discounts,1);
          this.indexResultsTable.map(item => {
            if (item.selected === true) {
              item.discountType = "DISCOUNT";
              item.promotionPrice = promotionPrice;
            }
          })
          this.goodsList.map(item => {
            if (item.selected === true) {
              item.promotionPrice = promotionPrice;
            }
          })
          this.discounts = promotionPrice
        } else {
          this.$message.error('请输入0.1~9.9之间折扣')
          this.discounts = ''
        }
      }
    },
    changeRowDiscountType(type, row) {
      const index = this.indexResultsTable.findIndex(item=> item.id === row.id);
      if(type == 'DISCOUNT') {
        if (row.promotionPrice > 9.9) {
          this.indexResultsTable[index].promotionPrice = 9.9;
        } else {
          this.indexResultsTable[index].promotionPrice = this.fomatFloat(this.indexResultsTable[index].promotionPrice, 1)
        }
      }
    },
    blurPromotionPrice(row) {
      let index = this.indexResultsTable.findIndex(item=> item.id === row.id);
      if(row.discountType == 'DISCOUNT') {
        if(!isNaN(row.promotionPrice)) {
          // 是数字
          if(row.promotionPrice < 0.1 || row.promotionPrice >= 10) {
            this.$message.error('请输入0.1~9.9之间的折扣');
            this.indexResultsTable[index].promotionPrice = 9.9;
          } else{
            this.indexResultsTable[index].promotionPrice = this.fomatFloat(row.promotionPrice,1) >= 10 ? 9.9 : this.fomatFloat(row.promotionPrice,1);
          }
        } else {
          this.indexResultsTable[index].promotionPrice = 9.9;
        }
      } else {
        if (typeof row.promotionPrice !== 'number' || row.promotionPrice.toString() === '0') {
          this.$message.error('请输入大于0的特价');
          this.indexResultsTable[index].promotionPrice = 0.1;
          return;
        }
        this.indexResultsTable[index].promotionPrice = this.fomatFloat(row.promotionPrice,2);
      }
    },
    //作废
    async handleState() {
      let param = {
        couponStatusEnum: 'OBSOLETE',
        id: this.id
      }
      const data = await updatePromotionPackageState(param)
      if (data.code === 0) {
        this.$message.success('作废操作成功！')
        this.getDetail()
      }
    },
    //打开商品
    openProducts() {
      if (this.form.startTime.length < 2 && this.form.endTime.length < 2) {
        this.$message.warning('请先选择活动时间');
        return
      }
      this.productVisible = true;
      this.form.productRelList = this.goodsList
      if (this.id) {

        this.notSearchId = this.id
      } else {
        this.notSearchId = ''
      }

    },
    closeProductDia() {
      this.productVisible = false;
    },
    checkBoxChange(row) {
      if (row.main) {
        row.main = true
      } else {
        row.main = false
      }
    },
    // 获取详情
    async getDetail() {
      this.loading = true
      if (this.id) {
        let { data } = await detail(this.id)
        this.form = {
          ...this.form,
          ...data,
          customerLabelIds: !Boolean(data.customerLabelIds) ? [] : data.customerLabelIds,
          startTime: data.activityStartTime,
          endTime: data.activityEndTime,
          limitObjectType: data.limitObjectType.code,
          limitBuyType: data.limitBuyType.code
        }
        const discountProductRelList = data.discountProductRelList.map(item => {
          item.whetherLimitBuy = item.whetherLimitBuy.code === 'Y'
          return item
        })
        this.form.productRelList = discountProductRelList
        this.goodsList = discountProductRelList
        if (data.merchantTypeIds === null) {
          this.form.merchantTypeIds = []
        }
        if (data.merchantGroupIds === null) {
          this.form.merchantGroupIds = []
        }
        if (data.merchantGroupIds) {
          this.getGroupList();
        }
        if (this.goodsList.length > 0) {
          this.getCheckGoodsList()
        }
      }
    },
    // 获取已有客户分组列表
    async getGroupList() {
      this.loading = true;
      const query = {
        model: {}
      }
      const { data } = await merchantGroupListNew(query);
      this.loading = false;
      this.groupTableData = data.records.filter((items) => {
        if (this.form.merchantGroupIds.includes(items.id)) {
          return {
            items
          };
        }
      })
    },
    // 获取已有的产品列表
    async getCheckGoodsList(ids) {
      const formData = new FormData()
      let idArr = ids ? ids : this.form.productRelList.map((items) => items.productId);
      idArr = Array.from(new Set(idArr));
      formData.append('ids', idArr.toString())
      let { data } = await postGoodsList(formData)

      this.goodsList = [];

      this.goodsList = data.map((items, index) => {
        // 筛选新添加的项，避免重置已有表单
        if (ids) {
          let targetItem = this.indexResultsTable.find(({ id }) => id === items.id) || {
            activityQuantity: items.activityQuantity || Number(items.stockQuantity), // 活动数量 默认为 库存数量
            activityMinBuyQuantity: items.activityMinBuyQuantity || Number(items.minBuyQuantity), // 活动起订量 默认为 最低起订量
            discountType: items.discountType || 'DISCOUNT', // 折扣类型，默认为折扣
            promotionPrice: items.promotionPrice || 0, // 折扣力度
          };
          // 活动数量
          items.activityQuantity = targetItem.activityQuantity
          // 活动起订量
          items.activityMinBuyQuantity = targetItem.activityMinBuyQuantity
          // 折扣类型 默认为折扣
          items.discountType = targetItem.discountType;
          // 折扣力度
          items.promotionPrice = targetItem.promotionPrice;
          // 如果原本有布尔值，则设置回原来的值，没有则取接口返回值
          items.whetherLimitBuy = typeof targetItem.whetherLimitBuy === 'boolean' ? targetItem.whetherLimitBuy : false
          items.peopleLimitQuantity = targetItem.peopleLimitQuantity || '';

        }

        if (idArr.includes(items.id)) {
          if (this.form.productRelList.length === 0) {
            this.$set(items, 'promotionPrice', undefined)
            this.$set(items, 'reduced', undefined)
            this.$set(items, 'discount', undefined)
            this.$set(items, 'selected', false)
          } else {
            if (this.form.productRelList.length - 1 < index) {
              this.$set(items, 'reduced', '-')
              this.$set(items, 'discount', '-')
              this.$set(items, 'promotionPrice', undefined)
              this.$set(items, 'selected', false)
            } else {
              this.$set(items, 'promotionPrice', this.form.productRelList[index].promotionPrice)
              this.$set(items, 'reduced', this.form.productRelList[index].reduced)
              this.$set(items, 'discount', this.form.productRelList[index].discount);
              this.$set(items, 'discountType', this.form.productRelList[index].discountType.code || items.discountType)
            }
          }
          const target = this.form.productRelList.find(v => v.productId === items.id) || {}
          return { ...target, ...items };
        }
      })
      this.loading = false
      this.getResultsTable()
    },
    // 清空表单
    resetForm() {
      this.$refs['ruleForm'].resetFields();
    },
    // 提交表单
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {

          if (this.goodsList.length === 0) {
            this.$message.warning('请选择活动商品！')
            return false
          }
          if (this.form.limitObjectType === 'CUSTOMER_TYPE' && this.form.merchantTypeIds.length === 0) {
            this.$message.warning('请点击选择相关的客户类型')
            return false
          } else if (this.form.limitObjectType === 'CUSTOMER_GROUP' && this.form.merchantGroupIds.length === 0) {
            this.$message.warning('请点击选择相关的客户分组')
            return false
          }
          if (!this.goodsList.every(item => item.promotionPrice > 0)) {
            this.$message.error('活动商品促销价不能为空，请前往设置再保存！')
            return false
          }

          if (this.goodsList.some(item => item.activityQuantity <= 0)) {
            this.$message.error('活动数量不能为空，请前往设置再保存！')
            return false
          }
          let dataLegitimate = true; // 折扣数字是否合法，默认合法
          if (this.goodsList.length > 0) {
            this.discountProductRelList = this.goodsList.map((item) => {
              if(item.promotionPrice <= 0) {
                dataLegitimate = false;
              };
              if(item.discountType == 'DISCOUNT' && (item.promotionPrice <= 0 || item.promotionPrice > 9.9)) {
                dataLegitimate = false;
              }
              return {
                limitTimeDiscountId: this.id,
                productId: item.id,
                promotionPrice: item.promotionPrice,
                activityQuantity: item.activityQuantity,
                activityMinBuyQuantity: item.activityMinBuyQuantity,
                peopleLimitQuantity: item.peopleLimitQuantity,
                whetherLimitBuy: item.whetherLimitBuy ? 'Y' : 'N',
                discountType: item.discountType // 折扣类型
              }
            })
          }

          if(!dataLegitimate) {
            this.$message.error('折扣信息输入不正确');
            return
          }
          // 选择指定客户类型
          if (this.form.limitObjectType === 'CUSTOMER_TYPE') {
            this.form.merchantGroupIds = []
          }
          if (this.form.limitObjectType === 'CUSTOMER_GROUP' || this.form.limitObjectType === 'NONE') {
            this.form.merchantTypeIds = []
          }
          if (this.form.limitBuyType === 'N') {
            this.form.limitBuyNum = ''
          }
          // 是否有id
          if (this.id) {
            this.form.id = this.id
          } else {
            delete this.form.id
          }
          this.loading = true;
          let param = {
            ...this.form,
            discountProductRelList: this.discountProductRelList,
            activityStartTime: this.form.startTime,
            activityEndTime: this.form.endTime,
          }
          delete param.startTime;
          delete param.endTime;
          if (param.limitObjectType !== 'CUSTOMER_TAG') {
            delete param.customerLabelIds;
          }

          delete param.productRelList;


          let method = this.id ? request.put('product/merchant/limitTimeDiscount', param) : request.post('product/merchant/limitTimeDiscount', param)
          method.then((res) => {
            if (res.code == 0) {
              this.loading = false;
              this.$message.success('保存成功')
              if (!this.id) {
                sessionStorage.setItem('limitTime', '123')
              }
              this.timer = setTimeout(() => {
                this.$router.push({
                  path: '/promotion/limitTime/list'
                })
              }, 500)
            } else {
              this.loading = false;
            }
          }).catch(() => {
            this.loading = false;
          })
        }
      });
    },
    // 删除选中客户组
    deleteRow(i) {
      this.groupTableData.splice(i, 1)
    },
    deleteRowGoods(row) {
      let index = this.goodsList.findIndex((item) => item.id === row.id)
      this.goodsList.splice(index, 1)
      if (JSON.parse(JSON.stringify(this.indexResultsTable)).length === 1 && JSON.parse(JSON.stringify(this.goodsList)).length > 1) {
        this.currentPage = 1
      }
      this.getResultsTable()
      // this.$refs['products-modal'].isSelect = true;
    },
    // 改变客户分组回调
    changeAddUser(data) {
      this.groupTableData = data;
      this.form.merchantGroupIds = data.map((items) => items.id)
      this.showAdd = false
    },
    // 选择商品后回调
    async handleAddProducts(list) {
      let params = {
        productIds: [list.join(',')],
        endTime: this.form.endTime,
        startTime: this.form.startTime,
        notSearchId: this.notSearchId,
      }
      searchRepeatProduct(params).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.getCheckGoodsList(list)
        }
      })
    },
    async postCouponList(params) {
      let listQuery = {
        model: {
          limitTimeDiscountId: this.id
        }

      }
      Object.assign(listQuery, params)
      const data = await getCheckList(listQuery)
      this.goodsList = data.records

      return data
    },
    // 获取客户类型
    async getMerchantType() {
      const { data } = await request.get('merchant/admin/merchantType', {})
      this.merchantList = data
    },
    splitString(val) {
      return String(val).split(',')[0]
    }
  },
  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="scss" scoped>
.product-table {
  .el-input-number.is-controls-right .el-input__inner {
    padding-right: 15px;
  }
}

.limit-form .el-form-item {
  margin-bottom: 0;
}

.detail-wrapper {
  .el-pager li {
    width: 0;
  }

  .page-row-left {
    float: left;
  }

  .page-row-leftBlock {
    display: flex;

    .selectBlock {
      display: block;
      width: 14px;
      height: 14px;
      margin-right: 8px;

      img {
        width: 14px;
      }
    }
  }

  .page-row-right {
    float: right;
  }

  .status-box0 {
    width: 64px;
    height: 32px;
    display: inline-block;
    background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    background-size: cover;
    margin-left: 12px;
  }

  .status-box1 {
    width: 64px;
    height: 32px;
    display: inline-block;
    background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    background-size: cover;
    margin-left: 12px;
  }

  .status-box2 {
    width: 64px;
    height: 32px;
    display: inline-block;
    background: url('../../../assets/imgs/coupon/Icon_Revok.png') no-repeat;
    background-size: cover;
    margin-left: 12px;
  }

  .status-box3 {
    width: 64px;
    height: 32px;
    display: inline-block;
    background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    background-size: cover;
    margin-left: 12px;
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  background: #f7f7f8;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 20px;
  color: #8c939d;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.detail-item {
  border: none !important;
}

.detail-tit {
  display: flex;
  align-items: center;
  margin: 0;
}

.inline-input {
  width: 80px;
  margin: 0 10px;
}

.inline-item {
  display: inline-block;
}

.block-radio {
  margin-bottom: 16px;

  &-top {
    margin-top: 11px;
  }

  &-none {
    margin: 0;
  }
}

.detail {
  &-header {
    margin: 0 12px;
    padding: 19px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;

    p {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.tips {
  color: #999999;
  margin: 0;

  &-btn {
    border: none;
    margin: 0;
    padding: 0;
  }
}

.no-button {

  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
}

.choose_btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}
</style>
