<template>
    <el-dialog title="新增业务员" v-dialogDrag width="30%" v-bind="$attrs" v-on="$listeners"  :close-on-click-modal="false" @close="clearData">
			<el-form ref="form" :model="form" label-width="100px" class="je-pr30">
				<el-form-item label="注册手机号" prop="mobile" :rules="[
              {
                required: true,
                message: '请输入业务员手机号',
                trigger: 'blur',
              },
            ]">
						<el-input maxlength="11" @input="handleInput" show-word-limit v-model.trim="form.mobile"></el-input>
						<p class="exist-name" v-if="resObj.name && resObj.mobile">{{ resObj.name }} - {{ resObj.mobile }}</p>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="$emit('update:visible', false)">关 闭</el-button>
				<el-button type="primary" @click="handlePrimary">提 交</el-button>
			</div>
		</el-dialog>
</template>

<script>
import {
  phoneQueryEchoPort
} from '@/api/salemanCenter/index'
export default {
	name: 'SalesmanVerificationDialog',
	data() {
		return {
			form: {
				mobile: ''
			},
			resObj: {}
		}
	},
	methods: {
		handleInput(val) {
      if(val.length === 11) {
        phoneQueryEchoPort({ mobile: val }).then(res => {
					this.resObj = res.data
				})
			}else {
				this.resObj = {}
			}
		},
		clearData() {
			this.$refs.form.resetFields()
			this.form = {
				mobile: ''
			};
		},
    handlePrimary() {
      if (this.form.mobile.length !== 11) {
        this.$message.error('请输入正确的手机号')
        return
      }
			this.$emit('onVerification', this.resObj.id, this.form.mobile);
			this.$emit('update:visible', false)
		}
	}
}
</script>

<style lang="scss" scoped>
.exist-name {
	border: 1px solid #DCDDE0;
	border-top: none;
	padding-left: 20px;
}
</style>