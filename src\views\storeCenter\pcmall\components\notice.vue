<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" class="import-image" @confirm="confirm">
    <div class="dialog-content" :class="type">
      <div class="label">店铺公告：</div>
      <div class="content">
        <el-input type="textarea" v-model="value" placeholder="请输入公告内容，200个字以内" :rows="5" />
      </div>
    </div>
  </im-dialog>
</template>

<script>

import {noticeUpdate} from "@/api/pcmall";

export default {
  name: 'ImportImage',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    }
  },
  data() {
    return {
      visibleDialog: false,
      value: ''
    }
  },
  methods: {
    init(data) {
      this.value = data
      this.visibleDialog = true
    },
    confirm() {
      noticeUpdate({
        content: this.value
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('修改公告成功')
          this.$emit('confirm')
          this.visibleDialog = false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dialog-content{
  display: flex;
  .label{
    font-size: 14px;
    color: #1e2439;
    line-height: 38px;
    min-width: 80px;
  }
  .content{
    width: 100%;
  }
}
</style>
