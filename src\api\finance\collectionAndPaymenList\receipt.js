import request from "@/utils/request";
import requestExport from "@/utils/requestExport";
// 获取列表数据
export function list(data) {
  return request({
    url: "/finance/admin/financeCollect/page",
    method: "post",
    data,
  });
}

// 获取单一收款单
export function detail(data) {
  return request({
    url: "/finance/admin/financeCollect/" + data,
    method: "get",
  });
}
export function getFindCollect(orderNumber) {
  return request({
    url: `/finance/admin/financeCollect/findCollect?orderNumber=${orderNumber}`,
    method: "post",
  });
}
// 获取单一收款单
export function detailParam(data) {
  return request({
    url: "/finance/admin/financeCollect/getByOrderNumber/" + data,
    method: "get",
  });
}

// 确认收款
export function confirmReceipt(data) {
  return request({
    url: "/finance/admin/financeCollect/confirmCollect",
    method: "post",
    data
  });
}

// 修改状态
export function updateStatus(data) {
  return request({
    url: "/finance/admin/financeCollect/updateCollectStatus",
    method: "post",
    params: data,
  });
}

// 更新收款单信息
export function updateCollect(data) {
  return request({
    url: "/finance/admin/financeCollect/updateCollect",
    method: "post",
    params: data,
  });
}

/**
 * @description  收款单管理导出Excel
 * **/
export function exportFinanceCollect(data) {
  return requestExport({
    url: "/finance/admin/financeCollect/export",
    method: "post",
    data,
    headers: { responseType: "blob" },
  });
}
