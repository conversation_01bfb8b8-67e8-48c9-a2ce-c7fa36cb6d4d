<template>
  <div class="rolePageContent">
    <div class="title flex_between_center">
      <span>角色管理</span>
      <div>
        <el-button @click="getList()" >刷新</el-button>
        <el-button  type="primary" v-if="checkPermission(['admin','roleIndex:add'])"  @click="editClickFun({})">+新增角色</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="60"
          show-overflow-tooltip
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :width="item.width"
          :min-width="(item.width?item.width:'350px')"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span v-if="item.name=='status'">{{ row[item.name]?'启用':'禁用' }}</span>
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
        >
            <template slot-scope="{row}">
                <el-button v-if="checkPermission(['admin','roleIndex:update'])" @click="editClickFun(row)" type="text" >编辑</el-button>
                <span class="line" v-if="checkPermission(['admin','roleIndex:delete'])">|</span>
                <el-button type="text"  v-if="checkPermission(['admin','roleIndex:delete'])" @click="deleteFun(row)">删除</el-button>
            </template>
        </el-table-column>
      </el-table>
       <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
    </div>
    <!-- 设置 编辑 -->
    <el-dialog :close-on-click-modal="false" v-if="showEdit" :title="(row.id>0?'编辑':'新增')+'角色'" :show-close="false" :visible.sync="showEdit" :width="'1000px'" >
        <edit :visible.sync="showEdit" :isReload.sync="submitReload" :row.sync="row"></edit>
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { list,deleteApi } from '@/api/setting/permission/userRole'
import edit from '@/views/settingCenter/role/edit'
import Pagination from '@/components/Pagination'
export default {
  data() {
    return {
      showEdit:false,
      row:{
          id:0,
          type:"",
          name:"",
      },

      tableTitle:[
        {
          label:'角色名称	',
          name: "name",
          width:'150px'
        },
        {
          label:'角色状态	',
          name: "status",
          width:'150px'
        },
        {
          label:'描述',
          name: "describe",
        }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{},
        current: 1,
        size: 10
      },
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        this.getList()
      }
    }
  },
  components: { Pagination,edit },
  methods: {
    checkPermission,
    editClickFun(row) {
        this.row=row
        this.showEdit=true
    },
    deleteFun(row){
      var _this=this;
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async actionDeleteFun(row) {
      var data= await deleteApi(row.id)
      if(data.code==0){
          this.listQuery.current=1;
          this.getList()
      }
    },
    renderHeader (h,{column}) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      )
    },
    setHeaer:function(){
      this.showSelectTitle=!this.showSelectTitle
    },
    async getList() {
      this.listLoading = true
      const { data } = await list(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    }
  },
  mounted() {
      this.getList()
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.rolePageContent {
  padding: 0;
  // padding: 15px;
  .title{
      border-top: 16px solid #F2F3F4;
      border-bottom:2px solid #EBECEE;
      padding:0 12px;
      padding-top: 10px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }
  .table{padding:12px 12px;}
  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
}
</style>
