<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="onSubmit"
    >
      <im-search-pad-item prop="activityName">
        <el-input v-model.trim="model.activityName" placeholder="请输入活动名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="activityStatus">
        <el-select v-model="model.activityStatus" placeholder="活动状态">
          <el-option label="全部" value="" />
          <el-option label="未开始" value="NOT_START" />
          <el-option label="进行中" value="PROCEED" />
          <el-option label="已结束" value="FINISHED" />
          <el-option label="废弃" value="OBSOLETE" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="activeName"
        :tabs="[{ name: '限时折扣', value: 'first' }]"
      >
        <template slot="button">
          <el-button @click="onSubmit">刷新</el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-limitDiscount:add', 'sale-platform-promotion-limitDiscount:add'])" type="primary" @click="editCoupon()">+ 新增限时折扣</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData">
        <template slot="activityStartTime">
          <el-table-column label="活动时间">
            <slot slot-scope="{row}">
              {{ row.activityStartTime }}至{{ row.activityEndTime }}
            </slot>
          </el-table-column>
        </template>
        <template slot="whetherReward">
          <el-table-column label="裂变奖励">
            <slot slot-scope="{row}">
              {{ getWhetherRewardLabel(row.whetherReward && row.whetherReward.code) }}
            </slot>
          </el-table-column>
        </template>
        <template slot="whetherPointsDeduction">
          <el-table-column label="积分抵扣">
            <slot slot-scope="{row}">
              {{ getPointDeductionLabel(row.whetherPointsDeduction && row.whetherPointsDeduction.code) }}
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-button v-if="props.row.activityStatus.code == 'NOT_START' || props.row.activityStatus.code == 'PROCEED'  && checkPermission(['admin','sale-saas-promotion-limitDiscount:edit', 'sale-platform-promotion-limitDiscount:edit'])" type="text" @click="editCoupon(props.row.id)">编辑</el-button>
          <el-button v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE' ) && checkPermission(['admin','sale-saas-promotion-limitDiscount:detail', 'sale-platform-promotion-limitDiscount:detail'])" type="text" @click="editCoupon(props.row.id)">查看</el-button>
          <el-divider v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE')" direction="vertical" />
          <!-- <el-button v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE') && checkPermission(['admin','limitTime-delete'])" type="text" @click="delCoupon(props.row.id)">删除</el-button> -->
          <del-el-button
            v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE') && checkPermission(['admin','sale-saas-promotion-limitDiscount:del', 'sale-platform-promotion-limitDiscount:del'])"
            :targetId="props.row.id"
            :text="delText"
            @handleDel="delCoupon"
          ></del-el-button>
          <el-divider v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START') &&checkPermission(['admin','limitTime-off'])" direction="vertical" />
          <el-button v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START')&&checkPermission(['admin','sale-saas-promotion-limitDiscount:abolish', 'sale-platform-promotion-limitDiscount:abolish'])" type="text" @click="handleOnOff(props.row.id)">作废</el-button>
        </div>
      </table-pager>
<!--      <product-dialog ref="products-dialog" :check-list="goodsList" :detail="itemDetail" />-->
    </div>
  </div>
</template>

<script>
import { getList, updatePromotionPackageState, handleDel } from '@/api/limitTime'
import checkPermission from '../../../utils/permission'
// import productDialog from '../components/product-dialog'
import delElButton from "@/components/eyaolink/delElButton";
import { getPointDeductionLabel, getWhetherRewardLabel } from '../../products/product/components/data'

const TableColumns = [
  { label: '活动编码', name: 'activityCode', prop: 'activityCode' },
  { label: '活动名称', name: 'activityName', prop: 'activityName' },
  { label: '活动状态', name: 'activityStatus.desc', prop: 'activityStatus.desc' },
  { label: '裂变奖励', name: 'whetherReward', prop: 'whetherReward', slot: true },
  { label: '积分抵扣', name: 'whetherPointsDeduction', prop: 'whetherPointsDeduction', slot: true },
  { label: '活动时间', name: 'activityStartTime', prop: 'activityStartTime', slot: true },
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  name: 'discountLimit',
  components: {
    delElButton
  },
  data() {
    return {
      // table配置
      delText:'您确定删除该活动吗？',
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      loading: false,
      showCoupon: false,
      showAdd: false,
      activeName: 'first',
      model: {
        'activityName': '',
        'activityStatus': ''
      },
      during: '',
      tableData: [],
      goodsList: [],
      itemDetail: ''
    }
  },
  created() {
  },
  activated() {
    if(sessionStorage.getItem('limitTime')) {
      this.reload()
      sessionStorage.removeItem('limitTime')
    }
  },
  methods: {
    getWhetherRewardLabel,
    getPointDeductionLabel,
    checkPermission,
    async postCouponList(params) {
      const listQuery = {
        model: {
          ...this.model,
          activityStartTime: this.during[0],
          activityEndTime: this.during[1]
        }

      }
      Object.assign(listQuery, params)
      this.loading = true
      return await getList(listQuery)
    },
    async handleOnOff(id) {
      const param = {
        couponStatusEnum: 'OBSOLETE',
        id: id
      }
      const data = await updatePromotionPackageState(param)
      if (data.code === 0) {
        this.$message.success('该限时活动已作废！')
        this.onSubmit()
      }
    },
    // 删除作废和结束的
    async delCoupon(id) {
      const data = await handleDel([id])
      if (data.code === 0) {
        this.$message.success('删除成功！')
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      }
    },
    // 新增或者编辑
    editCoupon(id, type) {
      this.$router.push({
        path: '/promotion/limitTime/edit',
        query: {
          saleMerchantId: this.saleMerchantId,
          id: id,
          type: type
        }
      })
    },
    onSubmit() {
      // this.postCouponList()
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      return await this.postCouponList()
    },
    reload() {
      this.model = {}
      this.during = ''
      this.$refs.todoTable.doRefresh()
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
