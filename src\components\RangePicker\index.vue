<template>
    <div :class="[$style.container, isFocus ? $style.focus : '', isClearable ? $style.active : '']">
        <i :class="[$style.prefix]" class="el-icon-time"></i>
        <el-date-picker @change="(val) => handleChange(0, val)" :disabled="disabled" @focus="onFocus" @blur="onBlur"
            v-model="start" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :placeholder="startPlaceholder"
            :clearable="false">
        </el-date-picker>
        <span :class="$style.separator">{{ rangeSeparator }}</span>
        <el-date-picker @change="(val) => handleChange(1, val)" :disabled="disabled" @focus="onFocus" @blur="onBlur"
            v-model="end" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" default-time="23:59:59"
            :placeholder="endPlaceholder" :clearable="false">
        </el-date-picker>
        <i @click="onClear" :class="$style.suffix" class="el-icon-circle-close"></i>
    </div>
</template>
<script>
const MODEL = 'UPDATE_MODEL';
export default {
    props: {
        value: {
            type: Array,
            default: () => []
        },
        startPlaceholder: {
            type: String,
            default: '开始日期'
        },
        endPlaceholder: {
            type: String,
            default: '结束日期'
        },
        rangeSeparator: {
            type: String,
            default: '-'
        },
        clearable: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        value: {
            handler(val = []) {
                let [start = '', end = ''] = val;
                this.start = start;
                this.end = end;
            },
            immediate: true
        }
    },
    model: {
        prop: 'value',
        event: MODEL
    },
    data() {
        return {
            start: '',
            end: '',
            isFocus: false
        }
    },
    computed: {
        hasValue() {
            return !!this.start || !!this.end
        },
        isClearable() {
            return !this.disabled && this.hasValue
        }
    },
    methods: {
        onFocus() {
            this.isFocus = true;
            // console.log('onFocus')
            // this.isFocus = true;
            // this._isFocus = true;
            // window.clearTimeout(this._focusTimer)
            // setTimeout(() => {
            //     this._isFocus = false;
            // }, 300);
            // window.clearTimeout(this._blurTimer)
        },
        onBlur() {
            this.isFocus = false;
            // this._blurTimer = setTimeout(() => {
            //     if (this._isFocus) return;
            //     this._isFocus = false;
            //     this.isFocus = false;
            // }, 300);
        },
        onClear() {
            console.log('onClear');
            if (!this.isClearable) return

            this.start = '';
            this.end = '';
            this.$emit(MODEL, [])
            this.$emit('change', [])
        },
        handleChange(index, val) {
            let result = [this.start, this.end];
            result[index] = val;
            this.$emit(MODEL, result)
            this.$emit('change', result)
        }
    }
}
</script>
<style lang="scss" module>
.container {
    display: flex;
    align-items: center;
    width: 400px;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #DCDDE0;
    line-height: 36px;
    box-sizing: border-box;
    overflow: hidden;
    &.active:hover {
        .suffix {
            opacity: 1;
        }
    }

    &.focus {
        border-color: #0056e5;
    }

    .separator {
        color: #999;
    }

    :global {
        .el-date-editor.el-input {
            flex: 1;
            width: 100%;
        }

        .el-input__inner {
            padding: 0;
            border: none;
            text-align: center;
        }

        .el-input__prefix {
            display: none;
        }
    }

    .prefix,
    .suffix {
        color: #CDCED3;
    }

    .suffix {
        opacity: 0;
    }

}

.flex {
    display: flex;

    .items-center {
        align-items: center;
    }

    .flex-none {
        flex: 0 0 auto;
    }

    .flex-1 {
        flex: 1;
    }
}

.px-5 {
    padding-left: 5px;
    padding-right: 5px;
}
</style>