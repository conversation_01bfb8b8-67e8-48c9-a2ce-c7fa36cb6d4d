<template>
  <el-dialog
    title="收银台"
    :visible.sync="cashierVisible"
    width="40%"
    @open="getFlow(1)"
    :close-on-click-modal="false"
    :before-close="handleClose">
    <ul class="caisher-list">
      <li><span>保证金名称：</span> {{data.name}}</li>
      <li><span>费用金额：</span> {{data.configAmount|getDecimals}}</li>
      <li><span>支付方式：</span>
        <el-radio-group v-model="query.flowOnline" @change="getFlow">
          <el-radio-button :label="1">汇款支付</el-radio-button>
        </el-radio-group>
      </li>
    </ul>
    <div v-if="query.flowOnline===0" class="text-center">
      <img :src="url" class="code">
    </div>
    <div v-if="query.flowOnline===1">
      <detail-item item-name="收款信息">
        <div style="padding-left: 35px;"><p>你需汇款 {{data.configAmount|getDecimals}} 元至以下账户，汇款成功后上传凭证信息，审核通过后到账</p>
        <p>收款方户名：{{query.accountName}}</p>
        <p>收款方开户行：{{query.accountBank}}</p>
        <p>收款方账户：{{query.account}}</p>
        <el-form :model="query" :rules="rules" ref="ruleForm" label-width="110px" class="demo-queryForm">
          <el-form-item label="付款户名：" prop="payAccountName">
            <el-input v-model="query.payAccountName" placeholder="请输入付款户名"></el-input>
          </el-form-item>
          <el-form-item label="付款账号：" prop="payAccount">
            <el-input v-model="query.payAccount" placeholder="请输入付款账号"></el-input>
          </el-form-item>
          <el-form-item label="付款时间：" prop="payTime">
            <el-date-picker v-model="query.payTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择付款时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="付款流水号：" prop="serialNumber">
            <el-input v-model="query.serialNumber" placeholder="请输入付款流水号"></el-input>
          </el-form-item>
          <el-form-item label="上传凭证：" prop="certificatePath">
            <el-upload
              action
              :show-file-list="false"
              :before-upload="handleBefore"
              :http-request="upload"
              accept=".jpg,.png,.bmp,.jpeg"
              :on-remove="handleRemove">
              <div>
                <img class="el-upload-list__item-thumbnail" v-if="query.certificatePath"  :src="query.certificatePath" alt="点击上传商家LOGO" style="width: 100px;height: 100px;border: 1px dashed #ddd;">
                <p v-if="!query.certificatePath" style="margin-top:0;width: 100px;height: 100px;border: 1px solid #ddd;background: #f5f5f5;"><span style="font-size: 26px;display: block;margin-top: 15px;">+</span>上传</p>

              </div>

            </el-upload>
          </el-form-item>
        </el-form>
        </div>
      </detail-item>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" v-if="query.flowOnline===1" @click="submit">提交</el-button>
      <el-button type="primary" v-if="query.flowOnline===0" @click="pay">已完成支付</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getValue,earnestMoney,platformearnEstMoney} from '@/api/finance'
  import DetailItem from '../..//merchant/list/detail-item'
  import { getUploadFileUrl,beforeFileUpload } from '@/api/upload'
export default {
    name: "cashier-dialog",
  props: ['cashierFlag','data','type'],
  components: {DetailItem},
  data() {
    const validateTime = (rule, value, callback) => {
      if (new Date(value).getTime() > Date.now()) {

        callback(new Error('选择时间必须小于等于当前时间'));
      } else {
        callback()
      }
    }
    return {
      // pickerOptions: {
      //   disabledDate (time) {
      //     return time.getTime() > new Date(new Date().toLocaleDateString()).getTime(); // 选当前时间之前的时间
      //   }
      // },
      cashierVisible: false,
      url: '',
      dialogImageUrl: '',
      query: {
        flowOnline: 1,
        accountName: '',
        accountBank: '',
        account: '',
        payAccountName: '',
        payAccount: '',
        payTime: '',
        serialNumber: '',
        certificatePath: '',
        amount: ''
      },
      rules: {
        payAccountName: [{required: true, message: '请输入付款户名', trigger: 'blur'}],
        payAccount: [{required: true, message: '请输入付款账户', trigger: 'blur'}],
        payTime: [
          {required: true, message: '请选择付款时间', trigger: 'blur'},
          {validator: validateTime, trigger: 'blur',message: '付款时间应小于等于当前时间'}],
        serialNumber: [{required: true, message: '请输入流水号', trigger: 'blur'}],
        certificatePath: [{required: true, message: '请输入上传付款凭证', trigger: 'blur'}],
      }
    }
  },
  methods: {
    getFlow(val) {
      this.query.flowOnline = val
      if (val===1) {
        this.getName()
        this.getAccount()
        this.getBankName()
      }
    },
    async getName() {
      const {data} = await getValue({defVal: '0',key: 'PLFROM_ACCNAME'})
      this.query.accountName = data
    },

    async getBankName() {
      const {data} = await getValue({defVal: '0',key: 'PLFROM_BANKNAME'})
      this.query.accountBank = data
    },
    async getAccount() {
      const {data} = await getValue({defVal: '0',key: 'PLFROM_ACCNO'})
      this.query.account = data
    },
    async getCode() {
      const {data} = await getValue({defVal: '0',key:'PLFROM_QR_CODE'})
      this.url = data
    },
    handleClose() {
      this.$emit('changeShow','false')
      this.cashierVisible = false
    },
    async upload(fileObj) {
      const {data} = await getUploadFileUrl(fileObj.file)
      this.query.certificatePath = data.url
      this.dialogImageUrl = data.url
    },
    handleRemove(file, fileList) {
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleBefore (file) {
      return beforeFileUpload(file, this.$message)
    },
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.type==='deposit') {
            earnestMoney(this.query).then(res=>{
              if(res.code == 0) {
                this.$message.success('提交成功！')
                this.$emit('changeShow','false')
                this.cashierVisible = false
              }
            })
          } else if(this.type==='platform') {
            platformearnEstMoney(this.query).then(res=>{
              if(res.code == 0) {
                this.$message.success('提交成功！')
                this.$emit('changeShow','false')
                this.cashierVisible = false
              }
            })
          }


        } else {
          return false;
        }
      });
    },
    pay() {

    }
  },
  watch: {
    cashierFlag() {
      this.cashierVisible = this.cashierFlag
    },
    data() {
      this.query.amount = this.data.configAmount
    }
  }
}
</script>

<style lang="scss" scoped>
  .demo-queryForm {
    .el-input {
      width: 220px;
    }
  }
  .code {
    width: 150px;
  }
  .caisher-list {
    margin-top: -10px;
    li {list-style: none;line-height: 50px;}
    .caisher-left {
      display: inline-block;
      width: 100px;
      text-align: right;
    }
  }
</style>
