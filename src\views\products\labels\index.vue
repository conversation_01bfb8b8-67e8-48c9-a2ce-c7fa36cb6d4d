<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload"
                  @search="searchLoad"
    >
      <im-search-pad-item prop="labelName">
        <el-input v-model.trim="model.labelName" @keyup.enter.native="searchLoad" placeholder="请输入标签名称"/>
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '商品标签' }]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button type="primary" v-if="checkPermission(['admin', 'sale-saas-product-label:add', 'sale-platform-product-label:add'])" @click="showDialogFun">+ 新增商品标签</el-button>
        </template>
      </tabs-layout>

      <!-- table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
                  :pageSize="pageSize" :operation-width="180"
      >
        <template slot="labelType">
          <el-table-column label="标签类型" width="150">
            <slot slot-scope="{ row }">
              <!-- {EXTERNAL:100,外部标签;INTERNAL:110,内部标签;} -->
              {{
                row.labelType != undefined && row.labelType.code == 'EXTERNAL'
                  ? '外部标签'
                  : '内部标签'
              }}
            </slot>
          </el-table-column>
        </template>
        <template slot="exhibit">
          <el-table-column label="标签展示" width="150">
            <slot slot-scope="{ row }">
              <div class="boxStyle disp" :style="{ backgroundColor: row.labelColor }">
                {{ row.labelName }}
              </div>
            </slot>
          </el-table-column>
        </template>
        <template slot="showStatus">
          <el-table-column label="前端是否显示" width="200">
            <slot slot-scope="{ row }">
              <el-radio-group v-model="row.showStatus.code" @change="showStatusChange(row)">
                <el-radio label="Y">显示</el-radio>
                <el-radio label="N">隐藏</el-radio>
              </el-radio-group>
            </slot>
          </el-table-column>
        </template>
        <template slot="productQuantity">
          <el-table-column label="关联商品" width="140">
            <slot slot-scope="{ row }">
              <el-button  v-if="checkPermission(['admin', 'sale-saas-product-label:product-releation', 'sale-platform-product-label:product-releation'])" type="text" @click="handlEassociation(row.id)">
                {{ row.productQuantity }}
              </el-button>
              <span v-else>{{ row.productQuantity }}</span>
            </slot>
          </el-table-column>
        </template>
        <!--操作栏-->
        <div slot-scope="{ row }">
          <el-row class="table-edit-row">
                        <span class="table-edit-row-item">
                            <el-button type="text" @click="showImportFun(row.id)" v-if="checkPermission(['admin','sale-saas-product-label:product-import','sale-platform-product-label:product-import'])">导入商品</el-button>
                            <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-product-label:add', 'sale-platform-product-label:add'])" @click="handleEdit(row.id)">编辑</el-button>
                            <del-el-button v-if="!Boolean(row.productQuantity) && checkPermission(['admin', 'sale-saas-product-label:del', 'sale-platform-product-label:del'])" style="margin-left: 5px" :targetId="row.id" :text="delText"
                                @handleDel="handleDel">
                            </del-el-button>
                        </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog :title="diaText" v-dialogDrag :visible.sync="dialogVisible" width="500px">
      <el-form ref="productFrom" :model="productFrom" label-width="130px">
        <el-form-item label="标签名称：" prop="labelName" :rules="[
                    { required: true, trigger: 'blur', message: '请选择标签名称' },
                ]"
        >
          <el-input v-model="productFrom.labelName" style="width: 200px" :maxlength="5"
                    placeholder="请输入标签名称,限五个字"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="标签类型：" prop="labelType" :rules="[
                    { required: true, trigger: 'blur', message: '请选择标签类型' },
                ]"
        >
          <!-- {EXTERNAL:100,外部标签;INTERNAL:110,内部标签;} -->
          <el-select v-model="productFrom.labelType" style="width: 200px" placeholder="请选标签类型">
            <el-option label="请选择" value=""></el-option>
            <el-option label="外部标签" value="EXTERNAL"></el-option>
            <el-option label="内部标签" value="INTERNAL"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签颜色：" prop="labelColor" :rules="[
                    { required: true, trigger: 'change', message: '请选择标签颜色' },
                ]"
        >
          <div style="display: flex; align-items: center">
            <el-color-picker v-model="productFrom.labelColor"></el-color-picker>
            <div class="boxStyle" v-if="
                            productFrom.labelName != undefined &&
                            productFrom.labelName.length > 0 &&
                            productFrom.labelColor != '#FFFFFF'
                        " :style="{ backgroundColor: productFrom.labelColor }"
            >
              {{ productFrom.labelName }}
            </div>
          </div>
        </el-form-item>
        <el-form-item class="formItem" prop="sort" label="分类排序:">
          <el-input-number clearable :min="1" v-model="productFrom.sort" placeholder="请填写分类排序">
          </el-input-number>
        </el-form-item>
        <el-form-item class="formItem" prop="showStatus" label="是否前端显示:" :rules="[
                    {
                        required: true,
                        message: '请填写是否前端显示',
                        trigger: 'change',
                    },
                ]"
        >
          <el-radio v-model="productFrom.showStatus" label="Y">显示</el-radio>
          <el-radio v-model="productFrom.showStatus" label="N">隐藏</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
    </el-dialog>
    <el-dialog title="商品列表" append-to-body v-if="productStatus" width="85%" v-dialogDrag lock-scroll :close-on-click-modal="false" top="4.5vh" :visible.sync="productStatus"
              :before-close="closeProduct" class="componentsList"
    >
      <div slot="title" class="title"></div>
      <product-list :productLabelId="currentId" @closeProduct="closeProduct">
      </product-list>
    </el-dialog>
    <!--    导入商品弹窗-->
    <importDialog ref="importDialogRef" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="true" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
  </div>
</template>

<script>
const TableColumns = [
  {
    label: '标签编码',
    name: 'labelCode',
    prop: 'labelCode',
    width: '120'
  },
  {
    label: '标签名称',
    name: 'labelName',
    prop: 'labelName',
    width: '150'
  },
  {
    label: '标签类型',
    name: 'labelType',
    prop: 'labelType',
    width: '150',
    slot: true
  },
  {
    label: '标签颜色',
    name: 'labelColor',
    prop: 'labelColor',
    width: '120'
  },
  {
    label: '标签展示',
    name: 'exhibit',
    prop: 'exhibit',
    width: '150',
    slot: true
  },
  {
    label: '排序',
    name: 'sort',
    prop: 'sort',
    width: '100'
  },
  {
    label: '前端是否展示',
    name: 'showStatus',
    prop: 'showStatus',
    width: '250',
    slot: true
  },
  {
    label: '关联商品',
    name: 'productQuantity',
    prop: 'productQuantity',
    width: '140',
    slot: true
  }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}
import delElButton from '@/components/eyaolink/delElButton'
import productList from '@/views/products/labels/components/productList'
import importDialog from "@/components/eyaolink/importDialog/index"
import checkPermission from '@/utils/permission'

import { fetchLabslList, fetchLabel, updateLabel, addLabel, deleteLabel } from '@/api/products/label'

export default {
  //import引入的组件
  components: {
    delElButton,
    productList,
    importDialog
  },

  data() {
    return {
      isExpand: false,
      model: {
        labelName: ''
      },
      diaText: '新增商品标签',
      productFrom: {
        labelType: '',
        labelName: '',
        labelColor: '#00CB25',
        sort: 1,
        showStatus: 1
      },
      currentId: '',
      delText: '您确定删除该商品标签吗？',
      dialogVisible: false,
      productStatus: false,
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
      actionUploadUrl: '/api/product/admin/productImport/importProductExcel',
      templateKey: 'IMPORT_PRODUCT_EXCEL_TEMP',
      queryParams:{
        productType: 'IMPORT_PRODUCT_LABEL',
        importId:''
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.productFrom = {
          labelType: '',
          labelName: '',
          labelColor: '#00CB25',
          sort: 1,
          showStatus: 1
        }
      }
    }
  },
  //方法集合
  methods: {
    checkPermission,
    async load(params) {
      let listQuery = {
        model: this.model
      }
      Object.assign(listQuery, params)
      return await fetchLabslList(listQuery)
    },
    // 表格中前端是否展示的radio的改变事件
    showStatusChange(val) {
      let params = {
        labelType: val.labelType.code,
        labelName: val.labelName,
        labelColor: val.labelColor,
        sort: val.sort,
        showStatus: val.showStatus.code,
        id: val.id
      }
      updateLabel(params).then((res) => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('修改显示状态成功')
          this.reload()
        }
      })
    },
    //    编辑
    handleEdit(id) {
      fetchLabel(id).then((res) => {
        if (res.code == 0 && res.msg == 'ok') {
          this.dialogVisible = true
          this.diaText = '编辑商品标签'
          this.productFrom = {
            labelType: res.data.labelType.code,
            labelName: res.data.labelName,
            id: res.data.id,
            showStatus: res.data.showStatus.code,
            labelColor: res.data.labelColor,
            sort: res.data.sort
          }
        }
      })
    },
    // 删除
    handleDel(id) {
      deleteLabel(id).then((res) => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('删除成功')
          this.reload()
        }
      })
    },
    // 新增商品标签
    showDialogFun() {
      this.diaText = '新增商品标签'
      this.dialogVisible = true
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    //   关联商品弹窗
    handlEassociation(id) {
      this.currentId = id
      this.productStatus = true
    },
    closeProduct() {
      this.productStatus = false
      this.reload()
    },
    submit() {
      this.$refs['productFrom'].validate((valid) => {
        if (valid) {
          // 验证成功
          let params = {
            ...this.productFrom
          }
          if (params.id && this.diaText == '编辑商品标签') {
            // 编辑
            updateLabel(params).then((res) => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('修改商品标签成功')
                this.dialogVisible = false
                this.reload()
              }
            })
          } else {
            // 新增
            addLabel(params).then((res) => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('新增商品标签成功')
                this.dialogVisible = false
                this.reload()
              }
            })
          }
        } else {
          // 验证失败
        }
      })
    },
  //  导入商品
    showImportFun(id) {
      this.queryParams.importId = id;
      this.$refs.importDialogRef.initOpen();
    },
    //  导入商品回调
    uploadChangeData(){
      this.reload();
    },
  }
}
</script>

<style lang="scss" scoped>
.boxStyle {
  margin-left: 10px;
  padding: 0 10px;
  border-radius: 5px;
  color: #fff;
}

.disp {
  display: inline;
  padding: 5px 10px;
}

.componentsList {
  ::v-deep .el-dialog__headerbtn {
    z-index: 99999;
  }

  ::v-deep .el-dialog__header {
    padding: 0 !important;
    border-bottom: none !important;
  }

  ::v-deep .el-dialog__body {
    padding: 10px 0 30px !important;

    .im-search-pad {
      margin-left: 20px !important;
    }

    .bottom_btn {
      margin-right: 20px !important;
    }
  }

  ::v-deep .varietiesBan-list-container .varietiesBan-list-tabs-wrapper .varietiesBan-list-tabs {
    padding-left: 20px
  }
}

</style>
