<template>
  <div class="archivesPageContent">
    <div class="title flex_between_center">
      <div>
        <el-tabs :value="'list'" class="typeTabs">
          <el-tab-pane label="在线客服" name="list"></el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <el-button type="primary" @click="newFun">+新增客服</el-button>
      </div>
    </div>

    <div class="table">
      <el-table ref="singleTable" v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
        <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in tableTitle['list']" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip
          align="center">
          <template slot-scope="{row}">
            <span>{{ row[item.name] || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" align="center" label="操作" width="130" class="itemAction">
          <template slot-scope="scope">
            <el-button @click="detailFun(scope.row)" type="text" >编辑</el-button>
            <el-button @click="deleteFun(scope.row)" type="text" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
    </div>
    <el-dialog v-if="showEdit" :title="row.id?'编辑客服':'添加客服'" :visible.sync="showEdit" width="500px" @close='closed'>
      <el-form ref="serviceForm" :model="row" label-width="110px" :rules="rule">

        <el-form-item label="坐席名称:" prop="serviceType">
          <el-input v-model="row.serviceType" style="width: 300px" placeholder="请输入坐席名称"></el-input>
        </el-form-item>

        <el-form-item label="客服qq:" prop="serviceQq">
          <el-input v-model="row.serviceQq" style="width: 300px" placeholder="请输入客服QQ"></el-input>
        </el-form-item>

        <!-- <el-form-item label="客服类型:" prop="serviceType">
          <el-select v-model="row.serviceType" placeholder="请选择客服类型" style="width: 300px">
            <el-option v-for="item in options"  :key="item.type" :label="item.label" :value="item.type"></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closed">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import tableInfo from "@/views/callCenter/onlineService/tableInfo";
import Pagination from "@/components/Pagination";
import { list, delFun, add, reset } from "@/api/callCenter/onlineService";
import rules from "@/utils/rules";
export default {
  data() {
    return {
      rule: {
        serviceQq: rules.QQ,
        serviceType: [
          { required: true, message: "请输入坐席名称", trigger: "blur" },
        ],
      },
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      showEdit: false,
      list: [],
      listLoading: false,
      tableTitle: tableInfo,
      total: 0,
      row: {},
      // options: [
      //   {
      //     label: '订单客服',
      //     type: 'ORDERSERVICE'
      //   },
      //   {
      //     label: '财务客服',
      //     type: 'FINANCESERVICE'
      //   },
      //   {
      //     label: '开票客服',
      //     type: 'INVOICESERVICE'
      //   },
      //   {
      //     label: '物流客服',
      //     type: 'LOGISTICSSERVICE'
      //   }
      // ]
    };
  },
  methods: {
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    newFun() {
      this.showEdit = true;
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    onSubmit() {
      let that = this;
      this.$refs.serviceForm.validate(async (valid) => {
        if (valid) {
          if (this.row.id) {
            let { data } = reset(this.row);
            this.$message.success("修改成功");
            this.showEdit = false;
            for(let i=0;i <this.list.length; i++) {
              if(this.list[i].id == this.row.id) {
                this.$set(this.list,i,this.row)
              }
            }
            this.row = {}
          } else {
            let { data } = await add(this.row);
            this.$message.success(
              "已添加坐席名称为 “" + this.row.serviceType + "” 的客服"
            );
            this.row = {};
            this.showEdit = false;
            this.getlist();
            that.setCurrent(data);
          }
        }
      });
    },
    closed() {
      this.row = {};
      this.showEdit = false;
    },
    async deleteFun(row) {
      let { data } = await delFun({ ids: [row.id] });
      if (data) {
        this.$message.success("已删除QQ为" + row.serviceQq + "客服信息");
      }
      this.list.forEach((item, index) => {
        if (item.id == row.id) {
          this.list.splice(index, 1);
        } else {
          console.log(33333);
        }
      });
    },
    detailFun(item) {
      this.row = JSON.parse(JSON.stringify(item));
      this.showEdit = true;
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  created() {
    this.getlist();
  },
  components: {
    Pagination,
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-top: 16px solid #f2f3f4;
    border-bottom: 2px solid #ebecee;
    margin-bottom: 20px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
    padding: 0 12px;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 10px;
  }
}
</style>
