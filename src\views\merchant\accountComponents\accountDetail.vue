<template>
  <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" width="80%">
    <div class="member_list">
      <im-search-pad :model="listQuery" @reset="handleReset" :hasExpand="false" @search="onSubmit">
        <im-search-pad-item prop="code">
          <el-input v-model.trim="listQuery.model.billNo" placeholder="请输入业务单号" />
        </im-search-pad-item>
        <im-search-pad-item prop="code">
          <el-select v-model="listQuery.model.transactionDirection" placeholder="请选择收支类型">
            <el-option v-for="item in transactionDirectionData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </im-search-pad-item>
        <im-search-pad-item prop="timeRange">
          <el-date-picker
            v-model="listQuery.model.timeRange"
            range-separator="-"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            start-placeholder="开始时间"
            :default-time="['00:00:00', '23:59:59']"
            unlink-panels
            end-placeholder="结束时间"
          />
        </im-search-pad-item>
      </im-search-pad>
      <div class="tab_bg">
        <table-pager
          ref="todoTable"
          :options="tableTitle"
          :data.sync="tableData"
          :operation-width="240"
          :remote-method="load"
          :isNeedButton="false"
        >
          <el-table-column label="收支类型" width="90" slot="transactionDirection">
            <slot slot-scope="{row}">
              {{ row.transactionDirection && row.transactionDirection.desc }}
            </slot>
          </el-table-column>
          <el-table-column label="记账数量" width="150" slot="amount">
            <slot slot-scope="{row}">
              <span v-if="row.transactionDirection && row.transactionDirection.code === 'EXPENSES'" style="color: red">
                -{{ row.amount }}
              </span>
              <span v-else>{{ row.amount }}</span>
            </slot>
          </el-table-column>
        </table-pager>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getCustomerPointDetail } from '@/api/retailStore'
import { deepClone } from "@/utils";

const TableColumns = [
  {
    label: '记账时间',
    prop: 'updateTime',
  },
  {
    label: "业务单号",
    prop: 'billNo',
  },
  {
    label: '收支类型',
    name: 'transactionDirection',
    slot: true
  },
  {
    label: '记账数量',
    name: 'amount',
    slot: true,
  },
  {
    label: '备注',
    prop: 'remark'
  },
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}

export default {
  name: 'pointDetail',
  data() {
    return {
      totalPage: 0,
      tableTitle: TableColumnList,
      tableData: [],
      name: '',
      list: [],
      total: 0,
      transactionDirectionData: [
        { label: '收入', value: 'INCOME' },
        { label: '支出', value: 'EXPENSES' },
      ],
      listLoading: false,
      listQuery: {
        model: {
          accountId: '',
          billNo: '',
          transactionDirection: '',
          timeRange: []
        }
      },
      loading: false,
      visible: false,
    }
  },
  computed: {
    title() {
      return '账户明细 - ' + ''
    }
  },
  methods: {
    show(accountId, name) {
      this.visible = true
      this.name = name
      this.listQuery.model.accountId = accountId
      this.handleReset()
    },
    hide() {
      this.visible = false
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      let listQuery = deepClone(this.listQuery)
      const model = listQuery.model
      if (Array.isArray(model.timeRange) && model.timeRange.length > 0) {
        listQuery.model.beginTime = model.timeRange[0]
        listQuery.model.endTime = model.timeRange[1]
      } else {
        listQuery.model.beginTime = undefined
        listQuery.model.endTime = undefined
      }
      this.listLoading = true
      try {
        const res = await getCustomerPointDetail(listQuery)
        this.totalPage = res.data.pages
        this.total = res.data.total
        return res
      } catch (e) {
        this.listLoading = false
      }
    },
    handleReset() {
      this.listQuery.model = {
        ...this.listQuery.model,
        billNo: '',
        transactionDirection: '',
        timeRange: []
      }
      this.onSubmit()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable?.doRefresh(pageParams)
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
  }
}
</script>

<style lang="scss" scoped></style>
