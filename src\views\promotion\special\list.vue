<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="onSubmit"
    >
      <im-search-pad-item prop="featuredName">
        <el-input v-model.trim="model.featuredName" placeholder="请输入专题活动名称" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <tabs-layout
        :tabs="[{ name: '专题列表' }]"
      >
        <template slot="button">
          <el-button @click="onSubmit">刷新</el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-topic:add', 'sale-platform-promotion-topic:add'])" type="primary" @click="showEditSpecial()">+ 新增专题</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData" :operation-width="180">
        <template slot="productAmount">
          <el-table-column label="推荐商品数">
            <slot slot-scope="{row}">
              <a v-if="checkPermission(['admin','sale-saas-promotion-topic:commandProduct', 'sale-platform-promotion-topic:commandProduct'])" class="primaryColor" @click="handleProduct(row.id)" href="javascript:void(0)">{{ row.productAmount||0 }}</a>
              <span v-else>{{ row.productAmount||0 }}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          
          <el-button type="text" @click="showImportFun(props.row.id)" v-if="checkPermission(['admin','sale-saas-promotion-topic:importProduct', 'sale-platform-promotion-topic:importProduct'])">导入商品</el-button>
          <el-divider v-if="checkPermission(['admin','sale-saas-promotion-topic:importProduct', 'sale-platform-promotion-topic:importProduct'])" direction="vertical" />
          <del-el-button
            v-if="(props.row.status.code == 'ENABLED') && checkPermission(['admin','sale-saas-promotion-topic:del', 'sale-platform-promotion-topic:del'])"
            :targetId="props.row.id"
            :text="delText"
            :btnText="'移除'"
            @handleDel="delSpecial"
          ></del-el-button>
          <el-divider v-if="(props.row.status.code == 'ENABLED')" direction="vertical" />
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-topic:edit', 'sale-platform-promotion-topic:edit'])" type="text" @click="showEditSpecial(props.row)">编辑</el-button>
        </div>
      </table-pager>
        <el-dialog v-if="editDiallog"
            :title="title"
            :visible.sync="editDiallog"
            width="500px"
            v-dialogDrag
            @close='closeEditDiallog'>
            <el-form ref="addUserForm" :model="formData" label-width="110px" :rules="rule">
                <el-form-item label="专题页名称：" prop="featuredName">
                    <el-input v-model="formData.featuredName" style="width: 300px" placeholder="请输入专题名称"  clearable></el-input>
                </el-form-item>
                <el-form-item label="专题页描述:" prop="featuredDescribe">
                    <el-input type="textarea" v-model="formData.featuredDescribe" style="width: 300px" placeholder="请输入专题页描述" ></el-input>
                </el-form-item>

            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeEditDiallog">取 消</el-button>
                <el-button type="primary" @click="editSpecial">确 定</el-button>
            </span>
        </el-dialog>
        <div v-if="featuredProductEditDiallog" >
          <chooseProduct :pageId="pageId" :visible.sync="featuredProductEditDiallog"></chooseProduct>
        </div>

    </div>
    <!--    导入商品弹窗-->
    <importDialog ref="importDialogRef" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="true" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
  </div>
</template>

<script>
import { getList, editOrAddpage, handleDel } from '@/api/featured'
import delElButton from "@/components/eyaolink/delElButton";
import chooseProduct from "@/views/promotion/special/chooseProduct.vue";
import importDialog from "@/components/eyaolink/importDialog/index"
import checkPermission from '@/utils/permission'

const TableColumns = [
  { label: '专题页名称', name: 'featuredName', prop: 'featuredName' },
  { label: '专题页描述', name: 'featuredDescribe', prop: 'featuredDescribe' },
  { label: '推荐商品数', name: 'productAmount', prop: 'productAmount', slot: true },
  { label: '操作时间', name: 'updateTime', prop: 'updateTime' }
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  name: 'topicPageManagement',
  components: {
    delElButton,
    chooseProduct,
    importDialog
  },
  data() {
    const validateName = (rule, value, callback) => {
      if (value == '' || value === null) {
        callback(new Error('专题名称不能为空'))
      } else {
        callback()
      }
    }
    return {
      formData:{
          featuredDescribe:"",
          featuredName:""
      },
      rule: {
        featuredName: [{ required: true, trigger: 'blur', validator: validateName }]
      },
      delText:'您确定移除该专题页吗？',
      editDiallog: false,
      tableTitle: TableColumnList,
      tableVal: [],
      loading: false,
      showCoupon: false,
      showAdd: false,
      pageId:'',
      model: {
        "featuredName": ""
      },
      during: '',
      tableData: [],
      goodsList: [],
      itemDetail: '',
      featuredProductEditDiallog:false,
      actionUploadUrl: '/api/product/admin/productImport/importProductExcel',
      templateKey: 'IMPORT_PRODUCT_EXCEL_TEMP',
      queryParams:{
        productType: 'IMPORT_PRODUCT_FEATURED_PAGE',
        importId:''
      },
      title: '新增专题'
    }
  },
  watch:{
    featuredProductEditDiallog(val,oldVal){
      if(!val && oldVal){
        this.reload();
      }
    }
  },
  methods: {
    checkPermission,
    async postCouponList(params) {
      const listQuery = {
        model: {
          ...this.model
        }

      }
      Object.assign(listQuery, params)
      this.loading = true
      return await getList(listQuery)
    },

    // 删除专题
    async delSpecial(id) {
      const data = await handleDel(id)
      if (data.code === 0) {
        this.$message.success('删除成功！')
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      }
    },
    // 新增或者编辑
    showEditSpecial(obj=null) {
        if(obj==null){
            this.formData={
                featuredDescribe:"",
                featuredName:""
            }
          this.title = '新增专题';
        }else{
            this.formData={
                ...obj
            }
            this.title = '编辑专题';
        }
      this.editDiallog=true
    },
    async editSpecial(){
      let {code, data,msg} = await editOrAddpage(this.formData)
      if (code === 0) {
        let str = `${this.formData.id==undefined ? '新增' : '编辑'}成功！`
        this.$message.success(str)
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
        this.editDiallog=false
      }else{
          this.$message.error(msg)
      }
    },
    closeEditDiallog(){
        this.editDiallog=false
    },
    closeFeaturedProductEditDiallog(){
        this.featuredProductEditDiallog=false
    },
    onSubmit() {
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      return await this.postCouponList()
    },
    reload() {
      this.model = {}
      this.during = ''
      this.$refs.todoTable.doRefresh()
    },
    handleProduct(id){
      this.featuredProductEditDiallog = true;
      this.pageId = id;
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
    },
    //  导入商品
    showImportFun(id) {
      this.queryParams.importId = id;
      this.$refs.importDialogRef.initOpen();
    },
    //  导入商品回调
    uploadChangeData(){
      this.reload();
    },
  }
}
</script>

<style lang="scss" scoped>
.primaryColor{
  color: #409EFF;
}
.featuredProductEdit{
  position:fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content:center;
  align-items: center;
  z-index: 1002;
  background: rgba(0,0,0, .5);

}
</style>
