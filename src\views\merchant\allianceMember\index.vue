<template>
  <div>
    <im-search-pad :is-expand.sync="isExpand" :model="listQuery" @reset="handleReset" @search="onSubmit">
      <im-search-pad-item prop="customerCode">
        <el-input @keyup.enter.native="onSubmit" v-model.trim="listQuery.model.customerCode" placeholder="请输入ERP客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input @keyup.enter.native="onSubmit" v-model.trim="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="address">
        <el-cascader v-model="listQuery.model.address" placeholder="请选择所在区域" :options="options" :props="{
          checkStrictly: true, expandTrigger: 'hover',
          value: 'id',
          label: 'label',
          children: 'children'
        }" clearable style="width: 240px;" @change="parentChangeAction" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="contacts">
        <el-input @keyup.enter.native="onSubmit" v-model.trim="listQuery.model.contacts" placeholder="请输入企业联系人" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="tabList" v-model="listQuery.model.effectiveStatus.code"
        @change="handleChangeTab">
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'sale-saas-member:price-set', 'sale-platform-member:price-set'])" @click="memberSett">会员价设置</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-member:add', 'sale-platform-member:add'])" type="primary" @click="showAddAlliance">+ 联盟会员</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :operation-width="130"
        :remote-method="load" :selection="false">
        <template slot="effectiveStatus">
          <el-table-column label="生效状态" width="100">
            <slot slot-scope="{row}">
              <span>{{ row.effectiveStatus && row.effectiveStatus.desc }}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="endTime">
          <el-table-column show-overflow-tooltip label="会员效期" width="200">
            <slot slot-scope="{row}">
              <span v-if="row.startTime != null && row.endTime != null">{{ row.startTime.split(' ')[0] }} 至
                {{ row.endTime.split(' ')[0] }}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-member:edit', 'sale-platform-member:edit'])" @click="handleEdit(props.row)">编辑</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-member:del', 'sale-platform-member:del'])" @click="delFun(props.row)">移除</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!-- 添加联盟会员 -->
    <add-alliance ref="addAllianceRef" @addSuccess="reload" />
    <!-- 编辑会员日期 -->
    <el-dialog v-dialogDrag title="设置会员效期" :visible.sync="memberTimeVisible" width="500px">
      <el-form ref="memberTimeForm" :model="memberTimeForm" label-width="130px" :rules="memberTimeFormRules">
        <el-form-item label="效期开始时间：" prop="startTime">
          <el-date-picker v-model="memberTimeForm.startTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="效期结束时间：" prop="endTime">
          <el-date-picker v-model="memberTimeForm.endTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm('memberTimeForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('memberTimeForm')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 会员价设置 -->
    <el-dialog v-dialogDrag title="会员价设置" :visible.sync="memberDialog" width="30%" :before-close="handleClose">
      <el-form :model="memberForm" ref="ruleForm" label-width="100px">
        <!--  :rules="[{validator:coefficientValidator,required: true,trigger: 'blur'}]" -->
        <el-form-item label="会员价系数:" prop="memberPriceCoefficient">
          <el-input v-model="memberForm.memberPriceCoefficient" placeholder="请输入会员价系数"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitMember">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMemberManagementCount,
  editMemberManagement,
  deleteMemberManagement,
  memberPage,
  memberDetail,
  getManagementRelBySaleMerchantId,
  saveOrUpdate
} from "@/api/merchantApi/allianceMember"
import {
  trees
} from '@/api/group';
import checkPermission from '@/utils/permission'
import AddAlliance from '@/views/merchant/allianceMember/addAlliance'

const TableColumns = [{
  label: '生效状态',
  prop: 'effectiveStatus',
  name: 'effectiveStatus',
  width: '100',
  slot: true
},
{
  label: 'ERP客户编码',
  prop: 'customerCode',
  width: '145'
},
{
  label: '客户名称',
  prop: 'purMerchantName',
  width: '150'
},
{
  label: '客户类型',
  prop: 'merchantType',
  width: '150'
},
{
  label: '联系人',
  prop: 'contacts',
  width: '150'
},
{
  label: '联系电话',
  prop: 'contactNumber',
  width: '120'
},
{
  label: '所在地区',
  prop: 'detailAddress',
  width: '180'
},
{
  label: '会员效期',
  prop: 'endTime',
  width: '200',
  name: 'endTime',
  slot: true
},
{
  label: "最新操作时间",
  prop: 'updateTime',
  name: 'updateTime',
  width: '170'
},
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}

export {
  TableColumnList
}

export default {
  name: 'unionMembers',
  components: {
    AddAlliance
  },
  data() {
    return {
      memberDialog: false,
      memberForm: {
        memberPriceCoefficient: 1.0
      },
      isExpand: false,
      showSelectTitle: false,
      tableTitle: TableColumnList,
      costometList: [],
      ids: [],
      tableData: [],
      tableVal: [],
      list: [],
      total: 0,
      page: 0,
      listLoading: false,
      rules: {
        firstCampStatusEnum: [{
          required: true,
          trigger: 'blur',
          message: '请选择变更状态'
        }],
        remark: [{
          required: true,
          trigger: 'blur',
          message: '请选择变更备注'
        }]
      },
      memberTimeVisible: false,
      listQuery: {
        model: {
          contacts: '',
          cityId: '',
          countyId: '',
          customerCode: '',
          effectiveStatus: {
            code: 'All'
          },
          provinceId: '',
          purMerchantName: ''
        }
      },
      form: {
        ceoName: '',
        name: '',
        code: '',
        address: '',
        merchantGroup: '',
        purMerchantCode: ''
      },
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first',
      options: [],
      memberTimeForm: {
        startTime: '',
        endTime: '',
        id: '',
      },
      tabNum: {
        allCount: 0,
        effectCount: 0,
        invalidCount: 0
      },
      memberTimeFormRules: {
        startTime: [{
          required: true,
          message: '请选择效期开始时间',
          trigger: 'change'
        }],
        endTime: [{
          required: true,
          message: '请选择效期结束时间',
          trigger: 'change'
        }],
      }
    }
  },
  created() {
    this.getArea();
    this.getTableCount();
  },
  computed: {
    tabList() {
      return [{
        name: '全部' + ((this.tabNum.allCount) > 0 ? '(' + this.tabNum.allCount + ')' : ''),
        value: 'All',
        hide: !checkPermission(['admin', 'sale-saas-member:view', 'sale-platform-member:view'])
      },
      {
        name: '生效中' + ((this.tabNum.effectCount) > 0 ? '(' + this.tabNum.effectCount + ')' : ''),
        value: 'EFFECT',
        hide: !checkPermission(['admin', 'sale-saas-member-valid:view', 'sale-platform-member-valid:view'])
      },
      {
        name: "已失效" + ((this.tabNum.invalidCount) > 0 ? '(' + this.tabNum.invalidCount + ')' : ''),
        value: 'INVALID',
        hide: !checkPermission(['admin', 'sale-saas-member-invalid:view', 'sale-platform-member-invalid:view'])
      }
      ]
    }
  },
  watch: {
    memberTimeVisible(val) {
      if (!val) {
        this.memberTimeForm = {
          startTime: '',
          endTime: '',
          id: '',
        }
      }
    }
  },
  methods: {
    checkPermission,
    coefficientValidator(rule, value, callback) {
      if (!value) {
        return callback(new Error('会员价系数不能为空'));
      } else {
        if (isNaN(value)) {
          return callback(new Error('请输入数字'));
        } else if (!isNaN(value) && Number(value) <= 0) {
          return callback(new Error('会员价系数不能小于或等于0'));
        } else if (!isNaN(value) && Number(value) > 1) {
          return callback(new Error('会员价系数不能大于1'));
        } else if (value.indexOf('.') != -1) {
          if (value.split('.')[1].length > 2) {
            return callback(new Error('会员价系数不能超过两位小数'))
          } else {
            return callback();
          }
        } else {
          return callback();
        }
      }
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    getTableCount() {
      if (this.tabList.every(item => item.hide)) return
      getMemberManagementCount().then(res => {
        // console.log('=======>',res);
        if (res.code == 0 && res.msg == 'ok') {
          this.tabNum.allCount = res.data.allCount;
          this.tabNum.effectCount = res.data.effectCount;
          this.tabNum.invalidCount = res.data.invalidCount;
        }
      })
    },
    handleChangeTab(tab, index) {
      console.log('tan--> index--', tab, index);
      if (tab.value == 'All') {
        this.listQuery.model.effectiveStatus.code = '';
      } else {
        this.listQuery.model.effectiveStatus.code = tab.value;
      }
      this.onSubmit();
    },
    handleEdit(row) {
      console.log('row----->', row);
      this.memberTimeVisible = true;
      this.memberTimeForm.id = row.id;
      memberDetail(row.id).then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          if (res.data.startTime != null && res.data.endTime != null) {
            this.memberTimeForm.startTime = res.data.startTime;
            this.memberTimeForm.endTime = res.data.endTime;
          }
        }
      })
    },
    // 地区
    async getArea() {
      const {
        data
      } = await trees();
      if (data.length > 0) {
        data.forEach(element => {
          if (element.id != '0') {
            this.options.push(element);
          }
        });
      }
    },
    parentChangeAction(val) {
      this.listQuery.model = {
        provinceId: val[0],
        cityId: val[1],
        countyId: val[2]
      }
    },

    async load(params) {
      if (this.tabList.every(item => item.hide)) return
      this.listLoading = true
      Object.assign(this.listQuery, params);
      this.ids = [];

      if (this.listQuery.model.effectiveStatus.code == 'All') {
        this.listQuery.model.effectiveStatus.code = '';
      }

      return await memberPage(this.listQuery)
    },
    handleReset() {
      this.$refs['tabs-layout'].reset();
      this.getTableCount();
      this.listQuery = {
        model: {
          contacts: '',
          cityId: '',
          countyId: '',
          customerCode: '',
          effectiveStatus: {
            code: 'All'
          },
          provinceId: '',
          purMerchantName: ''
        }
      }
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    showAddAlliance() {
      this.$refs.addAllianceRef.show()
    },
    reload() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      });
      this.getTableCount();
    },
    // 清除选择时间
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.memberTimeVisible = false;
    },
    // 提交效期
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.memberTimeForm);
          editMemberManagement(this.memberTimeForm).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('修改有效期成功');
              this.reload();
              this.memberTimeVisible = false;
            }
          })
        }
      })
    },
    delFun(row) {
      this.$confirm("您确定移除该客户吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteMemberManagement(row.id).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('删除成功！');
            this.reload();
          }
        })
      })
    },
    // 会员价设置
    memberSett() {
      getManagementRelBySaleMerchantId().then(res => {
        if (res.code == 0 && res.msg == 'ok') {
          this.memberForm.memberPriceCoefficient = res.data && res.data.memberPriceCoefficient;
          this.memberDialog = true;
        }
      })
    },
    // 取消会员价设置
    handleClose() {
      this.memberDialog = false;
    },
    // 确定会员价设置
    submitMember() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          saveOrUpdate({ memberPriceCoefficient: this.memberForm.memberPriceCoefficient }).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('设置会员价系数成功');
              this.memberDialog = false;
            }
          })
        }
      })
    }
  }
}

</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style scoped>
.item_customer {
  display: flex;
  align-content: center;
  margin-top: 15px;
}
</style>
