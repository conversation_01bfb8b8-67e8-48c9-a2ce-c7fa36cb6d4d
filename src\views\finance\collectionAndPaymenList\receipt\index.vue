<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="timePicker">
        <el-date-picker
          style="width: 260px"
          v-model="timePicker"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="查询起始日期"
          end-placeholder="查询结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="selectTime"
        >
        </el-date-picker>
      </im-search-pad-item>
      <im-search-pad-item prop="id">
        <el-input v-model.trim="listQuery.model.id" placeholder="请输入收款单号" @input="listQuery.model.id = mixinReplaceStr(listQuery.model.id)" />
      </im-search-pad-item>
      <im-search-pad-item prop="businessNo">
        <el-input
          v-model.trim="listQuery.model.businessNo"
          placeholder="请输入业务单号"
          @input="listQuery.model.businessNo = mixinReplaceStr(listQuery.model.businessNo)"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="flowPayOrderNo">
        <el-input
          v-model.trim="listQuery.model.flowPayOrderNo"
          placeholder="请输入交易单号"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="payerName">
        <el-input
          v-model.trim="listQuery.model.payerName"
          placeholder="请输入付款方"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="type">
        <el-select v-model="listQuery.model.type" placeholder="请选择收款单">
          <el-option label="订单支付" value="ORDER"></el-option>
          <el-option label="余额充值" value="CASH"></el-option>
          <el-option label="商家保证金缴纳" value="SELLER"></el-option>
          <el-option label="品种保证金缴纳" value="DEPOSIT"></el-option>
          <el-option label="平台技术服务费缴纳" value="SERVICE"></el-option>
        </el-select>
      </im-search-pad-item>
      
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.paymentStatus"
        :tabs="tabs"
        @change="chageTabsFun"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'sale-saas-finance-financeCollect:export','sale-platform-finance-financeCollect:export'])" @click="handleOutExcel">导 出</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            align="center"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <span
                v-if="
                  item.name == 'payerType' ||
                  item.name == 'method' ||
                  item.name == 'type'
                "
                >{{ row[item.name].desc }}</span
              >
              <span v-else-if="item.name == 'paymentAmount'">{{
                row[item.name] | getDecimals
              }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="160"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
                <span
                  v-if="listQuery.model.paymentStatus == 'WAIT' && checkPermission(['admin', 'sale-saas-finance-financeCollect:confirm','sale-platform-finance-financeCollect:confirm'])"
                  class="table-edit-row-item"
                >
                  <el-button type="text" @click="acceptedFun(row)"
                    >确认收款</el-button
                  >
                </span>
                <span class="table-edit-row-item" v-if="checkPermission(['admin', 'sale-saas-finance-financeCollect:detail','sale-platform-finance-financeCollect:detail'])">
                  <el-button type="text" @click="detailFun(row.id)"
                    >查看详情</el-button
                  >
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <el-dialog
      append-to-body
      title="审核收款单"
      :visible.sync="acceptedFlag"
      width="30%"
      @close="close"
      :close-on-click-modal="false"
    >
      <el-form ref="accepted" :model="acceptedForm" label-width="100px">
        <el-form-item class="formItem" prop="flowMethod" label="付款方:">
          {{ row.payerName }}
        </el-form-item>
        <!-- <el-form-item class="formItem" prop="remarks" label="付款账号:">
          {{ row.financePaymentFlow.payAccount }}
        </el-form-item> -->
        <el-form-item class="formItem" prop="smallAmount" label="付款金额:">
          {{ row.paymentAmount || 0 }}
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款时间:">
          {{
            row.financePaymentFlow.paymentTime
              ? row.financePaymentFlow.paymentTime.substr(0, 10)
              : ""
          }}
        </el-form-item>
        <el-form-item class="formItem" prop="flowMethod" label="付款方式:">
          {{ flowMethod }}
          <el-checkbox style="margin: 0 15px;" v-model="changeMethod">变更方式</el-checkbox>
          <el-select v-if="changeMethod" v-model="acceptedForm.method" placeholder="请选择" size="small" style="width: 120px;">
            <el-option v-for="item in methodList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="formItem" prop="remarks" label="付款流水号:">
          {{ row.financePaymentFlow.serialNumber }}
        </el-form-item>
        <!-- <el-form-item class="formItem" prop="remarks" label="付款名称:">
          {{ row.financePaymentFlow.payAccountName }}
        </el-form-item> -->
        <el-form-item class="formItem" prop="remarks" label="付款凭证:">
          <el-image
            v-if="row.financePaymentFlow.certificatePath"
            style="width: 80px; height: 80px"
            :src="row.financePaymentFlow.certificatePath"
            :preview-src-list="[row.financePaymentFlow.certificatePath]"
          >
          </el-image>
          <div
            v-if="row.financePaymentFlow.certificatePath"
            style="font-size: 14px; color: #ccc"
          >
            点击图片可放大预览
          </div>
        </el-form-item>
        <el-form-item
          class="formItem"
          prop="remarks"
          label="备注信息:"
          :rules="[
            { required: true, message: '请输入备注信息', trigger: 'blur' },
          ]"
        >
          <el-input
            style="width: 90%; min-width: 200px"
            type="textarea"
            rows="3"
            v-model="acceptedForm.remarks"
            placeholder="请输入备注信息"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" :loading="acceptedLoading || loading" @click="acceptedFormFun">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="导出数据"
      append-to-body
      v-if="exportStatus"
      width="50%"
      :visible.sync="exportStatus"
      :before-close="closeExportDia"
    >
      <!-- <exportdataList ref="exportdataDia" :total="total" :totalPage="totalPage"></exportdataList> -->
      <exportpage
        ref="exportPage"
        :total="total"
        :totalPage="totalPage"
      ></exportpage>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportStatus = false">取消</el-button>
        <el-button type="primary" @click="submitExport">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { MessageConfirmExport } from "@/utils/index";
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/finance/collectionAndPaymenList/receipt/tableInfo";
import {
  list,
  confirmReceipt,
  detail,
  exportFinanceCollect,
} from "@/api/finance/collectionAndPaymenList/receipt";
import { setContextData } from "@/utils/auth";
import TabsLayout from "@/components/TabsLayout";
import exportpage from "@/views/finance/collectionAndPaymenList/receipt/exportPage";
import checkPermission from '@/utils/permission';

export default {
  name:'collectionOrder',
  data() {
    return {
      changeMethod: false, // 是否变更支付方式
      methodList: [
        { value: 'BANK', label: '银行汇款' },
        { value: 'BANK_ACCEPTANCE', label: '银行承兑' },
      ],
      exportStatus: false,
      isExpand: false,
      listLoading: false,
      list: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      timePicker: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          startTime: "",
          endTime: "",
          paymentStatus: "WAIT",
          clientType: "SALE_TO_SALE",
          flowPayOrderNo: ""
        },
      },
      tabs: [
        {
          name: "待收款",
          value: "WAIT",
          hide: !checkPermission(['admin', 'sale-saas-finance-financeCollect:pendingView','sale-platform-finance-financeCollect:pendingView'])
        },
        {
          name: "收款完成",
          value: "FINISH",
          hide: !checkPermission(['admin', 'sale-saas-finance-financeCollect:finishedView','sale-platform-finance-financeCollect:finishedView'])
        },
        {
          name: "收款关闭",
          value: "COLSE",
          hide: !checkPermission(['admin', 'sale-saas-finance-financeCollect:closedView','sale-platform-finance-financeCollect:closedView'])
        },
      ],
      acceptedFlag: false,
      acceptedForm: {
        remarks: '',
        method: ''
      },
      row: {
        financePaymentFlow: {},
      },
      total: 0,
      totalPage: 0,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      itemLoading: false,
      loading: false,
      acceptedLoading: false
    };
  },
  computed: {
    flowMethod() {
      return this.row.method?.desc ? this.row.method.desc : ''
    }
  },
  methods: {
    checkPermission,
    selectTime(e) {
      this.listQuery.model.startTime = e[0];
      this.listQuery.model.endTime = e[1];
    },
    chageTabsFun() {
      this.listQuery.current = 1;
      this.initTbaleTitle();
      this.list = [];
      this.getlist();
    },
    close() {
      this.acceptedFlag = false;
      this.$refs.accepted.resetFields();
    },
    detailFun(id) {
      setContextData("receipt_detail", this.listQuery);
      this.$router.push({
        path: "/finance/collectionAndPaymen/receipt/detail",
        query: {
          id,
        },
      });
    },
    async acceptedFun(row) {
      this.loading = true;
      this.acceptedFlag = true;
      const { id } = row
      this.row = { financePaymentFlow: {} }
      this.changeMethod = false
      detail(id).then(res => {
        this.row = res.data;
        this.acceptedForm.method = this.row.method?.code || ''
      }).finally(() => {
        this.loading = false;
      })
    },
    acceptedFormFun() {
      this.$refs.accepted.validate(async (vald) => {
        if (vald) {
          if (!this.changeMethod) {
            this.acceptedForm.method = this.row?.method?.code || ''
          }
          this.acceptedLoading = true
          confirmReceipt({
            id: this.row.id,
            ...this.acceptedForm
          }).then(res => {
            if (res.code !== 0) return;
            this.$message.success("已确认收款该收款单");
            this.resetForm();
            this.acceptedFlag = false;
          }).finally(() => {
            this.acceptedLoading = false
          })
        }
      });
      return;
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    resetForm(type) {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          startTime: "",
          endTime: "",
          paymentStatus: this.listQuery.model.paymentStatus,
          clientType: "SALE_TO_SALE",
        },
      };
      this.timePicker = [];
      this.list = [];
      this.getlist();
    },

    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.listQuery.model.paymentStatus];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.paymentStatus];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.paymentStatus];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    // 导出数据选择弹窗关闭
    closeExportDia() {
      this.exportStatus = false;
    },
    handleOutExcel() {
      this.exportStatus = true;
    },
    // 确定导出
    submitExport() {
      const loading = this.$loading({
        lock: true,
        text: "正在导出中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.4)",
      });
      let params = this.$refs["exportPage"].getData();
      console.log("params", params);
      let model = {
        ...this.listQuery.model,
        ...params,
        "ids[]": [],
      };
      delete model.id;

      let listParams = {
        current: this.page,
        size: this.pageSize,
        map: {},
        model,
        order: "descending",
        sort: "createTime",
      };
      console.info(listParams);
      exportFinanceCollect(listParams).then((res) => {
        loading.close();
        MessageConfirmExport().then(result=>{
          if (result) {
            this.exportStatus = false;
          }
        })
        // exoprtToExcel(
        //   res.data,
        //   `收款单信息导出${formatDataTime("yyyyMMDDHHmmss")}.xlsx`
        // );
      });
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  },
  components: {
    Pagination,
    exportpage,
    TabsLayout,
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
<style>
.el-icon-circle-close {
  color: #fff;
}
</style>
