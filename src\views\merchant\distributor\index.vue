<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="handleReset"
      @search="onSubmit"
    >
      <im-search-pad-item prop="purMerchantCode">
        <el-input v-model.trim="listQuery.model.purMerchantCode" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="address">
        <el-cascader
          v-model="listQuery.model.address"
          placeholder="请选择所在区域"
          :options="options"
          :props="{ checkStrictly: true,expandTrigger: 'hover',
                    value: 'id',
                    label: 'label',
                    children: 'children'}"
          clearable
          style="width: 240px;"
          @change="parentChangeAction"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="firstCampStatus">
        <el-select v-model="listQuery.model.firstCampStatus" placeholder="请选择首营状态">
          <el-option v-for="item in firstCampStatusoptions" :label="item.label" :value="item.value"  :key="item.value"/>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[{ name: '分销商档案', value: 'first' }]" >
            <template slot="button">
          <el-button @click="load">刷新</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :operation-width="150" :remote-method="load" :selection="true" @selection-change="handleSelectionChange">
        <template slot="publishStatus">
          <el-table-column label="分销商状态" width='120'>
            <slot slot-scope="{row}">
              <span v-text="row.publishStatus.code === 'Y'?'启用':'停用'" />
            </slot>
          </el-table-column>
        </template>

        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','sale-saas-manufacturer-distributor:firstCampChange', 'sale-platform-manufacturer-distributor:firstCampChange'])" class="table-edit-row-item">
              <el-button type="text" @click="handleChange(props.row)">首营变更</el-button>
            </span>
            <span v-if="checkPermission(['admin','sale-saas-manufacturer-distributor:detail','sale-platform-manufacturer-distributor:detail'])" class="table-edit-row-item">
              <el-button type="text" @click="$router.push({ path: '/merchant/distributorDetail', query: { id: props.row.id ,purMerchantId: props.row.purMerchantId} })">查看</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog v-dialogDrag title="首营变更" :visible.sync="changeVisible">
      <el-form ref="changeForm" :model="changeForm" label-width="100px" :rule="rules">
        <el-form-item label="首营状态：" prop="firstCampStatusEnum">
          <el-select v-model="changeForm.firstCampStatusEnum" placeholder="请选择">
            <el-option label="未建立" value="NOT_ESTABLISH" />
            <el-option label="已建立" value="ESTABLISH" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更备注：">
          <el-input v-model="changeForm.remark" autocomplete="off" maxlength="32" show-word-limit />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm('changeForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('changeForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pagePurMerchantBySaleMerchant,
trees,
updateSetFirstCampStatusAndRemark } from '@/api/distributorManagement'
import checkPermission from '@/utils/permission'
import TabsLayout from "../../../components/TabsLayout/index";

const TableColumns = [
  { label: '分销商状态', prop: 'publishStatus', name: 'publishStatus',  slot: true },
  { label: '销售商编码', prop: 'code', width: '145' },
  { label: '销售商名称', prop: 'name', width: '150' },
  { label: '社会统一信用代码', prop: 'socialCreditCode', width: '190' },
  { label: '法人代表', prop: 'legalPerson' },
  { label: '负责人', prop: 'ceoName' },
  { label: '联系电话', prop: 'ceoMobile', width: '115' },
  { label: '所在区域', prop: 'region', width: '160' },
  { label: '注册地址', prop: 'registerAddress', width: '170' },
  { label: '首营状态', prop: 'firstCampStatus.desc' },
  { label: '创建时间', prop: 'createTime', width: '160' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}

export { TableColumnList }

export default {
  components: {TabsLayout},
  name: 'DragTable',
  data() {
    return {
      isExpand: false,
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableData: [],
      tableVal: [],
      list: [],
      total: 0,
      page: 0,
      listLoading: false,

        firstCampStatusoptions: [{
          value: 'ESTABLISH',
          label: '已建立'
        }, {
          value: 'NOT_ESTABLISH',
          label: '未建立'
        }],

      changeForm: {
        firstCampStatusEnum: 'NOT_ESTABLISH',
        remark: '',
        id: ''
      },
      rules: {
        firstCampStatusEnum: [
          { required: true, trigger: 'blur', message: '请选择变更状态' }
        ]
      },
      changeVisible: false,
      listQuery: {
        model: {
          firstCampType:"SALE",
          purMerchantName:"",
          firstCampStatus:"",

          saleMerchantId: '',
          ceoName: '',
          provinceId: '',
          cityId: '',
          countyId: '',
          merchantGroupId: '',
          merchantTypeId: '',
          purMerchantCode: ''
        }
      },
      form: {
        firstCampType:"SALE",
        ceoName: '',
        purMerchantName: '',
        code: '',
        address: '',
        firstCampStatus: '',
        purMerchantCode: ''
      },
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first',
      options: []
    }
  },
  created() {
    /*    this.getSaleId()*/
    this.getArea()
  },
  methods: {
    checkPermission,
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          updateSetFirstCampStatusAndRemark(this.changeForm).then(res => {
            this.$message.success('首营变更操作成功！')
            this.changeVisible = false
            this.onSubmit()
          })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.changeVisible = false
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleChange(row) {
      this.changeForm.id = row.id
      this.changeVisible = true
    },

    // 地区
    async getArea() {
      const { data } = await trees()
      this.options = data
    },
    parentChangeAction(val) {
      this.listQuery.model = {
        firstCampType:"SALE",
        provinceId: val[0],
        cityId: val[1],
        countyId: val[2]
      }
    },
    // 获取经销商id

    async load(params) {
      this.listLoading = true
      Object.assign(this.listQuery, params)
      return await pagePurMerchantBySaleMerchant(this.listQuery)
    },
    handleReset() {
      this.listQuery.model = {
        ceoName: '',
        purMerchantName: '',
        code: '',
        address: '',
        firstCampStatus: '',
        purMerchantCode: '',
        firstCampType:"SALE",
      }
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },

    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item, index) {
        return item.id
      })
    }
  }
}
</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style scoped>
</style>
