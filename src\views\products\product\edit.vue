<template>
  <div class="product-edit detail-wrapper" v-loading="loading">
    <page-title title="商品档案详情">
      <el-button type="primary" @click="submit">提交</el-button>
    </page-title>
    <product-form ref="form" action="EDIT" :areaData="areaData"/>
  </div>
</template>

<script>
import request from '@/utils/request'
import ProductForm from './form'
export default {
  components: {
    ProductForm
  },
  data () {
    return {
      loading: false,
      areaData: [],
      detail: {}
    }
  },

  mounted () {
    this.load();
  },

  methods: {
    async load () {
      this.loading = true
      try {
        const areaData = await request.get('authority/area/anno/tree')
        this.areaData = areaData.data
        const { data } = await request.get(`product/admin/product/detail/${this.$route.query.id}`)
        data.expDate = data.skuList[0].expDate
        this.detail = data
        this.$refs.form.restore(data)
      } catch (e) {

      }
      this.loading = false
    },

    async submit () {
      if (this.loading) {
        return;
      }
      this.loading = true
      let formData = this.$refs.form.getData()

      if (!formData) {
        this.loading = false
        return;
      }
      formData.id = this.$route.query.id
      formData = {...this.detail, ...formData}
      formData.productLicenseRelUpdateDTOList = formData.productLicenseRelSaveDTOList;
      // 处理sku
      if (formData.skuList) {
        let keys = {
          "costPrice": 0,
          "expDate": "",
          "id": 0,
          "integral": 0,
          "limitQuantity": 0,
          "medicarePrice": 0,
          "operation": "",
          "productId": 0,
          "retailPrice": 0,
          "salePrice": 0,
          "stockQuantity": 0,
          "whetherEnableMultiSpec": "",
          "whetherGiveIntegral": "",
          "supplyPrice":"",
          "wholesalePrice":"",
          "memberPrice":"",
          "productMemberCoefficient": "",
          "whetherProductMemberCoefficient":''
        }
        formData.skuList = formData.skuList.map(item => {
          let ret = {};
          for (let key in keys) {
            ret[key] = item[key]
            // 处理返回的多余的枚举
            if (['whetherEnableMultiSpec', 'whetherGiveIntegral'].indexOf(key) > -1) {
              if (ret[key]) {
                ret[key] = ret[key].code;
              }
            }
          }
          return ret;
        })
      }

      let keys = {
        saleMerchantId: '',
        "agentiaType": "",
        "approvalNumber": "",
        "approvalStatus": "",
        "area": "",
        "barCode": "",
        "brandId": 0,
        "businessRangeId": 0,
        "categoryId": 0,
        "curingCycle": "",
        "curingType": "",
        "drugName": "",
        "id": 0,
        "instructions": "",
        "keepCondition": "",
        "keepWay": "",
        "licenseHolders": "",
        "manufacturer": "",
        "measurement": "",
        "marketing":"",
        "medicareNum": "",
        "midPackTotal": 0,
        "minBuyQuantity": 0,
        "mnemonicCode": "",
        "operation": "",
        "otcType": "",
        "packTotal": 0,
        "performance": "",
        "pictIdS": "",
        "platformCategoryId": 0,
        "platformProductId": 0,
        "productCode": "",
        erpCode: '',
        expDate: '',
        skuSyncType: null,
        "productControlSaleUpdateDTOList": [
          {
            "areaProductControlSaleRelUpdateDTOList": [
              {
                "amount": 0,
                "cityIdS": "",
                "controlType": "",
                "districtIdS": "",
                "innerId": 0,
                "operation": "",
                "productControlSaleId": 0,
                "productId": 0,
                "provinceId": "",
                "saleMerchantId": 0,
                "streetIdS": ""
              }
            ],
            "controlBeginTime": "",
            "controlEndTime": "",
            "controlType": "",
            "createUser": 0,
            "enterpriseTypeProductControlSaleRelUpdateDTOList": [
              {
                "amount": 0,
                "controlType": "",
                "enterpriseTypeId": 0,
                "innerId": 0,
                "operation": "",
                "productControlSaleId": 0,
                "productId": 0,
                "saleMerchantId": 0
              }
            ],
            "id": 0,
            "merchantGroupProductControlSaleRelUpdateDTOList": [
              {
                "amount": 0,
                "controlType": "",
                "innerId": 0,
                "merchantGroupId": 0,
                "operation": "",
                "productControlSaleId": 0,
                "productId": 0,
                "saleMerchantId": 0
              }
            ],
            "productId": 0,
            "purchaseMerchantProductControlSaleRelUpdateDTOList": [
              {
                "amount": 0,
                "controlType": "",
                "innerId": 0,
                "operation": "",
                "productControlSaleId": 0,
                "productId": 0,
                "purMerchantId": 0,
                "saleMerchantId": 0
              }
            ],
            "selectCustomer": "",
            "updateUser": 0
          }
        ],
        "productDescription": "",
        "productExtensionFieldList": [
          {
            "name": "",
            "value": ""
          }
        ],
        "productLicenseRelUpdateDTOList": [
          {
            "certificateNumber": "",
            "fileIds": "",
            "id": 0,
            "licenseEndDate": "",
            "licenseId": 0,
            "licenseStartDate": "",
            "licenseType": "",
            "productId": 0,
            "reminderDate": "",
            "reminderDateType": "",
            "saleMerchantId": 0,
            "whetherForever": ""
          }
        ],
        "productName": "",
        "productPricePolicyList": [
          {
            "cityIdS": "",
            "districtIdS": "",
            "enterpriseTypeIdS": "",
            "id": 0,
            "merchantGroupIdS": "",
            "operation": "",
            "price": 0,
            "productId": 0,
            "provinceId": "",
            "saleMerchantId": 0,
            "skuId": 0,
            "streetIdS": ""
          }
        ],
        "publishStatus": "",
        "purpose": "",
        "qualityStandard": "",
        "rejectReason": "",
        "skuList": [
          {
            "costPrice": 0,
            "expDate": "",
            "id": 0,
            "integral": 0,
            "limitQuantity": 0,
            "medicarePrice": 0,
            "wholesalePrice":"",
            "supplyPrice":"",
            "operation": "",
            "productId": 0,
            "retailPrice": 0,
            "salePrice": 0,
            "stockQuantity": 0,
            "whetherEnableMultiSpec": "",
            "whetherGiveIntegral": "",
            "memberPrice":0,
            "productMemberCoefficient":'',
            "whetherProductMemberCoefficient":'N'
          }
        ],
        "spec": "",
        "specialMngMedicinal": "",
        "standardCode": "",
        "title": "",
        "unit": "",
        "whetherAdjustPrice": "",
        "whetherCashTransaction": "",
        "whetherColdChain": "",
        "whetherContainEphedrine": "",
        "whetherShare":"",
        "whetherReward": "",
        "whetherEnableMultiSpec": "",
        "whetherMedicareVariety": "",
        "whetherReturnable": "",
        "whetherUnbundled": "",
        "whetherUseCoupon": "",
        "warehouseId": ''
      };

      // 移除不必要的内容
      let updateObj = {};
      for(let k in formData) {
        if (keys.hasOwnProperty(k)) {
          updateObj[k] = formData[k];
        }
      }
      updateObj.skuList[0].expDate =updateObj.expDate? updateObj.expDate + ' 00:00:00':'';
      updateObj.skuList[0].wholesalePrice = formData.skuList[0].wholesalePrice
      updateObj.skuList[0].supplyPrice = formData.skuList[0].supplyPrice
      updateObj.platformProductId = formData.platformProductId ?formData.platformProductId:0
      // updateObj.whetherShare = formData.whetherShare
      request({
        url: 'product/admin/product',
        method: 'put',
        data: updateObj
      }).then(res => {
        this.loading = false
        if(res.code==0){
          this.$message.success('修改商品成功')
        this.$router.push({ path: '/productCenter/productsManagement/list' })
        }else{
          this.$message.error('修改商品失败')
        }


      }).catch(e => {
        this.loading = false;
      })
    }
  }
}
</script>
