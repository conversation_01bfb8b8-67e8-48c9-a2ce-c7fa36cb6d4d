<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="onSubmit"
    >
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="activityName">
        <el-input v-model.trim="model.activityName" placeholder="请输入活动名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="activityStatus"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-groupBuy:add','sale-platform-promotion-groupBuy:add'])" type="primary" @click="editCoupon()">+ 新增团购活动</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData">
        <template slot="collageStatus">
          <el-table-column label="成团状态">
            <slot slot-scope="{row}">
              {{ row.collageStatus==1?'待成团':(row.collageStatus==2?'成团成功':'成团失败') }}
            </slot>
          </el-table-column>
        </template>
        <template slot="activityTime">
          <el-table-column min-width="250" label="活动时间">
            <slot slot-scope="{row}">
              {{ row.activityStartTime }}至{{ row.activityEndTime }}
            </slot>
          </el-table-column>
        </template>

        <div slot-scope="props">
          <!-- 1.只有未开始的可进行编辑，其他的状态均为可查看 -->
          <!-- 2.未开始和进行中可作废 -->
          <!-- 3.已结束和已作废可删除 -->
          <el-button v-if="props.row.activityStatus.code == 'NOT_START'&&checkPermission(['admin', 'sale-saas-promotion-groupBuy:edit','sale-platform-promotion-groupBuy:edit'])" type="text" @click="editCoupon(props.row.id)">编辑</el-button>
          <el-button v-if="props.row.activityStatus.code != 'NOT_START' && checkPermission(['admin', 'sale-saas-promotion-groupBuy:detail','sale-platform-promotion-groupBuy:detail'])" type="text" @click="editCoupon(props.row.id)">查看</el-button>
          <el-divider v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE')&&checkPermission(['admin', 'sale-saas-promotion-groupBuy:del','sale-platform-promotion-groupBuy:del'])" direction="vertical" />
          <el-button v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE') && checkPermission(['admin', 'sale-saas-promotion-groupBuy:del','sale-platform-promotion-groupBuy:del'])" type="text" @click="delCoupon(props.row.id)">删除</el-button>
          <el-divider v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START') &&checkPermission(['admin', 'sale-saas-promotion-groupBuy:abolish','sale-platform-promotion-groupBuy:abolish'])" direction="vertical" />
          <el-button v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START')&&checkPermission(['admin', 'sale-saas-promotion-groupBuy:abolish','sale-platform-promotion-groupBuy:abolish'])" type="text" @click="handleOnOff(props.row.id)">作废</el-button>
        </div>
      </table-pager>
<!--      <product-dialog ref="products-dialog" :check-list="goodsList" :detail="itemDetail" />-->
    </div>
  </div>
</template>

<script>
import { getList, updatePromotionPackageState, handleDel } from '@/api/limitTime'
import checkPermission from '@/utils/permission'
// import productDialog from '../components/product-dialog'

import { groupBuyPage,setVoid,delGroupBuy } from '@/api/promotionCenter/index'

const TableColumns = [
  { label: '活动编码', name: 'activityCode', prop: 'activityCode', width: "100"  },
  { label: '活动名称', name: 'activityName', prop: 'activityName' },
  { label: '商品名称', name: 'productName', prop: 'productName' , width: "150"},
  { label: '目标采购量', name: 'startBuyNumber', prop: 'startBuyNumber', width: "100" },
  { label: '成团状态', name: 'collageStatus', prop: 'collageStatus',slot: true },
  { label: '还差多少成团', name: 'disparity', prop: 'disparity', width: "120" },
  { label: '销售量', name: 'salesVolume', prop: 'salesVolume', width: "80" },
  { label: '订单笔数', name: 'orderNumber', prop: 'orderNumber',width:'80' },
  { label: '活动状态', name: 'activityStatus.desc', prop: 'activityStatus.desc',width:'80' },
  { label: '活动时间', name: 'activityTime', prop: 'activityTime',width:'250', slot: true },
  // { label: '操作时间', name: 'updateTime', prop: 'updateTime', slot: true }
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  name: 'groupBuyingActivities',
  components: {
  },
  data() {
    return {
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      loading: false,
      showCoupon: false,
      showAdd: false,
      activityStatus: 'first',
      model: {
        activityName: '',
        productName: '',
        activityStatus:'',
      },
      during: '',
      tableData: [],
      goodsList: [],
      itemDetail: '',
      tabNum:{
         all:0,
         pending:0,
         waitPay:0,
         waitDelivery:0,
         partDelivery:0
      },
    }
  },
  created() {
  },
  computed:{
    tabs () {
      return [
        {
          name: '全部' + ((this.tabNum.all) >0 ? '(' +this.tabNum.all + ')' : ''),
          value:'first',
          hide: !checkPermission(['admin', 'sale-saas-promotion-groupBuy:allView','sale-platform-promotion-groupBuy:allView'])
        },
        {
          name: '未开始' + ((this.tabNum.pending) >0 ? '(' +this.tabNum.pending + ')' : ''),
          value:'NOT_START',
          hide: !checkPermission(['admin', 'sale-saas-promotion-groupBuy:pendingView','sale-platform-promotion-groupBuy:pendingView'])
        },
        {
          name: '进行中' + ((this.tabNum.waitPay) >0 ? '(' +this.tabNum.waitPay + ')' : ''),
          value:'PROCEED',
          hide: !checkPermission(['admin', 'sale-saas-promotion-groupBuy:processingView','sale-platform-promotion-groupBuy:processingView'])
        },
        {
          name: '已结束' + ((this.tabNum.waitDelivery) >0 ? '(' +this.tabNum.waitDelivery + ')' : ''),
          value:'FINISHED',
          hide: !checkPermission(['admin', 'sale-saas-promotion-groupBuy:finishedView','sale-platform-promotion-groupBuy:finishedView'])
        },
        {
          name: '已作废' + ((this.tabNum.partDelivery) >0 ? '(' +this.tabNum.partDelivery + ')' : ''),
          value:'OBSOLETE',
          hide: !checkPermission(['admin', 'sale-saas-promotion-groupBuy:abolishedView','sale-platform-promotion-groupBuy:abolishedView'])
        }
      ]
    }
  },
  activated() {
    if(sessionStorage.getItem('groupBuy')) {
      this.reload()
      sessionStorage.removeItem('groupBuy')
    }
  },
  methods: {
    checkPermission,
    async postCouponList(params) {
      // let modelObj = this.model
      // if(this.model.activityStatus == 'first'){
      //   model.activityStatus = '';
      // }
      const listQuery = {
        model: {
          ...this.model,
          // activityName:this.model.activityName,
          // productName:this.model.productName,
          activityStartTime: this.during[0],
          activityEndTime: this.during[1]
        }
      }
      Object.assign(listQuery, params)
      this.loading = true
      // return await getList(listQuery)
      return await groupBuyPage(listQuery)
    },
    async handleOnOff(id) {
      const param = {
        id: id
      }
      const data = await setVoid(param)
      if (data.code === 0 && data.msg == 'ok') {
        this.$message.success('该团购活动作废成功！')
        this.reload()
      }
    },
    handleChangeTab(tab){
      console.log('tab----->',tab);
      this.activityStatus = tab.value;
      if(tab.value == 'first') {
        this.model.activityStatus = '';
      } else {
        this.model.activityStatus = tab.value;
      }
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
      // this.postCouponList();

    },
    // 删除作废和结束的
    async delCoupon(id) {
      const data = await delGroupBuy([id])
      if (data.code === 0 && data.msg == 'ok') {
        this.$message.success('删除成功！')
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      }
    },
    // 新增或者编辑
    editCoupon(id, type) {
      this.$router.push({
        // path: '/promotionCenter/groupBuy/detail',
        path: '/promotion/groupBuy/detail',
        query: {
          saleMerchantId: this.saleMerchantId,
          id: id,
          type: type
        }
      })
    },
    onSubmit() {
      // this.postCouponList()
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      return await this.postCouponList()
    },
    reload() {
      this.activityStatus='first';
      this.during = ''
      this.$refs['tabs-layout'].reset();
      this.model =  {
        activityName: '',
        productName: '',
        activityStatus:''
      };
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
