<template>
  <div class="taskAddOrEdit">
    <div class="taskAddOrEdit_Header">
      <span class="title">{{ id ? "编辑" : "新增" }}任务</span>
      <div>
        <el-popover
          v-model="rejectFlag"
          placement="bottom-end"
          title="取消提醒"
          width="300"
          trigger="click"
        >
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0; padding-top: 14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="toBack"
              >确定</el-button
            >
          </div>
        </el-popover>
        <el-button type="primary" :loading="saveLoading" @click="toSave"
          >保存</el-button
        >
      </div>
    </div>
    <div class="taskAddOrEdit_Content">
      <el-form
        ref="taskForm"
        :rules="taskRules"
        :model="taskInfo"
        label-width="120px"
        label-position="right"
        :show-message="false"
      >
        <div class="form_box">
          <el-form-item label="任务类型:" prop="taskType">
            <el-select
              v-model="taskInfo.taskType"
              style="width: 200px"
              placeholder="任务类型"
              :disabled="!!id"
              @change="taskInfo.time = ''"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="taskInfo.taskType === 3"
            label="任务周期:"
            prop="time"
          >
            <el-date-picker
              :key="1"
              v-model="taskInfo.time"
              type="year"
              value-format="yyyy"
              placeholder="选择年"
              :disabled="!!id"
              @change="handleYearRange"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            v-if="taskInfo.taskType !== 3"
            label="任务周期:"
            prop="time"
          >
            <el-date-picker
              :key="2"
              v-model="taskInfo.time"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              :clearable="!id"
              @change="handleMonthRange"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="form_box_type">
          <el-form-item
            v-if="taskInfo.taskType === 0"
            label="代表目标:"
            prop="dbmb"
            ><el-input-number
              :controls="false"
              v-model="taskInfo.dbmb"
              placeholder="请输入"
              style="width: 200px"
            ></el-input-number
            ><span>家次/人</span>
          </el-form-item>
          <div v-if="taskInfo.taskType === 1">
            <el-form-item label="主管目标:" prop="dbmb"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.dbmb"
                placeholder="请输入"
                style="width: 200px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
            <el-form-item label="省区目标:" prop="dbmb"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.dbmb"
                placeholder="请输入"
                style="width: 200px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
            <el-form-item label="大区目标:" prop="dbmb"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.dbmb"
                placeholder="请输入"
                style="width: 200px"
              ></el-input-number
              ><span>家次/人</span>
            </el-form-item>
          </div>
          <el-form-item
            v-if="taskInfo.taskType === 2"
            label="代表目标:"
            prop="dbmb"
            ><el-input-number
              :controls="false"
              v-model="taskInfo.dbmb"
              placeholder="请输入"
              style="width: 200px"
            ></el-input-number>
          </el-form-item>
          <el-form-item
            v-if="taskInfo.taskType === 3"
            label="上传指标文件:"
            prop=""
          >
            <el-upload
              class="upload-demo"
              :file-list="fileList"
              :action="uploadParams.action"
              :headers="uploadParams.headers"
              :data="uploadParams.data"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="handleUploadRemove"
              :on-change="handleUploadChange"
              accept=".xlsx, .xls"
              :limit="1"
              :on-exceed="handleUploadExceed"
            >
              <el-button icon="el-icon-upload2">上传文件</el-button>
            </el-upload>
            <p>
              支持上传.xls,.xlsx文件，文件格式请参考实例文件。<el-link
                type="primary"
                :href="tempUrl"
                target="_blank"
                :underline="false"
                >下载示例文件</el-link
              >
            </p>
          </el-form-item>
          <div v-if="taskInfo.taskType === 4">
            <p class="tit">阶段1 20+5</p>
            <el-form-item
              label="下单客户总家数:"
              prop="firstOrderCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.firstOrderCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
            <el-form-item
              label="下单客户-重点客户家数:"
              prop="firstOrderKeyCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.firstOrderKeyCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
            <p class="tit">阶段2 40+10</p>
            <el-form-item
              label="下单客户总家数:"
              prop="secondOrderCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.secondOrderCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
            <el-form-item
              label="下单客户-重点客户家数:"
              prop="secondOrderKeyCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.secondOrderKeyCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
            <p class="tit">阶段3 60+15</p>
            <el-form-item
              label="下单客户总家数:"
              prop="thirdOrderCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.thirdOrderCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
            <el-form-item
              label="下单客户-重点客户家数:"
              prop="thirdOrderKeyCustomerNum"
              label-width="180px"
              ><el-input-number
                :controls="false"
                v-model="taskInfo.thirdOrderKeyCustomerNum"
                placeholder="请输入"
                style="width: 200px"
                :min="0"
                :precision="0"
              ></el-input-number
              ><span>家/人</span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  salesmanTaskDetail,
  salesmanTaskUpdate,
  salesmanTaskAdd,
} from "@/api/salemanCenter/taskManagement.js";
import { getParameterByKey } from "@/api/settingCenter";
import dayjs from "dayjs";
import { getToken } from "@/utils/auth";
import { getLocalUser } from "@/utils/local-user";
export default {
  name: "taskAddOrEdit",
  components: {},
  props: {},
  data() {
    return {
      id: "",
      rejectFlag: false,
      saveLoading: false,
      typeOptions: [
        // {
        //   label: "拜访任务",
        //   value: 0,
        // },
        // {
        //   label: "协访任务",
        //   value: 1,
        // },
        // {
        //   label: "拓客任务",
        //   value: 2,
        // },
        {
          label: "业绩任务",
          value: 3,
        },
        {
          label: "客户数发展目标",
          value: 4,
        },
      ],
      taskInfo: {
        taskType: 3,
        time: "",
        yearTime: "",
        // dbmb: null,
        kpiFilePath: "",
      },
      initialMonth: [],
      taskRules: {
        taskType: [
          { required: true, message: "请选择任务类型", trigger: "blur" },
        ],
        time: [{ required: true, message: "请选择任务周期", trigger: "blur" }],
        // dbmb: [{ required: true, message: "请输入代表目标", trigger: "blur" }],
        firstOrderCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        firstOrderKeyCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        secondOrderCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        secondOrderKeyCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        thirdOrderCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        thirdOrderKeyCustomerNum: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
      },
      tempUrl: "", // 下载示例文件的链接
      actionUploadUrl: "/api/merchant/admin/merchantImport/importMerchantExcel", // 上传地址
      fileList: [],
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + "/file/file/upload",
        headers: {
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
          token: `Bearer ${getToken()}`,
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0,
        },
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.id = this.$route.query.id ?? null;
    this.id ? this.getData() : this.reset();
    this.getParameterByKeyFun();
  },
  mounted() {},
  methods: {
    // 查询数据
    getData() {
      salesmanTaskDetail(this.id).then((res) => {
        if (res?.data) {
          if (res.data.taskType === 3) {
            this.initialMonth = dayjs(res.data.startYearMonth).format("YYYY");
          } else {
            this.initialMonth =
              res.data.startYearMonth && res.data.endYearMonth
                ? [res.data.startYearMonth, res.data.endYearMonth]
                : '';
          }

          this.taskInfo = {
            ...res.data,
            ...res.data.customerDevelop,
            time: this.initialMonth,
          };
        }
      });
    },
    // 重置
    reset() {
      this.taskInfo = {
        taskType: 3,
        time: "",
        kpiFilePath: "",
      };
      this.fileList = [];
      this.initialMonth = [];
    },
    // 月份校验规则
    handleMonthRange(value) {
      if (!value) {
        this.taskInfo.time = value;
        return;
      }
      let start = value[0];
      let end = value[1];
      // 编辑状态不可更改开始时间，结束时间在开始时间之前
      if (this.id && this.initialMonth[0] !== value[0]) {
        start = this.initialMonth[0];
        end = dayjs(end).isBefore(this.initialMonth[0], "month")
          ? this.initialMonth[0]
          : end;
        this.$message.error("编辑状态不可更改开始时间!");
      }
      // 新增状态开始时间不能在当前时间之前
      if (!this.id && dayjs(start).isBefore(dayjs(), "month")) {
        start = dayjs().format("YYYY-MM");
        this.$message.error("不能选择过去月份!");
      }
      // 结束时间在当前时间之后
      if (dayjs(end).isBefore(dayjs(), "month")) {
        end = dayjs().format("YYYY-MM");
      }
      this.$nextTick(() => {
        this.taskInfo.time = [start, end];
      });
    },
    // 年份校验规则
    handleYearRange(value) {
      let currentYear = new Date().getFullYear();
      let selectedYear = value ? new Date(value).getFullYear() : null;
      if (selectedYear && selectedYear < currentYear) {
        this.$message.error("不能选择过去年份!");
        this.taskInfo.time = currentYear.toString();
      }
    },
    // 返回任务管理页面
    toBack() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/salesmanCenter/taskManagement");
    },
    // 更新
    onUpdate(data) {
      this.saveLoading = true;
      let temp = {
        ...data,
        customerDevelop: {
          ...this.taskInfo,
        },
      };
      salesmanTaskUpdate(temp)
        .then((res) => {
          this.saveLoading = false;
          if (res.code === 0 && res.data) {
            this.$message.success("编辑成功!");
            this.toBack();
          }
        })
        .catch((err) => {
          this.saveLoading = false;
          console.log(err);
        });
    },
    // 新增
    onSave(data) {
      this.saveLoading = true;
      let temp = {
        ...data,
        customerDevelop: {
          ...this.taskInfo,
        },
      };
      salesmanTaskAdd(temp)
        .then((res) => {
          this.saveLoading = false;
          if (res.code === 0 && res.data) {
            this.$message.success("新增成功!");
            this.toBack();
          }
        })
        .catch((err) => {
          this.saveLoading = false;
          console.log(err);
        });
    },
    // 保存前置校验
    async toSave() {
      try {
        let f = await this.$refs.taskForm.validate();
        if (f) {
          let loginInfo = localStorage.getItem("LOCAL_USER");
          if (loginInfo) loginInfo = JSON.parse(loginInfo);
          let temp = {
            startYearMonth: this.taskInfo.time?.[0] || "",
            endYearMonth: this.taskInfo.time?.[1] || "",
            taskType: this.taskInfo.taskType ?? "",
            id: this.id ?? "",
            saleMerchantId: loginInfo.saleMerchantId ?? "",
            kpiFilePath: this.taskInfo.kpiFilePath ?? "",
          };
          if (this.taskInfo.taskType === 3) {
            temp.startYearMonth = this.taskInfo.time
              ? dayjs(this.taskInfo.time).format("YYYY-01")
              : "";
            temp.endYearMonth = this.taskInfo.time
              ? dayjs(this.taskInfo.time).format("YYYY-12")
              : "";
          }
          this.id ? this.onUpdate(temp) : this.onSave(temp);
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取下载模板的链接
    getParameterByKeyFun() {
      getParameterByKey({ key: "TASK_KPI_EXCEL_TEMPLATE" }).then((res) => {
        if (res.code === 0 && res.msg === "ok") {
          this.tempUrl = res.data.value;
        }
      });
    },
    // 上传前文件类型校验
    beforeUpload(file) {
      let extension = file.name.substring(file.name.lastIndexOf(".") + 1);
      if (!["xlsx", "xls"].includes(extension)) {
        this.$message.warning("只能支持上传.xls,.xlsx文件");
        return false;
      }
    },
    // 文件改变
    handleUploadChange(file, fileList) {
      this.fileList = [file];
    },
    // 文件上传失败回调
    handleUploadError() {
      this.$message.error("文件上传失败");
      this.fileList = [];
      this.taskInfo.kpiFilePath = "";
    },
    // 文件移除
    handleUploadRemove(file, fileList) {
      this.taskInfo.kpiFilePath = "";
    },
    // 文件数量超出限制
    handleUploadExceed(file, fileList) {
      this.$message.warning("文件超出个数！");
    },
    // 文件上传成功
    handleUploadSuccess(response) {
      if (response.code === 0 && response.msg === "ok") {
        this.taskInfo.kpiFilePath = response.data.url;
      } else {
        this.$message.error("文件上传失败");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.taskAddOrEdit {
  background: #fff;
  height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  .taskAddOrEdit_Header {
    height: 56px;
    min-height: 56px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
  .taskAddOrEdit_Content {
    flex: auto;
    color: #505465;
    overflow: scroll;
    .form_box {
      display: flex;
      margin-bottom: 24px;
      > .el-form-item {
        min-width: 400px;
      }
    }
    .form_box_type {
      .el-form-item {
        margin-bottom: 8px;
        & + .tit {
          margin-top: 50px;
        }
      }
      .el-input-number + span {
        margin-left: 8px;
      }
      .tit {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
      }
      .upload-demo {
        width: 400px;
      }
    }
    ::v-deep .el-form .el-input__inner {
      text-align: left;
    }
  }
}
</style>
