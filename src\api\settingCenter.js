import request from '@/utils/request'
import qs from 'qs'
// 根据信用代码查询企业档案
export function findMerchantTypeDetailsById(id) {
  return request({
    url: `/merchant/admin/saleMerchant/getMerchantDetails`,
    method: 'get',
  })
}
//修改企业资料
export function editSaleMerchant(query) {
  return request({
    url: '/merchant/admin/saleMerchant',
    method: 'PUT',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据地区树选择返回解析结果
export function analysisByCheck(query) {
  return request({
    url: '/authority/area/anno/analysisByCheck',
    method: 'POST',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据数据解析出选中节点集合
export function analysisByDb(query) {
  return request({
    url: '/authority/area/anno/analysisByDb',
    method: 'POST',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//配送服务
export function batchSave(query) {
  return request({
    url: '/cart/admin/delivery/admin/batchSave',
    method: 'POST',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function getDeatils() {
  return request({
    url: '/cart/admin/delivery/getDetail',
    method: 'get'
  })
}
//删除
export function delArea(ids) {
  return request({
    url: '/cart/admin/delivery',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//角色
export function roleList(query) {
  return request({
    url: '/authority/role/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//角色新增以及权限
export function addRole(query) {
  return request({
    url: '/authority/role/roleAndAuthority',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function editRole(query) {
  return request({
    url: '/authority/role/roleAndAuthority',
    method: 'put',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除
export function delRole(ids) {
  return request({
    url: '/authority/role',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}

//角色
export function roleDetail(id) {
  return request({
    url: `/authority/role/${id}`,
    method: 'get'
  })
}
//查询角色拥有的资源id集合、
export function getRoleId(roleId) {
  return request({
    url: `/authority/role/authority/${roleId}`,
    method:'get'
  })

}
//菜单
export function menuTree() {
  return request({
    url: '/authority/menu/tree',
    method: 'get'
  })
}
//员工
export function employeeList(query) {
  return request({
    url: '/authority/merchant/employee/page',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function addEmployee(query) {
  return request({
    url: '/authority/merchant/employee',
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function editEmployee(query) {
  return request({
    url: '/authority/merchant/employee',
    method: 'put',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

//获取账户 userId
export function getByAccount(account) {
  return request({
    url: '/authority/user/getByAccount',
    method: 'get',
    params: {
      account: account
    }
  })
}

export function detail(id) {
  return request({
    url: `/authority/merchant/employee/${id}`,
    method: 'get'
  })
}


/**
 * @description 参数配置/系统参数 查询 key 值
 */
export function getParameterByKey(data) {
  return request({
    url: '/authority/parameter/getByKey',
    method: 'POST',
    transformRequest: [function () {
      return qs.stringify(data, { arrayFormat: 'brackets' })
    }],
    data,
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' }
  })

}

// 支付类型分页查询
export function paymentSetting(data){
  return request({
    url:'/general/admin/paymentSetting/page',
    method:'post',
    data
  })
}

// 启用或者添加，修改支付方式
export function paymentSettingOpen(data){
  return request({
    url:'/general/admin/paymentSetting',
    method:'post',
    data
  })
}

// 删除或者关闭支付方式
// export function paymentSettingOff(id){
//   return request({
//     url:'/general/admin/paymentSetting',
//     method:'delete',
//     params: {
//       ids: id
//     }
//   })
// }

// 支付方式是否启动
export function isEnabled(data){
  return request({
    url: '/general/admin/paymentSetting/enabled',
    method: 'post',
    data:qs.stringify(data),
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' }
  })
}

// 查询支付详情
export function paymentSettingInfo(id, saleMerchantId){
  return request({
    url: `/general/admin/paymentSetting/${id}/${saleMerchantId}`,
    method:'get'
  })
}

// 获取商家收货地址
export function addrList() {
  return request({
    url: '/merchant/admin/deliveryAddress/listDeliveryAddressByMerchantId',
    method: 'post'
  })
}

/*
* 经营范围列表
* */
export function listBusinessCategory (data) {
  return request({
    url: '/merchant/businessCategory/listBusinessCategory',
    method: 'post',
    data
  })
}

/*
* 经营范围 --- 删除
* */
export function deleteBusinessCategory(id) {
  return request({
    url: `/merchant/businessCategory?ids[]=${id}`,
    method: 'delete'
  })
}

/*
* 经营范围 -- 修改
* */
export function editBusinessCategory (data) {
  return request({
    url: '/merchant/businessCategory',
    method: 'put',
    data
  })
}

/*
* 经营范围 -- 新增
* */
export function addBusinessCategory(data) {
  return request({
    url: "/merchant/businessCategory",
    method: "post",
    data
  })
}
