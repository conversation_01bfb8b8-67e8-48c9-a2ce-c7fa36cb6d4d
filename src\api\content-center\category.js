import requestAxios from '@/utils/request'

// 分页数据获取
export  function list(data) {
  return requestAxios({
    url: '/general/admin/articleCategory/page',
    method: 'post',
    data
  })
}

// 新增
export  function add(data) {
  return requestAxios({
    url: '/general/admin/articleCategory',
    method: 'post',
    data
  })
}

// 获取所有父分类-根据当前父分类Id获取所有子分类
export  function category(data) {
  return requestAxios({
    url: '/general/admin/articleCategory/getAllParentCodeOrChildren',
    method: 'get',
    params: data
  })
}

// 删除元素
export  function delFun(data) {
  return requestAxios({
    url: '/general/admin/articleCategory',
    method: 'delete',
    params: data
  })
}

// 修改
export  function edit(data) {
  return requestAxios({
    url: '/general/admin/articleCategory',
    method: 'put',
    data
  })
}


