<template>
  <div class="proxySetings" v-loading="listLoading">
    <div class="top_title flex_between_center">
      <div>
        推广申请审核详情
        <img
          v-if="publishStatusEnum.code == 'PENDING'"
          src="@/assets/promoteImg/PENDING.png"
          alt=""
        />
        <img
          v-if="publishStatusEnum.code == 'REJECTED'"
          src="@/assets/promoteImg/REJECTED.png"
          alt=""
        />
        <img
          v-if="publishStatusEnum.code == 'REPEAL'"
          REPEAL
          src="@/assets/promoteImg/Revoked.png"
          alt=""
        />
      </div>
      <div>
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button v-if="publishStatusEnum.code == 'PENDING'"
          @click="cancelPromotion"
          >撤销推广商品</el-button
        >
        <el-button @click="toEdit">编辑</el-button>
        <el-button
          v-if="publishStatusEnum.code == 'REJECTED'"
          @click="showRejectReason"
          >驳回理由</el-button
        >
      </div>
    </div>
    <div class="archivesEditContent">
      <div class="item">
        <div class="title">
          <span>品种信息</span>
        </div>
        <!-- <el-button
          type="primary"
          @click="addGoodsFlag = true"

          style="margin: 10px 0 20px 0"
          :disabled="list.length != 0"
          >+ 选择商品</el-button
        > -->
        <!-- @selection-change="selectTableItemFun" -->
        <div class="table">
          <el-table
            ref="table"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column align="center" width="80" label="序号">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }} </span>
              </template>
            </el-table-column>

            <el-table-column label="商品主图" align="center" width="80" class-name="img-cell">
              <template slot-scope="{row}">
                <img :src="splitString(row.pictIdS)[0]" width="50px" height="50px">
              </template>
            </el-table-column>

            <el-table-column
              v-for="(item, index) in tableTitle"
              :key="index"
              :min-width="item.width ? item.width : '350px'"
              :label="item.label"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="{ row }">
                <img
                  v-if="item.name == 'pictIdS'"
                  :src="splitString(row.pictIdS)[0]"
                  style="width:50px;height:50px"
                />
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              fixed="right"
              align="center"
              label="操作"
              width="120"
              class="itemAction"
            >
              <template slot-scope="{ row }">
                <el-button type="text"  @click="delFun(row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>地区设置</span>
          <i class="textgray">指定地区范围内推广费</i>
        </div>
        <el-table :data="addrtableDate" style="width: 100%" border>
          <el-table-column width="50" align="center">
            <template slot="header">
              <span><i class="el-icon-menu" /></span>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="provice" label="省份" min-width="130">
            <template slot-scope="{ row }">
              <span>{{ row.provinceName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="citys" label="城市" min-width="200">
            <template slot-scope="{ row }">
              <span>{{ row.cityNames }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="country" label="区 / 区县" min-width="300">
            <template slot-scope="{ row }">
              <span>{{ row.districtNames }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="promotionExpenses" label="推广费（元）" min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.promotionExpenses }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="expectSale"
            label="期望月度销售额（元）"
            min-width="120"
          >
            <template slot-scope="{ row }">
              <span>{{ row.expectSale }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="130"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="editAddrItem(scope.row, scope.$index)"
                v-show="!scope.row.isEdit"

                >编辑</el-button
              >
              <el-button
                type="text"
                v-show="!scope.row.isEdit"
                @click="delAddr(scope.row, scope.$index)"

                >删除</el-button
              >
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
    <el-dialog
      v-if="addrFlag"
      title="添加地区"
      :visible.sync="addrFlag"
      width="70%"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="addrclose"
    >
      <div class="addrtree">
        <el-tree
          class="addrtrees"
          ref="addrTree"
          :data="addrTree"
          show-checkbox
          node-key="id"
          accordion
          :default-expanded-keys="defaultCheck"
          :default-checked-keys="defaultCheck"
          :props="defaultProps"
          @check="addrChange"
        >
        </el-tree>
      </div>
      <div></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addrclose">取 消</el-button>
        <el-button type="primary" @click="addrConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 设置 编辑 -->
    <!-- <el-dialog
      :close-on-click-modal="false"
      v-if="addGoodsFlag"
      :title="'选择商品'"
      :visible.sync="addGoodsFlag"
      width="80%"
      :show-close="true"
    >
      <goods-list :showFlag.sync="addGoodsFlag" :row.sync="list[0]">
      </goods-list>
    </el-dialog> -->
    <!-- 设置 编辑 -->
  </div>
</template>

<script>
const tableTitle = [
  {
    label: "商品编码",
    name: "productCode",
    width: "170px",
  },
  {
    label: "商品名称",
    name: "productName",
    width: "120px",
  },
  // {
  //   label: "通用名称",
  //   name: "drugName",
  //   width: "120px",
  // },
  {
    label: "规格",
    name: "spec",
    width: "120px",
  },
  {
    label: "生产厂家",
    name: "manufacturer",
    width: "120px",
  },
  {
    label: "销售价",
    name: "salePrice",
    width: "120px",
  },
];
import requestAxios from "@/utils/request";
import request from "@/utils/request";
import { setContextData } from "@/utils/auth";
// import goodsList from "./goodsList";
// import { areas } from "@/api/businessCenter/businessList";
// import {checkAddr} from "@/api/businessCentric/proxySettings"
export default {
  data() {
    return {
      rules: {},
      tableTitle,
      listLoading: false,
      list: [],
      addrtableDate: [],
      query: {},
      addrFlag: false,
      addrTree: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      checkdNodeIds: [],
      defaultCheck: ["110000"],
      currentTree: "", //当前选中的树
      addGoodsFlag: false,
      publishStatusEnum: "",
      rejectionReason: ''
    };
  },
  methods: {
    async cancelPromotion() {
      let { data } = await requestAxios({
        url: "/agent/agentProduct/merchant/batchRepeal",
        method: "post",
        data: {list: [this.$route.query.id]} ,
      });
      if (data) {
        setContextData("promoteCenter_promotionCheck", {
          current: 1,
          size: 10,
          model: {
            publishStatusEnum: "REPEAL",
          },
        });
        this.$message.success(
          "撤销 “ " + this.list[0].productName + " ” 的推广成功"
        );
        this.$router.push("/promoteCenter/promotionCheck");
      }
    },
    showRejectReason() {
      this.$confirm(this.rejectionReason, '驳回理由',{
        confirmButtonText: '知道了',
        showCancelButton: false,
        closeOnClickModal: false
      })
      .then(res => {

      })
      .catch(req => {

      })

    },
    toEdit() {
      this.$router.push({
        path: "/promoteCenter/proxyList/editItem",
        query: {
          id: this.$route.query.id,
        },
      });
    },
    async getitem() {
      this.listLoading = true;
      let { data } = await requestAxios({
        url: "/agent/agentProduct/merchant/query/" + this.$route.query.id,
        method: "post",
      });
      this.list.push(data.productVo);
      this.addrtableDate = data.list;
      this.publishStatusEnum = data.publishStatusEnum;
      this.rejectionReason = data.rejectionReason
      this.listLoading = false;
    },
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    delFun() {
      this.$confirm("您确定要删除该商品吗?", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.list = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addrChange(node, tree) {
      if (this.currentTree === "") {
        this.currentTree = node.id.slice(0, 2);
      } else {
        if (node.id.slice(0, 2) !== this.currentTree) {
          this.currentTree = node.id.slice(0, 2);
          this.$refs.addrTree.setCheckedKeys([]);
          this.$refs.addrTree.setCheckedKeys([node.id]);
        } else {
          this.$refs.addrTree.setCurrentKey(node.id);
        }
      }
    },
    async addrConfirm() {
      let checkedKeys = this.$refs["addrTree"].getCheckedKeys();
      let checkedKeysTrue = this.$refs["addrTree"].getCheckedKeys(true);
      let halfCheckedKeys = this.$refs["addrTree"].getHalfCheckedKeys();
      let { data } = await request({
        url: "/authority/area/anno/analysisByCheck",
        method: "post",
        data: {
          checkedKeys,
          checkedKeysTrue,
          halfCheckedKeys,
        },
      });
      let obj = {
        provice: data.provinceName,
        citys: data.cityNames,
        countys: data.districtNames,
        country: "",
        money: "400",
        isEdit: false,
        publishStatus: { code: "Y" },
      };
      this.addrtableDate.push(obj);
      this.addrFlag = false;
    },

    async getAddrTree() {
      request({
        url: "/authority/area/anno/tree",
        method: "get",
      })
        .then((res) => {
          this.addrTree = res.data;
        })
        .catch((req) => {
        });
    },
    addrclose() {
      this.addrFlag = false;
      this.rejectedFlag = false
    },
    editAddrItem() {},
    delAddr() {},
  },
  components: {
  },
  created() {
    this.getAddrTree();
    this.getitem();
  },
};
</script>

<style lang="less" scoped>
.proxySetings {
  border-top: 1px solid #ebecee;
  background-color: #fff;
  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
    padding: 0 20px;
    .el-button {
      margin-left: 10px;
    }
    img {
      display: inline-block;
      width: 70px;
      position: relative;
      top: 10px;
    }
  }
  .archivesEditContent {
    padding: 0px 20px;
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
      .title {
        padding: 0 0 15px;
        span {
          padding: 0 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: 650;
          font-style: normal;
          font-size: 15px;
        }
        i {
          font-style: normal;
          font-size: 14px;
        }
      }
      /deep/.el-form-item--medium .el-form-item__content {
        text-align: left;
        color: #cdced3;
      }
      /deep/ .el-col {
        line-height: unset;
      }
    }
    .textgray {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 14px;
      color: #505465;
      text-align: left;
      line-height: 22px;
    }
  }
  .addrtree {
    .addrtrees {
      width: 300px;
      max-height: 500px;
      overflow-y: scroll;
    }
    display: flex;
  }
  .el-table {
    margin-bottom: 30px;
  }
  /deep/ .el-dialog__footer {
    border-top: 1px solid #ddd;
  }
}
</style>
