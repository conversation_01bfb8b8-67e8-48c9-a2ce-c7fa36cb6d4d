<template>
  <div>
    <el-cascader ref="addr" style="width: 200px;" :disabled ="disabled" placeholder="请选择所在区域"  v-model="addr" :props="props" @change="addrcityChange " clearable> </el-cascader>
  </div>
</template>

<script>
import { areas } from "@/api/enterprise";
export default {
  data() {
    let that = this
    return {
      addr: ['0'],
      oldAddr: {},
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          let { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          if (!id && that.defaultAddr.region) {
            that.addr.regionId = ["0"];
            list.unshift({
              label: that.defaultAddr.region,
              leaf: true,
              provinceId: that.defaultAddr.provinceId,
              cityId: that.defaultAddr.cityId,
              countyId: that.defaultAddr.countyId,
              value: "0",
            });
          }
          resolve(list);
        },
      },
    };
  },
  created() {
    this.oldAddr = JSON.parse(JSON.stringify(this.defaultAddr))
  },
  methods: {
    addrcityChange(e) {
      if(e[0] != '0') {
        this.$emit('update:defaultAddr', {
          provinceId: e[0],
          cityId: e[1],
          countyId: e[2]
        })
      }else {
        this.$emit('update:defaultAddr', this.oldAddr)
      }
    },
  },
  watch: {
    defaultAddr(newl, old) {
      if(JSON.stringify(this.defaultAddr) == '{}') {
        this.addr = []
      }
    }
  },
  props: {
    defaultAddr: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
};
</script>

<style>
</style>