<template>
  <div class="detail-wrapper" v-loading="loading">
    <page-title title="客户档案详情"/>
    <div>
      <page-module-card title="首营状态">
        <ul v-if="firstCampData.firstCampStatus">
          <li>首营状态：{{firstCampData.firstCampStatus.desc}}</li>
          <li style="width: 75%;">变更备注：{{firstCampData.remark}}</li>
        </ul>
      </page-module-card>
      <page-module-card title="基础信息">
        <ul>
          <li>客户编码：{{data.code}}</li>
          <li>客户名称：{{data.name}}</li>
          <li>统一社会信用代码：{{data.socialCreditCode}}</li>
          <li>法人代表：{{data.legalPerson}}</li>
          <li>负责人：{{data.ceoName}}</li>
          <li>负责人手机：{{data.ceoMobile}}</li>
          <li>质量负责人：{{data.qualityPersonInCharge}}</li>
          <li>所在区域：{{data.region}}</li>
          <li>注册地址：{{data.registerAddress}}</li>
          <li>注册资金：{{data.registerCapital}}</li>
          <li>客户标识码：{{data.identifyCode}}</li>
          <li>客户分组：{{data.merchantGroup}}</li>
        </ul>
      </page-module-card>
      <page-module-card title="经营类目">
        <div style="padding: 10px 30px 30px 20px" class="cateGory">
          <el-row :gutter="20" v-if="cateGory" style="width:100%">
            <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8"
                    :style="'width:' + getFlexNum()+'%;flex:1;display:flex;flex-wrap:wrap;'">
              <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids"
                                                :key="ite.id">{{ite.label + '、'}}</span>
            </el-col>
          </el-row>
          <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
        </div>
      </page-module-card>
      <page-module-card title="采购资质">
        <procurement-table  :newLisenceTableDate.sync="newLisenceTableDate"  />
      </page-module-card>
      <page-module-card title="发票信息" style="border:0">
        <ul>
          <li>发票类型：{{data.invoiceInfo ? data.invoiceInfo.invoiceType.desc : '无'}}</li>
          <li>发票抬头：{{data.invoiceInfo ? data.invoiceInfo.name : '无'}}</li>
          <li>税号：{{data.invoiceInfo ? data.invoiceInfo.taxNumber : '无'}}</li>
          <li>注册电话：{{data.invoiceInfo ? data.invoiceInfo.registerMobile: '无'}}</li>
          <li>银行账号：{{data.invoiceInfo ? data.invoiceInfo.bankNumber:'无'}}</li>
          <li>开户银行：{{data.invoiceInfo ? data.invoiceInfo.depositBank:'无'}}</li>
        </ul>
      </page-module-card>
      <page-module-card title="发货地址">
        <el-table
          :data="data.deliveryAddressList"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%;margin-bottom: 20px;"
        >

          <el-table-column
            v-for="(item, index) in tableAddressTitle"
            :key="index"
            :width="item.width"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span>{{ row[item.name] }}</span>


            </template>
          </el-table-column>
        </el-table>
      </page-module-card>
    </div>
    <el-dialog
      title="客户资质"
      :visible.sync="deliveryVisible"
      width="75%">
      <div style="width:100%;height: 500px;text-align: center">
        <img :src="filePath" style="height: 100%;"/>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {getMerchantDetails, getListByLicenseBaseType, merchantPurSaleRel} from '@/api/distributorManagement'
  import ProcurementTable from "./procurementTable.vue";

  const TableColumns = [
    {label: "证件类型", name: "name", isImg: false},
    {label: "证件号", name: "licenseNumber", isImg: false},
    {label: "过期时间", name: "licenseEndTime", isImg: false},
    {label: "证件图片", name: "filePath", isImg: true}
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({key: i, ...TableColumns[i]});
  }
  const TableAddressColumns = [
    {label: "发货人姓名", name: "name"},
    {label: "联系手机", name: "mobilPhone"},
    {label: "联系电话", name: "fixedPhone"},
    {label: "收货区域", name: "region"},
    {label: "详细地址", name: "detailedAddress"}
  ];
  const TableAddressColumnList = [];
  for (let i = 0; i < TableAddressColumns.length; i++) {
    TableAddressColumnList.push({key: i, ...TableAddressColumns[i]});
  }

  import DetailItem from './detail-item'
  import checkPermission from "@/utils/permission";
  import PageModuleCard from "../../../components/PageModuleCard/index";

  export default {
    components: {
      PageModuleCard,
      DetailItem,
      ProcurementTable

    },
    data() {
      return {
        tableTitle: TableColumnList,
        tableAddressTitle: TableAddressColumnList,
        purMerchantId: '',
        data: {},
        firstCampData: {},
        type: 'MERCHANT_BUY',  //MERCHANT
        merchantLicenses: [],
        newLisenceTableDate: [],
        deliveryVisible: false,
        filePath: '',
        cateGory: false,
        loading: false
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      checkPermission,
      getFlexNum() {
        let num = 0
        this.cateGory
        for (const key in this.cateGory) {
          num++
        }
        if (num == 1) {
          return 100
        } else if (num == 2) {
          return 50
        } else {
          return 33
        }
      },
      goDetail(row) {
        this.deliveryVisible = true
        this.filePath = row.filePath
      },
      async getDetail() {
        const {data} = await getMerchantDetails(this.$route.query.purMerchantId);
        merchantPurSaleRel(this.$route.query.id).then(res => {
          this.firstCampData = res.data;
        })
        this.data = data;
        this.merchantLicenses = data.merchantLicenses
        let obj = {};
        if (!data.businessCategoryDetailList) {
          this.cateGory = false;
        } else {
          data.businessCategoryDetailList.forEach((item) => {
            if (!obj[item.parentName]) {
              obj[item.parentName] = [];
              obj[item.parentName].push(item);
            } else {
              obj[item.parentName].push(item);
            }
          });
          this.cateGory = obj;
        }
        this.getType()
      },
      getsrc(str) {
        if (!str) {
          return [];
        } else {
          let arr = str.split(",");
          let list = [];
          arr.forEach((item) => {
            let obj = {
              response: {
                data: {
                  url: "",
                },
              },
            };
            obj.response.data.url = item;
            obj.url = item;
            list.push(obj);
          });
          return list;
        }
      },
      async getType() {
        const {data} = await getListByLicenseBaseType(this.type)
       data.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            isForever: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            id: "",
            filePathList: [],
            limit: item.multiple.code == "Y" ? 5 : 1,
          };
         console.log("ids.item",item.id)

         this.merchantLicenses.find((ids) => {
            if (item.id == ids.licenseBaseId) {
              console.log("ids.ids",ids.licenseBaseId)
              obj.licenseEndTime = ids.isForever.code === 'Y' ? '' :ids.licenseEndTime;
              obj.filePath = ids.filePath;
              obj.filePathList = this.getsrc(ids.filePath);
              obj.licenseNumber = ids.licenseNumber;
              obj.label = item.name;
              obj.merchantId = ids.merchantId;
              obj.id = ids.id;
              obj.isForever = ids.isForever.code === 'Y'
            }
          });
          this.newLisenceTableDate.push(obj);
        });
        console.log("this.newLisenceTableDate===",this.newLisenceTableDate);

      },
      download(row) {
        const eleLink = document.createElement('a')
        eleLink.download = row.name
        eleLink.style.display = 'none'
        eleLink.href = row.filePath
        // 触发点击
        document.body.appendChild(eleLink)
        eleLink.click()
        // 然后移除
        document.body.removeChild(eleLink)
      }
    }
  }
</script>
<style lang="scss">
  .cateGory .el-col-8 {
    word-break: break-all;
    font-size: 14px;
    line-height: 20px;
    padding-bottom: 10px;
    text-overflow: wrap;
  }
</style>
