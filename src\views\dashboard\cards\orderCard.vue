<template>
  <div class="orderCard" v-loading="listLoading">
    <OrderSelectTime @getDetail="getDetail" :Query="query" />
    <div class="left">
      <div class="item">
        <div class="title">订单实收金额（元）</div>
        <div class="number">{{ orderDetail.orderMoney }}</div>
        <div class="msg" v-if="orderDetail.orderNumberUpOrDown != null">
          较上周
          <img
            v-if="orderDetail.orderMoneyUpOrDown == 1"
            src="@/assets/home/<USER>"
            alt=""
          />
          <img
            v-else-if="orderDetail.orderMoneyUpOrDown == -1"
            src="@/assets/home/<USER>"
            alt=""
          />
          <span>{{
            orderDetail.orderMoneyCompare == 0
              ? "-"
              : orderDetail.orderMoneyCompare
          }}</span>
        </div>
      </div>

      <div class="item">
        <div class="title">订单数</div>
        <div class="number">{{ orderDetail.orderNumber }}</div>
        <div class="msg" v-if="orderDetail.orderNumberUpOrDown != null">
          较上周
          <img
            v-if="orderDetail.orderNumberUpOrDown == 1"
            src="@/assets/home/<USER>"
            alt=""
          />
          <img
            v-else-if="orderDetail.orderNumberUpOrDown == -1"
            src="@/assets/home/<USER>"
            alt=""
          />
          <span>{{
            orderDetail.orderNumberCompare == 0
              ? "-"
              : orderDetail.orderNumberCompare
          }}</span>
        </div>
      </div>
    </div>
    <div class="right">
      <div
        ref="macarons"
        class="macarons"
        :style="{ height: '100%', width: '100%' }"
      />
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "../admin/components/mixins/resize";
import { spectaculars } from "@/api/dashboard/index";
import OrderSelectTime from './components/orderSelectTime.vue';
export default {
  mixins: [resize],
  data() {
    return {
      options: [
        {
          value: 1,
          label: "近七天",
        },
        {
          value: 2,
          label: "近三十天",
        },
        {
          value: 3,
          label: "自然周",
        },
        {
          value: 4,
          label: "自然月",
        },
        {
          value: 5,
          label: "自定义",
        },
      ],
      orderType: [
        { value: 1, label: "待审核" },
        { value: 2, label: "待付款" },
        { value: 3, label: "待发货" },
        { value: 4, label: "发货中" },
        { value: 5, label: "已发货" },
        { value: 6, label: "已完成" },
        { value: 7, label: "已取消" },
      ],
      selectOrderType: 1,
      selectOptions: 1,
      orderTime: [],
      orderWeek: "",
      orderMonth: "",
      disAbledFun: {
        disabledDate(date) {
          return date.getTime() > Date.now();
        },
      },
      listLoading: false,
      orderDetail: {
        orderMoney: 0,
        orderMoneyCompare: 0,
        orderMoneyUpOrDown: 0,
        orderNumber: 0,
        orderNumberCompare: 0,
        orderNumberUpOrDown: 0,
      },
      query: {
        endTime: "",
        orderStatus: "1",
        startTime: "",
      },

      /////////
      chart: null,
      chartData: {
        expectedData: [100, 120, 161, 134, 105, 160, 165],
        actualData: [120, 82, 91, 154, 162, 140, 145],
      },
      messages: {
        expectedData: [200, 192, 120, 144, 160, 130, 140],
        actualData: [180, 160, 151, 106, 145, 150, 130],
      },
      purchases: {
        expectedData: [80, 100, 121, 104, 105, 90, 100],
        actualData: [120, 90, 100, 138, 142, 130, 130],
      },
      shoppings: {
        expectedData: [130, 140, 141, 142, 145, 150, 160],
        actualData: [120, 82, 91, 154, 162, 140, 130],
      },
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    async getDetail(val) {
      this.query = val
      this.listLoading = true;
      let { data } = await spectaculars(val);
      this.listLoading = false;
      this.orderDetail = data;
      this.chartData = data.data[0];
    },
    initChart() {
      this.chart = echarts.init(this.$refs.macarons, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions({ orderMoneyList, orderNumberList, time } = {}) {
      this.chart.setOption({
        title: [
          {
            text: "",
            left: "center",
          },
        ],
        //当trigger为’item’时只会显示该点的数据，
        //为’axis’时显示该列下所有坐标轴所对应的数据。
        //提示框组件
        tooltip: {
          trigger: "axis",
        },
        color: ["#FF6E1B", "#0056E5"],
        legend: {
          y: "top",
          x: "left",
          left: "0",
          itemHeight: 12,
          itemWidth: 12,
          // bottom: 0, // 类似于margin-bottom
          // 图例样式
          // icon : circle圆形、rect矩形、roundRect圆角矩形、triangle三角形、pin水滴、arrow箭头...
          data: [
            {
              name: "成交金额",
              icon: "roundRect",
              key: "dataCount",
            },
            {
              name: "成交数",
              icon: "roundRect",
              key: "defaultVal",
            },
          ],
          textStyle: {
            // 统计项的样式
            color: "#333",
            fontSize: 12,
          },
          itemGap: 10,
        },
        grid: {
          left: 0, // 默认10%，给24就挺合适的。
          top: 30, // 默认60
          right: 30, // 默认10%
          bottom: 0, // 默认60
          containLabel: true,
        },
        xAxis: {
          type: "category", //category:类目轴(适用于离散无序数组)
          data: time,
          axisLabel: {
            show: true,
            textStyle: {
              color: "#595959", //X轴文字颜色
              fontSize: 14, // x轴文字大小
            },
            rotate: 0, // 倾斜角度
            align: "center", // x轴文字居中
            margin: 10, // 文字与x轴的间隔
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#595959", // x轴颜色
            },
          },
          axisTick: {
            inside: false, // true刻度朝向内侧
            alignWithLabel: {
              boundaryGap: true, // 值与刻度对齐
            },
          },
        },
        yAxis: [
          {
            type: "value",
            name: "",
            splitLine: {
              lineStyle: {
                type: "dashed",
              },
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#7C8492", //y轴文字颜色
                fontSize: 14, // y轴文字大小
              },
              formatter: "{value}",
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#7C8492", // y轴颜色
              },
            },
            axisTick: {
              show: false, // 隐藏坐标轴上的刻度
            },
          },
          {
            type: "value",
            name: "",
            min: 0,
            splitLine: { show: false }, // 不显示分割线
            axisLabel: {
              show: true,
              textStyle: {
                color: "#7C8492", //y轴文字颜色
                fontSize: 14, // y轴文字大小
              },
              formatter(value) {
                if (value % 1 != 0) {
                  return "";
                } else {
                  return value;
                }
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false, // 隐藏坐标轴上的刻度
            },
          },
        ],
        series: [
          {
            name: "成交金额",
            type: "bar",
            stack: "总量",
            barWidth: "30%",
            yAxisIndex: 0,
            barGap: "10%",
            data: orderMoneyList,
          },
          {
            name: "成交数",
            type: "bar",
            barWidth: "30%",
            yAxisIndex: 1,
            barGap: "10%",
            stack: "总量1",
            data: orderNumberList,
          },
        ],
      });
    },
  },
  components: {
    OrderSelectTime,
  },
};
</script>

<style lang="scss" scoped>
.orderCard {
  display: flex;
  justify-content: flex-start;
  position: relative;
  .btn {
    position: absolute;
    top: -50px;
    right: 0;
  }
  .left {
    .item {
      margin-bottom: 24px;
      padding-left: 30px;
      width: 300px;
      div {
        padding-top: 10px;
      }
      .title {
        color: #abadb4;
        font-size: 14px;
      }
      .msg {
        font-size: 14px;
        color: #7c8492;
        img {
          width: 10px;
          margin: 0 5px;
        }
      }
      .number {
        font-size: 32px;
        color: #0f1831;
        font-weight: 600;
      }
    }
  }
  .right {
    flex: 1;
  }
}
</style>
