<template>
    <popup-selection @open="handleOpen" :formatQueryParamsBeforeFetch="formatQueryParamsBeforeFetch"
        :formatResponse="formatResponse" :disabled="disabled" :data.sync="list" label="客户" :request="request"
        :searchFields="searchFields" :selectTableColumns="selectTableColumns" :tableColumns="tableColumns"
        @ok="handleOk" @change="handleChange">
        <!-- <template slot="header">
            <el-button type="primary">导入客户</el-button>
        </template> -->
    </popup-selection>
</template>

<script>

import PopupSelection from "../LogicalComponents/PopupSelection.vue"

import { merchantType } from "@/api/archivesList";
import { merchantPurSaleRelList } from '@/api/group'
import { getAllStore } from "@/api/products/store";

const selectTableColumns = [
    { label: '客户编码', prop: 'customerCode' },
    { label: '客户名称', prop: 'name' },
    { label: '企业类型', prop: 'merchantType' },
    { label: '联系人', prop: 'ceoName' },
    { label: '联系电话', prop: 'ceoMobile' },
    { label: '所在仓库', prop: 'warehouseNames' },
    { label: '所在地区', prop: 'region' }
].map((v, i) => ({ key: i, ...v }))


const tableColumns = [
    { label: '客户编码', prop: 'customerCode' },
    { label: '客户名称', prop: 'name' },
    { label: '企业类型', prop: 'merchantType' },
    { label: '联系人', prop: 'ceoName' },
    { label: '联系电话', prop: 'ceoMobile' },
    { label: '所在仓库', prop: 'warehouseNames' },
    { label: '所在地区', prop: 'region' }
].map((v, i) => ({ key: i, ...v }))
const MODEL = 'UPDATE_MODEL'

export default {
    props: {
        data: {
            type: Array,
            default: () => []
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        },
    },
    inject: {
        elForm: {
            default: ''
        },
        elFormItem: {
            default: ''
        }
    },
    watch: {
        data: {
            handler(data) {
                this.list = data || [];
            },
            immediate: true,
            deep: true
        }
    },
    model: {
        prop: 'data',
        event: MODEL
    },
    data() {
        return {
            list: [],
            selectTableColumns,
            tableColumns,
            request: merchantPurSaleRelList,
            searchFields: [
                { prop: 'keyword', text: '客户名称/客户编码' },
                {
                    prop: 'merchantTypeIds',
                    text: '企业类型',
                    type: 'MultipleSelect',
                    options: []
                },
                {
                    prop: 'warehouseIds',
                    text: '所在仓库',
                    type: 'MultipleSelect',
                    options: []
                },
                {
                    type: 'Region',
                    prop: 'address',
                    text: '所在地区'
                }
            ]
        }
    },
    methods: {
        handleOpen() {
            !(this.searchFields[1]?.options?.length) && this.fetchMerchantTypes();
            !(this.searchFields[2]?.options?.length) && this.fetchWarehouseList();
            
        },
        formatQueryParamsBeforeFetch(params) {
            /** 业务关联 - 🌟🌟🌟🌟🌟 */
            if (params.model.address) {
                let { address, ...model } = params.model
                const [provinceId, cityId, countyId] = address
                params = {
                    ...params,
                    model: {
                        ...model,
                        provinceId, cityId, countyId
                    }
                }
            }
            return params
        },
        formatResponse(result) {
            /** 业务关联 - 🌟🌟🌟🌟🌟 */
            result.data?.records?.forEach(item => {
                item.selectable = item.publishStatus.code === 'N'
            });
            return result
        },
        handleChange(data) {
            this.$emit(MODEL, data)
            this.elFormItem && this.elFormItem.$emit('el.form.blur', data)
        },
        handleOk(data) {
            this.$emit(MODEL, data)
        },
        async fetchMerchantTypes() {
            const { data } = await merchantType();
            this.searchFields.splice(1, 1, {
                ...this.searchFields[1],
                options: data?.map(({ name: text, id: value }) => ({ text, value })) || []
            })
        },
        async fetchWarehouseList() {
            const { data } = await getAllStore();
            this.searchFields.splice(2, 1, {
                ...this.searchFields[2],
                options: data?.map(({ name: text, id: value }) => ({ text, value })) || []
            })
            console.log(data)
        }
    },
    components: {
        PopupSelection
    }
}
</script>

<style lang="scss" module>
</style>