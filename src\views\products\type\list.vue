<template>
  <div v-loading="loading" class="product-type-list">
    <div class="header">
      <div class="left">
        <el-input v-model.trim="searchType" class="search" placeholder="请输入产品分类" />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
      <div>
        <el-button @click="load">刷新</el-button>
        <el-button v-if="checkPermission(['admin','sale-saas-productType:addType', 'sale-platform-productType:addType'])" type="primary" @click="openFormCreate('0',10)">+新增商品分类</el-button>
      </div>
    </div>

    <el-table :data="list" row-key="id" border>
      <el-table-column label="分类编码" prop="code" />
      <el-table-column label="产品分类" prop="name" />
      <el-table-column label="排序" prop="sort" />
      <el-table-column label="分类图片" prop="pictIdS" width="100">
        <template slot-scope="scope">
          <el-popover placement="right" trigger="hover">
            <el-image
              style="width: 200px; height: 200px"
              fit="contain"
              :src="scope.row.pictIdS | imgFilter"
            ></el-image>
            <el-image
              slot="reference"
              style="width: 30px; height: 30px"
              fit="cover"
              :src="scope.row.pictIdS | imgFilter"
            ></el-image>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="前端是否显示">
        <template slot-scope="scope">
          <el-radio-group v-model="scope.row.frontShow" @change="updateRow(scope.row)">
            <el-radio :label="true">显示</el-radio>
            <el-radio :label="false">隐藏</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200px" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span v-if="scope.row.level < 2&&checkPermission(['admin','sale-saas-productType:addChildType', 'sale-platform-productType:addChildType'])" class="table-edit-row-item">
              <el-button type="text" @click="openFormCreate(scope.row.id,scope.row.level)">新增子分类</el-button>
            </span>
            <span v-if="checkPermission(['admin','sale-saas-productType:editType','sale-platform-productType:editType'])" class="table-edit-row-item">
              <el-button type="text" @click="openFormEdit(scope.row)">编辑</el-button>
            </span>
            <span v-if="checkPermission(['admin','sale-saas-productType:delType','sale-platform-productType:delType'])" class="table-edit-row-item">
              <el-button type="text" @click="deleteRow(scope.row)">删除</el-button>
            </span>
          </el-row>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="formVisible"
      :title="formTitle"
      :close-on-click-modal="!formLoading"
      :close-on-press-escape="!formLoading"
      :show-close="!formLoading"
      width="40%"
      v-dialogDrag
    >
      <el-form v-loading="formLoading" :model="formData" label-width="120px" ref="ruleForm">
        <el-form-item label="分类名称" prop="name" :rules="[
          { required:true, message: '请填写分类名称', trigger: 'blur' }
        ]">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item prop="parentId" v-if="formAction === 'EDIT'" :rules="formAction === 'EDIT'?[
          { required:true, message: '请选择父级分类',trigger: 'change' }
        ]:[]" label="父级分类">
          <el-cascader
            v-model="formData.parentId"
            :options="[{
              id: '0', name: '根目录',
              children: list
            }]"
            :props="{ checkStrictly: true, emitPath: false, label: 'name', value: 'id' }"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort" :rules="[
          {  validator: validateNumber, trigger: 'blur' }
        ]">
          <el-input v-model="formData.sort" type="number" />
        </el-form-item>
        <el-form-item label="前端是否显示" prop="frontShow" :rules="[
          { required: true, message: '请选择前端是否显示', trigger: 'change' }
        ]">
          <el-radio v-model="formData.frontShow" :label="true">显示</el-radio>
          <el-radio v-model="formData.frontShow" :label="false">隐藏</el-radio>
        </el-form-item>
        <el-form-item
          v-if="formData.level!=0"
          class="formItem"
          label="封面"
          prop="pictIdS"
        >
          <el-upload
            class="avatar-uploader"
            :action="uploadParams.action"
            :headers="uploadParams.headers"
            :data="uploadParams.data"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :before-upload="beforeUpload"
            accept=".jpg,.png,.bmp,.jpeg"
          >
            <el-image
              v-if="formData.pictIdS!=''"
              style="width: 100px; height: 100px"
              :src="formData.pictIdS"
              class="avatar"
              fit="contain"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button :disabled="formLoading" @click="closeForm">取消</el-button>
        <el-button type="primary" :disabled="formLoading" @click="submitForm('ruleForm')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'
import request from '@/utils/request'
import checkPermission from '../../../utils/permission'
import { getToken } from '@/utils/auth'
import { getLocalUser } from '@/utils/local-user'
export default {
  data() {
    return {
      loading: false,
      originalData: null,
      list: [],
      searchType: '',
      formVisible: false,
      formLoading: false,
      formAction: null, // CREATE || EDIT
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + '/file/file/upload',
        headers: {
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
          token: `Bearer ${getToken()}`
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0
        }
      },
      formData: {
        id: '0',
        name: '',
        sort: 1,
        pictIdS:'',
        frontShow: true,
        parentId: '0'
      }
    }
  },

  computed: {
    formTitle() {
      switch (this.formAction) {
        case 'CREATE': return '新增商品分类/新增子分类'
        case 'EDIT': return '编辑子分类'
        default: return ''
      }
    }
  },

  mounted() {
    this.load()
  },

  methods: {
    checkPermission,
    validateNumber(rule, value, cb) {
      console.log('value',value);
        // 输入正数，保留小数点后两位
        if (!value) {
          return cb()
        }
        if (!this.isNumber2(value)) {
          return cb(new Error('只能输入正数'))
        }
        cb()
    },
    isNumber2 (value) {
      let v = parseFloat(value);
      if (isNaN(v)) {
        return false;
      }
      value = v;
      if (value <= 0) {
        return false;
      }
      let arr = (value + '').split('.');
      if (arr[1] && arr[1].length > 2) {
        return false;
      }
      return true;
    },
    async load() {
      this.loading = true
      this.searchType = ''
      const { data } = await request.post('product/admin/category/query', {})
      this.originalData = data
      this.buildTree(data)

      this.loading = false
    },
    handleSearch() {
      this.loading = true
      this.buildTree(this.originalData.filter(({ label }) => label.indexOf(this.searchType) !== -1))
      this.loading = false
    },
    handleReset() {
      this.searchType = '';
      this.load();
    },
    buildTree(list) {
      const map = _.groupBy(list, 'parentId')

      this.list = this._buildTree(0, 0, map)
    },

    _buildTree(parentId, level, map) {
      const list = map[parentId]

      if (!list) { return null }

      return list.map(item => ({
        id: item.id,
        code: item.categoryCode,
        name: item.label,
        sort: item.sortValue,
        frontShow: _.get(item, 'whetherShowFrontend.code') === 'Y',
        parentId: item.parentId,
        pictIdS:item.pictIdS,
        level,
        children: this._buildTree(item.id, level + 1, map)
      })).sort((a, b) => a.sort - b.sort)
    },

    openFormCreate(parentId,level) {
      if(level==10){
        this.formData.level = 0;
      } else {
        this.formData.level = level + 1;
      }
      this.formAction = 'CREATE'
      this.formVisible = true
      this.formData.pictIdS = '';
      this.formData.id = '0'
      this.formData.name = ''
      this.formData.sort = 1
      this.formData.frontShow = true
      this.formData.parentId = parentId
    },

    openFormEdit(opts) {
      console.log('----->',opts);
      this.formAction = 'EDIT'
      this.formVisible = true

      this.formData.level = opts.level;
      this.formData.pictIdS = opts.pictIdS;

      this.formData.id = opts.id
      this.formData.name = opts.name
      this.formData.sort = opts.sort
      this.formData.frontShow = opts.frontShow
      this.formData.parentId = opts.parentId
    },
    uploadSuccess(res, file) {
      this.formData.pictIdS = res.data.url;
    },
    beforeUpload(file) {
      const fileTypeList = ['image/png', 'image/pjpeg', 'image/jpeg', 'image/bmp']
      const isJPG = fileTypeList.indexOf(file.type) > -1
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('上传图片格式错误!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },

    closeForm() {
      this.formVisible = false
      this.formAction = null
    },

    async submitForm(ruleForm) {
      this.$refs[ruleForm].validate(async valid => {
        console.log('valid----->',valid);
        if(valid){
          this.formLoading = true
          try {
            if (this.formAction === 'CREATE') {
              let data = await this.submitFormCreate();
              if(data.code == 0 && data.msg == 'ok') {
                this.$message.success('创建成功');
                this.closeForm();
              }
            } else if (this.formAction === 'EDIT') {
              let res = await this.submitFormEdit();
              if(res.code == 0 && res.msg == 'ok') {
                this.$message.success('修改成功');
                this.closeForm();
              }
            }
            this.formLoading = false;
            this.load();
          } catch (error) {
            this.formLoading = false;
          }
        }
      })
    },

    submitFormCreate() {
      return request.post('product/admin/category', {
        label: this.formData.name,
        parentId: this.formData.parentId,
        pictIdS: this.formData.pictIdS,
        sortValue: this.formData.sort,
        whetherShowFrontend: this.formData.frontShow ? 'Y' : 'N'
      })
    },

    submitFormEdit() {
      return request.put('product/admin/category', {
        id: this.formData.id,
        label: this.formData.name,
        operation: '',
        parentId: this.formData.parentId,
        pictIdS: this.formData.pictIdS,
        platformCategoryId: 0,
        sortValue: this.formData.sort,
        whetherShowFrontend: this.formData.frontShow ? 'Y' : 'N'
      })
    },

    async updateRow(row) {
      this.loading = true
      let upData = await request.put('product/admin/category', {
        id: row.id,
        label: row.name,
        operation: '',
        parentId: row.parentId,
        pictIdS: row.pictIdS,
        platformCategoryId: 0,
        sortValue: row.sort,
        whetherShowFrontend: row.frontShow ? 'Y' : 'N'
      })
      if(upData.code == 0 && upData.msg == 'ok') {
        this.$message.success('修改成功')
      }
      this.loading = false
      this.load()
    },

    async deleteRow(row) {
      try {
        await this.$confirm('此操作将永久删除该信息，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (e) {
        return
      }

      this.loading = true
      let delData = await request.delete('product/admin/category', {
        params: { ids: [row.id] }
      })
      this.loading = false;
      if(delData.code == 0 && delData.msg == 'ok') {
        this.$message.success('已删除')
      }
      this.load()
    }
  }
}
</script>

<style lang="scss" scoped>
.product-type-list {
  padding: 20px;
  background: #fff;
  > .header {
    display: flex; justify-content: space-between; align-items: center;
    margin-bottom: 20px;
    .left {
      display: flex; align-items: center;
      .search { margin-right: 20px; }
    }
  }
}
.avatar-uploader {
    width: 100px;
    height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 120px;
    text-align: center;
  }
</style>
