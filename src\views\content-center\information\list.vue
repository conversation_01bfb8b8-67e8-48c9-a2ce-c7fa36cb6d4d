<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="title">
        <el-input v-model.trim="listQuery.model.title" placeholder="请输入资讯标题" />
      </im-search-pad-item>
      <im-search-pad-item prop="articleCategoryId">
        <el-select v-model="listQuery.model.articleCategoryId" placeholder="请选择资讯分类">
          <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="timePicker">
        <el-date-picker v-model="timePicker" type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd" @change="selectTime">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        :tabs="[{ name: '资讯' }]"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'sale-saas-content-manage-info:add','sale-platform-content-manage-info:add'])" type="primary" @click="newFun">+新增资讯</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
            <template slot-scope="scope"> {{scope.$index + 1}} </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
            <span v-if="item.name == 'publishStatus'">
              <el-switch v-if="checkPermission(['admin', 'informationList:release'])" v-model="row[item.name].type" @change="swchange(row)"> </el-switch>
              <span v-else :style="row[item.name].code == 'Y' ? '' : 'color:#ff0036'">{{ row[item.name].code == 'Y' ? '已发布' : '未发布' }}</span>
            </span>
              <span v-else-if="item.name=='isOpenWindow'" :style="row[item.name].code=='N'?'color:#ff0066':''">
              {{row[item.name].code=='Y'? '是' : '否'}}
            </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="120" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'sale-saas-content-manage-info:edit','sale-platform-content-manage-info:edit'])" class="table-edit-row-item">
                  <el-button @click="reviewedFun(scope.row)" type="text" >编辑</el-button>
                </span>
                <span v-if="checkPermission(['admin', 'sale-saas-content-manage-info:del','sale-platform-content-manage-info:del'])" class="table-edit-row-item">
                  <el-button @click="delFun(scope.row, scope.$index)" type="text" >删除</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagenation v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <el-dialog v-if="showEditPage" :title="(artItem.id?'编辑':'新增')+'资讯'" :visible.sync="showEditPage" width="80%" :show-close="true">
      <edit :visible.sync="showEditPage" :isReload.sync="submitReload" :categoryList="options" :row.sync="artItem"></edit>
    </el-dialog>
    <!-- 设置 编辑 -->

    <!-- 预览 -->
    <el-dialog v-if="artflag" v-dialogDrag :title="artItem.title" :visible.sync="artflag" width="70%" :show-close="true" center :before-close="artclose">
      <div v-loading="artLoading">
        <div class="artTime" v-if="artItem.createTime">创建时间： {{artItem.createTime}}</div>
        <div v-html="artItem.content"></div>
      </div>
    </el-dialog>
    <!-- 预览 -->
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import pagenation from '@/components/Pagination';
import {
  list,
  getitem,
  putItem,
  category,
  del,
  start,
  disable,
  getTreeList,
} from '@/api/content-center/article';
import { setContextData, getContextData } from '@/utils/auth';
import TabsLayout from '@/components/TabsLayout'
export default {
  data() {
    return {
      list: [],
      tabType: 'list',
      tableTitle: [
        {
          label: "标题",
          name: "title",
          width: "400px",
        },
        {
          label: "发布状态",
          name: "publishStatus",
          width: "160px",
        },
        {
          label: "资讯分类",
          name: "articleCategoryName",
          width: "250px",
        },
        {
          label: "是否新窗口打开",
          name: "isOpenWindow",
          width: "160px",
        },
        {
          label: "创建人",
          name: "createUser",
          width: "150px",
        },
        {
          label: "创建时间",
          name: "createTime",
          width: "270px",
        },
      ],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          whetherNeedHelpArticle:'N'
        },
        map: {},
      },
      timePicker: [],
      options: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      total: 0,
      listLoading: false,
      showEditPage: false,
      artItem: {},
      submitReload: "",
      artflag: false,
    };
  },
  methods: {
    checkPermission,
    delFun(row, index) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let { data } = await del({ ids: [row.id] });
          if (data) {
            this.list.splice(index, 1);
            this.$message.success('已删除--' + row.title )
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    async swchange(row) {
      if (row.publishStatus.type) {
        let { data } = await start(row.id);
        this.$message.success(`已将 ${row.title} 的发布状态修改为发布`);
        if (!data) {
          row.publishStatus.type = !row.publishStatus.type;
        }
      } else {
        let { data } = await disable(row.id);
        this.$message.success(`已将 ${row.title} 的发布状态修改为未发布`);
      }
    },
    artclose() {
      this.artItem = {};
      this.artflag = false;
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await list(this.listQuery);
      this.total = data.total;
      data.records.forEach((item) => {
        if (!item.publishStatus) {
          item.publishStatus = { code: "N", desc: "是" };
          item.publishStatus.type = false;
        } else if (item.publishStatus.code == "Y") {
          item.publishStatus.type = true;
        } else {
          item.publishStatus.type = false;
        }
      });
      this.list = data.records;
      this.listLoading = false;
    },
    reviewedFun(row) {
      setContextData("informationEdit", this.listQuery);
      this.$router.push({
        path: "/content-center/information-edit",
        query: {
          id: row.id,
        },
      });
    },
    async getCategory() {
      let { data } = await category();
      this.options = data;
    },
    selectTime(e) {
      this.listQuery.map.createTime_st = e[0];
      this.listQuery.map.createTime_ed = e[1];
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    resetForm() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
        map: {},
      };
      this.timePicker = [];
      this.getlist();
    },
    newFun() {
      setContextData("informationEdit", this.listQuery);
      this.$router.push({
        path: "/content-center/information-edit",
      });
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <i class="el-icon-menu" />
        </div>
      );
    },
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.getlist();
        this.submitReload = false;
      }
    },
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/content-center/information-edit") {
        if (getContextData("informationEdit") != "") {
          vm.listQuery = getContextData("informationEdit");
        }
        vm.getlist();
      }
      // vm.initTbaleTitle();
      // vm.getlist();
    });
  },
  created() {
    this.getCategory();
    this.getlist();
  },
  components: {
    pagenation,
    TabsLayout
  },
};
</script>

<style lang="less" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .artTime {
    text-align: center;
    color: #969696;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
</style>
