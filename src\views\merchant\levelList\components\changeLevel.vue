<template>
  <el-dialog title="变更等级" :visible.sync="visible" direction="rtl" :wrapperClosable="false" width="600px">
    <div style="height: 100%;">
      <el-form ref="form" :rules="rules" class="form" size="small" :model="form" label-width="120px">
        <el-form-item label="当前等级">
          <span>银牌合伙人</span>
        </el-form-item>
        <el-form-item label="等级值">
          <span>1</span>
        </el-form-item>
        <el-form-item label="变更为" prop="businessScenario">
          <el-select v-model="form.businessScenario" placeholder="请选择等级名称" clearable filterable>
            <el-option v-for="item in businessScenarioData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button @click="hide">取消</el-button>
          <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import { createSmsTask } from "@/api/retailStore";

export default {
  name: 'changeLevel',
  data() {
    return {
      visible: false,
      loading: false,
      businessScenarioData: [
        { label: '直播', value: 'LIVE' },
        { label: '其他', value: 'OTHER' }
      ],
      form: {
        businessScenario: '', // 业务场景
        taskName: '', // 任务名称
        physicalWxUrlLink: '', // 报名链接
      },
      rules: {
        businessScenario: [{ required: true, message: '请选择等级名称', trigger: 'change' }],
      }
    }
  },
  methods: {
    show(data) {
      Object.assign(this.$data.form, this.$options.data().form)
      this.form = deepClone(data)
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        createSmsTask(this.form).then(() => {
          this.$message.success('变更等级成功')
          this.hide()
          this.$emit('updateSuccess')
        }).finally(() => {
          this.loading = false
        })
      });
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
