
*{
  padding: 0;
  margin: 0;
}
ul, li{
  list-style: none;
}
.edit-posi{
  position: relative;
  cursor: pointer;
  .edit-pop{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    max-width: 1200px;
    background: rgba(3,10,33,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 14px;
    z-index: 6;
  }
}
.pcmall-content{
  padding: 0 20px;
  background: #fff;
  height: calc(100vh - 118px);
  overflow-x: hidden;
  .content-title{
    background-color: #fff;
    height: 56px;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #EBECEE;
    .lt{
      font-size: 18px;
      font-weight: 500;
      color: #1e2439;
    }
    .el-button--primary{
      background-color: #0056E5;
      border-color: #0056E5;
    }
  }
  .businessbg {
    width: 100%;
    height: 120px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    .businesswrap {
      position: relative;
      width: 1200px;
      margin: 0 auto;
      .buslogo {
        width: 80px;
        height: 80px;
        float: left;
        margin: 20px 0px;
        border-radius: 5px;
        background-color: #ffffff;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
      }
      .bustext {
        float: left;
        margin: 20px;
        width: 60%;
        h2 {
          font-weight: bold;
          color: #ffffff;
          font-size: 20px;
          margin-top: 12px;
        }
        dl {
          position: relative;
          margin-top: 10px;
        }
        dd {
          display: inline-block;
          color: #ffffff;
          font-size: 12px;
          position: relative;
          padding-right: 10px;
          em {
            color: #ffffff;
            font-style: normal;
            margin-left: 4px;
          }
          b {
            display: block;
            width: 1px;
            height: 12px;
            background-color: #ffffff;
            position: absolute;
            top: 3px;
            right: 0px;
          }
          &:nth-child(2) {
            padding-left: 5px;
          }
        }
      }
      .touchmerchant {
        width: 244px;
        height: 100px;
        float: right;
        border-radius: 5px;
        margin-top: 10px;
        background-color: rgba(255, 255, 255, 0.8);
        h4 {
          padding: 10px 20px 0;
          font-weight: normal;
          color: #1e2439;
          font-size: 14px;
        }
        p {
          color: #505465;
          font-size: 12px;
          padding: 0px 20px;
        }
        .tchmtbtn {
          position: relative;
          margin: 7px 20px;
          a {
            display: block;
            height: 32px;
            line-height: 32px;
            width: 98px;
            background-color: #ffffff;
            color: #0056e5;
            font-size: 12px;
            text-align: center;
            border-radius: 5px;
          }
          i {
            width: 12px;
            height: 12px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 6px;
            margin-top: -3px;
            &.thicon_01 {
              background-image: url(../../../assets/imgs/pcIndex/thicon_01.png);
            }
            &.thicon_02 {
              background-image: url(../../../assets/imgs/pcIndex/thicon_02.png);
            }
          }
        }
      }
    }

  }
  .businessGroup {
    position: relative;
    .busHd {
      width: 100%;
      height: 40px;
      position: relative;
      background-color: #ffffff;
      .wrap {
        position: relative;
        width: 1200px;
        margin: 0 auto;
      }
      li {
        display: inline-block;
        margin-right: 48px;
        height: 38px;
        line-height: 40px;
        color: #373d50;
        font-size: 16px;
        font-weight: bold;
        position: relative;
        cursor: pointer;
        border-bottom: 2px solid #ffffff;
        &.on {
          border-color: #0056e5;
        }
      }
      .bussearch {
        width: 200px;
        height: 28px;
        border: 1px solid #e6e7ea;
        position: absolute;
        top: 6px;
        right: 0px;
        input {
          width: 100%;
          height: 28px;
          border: 0;
          background: none;
          padding-left: 8px;
        }
        .busseabtn {
          width: 16%;
          height: 28px;
          position: absolute;
          top: 0px;
          right: 0px;
          i {
            width: 12px;
            height: 12px;
            margin: 8px;
            background-image: url(../../../assets/imgs/pcIndex/Search.png);
          }
        }
      }
    }
    .busBd {
      position: relative;
      .busBox {
        position: relative;
        width: 1200px;
        margin: 0 auto;
      }
    }
  }
  .ShopNotices {
    position: relative;
    padding: 20px 24px;
    background-color: #ffffff;
    margin-top: 20px;
    h2 {
      color: #373d50;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      .spnticon {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 8px;
        margin-top: -3px;
        background-image: url(../../../assets/imgs/pcIndex/Icon_Notice.png);
      }
    }
    .spnttext {
      color: #505465;
      font-size: 12px;
      line-height: 22px;
    }
  }
  .Coupondiscount {
    position: relative;
    padding: 20px 24px 4px;
    background-color: #ffffff;
    margin-top: 20px;
    h2 {
      color: #373d50;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .couplist {
      position: relative;
      margin-top: 16px;
      li {
        width: 214px;
        float: left;
        margin-right: 20px;
        margin-bottom: 20px;
        border-radius: 5px;
        background-color: #f0f5ff;
        &:nth-child(5n + 5) {
          margin-right: 0px;
        }
      }
      .coupname {
        padding: 15px 20px 10px;
        position: relative;
        color: #0556fe;
        font-size: 12px;
        font-weight: bold;
        b {
          display: inline-block;
          font-size: 36px;
        }
        em {
          font-style: normal;
          font-size: 16px;
        }
        span {
          font-size: 16px;
        }
      }
      .couptime {
        color: #787e89;
        font-size: 12px;
        padding: 0px 20px 20px;
      }
      .coupdraw {
        width: 100%;
        height: 40px;
        display: block;
        line-height: 40px;
        text-align: center;
        color: #ffffff;
        font-size: 14px;
        background-image: url(../../../assets/imgs/pcIndex/img_coupon.png);
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }
    }
  }

  .Congratulations {
    width: 214px;
    height: 64px;
    line-height: 64px;
    color: #1e2439;
    font-size: 14px;
    background-color: #ffffff;
    border: 1px solid #eeeeee;
    border-radius: 8px;
    position: fixed;
    top: 60%;
    left: 40%;
    display: none;
    z-index: 100;
    .success {
      width: 28px;
      height: 28px;
      display: inline-block;
      vertical-align: middle;
      margin-right: 10px;
      margin-left: 32px;
      background-image: url(../../../assets/imgs/pcIndex/Icon_success.png);
    }
  }
  .productGroup {
    position: relative;
    margin-top: 20px;
    margin-bottom: 10px;
    .pdHd {
      width: 100%;
      height: 56px;
      background-color: #ffffff;
      line-height: 56px;
      li {
        display: inline-block;
        margin: 0px 24px;
        color: #1e2439;
        font-size: 16px;
        cursor: pointer;
        font-weight: bold;
        height: 54px;
        &.on {
          border-bottom: 3px solid #0056e5;
          color: #0556fe;
        }
      }
    }
    .pdBd {
      position: relative;
      margin: 10px 0px;
      .pdBox {
        position: relative;
      }
    }
  }
  .gooscontent {
    position: relative;
    li {
      position: relative;
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;
      background-color: #ffffff;
      cursor: pointer;
      max-width: 232px;
      overflow: hidden;
      &:nth-child(5n + 5) {
        margin-right: 0px;
      }
    }
    .recommenimg {
      width: 192px;
      height: 192px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      margin: 20px;
    }
    .recommentext {
      margin: 0px 20px 20px;
      height: 100px;
      .title {
        color: #1e2439;
        font-size: 14px;
        font-weight: normal;
        margin-bottom: 6px;
        width: 192px;
        line-height: 20px;
        a {
          display: block;
          color: #1e2439;
          width: 192px;
          font-size: 14px;
          overflow: hidden;
          white-space: nowrap;
          -webkit-text-overflow: ellipsis;
          -moz-text-overflow: ellipsis;
          -ms-text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          text-overflow: ellipsis;
        }
      }
      .lable {
        color: #828591;
        font-size: 12px;
        margin-bottom: 10px;
      }
      .text {
        color: #828591;
        font-size: 12px;
        margin-bottom: 10px;
      }
      .price {
        color: #ff5c00;
        font-size: 14px;
        font-weight: bold;
        display: flex;
        line-height: 24px;
        b {
          display: inline-block;
          font-size: 18px;
          line-height: 18px;
        }
      }
      .shop {
        position: relative;
        margin-top: 20px;
        color: #505465;
        font-size: 12px;
        a {
          color: #505465;
          font-size: 12px;
        }
        i {
          display: inline-block;
          vertical-align: middle;
          &.shopicon {
            width: 12px;
            height: 12px;
            margin-top: -3px;
            background-image: url(../../../assets/imgs/pcIndex/Icon_shop.png);
          }
          &.fan {
            width: 3px;
            height: 6px;
            margin-top: -2px;
            background-image: url(../../../assets/imgs/pcIndex/fan.png);
          }
        }
      }
    }
    .joinshop {
      width: 64px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #ffffff;
      font-size: 12px;
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: rgba(33, 46, 67, 0.8);
      display: none;
      a {
        display: block;
        color: #ffffff;
        font-size: 12px;
      }
    }
    .operationBox {
      background-color: #fafafb;
      box-shadow: 0px 8px 16px 0px rgba(2, 12, 38, 0.06);
      padding: 15px 18px;
      width: 196px;
      display: none;
      position: absolute;
      left: 0px;
      z-index: 10;
      em {
        font-style: normal;
        color: #828591;
        font-size: 12px;
      }
      .opera {
        position: relative;
        margin-top: 10px;
        .add_on {
          float: left;
          width: 90px;
          height: 32px;
          line-height: 32px;
          overflow: hidden;
          background-color: #f2f2f2;
          border: 1px solid #e6e7ea;
          border-radius: 3px;
          span {
            float: left;
            cursor: pointer;
            &.jian {
              border-right: 1px solid #e6e7ea;
              font-size: 16px;
              text-align: center;
              width: 24px;
            }
            &.jia {
              border-left: 1px solid #e6e6e6;
              font-size: 16px;
              text-align: center;
              width: 24px;
            }
            &.num {
              width: 40px;
              text-align: center;
              font-size: 12px;
              color: #b4b9c1;
            }
          }
        }
      }
      .operabtn {
        width: 92px;
        height: 32px;
        float: right;
        a {
          display: block;
          width: 92px;
          height: 32px;
          background-color: #0056e5;
          color: #ffffff;
          font-size: 12px;
          line-height: 32px;
          text-align: center;
          &.Login {
            display: block;
          }
          &.addcart {
            display: none;
          }
          &.improve {
            display: none;
          }
        }
      }
    }
  }
  .goodNva {
    position: relative;
    height: 52px;
    line-height: 52px;
    color: #373d50;
    font-size: 12px;
    a {
      color: #373d50;
      font-size: 12px;
      padding: 0px 5px;
      &:first-child {
        padding-left: 0px;
      }
      &:last-child {
        font-weight: bold;
      }
      &:hover {
        color: #0056e5;
      }
    }
    span {
      float: right;
      color: #505465;
      font-size: 12px;
      em {
        font-style: normal;
        color: #0056e5;
      }
    }
    .choiceclass {
      display: none;
      position: relative;
      div {
        display: inline-block;
        height: 24px;
        line-height: 24px;
        border: 1px dashed #ecedf0;
        background-color: #ffffff;
        padding: 0px 5px;
        color: #37455b;
        cursor: pointer;
        margin-right: 5px;
        b {
          font-weight: normal;
          color: #0056e5;
        }
        i {
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          width: 8px;
          height: 8px;
          background-image: url(../../../assets/imgs/pcIndex/Icon-clean_n.png);
        }
        &:hover {
          border: 1px solid #0056e5;
          i {
            background-image: url(../../../assets/imgs/pcIndex/Icon-clean_s.png);
          }
        }
      }
    }
  }
  .goodclassmain {
    position: relative;
    background-color: #ffffff;
    .gooditem {
      position: relative;
      padding: 20px 20px 0px;
      border-bottom: 1px dashed #ecedf0;
      &:last-child {
        border: none;
      }
      .gdname {
        width: 70px;
        position: relative;
        color: #808592;
        font-size: 12px;
        line-height: 24px;
      }
      .gdmain {
        width: 965px;
        height: 44px;
        overflow: hidden;
        position: relative;
        li {
          float: left;
          color: #1e2d46;
          font-size: 12px;
          margin-bottom: 20px;
          margin-right: 40px;
          line-height: 24px;
          cursor: pointer;
          i {
            width: 14px;
            height: 14px;
            display: none;
            vertical-align: middle;
            margin-right: 10px;
            margin-top: -3px;
            background-image: url(../../../assets/imgs/pcIndex/Control_checkbox_n.png);
          }
          &:hover {
            color: #0056e5;
            i {
              background-image: url(../../../assets/imgs/pcIndex/control_checkbox_ss.png);
            }
          }
          &.on {
            color: #0056e5;
            i {
              background-image: url(../../../assets/imgs/pcIndex/control_checkbox_ss.png);
            }
          }
        }
      }
      .gdbtn {
        position: relative;
        width: 123px;
        a {
          width: 52px;
          height: 24px;
          line-height: 24px;
          text-indent: 10px;
          color: #1e2d46;
          font-size: 12px;
          border: 1px solid #e6e7ea;
          background-color: #ffffff;
          margin-left: 7px;
          float: left;
        }
        i {
          width: 8px;
          height: 8px;
          display: inline-block;
          margin-left: 5px;
        }
        .choice {
          display: block;
          background-image: url(../../../assets/imgs/pcIndex/Multiple_choice_n.png);
          background-position: right 6px bottom 8px;
          background-repeat: no-repeat;
          background-size: 8px 8px;
          &:hover {
            border: 1px solid #0056e5;
            color: #0056e5;
            background-image: url(../../../assets/imgs/pcIndex/Multiple_choice_h.png);
          }
          &.on {
            background-image: url(../../../assets/imgs/pcIndex/Multiple_choice_h.png);
          }
        }
        .determine {
          display: none;
          &:hover {
            background-color: #0056e5;
            color: #ffffff;
            border: 1px solid #0056e5;
          }
        }
        .cancel {
          display: none;
          &:hover {
            background-color: #0056e5;
            color: #ffffff;
            border: 1px solid #0056e5;
          }
        }
        .open {
          display: block;
          background-image: url(../../../assets/imgs/pcIndex/open_n.png);
          background-position: right 6px bottom 8px;
          background-repeat: no-repeat;
          background-size: 8px 8px;
          i {
            background-image: url(../../../assets/imgs/pcIndex/open_n.png);
          }
          &:hover {
            border: 1px solid #0056e5;
            color: #0056e5;
            background-image: url(../../../assets/imgs/pcIndex/open_h.png);
          }
          &.on {
            background-image: url(../../../assets/imgs/pcIndex/Putit_away_n.png);
            &:hover {
              background-image: url(../../../assets/imgs/pcIndex/Putit_away_h.png);
            }
          }
        }
        .stop {
          display: none;
          i {
            background-image: url(../../../assets/imgs/pcIndex/Putit_away_n.png);
          }
          &:hover {
            border: 1px solid #0056e5;
            color: #0056e5;
          }
        }
      }
    }
  }
  .goodcontainer {
    position: relative;
  }
  .goodscreen {
    position: relative;
    padding: 10px;
    height: 28px;
    background-color: #ffffff;
    margin: 20px 0px 10px;
    .gdvolume {
      position: relative;
      width: 180px;
      float: left;
      border-top: 1px solid #e6e7ea;
      border-left: 1px solid #e6e7ea;
      border-bottom: 1px solid #e6e7ea;
      background-color: #ffffff;
      button {
        display: inline-block;
        width: 56px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        color: #1e2439;
        font-size: 12px;
        border: none;
        background: none;
        padding: 0px;
        margin: 0px;
        text-indent: 0px;
        cursor: pointer;
        border-right: 1px solid #e6e7ea;
        &:hover {
          color: #0056e5;
        }
        &.on {
          background-color: #0056e5;
          color: #ffffff;
        }
      }
      .price {
        text-indent: 10px;
        text-align: left;
        i {
          width: 12px;
          height: 12px;
          display: block;
          position: absolute;
          right: 5px;
          top: 7px;
          background-size: cover;
          background-image: url(../../../assets/imgs/pcIndex/xl.png);
        }
      }
    }
    .entry {
      float: left;
      width: 56px;
      height: 24px;
      line-height: 24px;
      color: #051632;
      margin-left: 10px;
      font-size: 12px;
      padding-left: 10px;
      border: 1px solid #e6e7ea;
      float: left;
      width: 56px;
      height: 24px;
      line-height: 24px;
      color: #051632;
      margin-left: 10px;
      font-size: 12px;
      padding-left: 10px;
      border: 1px solid #e6e7ea;
      input {
        width: 60%;
        height: 24px;
        border: none;
        background: none;
        width: 60%;
        height: 22px;
        border: none;
        background: none;
      }
    }
    .zhi {
      float: left;
      color: #697384;
      font-size: 12px;
      margin-left: 10px;
      line-height: 24px;
    }
    .quantity {
      float: left;
      color: #37455b;
      font-size: 12px;
      margin-left: 10px;
      input {
        width: 56px;
        height: 22px;
        margin: 0px 10px;
        border: 1px solid #e6e7ea;
      }
    }
    .Justlook {
      color: #37455b;
      font-size: 12px;
      width: 100px;
      height: 24px;
      line-height: 27px;
      float: left;
      margin-left: 10px;
      i {
        width: 14px;
        height: 14px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
        margin-top: -3px;
        cursor: pointer;
        background-image: url(../../../assets/imgs/pcIndex/Control_checkbox_n.png);
        &.on {
          background-image: url(../../../assets/imgs/pcIndex/control_checkbox_ss.png);
        }
      }
    }
    .pagingbtn {
      position: relative;
      float: right;
      a {
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        color: #b4b9c1;
        font-size: 14px;
        border: 1px solid #e6e7ea;
        background-color: #f2f2f2;
        vertical-align: middle;
        &:hover {
          background-color: #ffffff;
          color: #37455b;
        }
      }
      span {
        color: #1e2439;
        font-size: 12px;
        display: inline-block;
        line-height: 26px;
        vertical-align: middle;
      }
      .pagenumber {
        color: #0056e5;
      }
    }
    .gdview {
      float: right;
      button {
        color: #828591;
        font-size: 12px;
        background: none;
        border: none;
        width: 54px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
        &.on {
          color: #0056e5;
          i {
            &.listicon {
              background-image: url(../../../assets/imgs/pcIndex/picture_s.png);
            }
            &.gridicon {
              background-image: url(../../../assets/imgs/pcIndex/list_s.png);
            }
          }
        }
        &:hover {
          color: #0056e5;
          i {
            &.listicon {
              background-image: url(../../../assets/imgs/pcIndex/picture_s.png);
            }
            &.gridicon {
              background-image: url(../../../assets/imgs/pcIndex/list_s.png);
            }
          }
        }
      }
      i {
        width: 10px;
        height: 10px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        margin-top: -3px;
        &.listicon {
          background-image: url(../../../assets/imgs/pcIndex/picture_n.png);
        }
        &.gridicon {
          background-image: url(../../../assets/imgs/pcIndex/list_n.png);
        }
      }
    }
  }
  .datequallist {
    position: relative;
    margin-top: 10px;
    li {
      width: 32%;
      float: left;
      margin-right: 15px;
      margin-bottom: 15px;
      background-color: #ffffff;
      border: 1px solid #ecedf0;
      &:nth-child(3n+ 3) {
        margin-right: 0px;
      }
    }
    .qualimg {
      width: 370px;
      height: 246px;
      margin: 10px;
      position: relative;
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
      .watermark {
        width: 124px;
        height: 48px;
        background-image: url(../../../assets/imgs/pcIndex/watermark.png);
        position: absolute;
        top: 83px;
        left: 81px;
        z-index: 10;
      }
    }
    .qualtext {
      position: relative;
      text-align: center;
      background-color: #fafafb;
      padding: 15px 0px;
      b {
        display: block;
        color: #1e2439;
        font-size: 14px;
        font-weight: normal;
      }
      p {
        color: #828591;
        font-size: 12px;
        margin-top: 10px;
      }
    }
  }

  .telbg {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 300;
    background-color: rgba(30, 36, 57, 0.4);
    display: none;
  }
  .TelephonePopup {
    width: 340px;
    background-color: #ffffff;
    position: fixed;
    top: 40%;
    left: 40%;
    padding: 32px 0px;
    z-index: 310;
    display: none;
    b {
      display: block;
      text-align: center;
      color: #1e2439;
      font-size: 16px;
    }
    p {
      text-align: center;
      color: #828591;
      font-size: 12px;
      margin: 10px 0px 30px;
    }
    .telclose {
      display: block;
      margin: 0 auto;
      width: 92px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #ffffff;
      background-color: #0056e5;
      font-size: 14px;
    }
  }
}
