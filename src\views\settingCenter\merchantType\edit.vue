<template>
  <el-dialog
    :title="(row.id > 0 ? '编辑' : '新增') + '企业类型'"
    :visible.sync="visible"
    :show-close="true"
    :before-close="clearFun"
    :width="'900px'"
    :close-on-click-modal="false"
    destroy-on-close
    top="10vh"
  >
    <div class="merchantTypeEditContent">
      <el-form class="form" :model="query" ref="ruleForm" label-width="110px">
        <el-form-item
          class="formItem"
          prop="name"
          label="企业类型名称:"
          :rules="[
            { required: true, message: '请填写企业类型名称', trigger: 'blur' },
          ]"
        >
          <el-input
            clearable
            style="width: 250px"
            v-model="query.name"
            placeholder="请填写企业类型名称"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item
        class="formItem"
        label="资质类型:"
      >
        <el-select style="width:250px" v-model="type" placeholder="" @change="getLicenseBaseTypes()">
          <el-option key="PRODUCT" value="PRODUCT" label="产品"></el-option>
          <el-option key="BUYER"  value="BUYER" label="采购员"></el-option>
          <el-option key="MERCHANT"  value="MERCHANT" label="经销商"></el-option>
        </el-select>
         
      </el-form-item> -->

        <!-- <el-form-item
          class="formItem"
          prop="licenseBaseIds"
          label="企业资质文件:"
        >
          <el-checkbox-group v-model="query.licenseBaseIds">
            <p v-for="(item, index) in licenseBaseCheckBox" :key="index">
              <div style="width: 150px;">
                <el-checkbox :label="item.id" name="licenseName">{{
                  item.name
                }}</el-checkbox>
              </div>
              <el-radio v-model="item.isRequired" label="1">必填</el-radio>
              <el-radio v-model="item.isRequired" label="2">不必填</el-radio>
            </p>
          </el-checkbox-group>
        </el-form-item> -->
        <!-- <div class="formItemZZ">企业资质文件:</div> -->
        <el-form-item
          class="formItem"
          prop="licenseBaseDetails"
          label="企业资质文件:"
        >
          <el-table :data="licenseBaseClone" border>
            <el-table-column prop="name" label="资质文件"></el-table-column>
            <el-table-column
              prop="isShow"
              label="是否显示"
              width="150"
              :key="tableKey.isShow"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch
                  size="mini"
                  v-model="scope.row.isShow"
                  @change="(e) => onChange(scope.row, 'isShow', e)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="是否必填" align="center">
              <el-table-column
                prop="idIsRequired"
                label="证件号"
                width="120"
                align="center"
                :key="tableKey.idIsRequired"
              >
                <template slot-scope="scope">
                  <el-switch
                    size="mini"
                    v-model="scope.row.idIsRequired"
                    @change="(e) => onChange(scope.row, 'idIsRequired', e)"
                  >
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column
                prop="expireIsRequired"
                label="证件过期时间"
                align="center"
                :key="tableKey.expireIsRequired"
              >
                <template slot-scope="scope">
                  <el-switch
                    size="mini"
                    v-model="scope.row.expireIsRequired"
                    @change="(e) => onChange(scope.row, 'expireIsRequired', e)"
                  >
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column
                prop="photoIsRequired"
                label="附件"
                align="center"
                :key="tableKey.photoIsRequired"
              >
                <template slot-scope="scope">
                  <el-switch
                    size="mini"
                    v-model="scope.row.photoIsRequired"
                    @change="(e) => onChange(scope.row, 'photoIsRequired', e)"
                  >
                  </el-switch>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="clearFun()">取 消</el-button>
      <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getLicenseBaseByType, editApi } from "@/api/setting/merchantType";
import { cloneDeep } from "lodash-es";
export default {
  data() {
    return {
      visible: false,
      type: "BUYER",
      licenseBaseCheckBox: null,
      licenseBaseClone: null,
      query: {},
      row: {},
      tableKey: {
        isShow: 0,
        idIsRequired: 1,
        expireIsRequired: 2,
        photoIsRequired: 3,
      },
    };
  },
  props: {
    // row: {
    //   type: Object,
    // },
    // visible: {
    //   type: Boolean,
    //   default: false,
    //   required: true,
    // },
    isReload: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
  methods: {
    initQuery() {
      return {
        id: 0,
        isDelete: "",
        licenseBaseDetails: [],
        name: "",
      };
    },
    clearFun: function () {
      // this.$emit("update:visible", false);
      this.visible = false;
      this.$emit("update:row", {});
    },
    async submitFun(ruleForm) {
      this.query.licenseBaseDetails = this.licenseBaseClone.filter((item) => {
        if (item.isShow) {
          return {
            licenseBaseId: item.id,
            idIsRequired: item.idIsRequired ? "Y" : "N",
            photoIsRequired: item.photoIsRequired ? "Y" : "N",
            expireIsRequired: item.expireIsRequired ? "Y" : "N",
          };
        }
      });
      var _this = this;
      _this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          var data = await editApi(this.query);
          if (data.code == 0) {
            // _this.$emit("update:visible", false);
            this.visible = false;
            _this.$emit("update:isReload", true);
          }
        } else {
          return false;
        }
      });
    },
    async getLicenseBaseTypes() {
      var { data } = await getLicenseBaseByType(this.type);
      this.licenseBaseCheckBox = data;
      this.licenseBaseClone = cloneDeep(this.licenseBaseCheckBox);
    },

    showEdit(row) {
      this.query = this.initQuery();
      this.row = { id: 0 };
      this.licenseBaseClone = cloneDeep(this.licenseBaseCheckBox);
      // this.query = Object.assign(this.query, row);
      if (row) {
        this.row = row;
        this.query = cloneDeep(row);
        if (this.query.id != 0) {
          this.query.isDelete = this.query.isDelete.code;
        }
        this.licenseBaseClone.forEach((item) => {
          let temp = this.query.licenseBases.find((it) => it.id === item.id);
          item.isShow = Boolean(temp);
          item.idIsRequired = Boolean(temp?.idIsRequired.code == "Y");
          item.photoIsRequired = Boolean(temp?.photoIsRequired.code == "Y");
          item.expireIsRequired = Boolean(temp?.expireIsRequired.code == "Y");
        });
      }
      this.visible = true;
    },

    onChange(row, prop, val) {
      this.tableKey[prop] = Math.random();
    },
  },
  created() {
    this.getLicenseBaseTypes();
  },
  mounted() {
    // this.getLicenseBaseTypes();
    // this.query = Object.assign(this.query, this.row);
    // if (this.query.id != 0) {
    //   this.query.isDelete = this.query.isDelete.code;
    // }
    // if (
    //   this.query.licenseBases != undefined &&
    //   this.query.licenseBases.length != 0
    // ) {
    //   var licenseBaseIds = [];
    //   this.query.licenseBases.forEach(function (item) {
    //     if (item != null) {
    //       licenseBaseIds.push(item.id);
    //     }
    //   });
    //   this.query.licenseBaseIds = licenseBaseIds;
    // }
  },
};
</script>
<style lang="less" scoped>
.merchantTypeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
  // .form .el-form-item {
  //   margin-bottom: 12px;
  // }
}
.formItemZZ {
  width: 110px;
  line-height: 36px;
  text-align: right;
  padding-right: 12px;
  margin-bottom: 12px;
}
</style>
