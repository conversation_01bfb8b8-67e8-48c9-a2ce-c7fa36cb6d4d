// 控销设置字典
export const getWhetherRewardData = () => {
  return [
    { label: '参与裂变奖励', value: 'Y' },
    { label: '不参与裂变奖励', value: 'N' }
  ]
}
export const getWhetherRewardLabel = (value) => {
  const data = getWhetherRewardData()
  const item = data.find(item => item.value === value)
  if (item) return item.label
  return data[1].label
}

// 积分抵扣
export const getPointDeductionData = () => {
  return [
    { label: '参与积分抵扣', value: 'Y' },
    { label: '不参与积分抵扣', value: 'N' }
  ]
}
export const getPointDeductionLabel = (value) => {
  const data = getPointDeductionData()
  const item = data.find(item => item.value === value)
  if (item) return item.label
  return data[1].label
}
