<template>
  <!-- 商品列表 -->
  <div>
    <div class="tab_wrapper">
      <tabs-layout ref="tabs-layout" :tabs="tabs" />
    </div>
    <div class="point_product" style="position: relative;top: -16px;">
      <!-- 分组管理 -->
      <div class="reward_wrapper" style="width: 200px;">
        <div class="reward_list">
          <div
            :class="['reward_item', currentReward === item.value ? 'activated' : '']"
            v-for="item in rewardList"
            :key="item.value"
            @click="changeCurrentReward(item.value)"
          >
            <span class="name">{{ item.label }}</span>
          </div>
        </div>
      </div>
      <div class="product_list">
        <Fission v-if="currentReward === 'FISSION'" />
        <Duty v-if="currentReward === 'DUTY'" />
        <Once v-if="currentReward === 'ONCE'" />
        <Team v-if="currentReward === 'TEAM'" />
        <Year v-if="currentReward === 'YEAR'" />
      </div>
    </div>
  </div>
</template>
<script>
import Fission from './components/fission.vue';
import Duty from './components/duty.vue';
import Once from './components/once.vue';
import Team from './components/team.vue';
import Year from './components/year.vue';

export default {
  name: 'rewardSettlement',
  components: {
    Fission,
    Duty,
    Once,
    Team,
    Year
  },
  data() {
    return {
      tabs: [{ name: "奖励结算", value: 0 }],
      currentReward: '',
      rewardList: [
        { label: '裂变奖励', value: 'FISSION' },
        { label: '业务员职责奖励', value: 'DUTY' },
        { label: '管理佣金奖励', value: 'TEAM' },
        { label: '一次性奖励', value: 'ONCE' },
        { label: '年度奖励', value: 'YEAR' },
      ],
    }
  },
  created() {
    const initCurrentReward = this.rewardList[0].value
    this.changeCurrentReward(initCurrentReward)
  },
  methods: {
    changeCurrentReward(val) {
      if (val === this.currentReward) return
      this.currentReward = val
    },
  }
}
</script>

<style lang="scss" scoped>
.point_product {
  display: flex;
  .reward_wrapper {
    background-color: #fff;
    min-width: 150px;
    max-width: 150px;
    box-sizing: border-box;
    padding: 10px;
    .reward_list {
      background-color: #fff;
      .reward_item {
        display: flex;
        height: 35px;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        cursor: pointer;
        &.activated {
          background-color: #ddd !important;
        }
        &:hover {
          background-color: #eee;
        }
      }
    }
  }
  .product_list {
    flex: 1;
    box-sizing: border-box;
    padding: 10px;
    min-width: 0;
  }
}
</style>
