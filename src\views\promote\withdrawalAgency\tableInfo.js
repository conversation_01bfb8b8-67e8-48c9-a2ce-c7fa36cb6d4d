export default {
  PENDING: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '170px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '手机号',
      name: "contactNumber",
      width: '170px',
      disabled: true
    },
    // {
    //   key: 3,
    //   label: '撤销原因',
    //   name: "applyReason",
    //   width: '230px'
    // },
    {
      key: 4,
      label: '主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 5,
      label: '商品编码',
      name: "productCode",
      width: '168px'
    },
    {
      key: 6,
      label: '商品名称',
      name: "productName",
      width: '200px'
    },
    {
      key: 7,
      label: '规格',
      name: "spec",
      width: '170px'
    },
    {
      key: 8,
      label: '生产厂家',
      name: "manufacturer",
      width: '150px'
    },
    {
      key: 9,
      label: '所在区域',
      name: "area",
      width: '200px'
    },
    {
      key: 10,
      label: '推广费',
      name: "promotionExpenses",
      width: '170px'
    }
  ],
  REJECTED: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '170px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '手机号',
      name: "contactNumber",
      width: '170px',
      disabled: true
    },
    // {
    //   key: 3,
    //   label: '撤销原因',
    //   name: "applyReason",
    //   width: '200px'
    // },
    {
      key: 11,
      label: '驳回原因',
      name: "createTime",
      width: '200px'
    },
    {
      key: 4,
      label: '主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 5,
      label: '商品编码',
      name: "productCode",
      width: '168px'
    },
    {
      key: 6,
      label: '商品名称',
      name: "productName",
      width: '150px'
    },
    {
      key: 7,
      label: '规格',
      name: "spec",
      width: '170px'
    },
    {
      key: 8,
      label: '生产厂家',
      name: "manufacturer",
      width: '150px'
    },
    {
      key: 9,
      label: '所在区域',
      name: "area",
      width: '200px'
    },
    {
      key: 10,
      label: '推广费',
      name: "promotionExpenses",
      width: '170px'
    }
  ],
  REPEAL: [
    {
      key: 0,
      label: '业务员编码',
      name: "salesmanCode",
      width: '170px',
      disabled: true
    },
    {
      key: 1,
      label: '业务员姓名',
      name: "realName",
      width: '100px',
      disabled: true
    },
    {
      key: 2,
      label: '手机号',
      name: "contactNumber",
      width: '170px',
      disabled: true
    },
    {
      key: 3,
      label: '撤销原因',
      name: "applyReason",
      width: '200px'
    },
    {
      key: 4,
      label: '主图',
      name: "pictIdS",
      width: '80px'
    },
    {
      key: 5,
      label: '商品编码',
      name: "productCode",
      width: '168px'
    },
    {
      key: 6,
      label: '商品名称',
      name: "productName",
      width: '150px'
    },
    {
      key: 7,
      label: '规格',
      name: "spec",
      width: '170px'
    },
    {
      key: 8,
      label: '生产厂家',
      name: "manufacturer",
      width: '150px'
    },
    {
      key: 9,
      label: '所在区域',
      name: "area",
      width: '200px'
    },
    {
      key: 10,
      label: '推广费',
      name: "promotionExpenses",
      width: '170px'
    }
  ]
}