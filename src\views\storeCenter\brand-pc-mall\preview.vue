<template>
  <div class="main">
    <div class="content-title">
      <div class="lt">商城装修</div>
      <div class="rt">
        <el-button  @click="() => { $router.push('/fitment/pcmall/index') }">返回</el-button>
      </div>
    </div>
    <!-- 搜索、导航栏 -->
    <Search :preview="false" :page="page" />
    <!-- 头部轮播 -->
    <div class="header-container">
      <!-- 中间部分 -->
      <div class="nav-body">
        <!-- 中间部分 -->
        <div class="nav-content">
          <!-- 轮播图 -->
          <div class="nav-carousel">
            <el-carousel :interval="5000" arrow="always" height="370px">
              <el-carousel-item v-for="(item, index) in page.pageDataAdVoMap.PC_ADV_CAR" :key="index">
                <img v-if="page.pageDataAdVoMap.PC_ADV_CAR.length" style="width: 100%;height: 100%" :src="item.picUrl" alt @click="bindUrl(item.linkUrl)">
              </el-carousel-item>
            </el-carousel>
            <div class="nav-show edit-posi">
              <img :src="page.pageDataAdVoMap.PC_ADV_FIXED[0].picUrl" alt class="nav-show-left">
              <img :src="page.pageDataAdVoMap.PC_ADV_FIXED[1].picUrl" alt class="nav-show-right">
            </div>
          </div>
          <!-- 个人中心 -->
          <div v-if="false" class="nav-person">
            <div class="nav-person-img2">
              <div>
                <p>Hi，{{ buyersVo.buyersNm }}</p>
                <p v-if="userDetail.memberDetailsVo.isCredit === 'Y'" class="last">
                  可用额度：¥{{ userDetail.memberDetailsVo.availableCredit || 0 }}</p>
              </div>
            </div>
            <div v-if="userDetail.orderStateVos" class="nav-person-list">
              <div class="list-button" @click="jumpOrder('WAIT_PAY')">
                <p class="num">{{ userDetail.orderStateVos.toBePaid || 0 }}</p>
                <p class="text">待付款</p>
              </div>
              <div class="list-button" @click="jumpOrder('WAIT_SEND')">
                <p class="num">{{ userDetail.orderStateVos.toBeDelivered || 0 }}</p>
                <p class="text">待发货</p>
              </div>
              <div class="list-button" @click="jumpOrder('SEND')">
                <p class="num">{{ userDetail.orderStateVos.toBeReceived || 0 }}</p>
                <p class="text">待收货</p>
              </div>
              <div class="list-button" @click="jumpOrder('WAIT_APPROVE')">
                <p class="num">{{ userDetail.orderStateVos.toBConfirmed || 0 }}</p>
                <p class="text">待确认</p>
              </div>
            </div>
          </div>
          <div v-else class="nav-person">
            <div class="nav-person-img">
              <img src="../../../assets/img/index/user_purchaser.png">
              <p>Hi~欢迎来到基药云商城</p>
            </div>
            <div class="nav-person-button">
              <router-link to="">
                <el-button  round>登录</el-button>
              </router-link>
              <router-link to="" class="last">
                <el-button  round>注册</el-button>
              </router-link>
            </div>
          </div>
          <!-- 公告栏 -->
          <div class="nav-notice">
            <div class="notice-list quick">
              <div class="quick-title">快捷入口</div>
              <div class="quick-content">
                <div class="quick-list">
                  <img src="../../../assets/img/index/my_btn_payment.png" class="icon">
                  <p>待付款</p>
                </div>
                <div class="quick-list">
                  <img src="../../../assets/img/index/my_btn_shipments.png" class="icon">
                  <p>待发货</p>
                </div>
                <div class="quick-list">
                  <img src="../../../assets/img/index/my_btn_receipt.png" class="icon">
                  <p>待收货</p>
                </div>
              </div>
            </div>
            <div class="notice-list notice">
              <el-tabs v-model="activeNotice">
                <el-tab-pane label="商城公告" name="first">
                  <ul>
                    <li> · 111国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                  </ul>
                </el-tab-pane>
                <el-tab-pane label="健康资讯" name="second">
                  <ul>
                    <li> · 2222国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                  </ul>
                </el-tab-pane>
                <el-tab-pane label="医药财经" name="third">
                  <ul>
                    <li> · 3333国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                    <li> · 国家药品监督管理局关于印发中...</li>
                  </ul>
                </el-tab-pane>
              </el-tabs>
            </div>
            <div class="notice-list qual">
              <div class="qual-list">
                <img src="../../../assets/img/index/Icon_guarantee.png" class="icon">
                <p>正品保障</p>
              </div>
              <div class="qual-list">
                <img src="../../../assets/img/index/Icon_qualifications.png" class="icon">
                <p>资质完备</p>
              </div>
              <div class="qual-list">
                <img src="../../../assets/img/index/Icon_complete.png" class="icon">
                <p>药品齐全</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 内容 -->
    <div id="content" class="content">
      <!-- 折扣 -->
      <div class="offer aaa scroll-item">
        <div class="commodity">
          <!-- 头部 -->
          <div class="commodity-head">
            <div class="commodity-text">
              <div>
                <span class="commodity-title">优惠专区</span>
                <span class="commodity-remarks">优惠商品低价购</span>
              </div>
            </div>
          </div>
          <!-- 内容 -->
          <div class="commodity-content">
            <div class="discount">
              <div class="discount-lt edit-posi">
                <img :src="page.pageDataAdVoMap.PC_ADV_DISCOUNT_LT[0].picUrl" alt>
                <img :src="page.pageDataAdVoMap.PC_ADV_DISCOUNT_LT[1].picUrl" alt>
              </div>
              <div class="discount-rt edit-posi">
                <div class="discount-rt-content">
                  <div class="discount-list" v-for="(item, index) in page.pageDataAdVoMap.PC_ADV_DISCOUNT_RT" :key="index">
                    <div class="discount-title" v-if="index === 0">助力抗疫<span>抗疫精选</span></div>
                    <div class="discount-title" v-if="index === 1">效期促销<span>享惊喜折扣</span></div>
                    <div class="discount-title" v-if="index === 2">夏日防暑<span>家庭保护必备</span></div>
                    <div class="discount-title" v-if="index === 3">保健器械<span>享惊喜折扣</span></div>
                    <div class="discount-content">
                      <img :src="item.picUrl" alt>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- 精选推荐 -->
      <div class="commodity">
        <!-- 头部 -->
        <div class="commodity-head">
          <div class="commodity-text">
            <div>
              <span class="commodity-title">精选推荐</span>
              <span class="commodity-remarks">精选好物超值购</span>
            </div>
          </div>
        </div>
        <!-- 内容 -->
        <div class="commodity-content">
          <div class="reco-title">
            <div v-for="(item, index) in page.pageDataProductGroupVoList" :key="index" class="title-list" :class="{ 'active': groupIndex === index }">
              <p @click="groupSelect(index)">{{ item.name }}</p>
              <span>{{ item.name1 }}</span>
            </div>
          </div>
          <div class="reco-content">
            <div class="reco-list" v-for="(item, index) in page.pageDataProductGroupVoList[groupIndex].pageDataProductGroupRelVoList" :key="index">
              <img class="reco-logo" :src="item.product.pictIdS">
              <div class="name">{{ item.product.productName }}</div>
              <div class="spec">{{ item.product.spec }}</div>
              <div class="spec">{{ item.product.manufacturer }}</div>
              <div class="price">
                <span>￥</span>0.00
                <!--                <p>价格登录可见</p>-->
              </div>
              <div class="shop">
                <img class="icon-shop" src="../../../assets/img/index/icon_shop.png" alt>
                <span>{{ item.product.shopName }}</span>
                <img class="icon-arrow" src="../../../assets/img/index/icon-arrow-right-black.png" alt>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer :preview="false" :page="page" />
    </div>
  </div>
</template>

<script>
import Search from './components/Search'
import Footer from './components/Footer'
import {getPageData, copyrightPage, listByParentId} from './components'
export default {
  name: 'Index',
  components: {
    Search,
    Footer
  },
  data() {
    return {
      page: {
        pageDataAdVoMap: {
          PC_LOGO: [],
          PC_APPLETS: [],
          PC_ADV_CAR: [],
          PC_ADV_FIXED: [],
          PC_ADV_DISCOUNT_LT: [],
          PC_ADV_DISCOUNT_RT: [],
          PC_MALL_NAVIGATION: [],
          PC_SERVICE_CENTER: []
        },
        pageDataHotWordVoList: [],
        pageDataProductGroupVoList: [],
        content: '',
        LINK_LIST: []
      },
      pageDataFooter: {
        content: '',
        id: '',
        PC_SERVICE_CENTER: []
      },
      activeNotice: 'first',
      activeName: '',
      activeType: '',
      groupIndex: 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getPageData().then(res => {
        if (res.code == 0) {
          this.page = res.data
        }
      }).then(() => {
        copyrightPage().then(res => {
          console.log(res.data.content)
          this.page.content = res.data.content
          this.pageDataFooter.content = res.data.content
          this.pageDataFooter.id = res.data.id
          this.pageDataFooter.PC_SERVICE_CENTER = this.page.pageDataAdVoMap.PC_SERVICE_CENTER
          console.log(this.page.content)
        })
      })
    },
    bindUrl(path) {
      if (path.substr(0, 1) === '/') {
        this.$router.push({ path })
      } else {
        window.open(path, '_blank')
      }
    },
    groupSelect(index) {
      if (index !== this.groupIndex) {

      }
      this.groupIndex = index
    },
    confirm() {
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/home.scss'
</style>

<style lang="less">
.edit-posi{
  position: relative;
  cursor: pointer;
  .edit-pop{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(3,10,33,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 14px;
    z-index: 6;
  }
}
.nav-notice {
  .notice-list{
    &.notice{
      .el-tabs__item{
        padding: 0;
        height: 12px;
        line-height: 12px;
        border: 0;
      }
      .el-tabs__nav-wrap::after{
        height: 0;
      }
      .el-tabs__active-bar{
        height: 0;
      }
      .el-tabs__nav{
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
      .el-tabs__header{
        margin-bottom: 4px;
      }
    }
  }
}
.header-container {
  .nav-body {
    .nav-content {
      .nav-carousel {
        .el-carousel__button{
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }
        .el-carousel__indicator.is-active button{
          width: 30px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
