import { login, logout, getInfo, getUserResourceVisible } from '@/api/user'
import { getToken, setToken, removeToken, setRefreshToken, getRefreshToken, removeRefreshToken, getExpire, setExpire, removeExpire } from '@/utils/auth'
import { setLocalUser, removeLocalUser } from '@/utils/local-user'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  activeList: [],
  visibleObj: [],
  merchantsDetail: {}, // 商家信息
  refreshToken: getRefreshToken(), // 刷新token
  expire: getExpire(), // 刷新token过期时间
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_VISIBLE: (state, visibleObj) => {
    state.visibleObj = visibleObj
  },
  MERCHANTS_DETAIL: (state, data) => {
    state.merchantsDetail = data;
  },
  SET_REFRESH_TOKEN: (state, refreshToken) => {
    state.refreshToken = refreshToken
  },
  SET_EXPIRE: (state, expire) => {
    state.expire = expire
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password, key } = userInfo
    return new Promise((resolve, reject) => {
      login({
        grantType: 'password',
        account: username.trim(),
        password,
        key
      }).then(response => {
        const { data } = response
        if (data.extend?.isChangePassword === 'Y') {
          commit('SET_TOKEN', data.token)
          setToken(data.token)
          commit('SET_INTRODUCTION', data.workDescribe)
          setLocalUser(data)
          commit('SET_REFRESH_TOKEN', data.refreshToken)
          setRefreshToken(data.refreshToken)
          commit('SET_EXPIRE', new Date().getTime() + data.expire * 1000)
          setExpire(new Date().getTime() + data.expire * 1000)
        }
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  reloadToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 使用refreshToken而不是token来刷新
      const refreshTokenValue = state.refreshToken || getRefreshToken()
      if (!refreshTokenValue) {
        reject(new Error('No refresh token available'))
        return
      }

      login({
        grantType: "refresh_token",
        refreshToken: refreshTokenValue.replace("Bearer ", ""),
      }).then(async response => {
        if (response.code == 0) {
          const { data } = response
          if (data.extend?.isChangePassword === 'Y') {
            const { token, userId, name, avatar, refreshToken, expire } = data
            commit('SET_TOKEN', data.token)
            setToken(data.token)
            commit('SET_INTRODUCTION', data.workDescribe)
            setLocalUser(data)
            commit('SET_REFRESH_TOKEN', refreshToken)
            setRefreshToken(refreshToken)
            if (expire) {
              commit('SET_EXPIRE', new Date().getTime() + expire * 1000)
              setExpire(new Date().getTime() + expire * 1000)
            }
          } else {
            await this.$store.dispatch('user/logout')
            this.$router.push(`/login`)
          }
          resolve(response)
        } else {
          reject(new Error('Token refresh failed: ' + response.msg))
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response

        if (!data) {
          reject('Verification failed, please Login again.')
        }

        data.roles = ['admin']
        localStorage.setItem('userId', data.id)
        commit('SET_ROLES', data.roles)
        commit('SET_NAME', data.name)
        commit('SET_AVATAR', data.avatar)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getVisibleObj() {
    getUserResourceVisible().then(response => {
      const { data } = response
      commit('SET_ROLES', data)
      resolve(data)
    }).catch(error => {
      reject(error)
    })
  },
  getRoleS({ commit, state }) {
    //state.userId
    return new Promise((resolve, reject) => {
      getUserResourceVisible().then(response => {
        const { data } = response
        commit('SET_ROLES', data)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_REFRESH_TOKEN', '')
        commit('SET_EXPIRE', '')
        removeToken()
        removeRefreshToken()
        removeExpire()
        resetRouter()
        removeLocalUser()

        commit('MERCHANTS_DETAIL', {});
        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })
        dispatch('permission/resetRoutes', [], { root: true })
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_REFRESH_TOKEN', '')
      commit('SET_EXPIRE', '')
      removeRefreshToken()
      removeExpire()
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  },
  // 商家信息
  merchantsDetail({ commit }, data) {
    commit('MERCHANTS_DETAIL', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
