<!--
 * @: 团购活动已经取消使用这个组件
-->
<template>
  <el-dialog class="product-modal" title="选择商品" :visible.sync="visible">
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reset"
      @search="load"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model="model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="whetherUseCoupon">
        <el-select v-model="model.whetherUseCoupon" placeholder="是否可用券">
          <el-option label="是" value="Y" />
          <el-option label="否" value="N" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>

    <div class="product-wrap">
      <div v-loading="loading">
        <el-table ref="multipleTable" :data="list" border :row-key="getRowKeys" max-height="580px" @selection-change="handleSelectionChange" @select-all="handleSelectAll">
          <el-table-column label="序号" type="index" width="80" align="center" :reserve-selection="true" />
          <el-table-column type="selection" align="center" :reserve-selection="true" fixed />
          <el-table-column label="商品编码" prop="productCode" width="170" />
          <el-table-column label="产品主图" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.pictIdS" :src="splitString(scope.row.pictIdS)" width="50px" height="50px">
              <img v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''" :src="pictImg" width="50px" height="50px">
            </template>
          </el-table-column>
          <el-table-column label="商品名称" prop="productName" width="250px" show-overflow-tooltip />
          <el-table-column label="规格" prop="spec" width="150px" show-overflow-tooltip />
          <el-table-column label="生产厂家" prop="manufacturer" width="250px" show-overflow-tooltip />
          <el-table-column label="是否可退" prop="whetherReturnable" width="100">
            <template slot-scope="{row}">
              {{ row.whetherReturnable.desc }}
            </template>
          </el-table-column>
          <el-table-column label="是否可用劵" prop="whetherUseCoupon" width="100">
            <template slot-scope="{row}">
              {{ row.whetherUseCoupon.desc }}
            </template>
          </el-table-column>
          <el-table-column label="销售价" prop="salePrice" width="100" />
          <el-table-column label="成本价" prop="costPrice" width="100" />
          <el-table-column label="库存" prop="stockQuantity" />

        </el-table>

        <el-pagination
          style="margin-bottom: 16px;margin-top: 15px;"
          background
          :total="total"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          layout="->, prev, pager, next, sizes, jumper"
          @current-change="sizeChange()"
          @size-change="sizeChange()"
        />
      </div>

      <div slot="footer" class="table-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :disabled="submitDisabled" @click="ok">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import productImg from '../../../assets/product.png'
import { searchRepeatProduct } from '@/api/limitTime'
import {
  searchRepeatProductGroup
  } from '@/api/promotionCenter/index'

export default {
  components: {
  },
  props: ['checkList', 'limit', 'isActivity', 'during', 'notSearchId','activeType'],
  data() {
    return {
      // 获取row的key值
      getRowKeys(row) {
        return row.id
      },
      model: {
        productCode: '',
        productName: '',
        whetherUseCoupon: '',
        whetherOnSale:'Y',
        // whetherReturnable: '',
        ids: []
      },
      submitDisabled:true,
      visible: false,
      list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      loading: false,
      selection: [],
      isSelect: false,
      pictImg: productImg,
      selectionMapObject: {}, // 已加载被选中的全部选项
      timer: null
    }
  },
  watch:{
    selection(val){
      val.forEach(selected=>{
        // 只更改本业选中和未选中的数据
        if(this.list.find(item=>selected == item.id)) {
          this.selectionMapObject[selected] = true;
        } else {
          this.selectionMapObject[selected] = false;
        }
      });

      Object.keys(this.selectionMapObject).forEach(i=>{
        if(val.find(item => item == i)) {
          this.selectionMapObject[i] = true;
        } else {
          this.selectionMapObject[i] = false;
        }
      })
    }
  },
  methods: {
    reset() {
      this.model = {
        productCode: '',
        productName: '',
        whetherUseCoupon: '',
        whetherOnSale: 'Y',
        ids: []
      }
      this.load()
    },
    splitString(val) {
      return val.split(',')[0]
    },
    sizeChange() {
      this.isSelect = false
      this.load()
    },
    close() {
      this.visible = false
    },
    async ok() {
      let paramsList = [];
      this.checkList.forEach(item=>{
        if(this.selectionMapObject[item.id] == undefined || this.selectionMapObject[item.id]) {
          if(!paramsList.includes(item.id)) {
            paramsList.push(item.id);
          }
        }
      });

      this.selection.forEach(i=>{
        if(!paramsList.includes(i)) {
          paramsList.push(i);
        }
      })

      let timeSelect = true;
      try {
        this.during.forEach(item=>{
          if(item=='' || item == null){
            timeSelect = false;
          }
        })
      } catch (error) {
        timeSelect = true;
      }
      if (this.isActivity) {
        // if (this.during === '' ) {
        if (!timeSelect) {
          this.$message.warning('请先设置活动时间！')
          this.close()
        } else {
          const param = {
            productIds: [paramsList.join(',')],
            endTime: this.during[0],
            startTime: this.during[1],
            notSearchId: this.notSearchId
          }
          if(this.activeType=='groupBuy'){
            let query = {
              'productIds[]':paramsList.toString(),
              'endTime': this.during[0],
              'startTime': this.during[1],
            };
            let data = await searchRepeatProductGroup(query);
            if (data.code === 0) {
              this.$emit('change', paramsList, this.list)
              this.close()
            }
            if (data.code == -9) {
              this.$alert('同一活动时间内，' + data.msg + ',请修改商品设置', '活动冲突提醒', {
                confirmButtonText: '知道了'
              })
            }
          } else {
            let data = await searchRepeatProduct(param);
            if (data.code === 0) {
              this.$emit('change', paramsList, this.list)
              this.close()
            }
            if (data.code == -9) {
              this.$alert('同一活动时间内，' + data.msg + ',请修改商品设置', '活动冲突提醒', {
                confirmButtonText: '知道了'
              })
            }
          }
        }
      } else {
        this.$emit('change', paramsList, this.list)
        this.close()
      }
    },

    open() {
      this.visible = true
      this.page = 1
      this.load()
    },

    async load() {
      this.loading = true
      const { data } = await request.post('product/admin/product/page', {
        current: this.page,
        map: {},
        model: this.model,
        order: 'descending',
        size: this.pageSize,
        sort: 'id'
      })
      this.loading = false

      data.records.forEach((items) => {
        const arr = String(items.pictIdS).split(',')
        if (arr.length > 1) {
          items.pictIdS = arr[0]
        }
      })

      this.total = data.total
      this.list = data.records
      this.checkTrue()
    },

    checkTrue() {
      if (this.checkList && this.checkList.length > 0) {
        this.timer = setTimeout(() => {
          if (this.isSelect) {
            this.$refs.multipleTable.clearSelection()
          }
          const ids = this.checkList.map(items => items.id)
          const arr = this.list.filter(item => ids.includes(item.id))
          arr.forEach(row => {
            if (row) {
              this.$refs.multipleTable.toggleRowSelection(row, true)
            } else {
              this.$refs.multipleTable.clearSelection()
            }
          })
        })
      }
    },
    handleSelectAll(val) {
      if (this.limit && val.length > this.limit) {
        this.$refs.multipleTable.clearSelection()
        this.$nextTick(() => {
          val.slice(0, this.limit).forEach(row => {
            this.$refs.multipleTable.toggleRowSelection(row)
          })
          this.$message.warning('最多只能选择' + this.limit + '个商品！')
          return false
        })
      };
      if(this.selection.length>0){
        this.submitDisabled = false;
      } else {
        this.submitDisabled = true;
      }
    },
    handleSelectionChange(val) {
      if (this.limit && val.length > this.limit) {
        if (this.selection.length === this.limit) {
          const del_row = val.pop()
          this.$refs.multipleTable.toggleRowSelection(del_row, false)
          this.$message.warning('最多只能选择' + this.limit + '个商品！')
          return false
        }
      } else {
        this.selection = val.map(items => items.id)
      }
      if(this.selection.length>0){
        this.submitDisabled = false;
      } else {
        this.submitDisabled = true;
      }
    }
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  }
}
</script>

<style lang="scss" scoped>
  .product-modal .search-wrapper {
    padding: 0;
    border-left: none;
  }
  .table-footer{
    border-top: 1px solid #EBECEE;
    text-align: right;
    padding-top: 16px;
  }
</style>
