<template>
  <div id="customerPoolManage">
    <div class="poolManage">
      <div class="search_box">
        <el-input
          v-model.trim="input"
          placeholder="请输入客户池名称"
          style="width: 200px"
          clearable
          @keydown.enter.native="getData"
          @blur="getData"
          @clear="getData"
        ></el-input>
        <el-button type="primary" @click="getData">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
      <div class="main_box">
        <div class="main_content" v-if="!loading && poolList.length">
          <div
            class="card_main"
            v-for="(item, index) in poolList"
            :key="index"
            @click="onDetail(item)"
          >
            <p>{{ item.name }}</p>
            <p>客户数：{{ item.poolNum }}</p>
            <div class="tag_box">
              <div>
                <el-tag
                  type="success"
                  size="mini"
                  v-if="item.whetherSelfTaken.code === 'Y'"
                  >允许自取</el-tag
                >
                <el-tag
                  type="success"
                  size="mini"
                  v-if="item.whetherAutomaticRecycling.code === 'Y'"
                  >允许回收</el-tag
                >
              </div>
              <el-switch
                size="mini"
                v-model="item.whetherEnable.code"
                active-value="Y"
                inactive-value="N"
                @click.native.stop="onChange(item)"
              >
              </el-switch>
            </div>
            <i
              class="m-icon el-icon-edit-outline"
              @click.stop="onEdit(item)"
            ></i>
          </div>
        </div>
        <div class="main_content_loading" v-if="loading">
          <i class="el-icon-loading"></i>
        </div>
        <div class="main_content_empty" v-if="!loading && !poolList.length">
          <span>暂无数据</span>
        </div>
        <pagination
          v-show="paginationSet.total > 0"
          :total="paginationSet.total"
          :page.sync="paginationSet.current"
          :limit.sync="paginationSet.size"
          @pagination="getData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {
  getMerchantPoolList,
  merchantPoolEdit,
} from "@/api/merchantApi/customerPool.js";
export default {
  name: "customerPoolManage",
  components: {
    Pagination,
  },
  props: {},
  data() {
    return {
      input: "",
      paginationSet: {
        total: 1,
        current: 1,
        size: 10,
      },
      poolList: [],
      loading: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getData();
  },
  mounted() {},
  methods: {
    async getData() {
      try {
        let temp = {
          ...this.paginationSet,
          model: {
            name: this.input ?? "",
          },
        };
        this.loading = true;
        let res = (await getMerchantPoolList(temp)) ?? {};
        this.poolList = res?.data?.records ?? [];
        this.paginationSet.total = res?.data?.total ?? 0;
        this.paginationSet.current = res?.data?.current ?? 1;
        this.loading = false;
      } catch (err) {
        this.loading = false;
        this.poolList = [];
        console.log(err);
      }
    },
    reset() {
      this.input = "";
      this.getData();
    },
    onDetail(item) {
      this.$router.push({
        path: "customerPoolManage/detail",
        query: { id: item.id },
      });
    },
    onEdit(item) {
      this.$router.push({
        path: "customerPoolManage/edit",
        query: { id: item.id },
      });
    },
    async onChange(item) {
      merchantPoolEdit(item)
        .then((res) => {
          this.$message.success("操作成功！");
          this.getData();
        })
        .catch((err) => {
          this.getData();
          console.log(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.poolManage {
  height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  > div {
    background: #fff;
    padding: 16px;
  }
  > div + div {
    margin-top: 20px;
  }
}
.search_box {
  height: 68px;
  border-left: 4px solid #0056e5;
  display: flex;
  align-items: center;
  > .el-input + .el-button {
    margin-left: 10px;
  }
}
.main_box {
  flex: 1;
  display: flex;
  flex-direction: column;
  .main_content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  .main_content_loading,
  .main_content_empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    opacity: 0.8;
    .el-icon-loading {
      font-size: 38px;
      opacity: 0.2;
    }
  }
  .pagination-container {
    justify-content: right;
  }
  .card_main {
    width: 220px;
    height: 150px;
    border-radius: 10px;
    border: 1px solid gray;
    margin-right: 24px;
    margin-bottom: 24px;
    padding: 10px;
    position: relative;
    > p:first-child {
      font-weight: bold;
      font-size: 16px;
    }
    > p + p {
      margin-top: 10px;
    }
    .tag_box {
      width: calc(100% - 20px);
      position: absolute;
      bottom: 10px;
      left: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-tag + .el-tag {
        margin-left: 10px;
      }
      > .el-switch {
        margin-left: auto;
      }
    }
    .m-icon {
      font-size: 24px;
      position: absolute;
      top: 8px;
      right: 10px;
      cursor: pointer;
    }
  }
}
</style>
