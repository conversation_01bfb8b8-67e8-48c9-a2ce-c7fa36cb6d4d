<template>
  <div class="client-tags-panel">
    <im-search-pad  :has-expand="false"  :model="model" @reset="resetDialog" @search="submitDialog">
      <im-search-pad-item prop="">
        <el-input v-model.trim="keyword" placeholder="请输入客户标签" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="dialog-content">
      <el-tree
        :props="props"
        :data="renderedTreeList"
        node-key="customerTagId"
        empty-text="这里空空如也"
        show-checkbox
        :default-checked-keys="defaultCheckedKeys"
        @check-change="handleCheckChange">
      </el-tree>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary"  @click="handleConfirm">确 定</el-button>
    </div>
  </div>
</template>
<script>
import delElButton from "@/components/eyaolink/delElButton/index.vue";

export default {
  components: {delElButton},
  props: {
    treeListInfo: {
      type: Array,
      default:[]
    },
    dynamicTags: {
      type: Array,
      default:[]
    },
  },
  data() {
    return {
      model: {
        tagName: ''
      },
      props: {
        label: 'tagName',
      },
      defaultCheckedKeys: [], //默认勾选的树节点
      count: 1,
      renderedTreeList:this.treeListInfo,
      keyword:'',
      userSelectListInfo:  [...this.dynamicTags] || []
    };
  },
  created() {
  },
  mounted() {
    if(this.dynamicTags && this.dynamicTags.length > 0){
     this.defaultCheckedKeys =   this.dynamicTags.map(item => item.customerTagId) || []
    }

    console.log('加载组件')
  },
  watch: {
    // keyword(newValue, oldValue) {
    //   if(newValue === ''){
    //     this.renderedTreeList =  this.treeListInfo
    //   }
    // }
  },
  computed: {},
  methods: {
    // 节点选中状态发生变化时的回调
    handleCheckChange(data, checked, indeterminate) {
      if (checked) {
        // 当 checked 为 true，检查是否已经存在数据，存在则不 push
        const exists = this.userSelectListInfo.some(item => item.customerTagId === data.customerTagId);
        if (!exists) {
          this.userSelectListInfo.push(data);
        }
      } else {
        // 当 checked 为 false，从数组中移除该数据
        this.userSelectListInfo = this.userSelectListInfo.filter(item => item.customerTagId !== data.customerTagId);
      }
      console.log(data, checked, indeterminate);
    },
    // 重置弹窗搜索词
    resetDialog(){
      this.keyword = ''
      this.renderedTreeList =  this.treeListInfo
    },
    // 确定搜索
    submitDialog(){
      this.renderedTreeList =  this.treeListInfo.filter(item => item.tagName.includes(this.keyword)) || []
    },
    // 关闭弹窗
    handleClose(){
      this.$emit('closeTagDialog')
    },
    // 点击确定
    handleConfirm(){
      this.$emit('acceptClientTags',this.userSelectListInfo)
    },
  },

};
</script>

<style scoped lang="scss">
.dialog-content{
  max-height: 50vh;
  white-space: nowrap;
  overflow-y: scroll;
}
.dialog-footer{
  padding: 10px 20px 0px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
</style>
