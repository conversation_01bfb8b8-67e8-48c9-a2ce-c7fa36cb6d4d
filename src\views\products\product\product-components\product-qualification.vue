<template>
  <!-- <el-form :model="form" label-width="80px" label-position="right" :disabled="action==='SHOW'"> -->
  <el-form label-width="80px" label-position="right">
    <div class="product-qualification">
      <form-item-title title="商品资质"/>
      <el-table :data="productLicenseRelSaveDTOList">
        <el-table-column label="证件类型">
          <template slot-scope="scope">
            <div class="multiple-text">{{ formatType(scope.row.real.licenseId) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="证件号">
          <template slot-scope="scope">
            <div v-if="!scope.row.edit">{{ scope.row.real.certificateNumber }}</div>
            <el-input v-else v-model="scope.row.cache.certificateNumber"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="过期时间" width="300">
          <template slot-scope="scope">
            <div v-if="!scope.row.edit">
              {{ scope.row.real.whetherForever === 'Y' ? '长期' :  scope.row.real.licenseEndDate}}
            </div>
            <template v-else>
              <el-checkbox v-model="scope.row.cache.whetherForever" label="长期" border true-label="Y" false-label="N"></el-checkbox>
              <el-date-picker v-model="scope.row.cache.licenseEndDate"
                              type="date"
                              style="width: 160px; margin-left: 10px"
                              format="yyyy-MM-dd"
                              value-format="yyyy-MM-dd"
                              placeholder="选择日期"
                              v-if="scope.row.cache.whetherForever !== 'Y'"
              >
              </el-date-picker>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="200">
          <template slot-scope="scope">
            <el-upload
              :limit="5"
              :file-list="scope.row.cache.accessory"
              v-if="scope.row.edit"
              list-type="picture-card"
              :action="uploadParams.action"
              :headers="uploadParams.headers"
              :data="uploadParams.data"
              :on-success="(file) => { handleUploadSuccess(file, scope.row) }"
              :on-remove="(file, filelist) => { handleRemove(file, filelist, scope.row) }"
              accept=".jpg,.png,.bmp,.jpeg">
              <i class="el-icon-plus"></i>
            </el-upload>
            <div class="image-wrapper" v-if="!scope.row.edit && scope.row.real.accessory.length">
              <img v-for="(item, idx) of scope.row.real.accessory" :key="idx" :src="item.url" width="40px"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170">
          <template slot-scope="scope">
            <el-button v-if="!scope.row.edit" @click="handleEdit(scope.row)" type="text" :disabled="action==='SHOW'">编辑</el-button>
            <el-button v-if="scope.row.edit" @click="handleCancel(scope.row)" type="text"  style="color: #7f7f7f;">取消</el-button>
            <el-button v-if="scope.row.edit" @click="handleConfirm(scope.row)" type="text" >确定</el-button>
            <!-- <el-button v-if="!scope.row.edit && scope.row.real.accessory.length" type="text"  @click="previewImages=scope.row.real.accessory">预览</el-button> -->
            <el-button v-if="!scope.row.edit && scope.row.real.accessory.length" type="text"  @click="handlePreview(scope.row.real.accessory,scope.row.real.licenseId)">预览</el-button>
            <el-button v-if="!scope.row.edit && scope.row.real.accessory.length" type="text"  @click="onDownload(scope.row.real.accessory)">下载</el-button>
            <el-image :ref="`ref${scope.row.real.licenseId}`" class="showImgBtn" :src="bigPreview[0]"  :preview-src-list="bigPreview"></el-image>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        title="预览"
        :visible.sync="dialogCode"
        @close='closeDialog'

        width="40%">
        <div class="preview-images">
          <el-image v-for="image in previewImages" :key="image.url" :preview-src-list="bigPreview"  :src="image.url" style="width: 120px; height: 120px; margin-right: 10px;"/>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogCode = false">取消</el-button>
          <el-button type="primary" @click="onDownload(previewImages)">下载</el-button>
        </div>
      </el-dialog>
      <!-- <el-image ref="imagePreview" v-for="image in previewImages" :key="image.url" :preview-src-list="bigPreview"  :src="image.url" style="width: 120px; height: 120px;"/> -->
    </div>
  </el-form>
</template>

<script>
import { cloneDeep as _cloneDeep } from 'lodash'
import { getToken } from '@/utils/auth'
import { getLocalUser } from '@/utils/local-user'
import formItemTitle from '@/views/products/common-components/form-item-title'
import request from '@/utils/request'

export default {
  components: { formItemTitle },
  props: {
    action: {
      type: String
    },
    isNumber2: {
      type: Function
    },
    isInt7: {
      type: Function
    }
  },
  data () {
    return {
      dialogCode:false,
      previewImages: [],
      bigPreview:[],
      typeArray: [],
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + '/file/file/upload',
        headers: {
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
          token: `Bearer ${getToken()}`
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0
        }
      },
      editMap: {}, // 编辑的数据
    }
  },
  computed: {
    productLicenseRelSaveDTOList () {
      return this.typeArray.map(row => {
        let editMap = this.editMap || {};
        // 查找看，是否已经有
        let found = editMap[row.id] || {};
        let data = {
          licenseId: row.id,
          certificateNumber: found.certificateNumber || '',
          licenseEndDate: found.licenseEndDate || '',
          accessory: [],
          licenseType: found.licenseType ||'PRODUCT_LICENSE',
          productId: found.productId || 0,
          whetherForever: found.whetherForever || 'N',
          reminderDateType: found.reminderDateType || 'A_MONTH_AGO',
          reminderDate: found.reminderDate,
          saleMerchantId: getLocalUser().saleMerchantId,
        };
        if (found.id) {
          data.id = found.id;
        }
        if (found.fileIds) {
          data.accessory = ((found.fileIds || '').split(',').map(item => {return {url: item}}));
          console.log('data.accessory----->',data.accessory);
        }
        return {
          real: data,
          cache: _cloneDeep(data),
          edit: false
        }
      });
    },
  },
  async created () {
    const res = await request('merchant/admin/licenseBase/getAll')
    this.typeArray = res.data.filter(item => {
      return item.type.code === 'PRODUCT'
    })
  },
  methods: {
    closeDialog() {
      this.dialogCode = false
    },
    formatType (val) {
      if (val) {
        let find =  this.typeArray.find(item => item.id === val);
        if (find) {
          return find.name;
        }
      }
      return ''
    },
    handleEdit (item) {
      item.edit = true
      item.cache = _cloneDeep(item.real)
    },
    handleCancel (item) {
      item.cache = { licenseId: '', certificateNumber: '', licenseEndDate: '', accessory: [] }
      item.edit = false
    },
    handleConfirm (item) {
      item.edit = false
      item.real = _cloneDeep(item.cache)
    },
    handleUploadSuccess (file, item) {
      item.cache.accessory.push({
        name: file.data.name,
        url: file.data.url,
        id: file.data.id
      })
    },
    handleRemove (file, filelist, item) {
      item.cache.accessory = filelist
    },

    handlePreview(list=[],id=0){
      this.bigPreview = list.map(item=>{
        return item.url;
      });
      // this.dialogCode=true
      this.previewImages = list;
      this.$refs[`ref${id}`].showViewer = true;
    },

    getData () {
      let list = [];
      this.productLicenseRelSaveDTOList.forEach(item => {
        const data = item.real;
        // 如果都为空，则不传
        if (!data.certificateNumber
          && (!data.fileIds || !data.fileIds.length)
          && (data.whetherForever !== 'Y' && !data.licenseEndDate) // 过期日期
        ) {
          return;
        }
        let ret =  {
          certificateNumber: data.certificateNumber,
          fileIds: data.accessory.map(img => img.url).join(','),
          licenseEndDate: data.licenseEndDate,
          licenseId : data.licenseId,
          licenseType: data.licenseType ||'PRODUCT_LICENSE',
          productId: data.productId || 0,
          whetherForever: data.whetherForever || 'N',
          reminderDateType: data.reminderDateType || 'A_MONTH_AGO',
          reminderDate: data.licenseEndDate,
          saleMerchantId: getLocalUser().saleMerchantId,
        }
        if (data.id) {
          ret.id = data.id;
        }
        list.push(ret);
      });

      return {
        productLicenseRelSaveDTOList: list
      }
    },
    setForm (data) {
      let map = {};
      (data.productLicenseRelVoList || []).forEach(data => {
        map[data.licenseId] = data;
      })
      this.editMap = map;
    },
    onDownload(images) {
      images.forEach(item => {
        window.open(item.url, '_blank');
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.product-qualification{
  .el-table{

    ::v-deep{
      .el-upload-list{
        .el-upload-list__item{
          width: 40px;
          height: 40px;
        }
      }
      .el-table__body-wrapper{
        .el-table__append-wrapper{
          text-align: center;
        }
      }
      .el-upload{
        width: 40px;
        height: 40px;
        position: relative;
        >i{
          font-size: 16px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translateX(-50%) translateY(-50%);
        }
      }
    }
    .image-wrapper{
      >img{
        margin-right: 10px;
      }
    }
  }
}
.showImgBtn {
  width: 0;
  height: 0;
}
</style>
