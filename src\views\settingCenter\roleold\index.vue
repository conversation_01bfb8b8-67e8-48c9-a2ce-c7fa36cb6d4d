<template>
  <div>
    <div class="tab_bg">
      <el-tabs v-model="activeName">
        <el-tab-pane label="角色管理" name="first"></el-tab-pane>
      </el-tabs>
      <div class="tab_btns">
        <el-button @click="load">刷新</el-button>
        <el-button type="primary" @click="addOrEdit(1,'')">+ 新增角色</el-button>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <div slot-scope="props">
          <el-link @click="addOrEdit(2,props.row)" style="margin-right: 15px;">编辑</el-link>
          <el-link @click="del(props.row.id)">删除</el-link>
        </div>

      </table-pager>
    </div>
  </div>
</template>

<script>
  import {roleList,delRole} from '@/api/settingCenter'

  const TableColumns = [
    { label: "员工角色", prop: "name"},
    { label: "备注", prop: "describe" },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }

  export { TableColumnList };

  export default {
    name: "DragTable",
    data() {
      return {
        showSelectTitle: false,
        tableTitle: TableColumnList,
        tableData: [],
        tableVal: [],
        list: [],
        total: 0,
        page: 0,
        listLoading: false,
        sortable: null,
        activeName: 'first',
        options: [],
        listQuery: {
          model: {}
        }
      };
    },
    created() {
    },
    methods: {
      del(id) {
        this.$confirm('是否确定删除？').then(_ => {
          delRole([id]).then(res=>{
            this.$message.success('删除成功！')
            this.handleRefresh({
              page: 1,
              pageSize: 10
            })
          })

        }).catch(_ => {});

      },
      addOrEdit(type,row) {
        if(type === 1) {
          this.$router.push({path: './edit'})
        } else {
          this.$router.push({path: './edit',query: {id: row.id}})
        }
      },
      onSubmit() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
        this.load()

      },

      async load(params) {
        this.listLoading = true
        Object.assign(this.listQuery, params)
        return await roleList(this.listQuery)
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
      handleSelectionChange(val) {
        this.ids = val.map(function(item,index) {
          return item.id;
        })

      }
    },
  };
</script>

<style>
  .sortable-ghost {
    opacity: 0.8;
    color: #fff !important;
    background: #42b983 !important;
  }
</style>

<style scoped>
</style>
