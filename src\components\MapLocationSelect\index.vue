<template>
  <div class="map-container">
    <div class="btn-groups"><span class="tips">Tips: 可通过搜索或者鼠标移动地图，找到合适的位置点击地图即可获取坐标</span><span class="close-map" @click="closeMap">关闭地图</span></div>
    <el-row style="height: 30vh">
      <el-amap-search-box
        class="search-box"
        :search-option="searchOption"
        :on-search-result="onSearchResult"
      >
      </el-amap-search-box>
      <el-amap
        vid="locationSelect"
        :center="mapCenter"
        :zoom="zoom"
        class="map"
        :events="events">
      </el-amap>
    </el-row>
  </div>
</template>

<script>
  import VueAMap from 'vue-amap'
  import Vue from 'vue'
  Vue.use(VueAMap);
  VueAMap.initAMapApiLoader({
    key: '87d01c5bfa270bbe9111ff3f88faac06',
    plugin: ['AMap.Map','AMap.Geolocation', 'AMap.MouseTool','AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.Geocoder'],  // 默认高德 sdk 版本为 1.4.4
    v: '1.4.4'
  })
  let geoCoder = null
  export default {
    name: 'MapLocationSelect',
    props: {
      center: {
        type: Array,
        default: () => [113.347676, 23.104896]
      }
    },
    data () {
      let self = this;
      return {
        zoom: 18,
        mapCenter: [113.347676, 23.104896],
        address: '', // 地址
        lng: 0, // 维度
        lat: 0, // 经度
        searchOption: {
          city: '',
          citylimit: false
        },
        events: {
          click(e) {
            let { lng, lat } = e.lnglat
            self.lng = lng
            self.lat = lat
            self.getAddress(lng, lat, 'click')
          }
        }
      }
    },
    watch: {
      center(val) {
        this.mapCenter = val
      }
    },
    mounted() {
      this.initMap()
    },
    methods: {
      initMap() {
        const [lng, lat ] = this.center
        if (lng && lat) {
          this.mapCenter = this.center
        }
      },
      // 搜索
      onSearchResult (pois) {
        const center = this.handlePo(pois, false)
        this.mapCenter = [center.lng, center.lat]
        this.zoom = 20
        this.getAddress(center.lng, center.lat, 'search')
      },
      handlePo(po, isAverage) {
        if (isAverage) {
          let latSum = 0
          let lngSum = 0
          if (pois.length > 0) {
            pois.forEach(poi => {
              const { lng, lat } = poi
              lngSum += lng
              latSum += lat
            })
            return {
              lng: lngSum / pois.length,
              lat: latSum / pois.length
            }
          }
        } else {
          const { lng, lat } = po[0]
          return { lng, lat }
        }
      },
      getAddress(lng ,lat, type) {
        if (!geoCoder) {
          geoCoder = new AMap.Geocoder({
            radius: 1000,
            extensions: 'all'
          })
        }
        geoCoder.getAddress([lng ,lat], (status, result) => {
          if (status === 'complete' && result.info === 'OK') {
            if (result && result.regeocode) {
              this.address = result.regeocode.formattedAddress
              this.$emit(type, {
                lng: lng,
                lat: lat,
                address: this.address
              })
            }
          }
        })
      },
      closeMap() {
        this.$emit('close')
      }
    }
  }
</script>

<style lang="less" scoped>
  @mapHeight: 30vh;
  .map-container {
    height: @mapHeight;
    background-color: red;
    .btn-groups {
      width: 100%;
      height: 40px;
      .tips {
        float: left;
        font-size: 12px;
        line-height: 40px;
        color: rgba(0, 0, 0,.8);
        padding-left: 10px;
      }
      .close-map {
        float: right;
      }
    }
    .search-box {
      position: absolute;
      left: 10px;
      top: 10px;
    }
    .map {
      height: calc(@mapHeight - 40px);
    }
    .close-map {
      margin-right: 10px;
      cursor: pointer;
    }
  }
</style>
