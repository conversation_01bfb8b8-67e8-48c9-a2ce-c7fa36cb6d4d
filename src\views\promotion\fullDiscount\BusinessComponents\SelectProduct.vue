<template>
    <!-- <popup-selection :disabled="disabled" label="商品" :data.sync="list" :request="request" :searchFields="searchFields"
        :selectTableColumns="selectTableColumns" :tableColumns="tableColumns" @ok="handleOk" @change="handleChange">

    </popup-selection> -->
    <div :class="$style.container">
        <div :class="$style.btn" v-if="!disabled">
            <!-- <el-button type="primary" @click="onSelectClick()">选择商品</el-button> -->
            <!-- <slot name="header"></slot> -->
        </div>
        <front-end-page ref="frontEndPage" @openProductModel="onSelectClick" :data.sync="list"
        :options="tableColumns" :pageSize="pageSize" :operation-width="120" :total="totalNum" :operationWidth="100" :currentPage="currentPage" :selection="true"
        @patchDelete="handleDelete" :disableHandle="disabled">
          <el-table-column slot="pictIdS" label="产品主图" align="center" width="80">
            <template v-slot="scope">
              <el-image style="width:40px;height:40px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
            </template>
          </el-table-column>
          <el-table-column slot="erpCode" width="180" label="ERP商品编码/仓库">
            <template v-slot="{ row }">
              <span>编码：{{ row.erpCode || '无' }}</span><br />
              <span>仓库：{{ row.warehouseName || '无' }}</span>
            </template>
          </el-table-column>
          <div slot-scope="scope" align="center">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-button type="text"
                  @click="handleDelete([scope.row])">
                  删除</el-button>

              </span>
            </el-row>
          </div>
          <el-button :disabled="disabled" slot="moreHandleBtn" @click="handleImportProduct" v-if="checkPermission(['admin','reductionDetailImportProduct'])">导入商品</el-button>
        </front-end-page>


        <!-- 弹窗选择数据 start -->
        <div v-if="visible">
            <products-modal :isBackIdOrInfo="true" ref="products-modal" :productVisible="visible" @closeProductDia="closeProductDia" :check-list="list" @change="handleOk" />
        </div>
      <importDialog ref="importDialogRef" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="false" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
    </div>
</template>

<script>
import { list } from '@/api/products/product';
import importDialog from "@/components/eyaolink/importDialog/index"
import _ from "lodash";
// import { page } from "@/api/merchantApi/customerlabel";
import PopupSelection from "../LogicalComponents/PopupSelection.vue"
import DelButton from '@/components/eyaolink/delElButton/index.vue'
import ProductsModal from '@/components/eyaolink/productModel/index'
import checkPermission from '@/utils/permission'

const firstColRender = row => `<span>编码：${row.erpCode ? row.erpCode : '无'}</span> <br /><span>仓库：${row.warehouseName ? row.warehouseName : '无' }</span>`;

const selectTableColumns = [
    // { label: '产品主图', name: 'pictIdS', prop: 'pictIdS', slot: true, width: 80 },
    { label: 'ERP商品编码/仓库', name: 'erpCode', prop: 'erpCode', slot: true, width: 160, render: firstColRender },
    { label: '商品名称', name: 'productName', prop: 'productName', width: 200 },
    { label: '规格', name: 'spec', prop: 'spec', width: 50 },
    // { label: '商品标签', name: 'productLabels', prop: 'productLabels', width: 50 },
    { label: '生产厂家', name: 'manufacturer', prop: 'manufacturer', width: 100 },
    { label: '销售价（元）', name: 'salePrice', prop: 'salePrice', width: 110 },
    { label: '成本价（元）', name: 'costPrice', prop: 'costPrice', width: 110 },
    { label: '库存', name: 'stockQuantity', prop: 'stockQuantity', width: 60 }
].map((v, i) => ({ key: i, ...v }))


const tableColumns = [
    { label: 'ERP商品编码/仓库', name: 'erpCode', prop: 'erpCode', slot: true, width: 160, render: firstColRender },
    { label: '商品名称', name: 'productName', prop: 'productName' },
    { label: '生产厂家', name: 'manufacturer', prop: 'manufacturer' },
    { label: '规格', name: 'spec', prop: 'spec' },
    { label: '单位', name: 'unit', prop: 'unit' },
    { label: '销售价（元）', name: 'salePrice', prop: 'salePrice', width: 110 },
    { label: '成本价（元）', name: 'costPrice', prop: 'costPrice', width: 110 },
    { label: '库存', name: 'stockQuantity', prop: 'stockQuantity', width: 100 }
].map((v, i) => ({ key: i, ...v }))
const MODEL = 'UPDATE_MODEL'
export default {
    props: {
        data: {
            type: Array,
            default: () => []
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        },
    },
    inject: {
        elForm: {
            default: ''
        },
        elFormItem: {
            default: ''
        }
    },
    watch: {
        data: {
            handler(data) {
                this.list = data || [];
                this.totalNum = this.list.length;
            },
            immediate: true,
            deep: true
        }
    },
    model: {
        prop: 'data',
        event: MODEL
    },
    data() {
        return {
            list: [],
            selectTableColumns,
            tableColumns,
            request: list,
            visible: false,
            searchFields: [
                {
                    type: 'input',
                    prop: 'keyword',
                    value: '',
                    placeholder: '商品名称/商品编码'
                },
                {
                    type: 'input',
                    prop: 'manufacturer',
                    value: '',
                    placeholder: '生产厂家'
                },
                {
                    type: 'input',
                    prop: 'whetherOnSale',
                    value: 'Y',
                    placeholder: '状态',
                    hidden: true
                }
            ],
            pageSize: 10,
            totalNum: 0,
            currentPage: 1,
            actionUploadUrl: '/api/product/admin/productImport/importProductActivity',
            templateKey: 'IMPORT_PRODUCT_EXCEL_TEMP',
            queryParams:{
              productType: 'IMPORT_PRODUCT_FULL_REDUCE',
              activityId:''
            },
            timer: null
        }
    },
    created() {
      this.$nextTick(()=>{
        this.timer = setTimeout(()=>{
          this.queryParams.activityId = this.$route.query.id || '';
        },1000)
      })
    },
    methods: {
      checkPermission,
        handleChange(data) {
            this.$emit(MODEL, data)
            this.elFormItem && this.elFormItem.$emit('el.form.blur', data)
        },
        handleOk(data) {
            this.$emit(MODEL, data)
        },
        uploadChangeData(list = []) {
          let arr = _.cloneDeep(this.list || []);
          let newList = arr.concat(list);
          this.list = _.uniqBy(newList,'id');

          this.$emit(MODEL, this.list);
        },
        handleImportProduct() {
          this.$refs.importDialogRef.initOpen();
        },
        /**
         * 点击【选择商品】
         */
        onSelectClick() {
            this.visible = true;

        },
        closeProductDia(){
            this.visible = false;
        },
        /**
         * 删除已选择商品
         */
        // handleDelete(id) {
        //     let index = this.list.findIndex(v => v.id === id)
        //     if (index >= 0) {
        //         this.list.splice(index, 1)
        //     }
        // },
        // 删除
        handleDelete(row){
          const arrId = [];
          if(row.length) {
            row.forEach(item => {
              arrId.push(item.id)
            })
          }
	        this.list = this.list.filter(item => !arrId.includes(item.id))
          if (JSON.parse(JSON.stringify(this.$refs.frontEndPage.tableData)).length === 1 && JSON.parse(JSON.stringify(this.list)).length > 1) {
            this.$refs.frontEndPage.page = 1
          }
          this.handleChange(this.list)

          this.total = this.list.length;
        },
    },
    components: {
        PopupSelection,
        DelButton,
        ProductsModal,
        importDialog
    },
    beforeDestroy() {
      clearTimeout(this.timer);
    }
}
</script>

<style lang="scss" module>
.container {
    width: 100%;

    .btn {
        margin-bottom: 10px;
    }

    li {
        width: fit-content;
    }

    .w-full {
        width: 100%;
    }
}

</style>
