<template>
    <el-dialog width="600px" v-bind="$attrs" v-on="$listeners" v-dialogDrag @opened="handleOpen" :close-on-click-modal="false" @close="clearData">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules" class="je-pr30">
		  <el-form-item label="仓库名称" prop="name">
        <el-input placeholder="请输入仓库名称" v-model="form.name" maxlength="10" show-word-limit >
        </el-input>
      </el-form-item>
			<el-form-item label="仓库编码" prop="code" >
        <el-input placeholder="请输入仓库编码" v-model="form.code"  maxlength="10" show-word-limit>
        </el-input>
      </el-form-item>
			<el-form-item label="所在地区" prop="nickName">
        <el-cascader
          placeholder="请选择所在区域" 
          :options="options" 
          v-model="districtCode"
          :props="{ checkStrictly: true,expandTrigger: 'hover',
                    value: 'id',
                    label: 'label',
                    children: 'children'}" 
          clearable
          style="width: 240px;" 
          @change="parentChangeAction">
          </el-cascader>
      </el-form-item>
			<el-form-item label="详细地址" prop="detailedAddress">
        <el-input placeholder="请输入仓库名称" v-model="form.detailedAddress"  maxlength="30" show-word-limit>
        </el-input>
      </el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">关 闭</el-button>
      <el-button type="primary" @click="handlePrimary">提 交</el-button>
    </div>
    </el-dialog>
</template>

<script>
import {  addStoreJoggle, updateStoreJoggle, storDetail } from '@/api/products/store'
import {
    trees,
  } from '@/api/group'
    export default {
      name: 'AddStore',
      props: {
        typeVal: {
          type: String,
          default: () => {
            return 'add'
          }
        },
        id: {
          type: String,
          default: () => {
            return ''
          }
        }
      },
			data() {
				return {
					form: {
            name: '',
            code: '',
            detailedAddress: '',
            provinceId: 0,
            cityId: 0,
            regionId: 0
          },
					rules: {
            name: [{ required: true, message: '仓库名称不能为空', trigger: 'blur'}]
          },
          options: [],
          districtCode:[]
				}
			},
			mounted() {
        this.getArea()
      },
			methods: {
        // 地区
      async getArea() {
        const {
          data
        } = await trees()
        this.options = data
      },
				handleOpen() {
          if(this.typeVal === 'edit' && this.id) {
            this.getDetail(this.id)
          }
        },
				clearData() {
          this.form = {
            ...this.form, 
            name: '',
            code: '',
            detailedAddress: '',
            provinceId: 0,
            cityId: 0,
            regionId: 0
            };
            this.districtCode = []
        },
        getDetail(id) {
          storDetail(id).then(res => {
            const data = res.data;
            this.form = {
              ...this.form,
              id: data.id, 
              code : data.code,
              detailedAddress: data.detailedAddress,
              provinceId: data.provinceId,
              cityId: data.cityId,
              regionId: data.regionId,
              name: data.name
            }
            if(data.location === '全国') {
              this.districtCode = ['0']
            }else {
              this.districtCode = [data.provinceId, data.cityId, data.regionId]
            }
            
          })
        },
				// 提交
				handlePrimary() {
          this.$refs.form.validate(valid => {
              if(valid) {
                if(this.typeVal === 'add') {
                    this.addStore()
                }else {
                    this.editStore()
                }
              }
          })
        },
        parentChangeAction(val){
          if(val && val.length === 3) {
            this.form = {
                    ...this.form, 
                    provinceId: val[0],
                    cityId: val[1],
                    regionId: val[2]
                  }
          }else if(val && val.length === 2) {
            this.form = {
                          ...this.form, 
                          provinceId: val[0],
                          cityId: val[1],
                          regionId: '0'
                        }
          }else if(val && val.length === 1) {
            this.form = {
                          ...this.form, 
                          provinceId: val[0],
                          cityId: '0',
                          regionId: '0'
                        }
          }
        },
        addStore() {
          let query = {...this.form};
          Object.keys(query).forEach((key) => {
            if (query[key] === '') delete query[key]
          })
          addStoreJoggle(query).then(res => {
            if(res.code === 0) {
              this.form = {
              ...this.form, 
              name: '',
              code: '',
              detailedAddress: '',
              provinceId: 0,
              cityId: 0,
              regionId: 0
              };
              this.districtCode = []
              this.$message.success('新增成功！');
              this.$emit('onsuccess')
              this.$emit('update:visible', false)
            }
          })
        },
        editStore() {
          let query = {...this.form};
          Object.keys(query).forEach((key) => {
            if (query[key] === '') delete query[key]
          })
          updateStoreJoggle(query).then(res => {
            if(res.code === 0) {
              this.form = {
                ...this.form, 
                name: '',
                code: '',
                detailedAddress: '',
                provinceId: 0,
                cityId: 0,
                regionId: 0
                };
                this.districtCode = []
                this.$message.success('新增成功！');
                this.$emit('onsuccess')
                this.$emit('update:visible', false)
            }
          })
        }
			}
    }
</script>