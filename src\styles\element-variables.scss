/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #0056E5;
$--color-success: #2DAC0C;
$--color-warning:#FF6E1B;
$--color-danger: #FF2D47;
$--color-info: #909399;

/*Font Color */
$--color-text-primary:#2A3045;
$--color-text-regular:#505465;
$--color-text-secondary:#828591;
$--color-text-placeholder:#CDCED3;


/*Input */
$--input-width: 240px !default;
/// height||Other|4
$--input-height: 32px !default;
$--input-border-radius:2px;
$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

// Border Color
$--border-color-base: #DCDDE0;
$--border-color-light: #DCDDE0;
$--border-color-lighter: #DCDDE0;
$--border-color-extra-light: #DCDDE0;

$--table-border: 1px solid #dfe6ec;
$--table-header-background-color:#f7f7f8;
$--table-row-hover-background-color:#f2f6fd;
$--table-font-color:#505465;


$--button-border-radius:2px;



$--message-info-font-color:#0056E5;
$--message-padding:8px 12px 8px 12px;
$--message-min-width:320px;
/* icon font path, required */
$--font-path: "~element-ui/lib/theme-chalk/fonts";

@import "~element-ui/packages/theme-chalk/src/index";

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
