<template>
  <div class="container">
    <el-form
      :model="exportFrom"
      ref="exportFrom"
      label-width="80px"
      class="demo-dynamic"
    >
      <el-form-item label="输入页数:">
        <el-row>
          <!-- 开始页输入 start -->
          <el-col :span="4">
            <el-input-number
              v-model="exportFrom.startpage"
              class="full"
              :max="maxPage"
              :controls="false"
            >
            </el-input-number>
          </el-col>
          <!-- 开始页输入 end -->
          <el-col class="text-center" :span="1">到</el-col>
          <!-- 结束页输入 start -->
          <el-col :span="4">
            <el-input-number
              v-model="exportFrom.endpage"
              class="full"
              :max="maxPage"
              :controls="false"
            >
            </el-input-number>
          </el-col>
          <!-- 结束页输入 end -->

          <!-- 提示显示 start -->
          <el-col :span="15">
            <div class="text-right">
              已选付款单：<span class="text-red">{{ checkNum }}</span>
              个(单次最多{{ max }}个)
            </div>
          </el-col>
          <!-- 提示显示 end -->
        </el-row>
      </el-form-item>
      <!-- <el-form-item prop="exportFields" label="导出字段: ">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选
        </el-checkbox>
      </el-form-item>
      <el-form-item prop="checkField" label="">
        <el-checkbox-group v-model="checkList" @change="checkListFun">
          <el-checkbox v-for="item in exportFieldList" :key="item.code" :label="item.code" :disabled="item.isdisable"
            style="width:16%;margin-bottom:10px">
            <span>{{item.desc}}</span>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
import { exportField } from "@/api/products/product/index";
export default {
  //import引入的组件
  components: {},
  props: {
    total: {
      // 最大可导出数据量-->列表数据总数量
      type: Number,
      default: 0,
    },
    totalPage: {
      // 列表的页码总数
      type: Number,
      default: 0,
    },
    // 最大导出数据量
    max: {
      type: Number,
      default: 5000,
    },
    // 每页条数
    size: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      exportFrom: {
        startpage: 1,
        endpage: 1,
      },
      isIndeterminate: false,
      checkAll: false,
      exportFieldList: [], // 可导出的商品基本属性
      checkList: [],
    };
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getAllExportField();
  },

  computed: {
    checkNum() {
      let count = 0;
      let maxUsable = this.total - (this.exportFrom.startpage - 1) * this.size; // 计算出当前开始页，最大可导出数量
      // 两数都不等于0，结束页是否大于等于开始页
      if (
        this.exportFrom.endpage &&
        this.exportFrom.startpage &&
        this.exportFrom.endpage >= this.exportFrom.startpage
      ) {
        count = this.exportFrom.endpage + 1 - this.exportFrom.startpage;
      }

      return Math.min(maxUsable, count * this.size);
    },

    // 计算最大页码
    maxPage() {
      return Math.ceil(Math.min(this.total, this.max) / this.size);
    },
  },
  //方法集合
  methods: {
    validate() {
      let result = true;
      let message = "";
      if (this.total <= 0) {
        message = "没有可导出的数据";
        result = false;
      } else if (this.checkNum <= 0) {
        message = "请输入正确的页数";
        result = false;
      }
      if (!result && message) {
        this.$message.warning(message);
      }
      return result;
    },
    getAllExportField() {
      exportField().then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          res.data.forEach((item) => {
            let obj = {
              ...item,
            };
            if (item.code == "ERP_CODE" || item.desc == "erp编码") {
              this.checkList.push("ERP_CODE");
              obj.isdisable = true;
            } else {
              obj.isdisable = false;
            }
            this.exportFieldList.push(obj);
          });
          // this.exportFieldList = res.data || [];
        }
      });
    },
    // 全选按钮
    handleCheckAllChange(val) {
      this.checkList = ["ERP_CODE"];
      this.exportFieldList.forEach((item) => {
        if (val) {
          if (!this.checkList.includes(item.code)) {
            this.checkList.push(item.code);
          }
        } else {
          this.checkList = ["ERP_CODE"];
        }
      });
      this.isIndeterminate = false;
    },
    // 单选框
    checkListFun(e) {
      this.checkAll = e.length === this.exportFieldList.length;
      this.isIndeterminate =
        e.length > 0 && e.length < this.exportFieldList.length;
    },
    getData() {
      let result = {
        // exportFields: [...this.checkList],
        startPage: this.exportFrom.startpage,
        endPage: this.exportFrom.endpage,
      };
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  margin-left: 10px;
  margin-right: 10px;
  &.inline-block {
    display: inline-block;
  }
  .full {
    width: 100%;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .text-center {
    text-align: center;
  }
  .text-right {
    text-align: right;
  }
  .text-red {
    color: red;
  }
}
</style>
