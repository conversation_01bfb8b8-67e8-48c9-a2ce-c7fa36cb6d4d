<template>
	<!-- <el-dialog width="70%" class="componentsList" v-bind="$attrs" v-on="$listeners" @opened="handleOpen" center :close-on-click-modal="false" @close="clearData"
    v-dialogDrag>
		  <div slot="title" class="title"></div>
			<div>
				<tabs-layout
					ref="tabs-layout"
					:tabs="tabs"
					v-model="tabCode"
					@change="handleChangeTab"
				></tabs-layout>
				<im-search-pad
					:has-expand="false"
					:is-expand.sync="isExpand"
					:model="model"
					@reset="reload"
					@search="searchLoad"
				>
					<im-search-pad-item prop="keyword">
						<el-input
							v-model="model.keyword"
							@keyup.enter.native="searchLoad"
							placeholder="请输入客户名称/客户编码"
						/>
					</im-search-pad-item>
					<im-search-pad-item prop="clientType">
						<el-select v-model="model.merchantTyIds" style="width: 176px;" collapse-tags multiple placeholder="请选择客户类型">
							<el-option
								v-for="item in clientArr"
								:key="item.value"
								:label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
					</im-search-pad-item>
          
					<im-search-pad-item prop="districtCode">
						<el-cascader
							placeholder="请选择所在区域" 
							:options="options" 
							v-model="districtCode"
							:props="{ checkStrictly: true,expandTrigger: 'hover',
												value: 'id',
												label: 'label',
												children: 'children'}" 
							clearable
							style="width: 240px;" 
							@change="parentChangeAction">
            </el-cascader>
					</im-search-pad-item>
					
				</im-search-pad>
				<div class="tab_bg">
					<el-table
						ref="multipleTable"
						:row-key="getRowKeys"
						border
						:data="tableData"
						tooltip-effect="dark"
						style="width: 100%"
						@selection-change="handleSelectionChange"
						@select-all="handleSelectAll"
						max-height="400"
						v-loading="loading"
					>
					  <el-table-column type="" align="center" width="65" :render-header="renderHeader" fixed>
							<template slot-scope="scope">
								<span>{{ scope.$index + 1 }}</span>
							</template>
						</el-table-column>
						<el-table-column
							type="selection"
							width="55"
							fixed
							align="center"
							:reserve-selection="true"
						></el-table-column>
						<el-table-column
							prop="code"
							label="客户编码"
							width="120"
						></el-table-column>
						<el-table-column prop="name" label="客户名称" :show-overflow-tooltip='true'></el-table-column>
						<el-table-column
							prop="merchantType"
							label="客户类型"
							width="120"
						></el-table-column>
						<el-table-column
							prop="ceoName"
							label="联系人"
							width="90"
						></el-table-column>
						<el-table-column
							prop="ceoMobile"
							label="联系电话"
							width="130"
						></el-table-column>
						<el-table-column
							prop="region"
							label="所在地区"
							width="250"
						></el-table-column>
					</el-table>
					
					<div class="page-row">
						<el-pagination
							background
							@size-change="handleSizeChange"
							@current-change="handleCurrentChange"
							:current-page="page"
							:page-sizes="[10, 20, 50, 100]"
							:page-size.sync="limit"
							layout="total, sizes, prev, pager, next, jumper"
							:total="totalCount"
						>
						</el-pagination>
					</div>
				</div>
				<div class="bottom_btn">
					<el-button @click="handleCancel">取消</el-button>
					<el-button type="primary" @click="submit"
						>{{ tabCode == "ALREADY" ? "移除" : "添加"
						}}{{
							this.multipleSelection.length === 0
								? ""
								: `(${this.multipleSelection.length})`
						}}</el-button
					>
				</div>
			</div>
</el-dialog> -->
	<clients-model ref="model" @close="handleCancel" :tabs="tabs" @ok="submit" :visible="visible"></clients-model>
</template>

<script>

import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'

import {
	trees,
} from '@/api/group'
import {
	clientTypeList,
	notAssociatedClient,
	hasAssociatedClient,
	statisticalCorrelation,
	deleteAssociatedClient,
	addAssociatedClient
} from "@/api/products/store";

const TableColumns = [
	{
		prop: 'code',
		name: "code",
		label: '客户编码',
		slot: true
	},//新增字段
	{
		prop: 'name',
		name: 'name',
		label: '客户名称',
		width: 80
	},
	{
		prop: 'merchantType',
		name: 'merchantType',
		label: '客户类型',
		slot: true
	},
	{
		prop: 'ceoName',
		name: 'ceoName',
		label: '联系人',
		slot: true
	},
	{
		prop: 'ceoMobile',
		name: 'ceoMobile',
		label: '联系电话',
		slot: true
	},
	{
		prop: 'region',
		name: 'region',
		label: '所在地区',
		slot: true
	}
];

export default {
	name: 'StoreCustomAccount',
	components: { ClientsModel },
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		clientId: {
			type: String,
			default: () => {
				return ''
			}
		}
	},
	data() {
		return {
			options: [],
			// 获取row的key值
			getRowKeys(row) {
				return row.id;
			},
			isExpand: false,
			model: {
				cityId: 0,
				keyword: "",
				provinceId: 0,
				regionId: 0,
				warehouseId: 0,
				merchantTyIds: []
			},
			loading: false,
			tabCode: "ALREADY",
			page: 1,
			limit: 10,
			totalCount: 0,
			tableData: [],
			multipleSelection: [],
			tabNum: {
				associatedCount: 0,
				notAssociatedCount: 0,
			},
			clientArr: [],
			districtCode: [],
			showSelectTitle: false,
			showOptions: TableColumns,
			showOptionsOrigin: TableColumns,
			transferSelectOptions: [],
			transferData: TableColumns,
		}
	},
	watch: {
		visible(v) {
			v && this.tabNumFn()
		}
	},
	computed: {
		tabs() {
			let warehouseId = this.clientId
			return [
				{
					text: `已关联客户(${this.tabNum.associatedCount})`,
					value: "ALREADY",
					request: hasAssociatedClient,
					defaultValues: {
						warehouseId
					},
					button: {
						okText: '移除'
					}
				},
				{
					text: `未关联客户(${this.tabNum.notAssociatedCount})`,
					value: "NOT",
					request: notAssociatedClient,
					defaultValues: {
						warehouseId
					},
					button: {
						okText: '添加'
					}
				},
			];
		},
	},
	async mounted() {
		await this.getArea();
		await this.getClientType()
	},
	methods: {
		/**
		 * 获取客户类型
		 */
		getClientType() {
			clientTypeList().then(res => {
				const data = res.data;
				if (data.length) {
					data.filter(item => {
						const obj = {};
						obj.value = item.id;
						obj.label = item.name;
						this.clientArr.push(obj)
					})
				}
			})
		},
		submit(multipleSelection, index) {
			this.multipleSelection = multipleSelection
			if (this.multipleSelection.length == 0) {
				return;
			}
			let list = this.multipleSelection.map((item) => {
				return item.id;
			});
			list = {
				'ids[]': list,
				warehouseId: this.clientId,
			};
			// if (this.tabCode === "ALREADY") {
			if (index === 0) {
				// 批量移除
				this.$confirm("您确定批量移除这些客户吗?", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				}).then((res) => {
					deleteAssociatedClient({ ...list }).then((data) => {
						if (data.code === 0) {
							this.$refs.model.refresh()
							this.tabNumFn()
							// this.multipleSelection = []
							// this.$refs.multipleTable.clearSelection()
							// this.reload();
						}
					});
				});
			} else {
				// 批量添加
				this.$confirm("您确定批量添加这些客户吗?", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				}).then((res) => {
					addAssociatedClient({ ...list }).then((data) => {
						if (data.code === 0) {
							this.$refs.model.refresh()
							this.tabNumFn()
							// this.multipleSelection = [];
							// this.$refs.multipleTable.clearSelection()
							// this.reload();
						}
					});
				});
			}
		},
		searchLoad() {
			this.handleRefresh();
		},
		reload() {
			this.model = {
				cityId: 0,
				keyword: "",
				provinceId: 0,
				regionId: 0,
				warehouseId: this.clientId,
				merchantTyIds: []
			};
			this.districtCode = []
			this.handleRefresh();
		},
		handleChangeTab(tab, index) {
			this.searchLoad()
		},
		clearData() { },
		parentChangeAction(val) {
			if (val.length) {
				this.model = {
					...this.model,
					provinceId: val[0],
					cityId: val[1],
					regionId: val[2]
				}
			} else {
				this.model = {
					...this.model,
					provinceId: 0,
					cityId: 0,
					regionId: 0
				}
			}
		},
		async getArea() {
			const {
				data
			} = await trees()
			this.options = data
		},
		handleOpen() {
			this.model = { ...this.model, warehouseId: this.clientId, }
			// this.load();
			this.tabNumFn()
		},
		load() {
			this.loading = true;
			const query = { ...this.model }
			Object.keys(query).forEach((key) => {
				if (!query[key]) delete query[key]
			})
			let params = {
				current: this.page,
				map: {},
				model: {
					...query,
				},
				order: "descending",
				size: this.limit,
				sort: "id",
			};
			if (this.tabCode === 'ALREADY') {
				hasAssociatedClient(params).then(res => {
					if (res.code === 0) {
						this.tableData = res.data.records || [];
						this.totalCount = res.data.total;
					}
					this.loading = false;
				})
			} else {
				notAssociatedClient(params).then(res => {
					if (res.code === 0) {
						this.tableData = res.data.records || [];
						this.totalCount = res.data.total;
					}
					this.loading = false;
				})
			}

		},
		handleSelectionChange(val) {
			this.multipleSelection = val;
		},
		handleSelectAll(val) {
			this.multipleSelection = val;
		},
		handleSizeChange(val) {
			this.limit = val;
			this.load();
			this.tabNumFn();
		},
		handleCurrentChange(val) {
			this.page = val;
			this.load();
			this.tabNumFn();
		},
		handleCancel() {
			this.$emit("update:visible", false);
		},
		tabNumFn() {
			const query = { ...this.model, warehouseId: this.clientId }
			Object.keys(query).forEach((key) => {
				if (!query[key]) delete query[key]
			})
			statisticalCorrelation({ ...query }).then(res => {
				if (res.code === 0) {
					this.tabNum = res.data
				}
			})
		},
		handleRefresh() {
			this.page = 1;
			this.load();
			this.tabNumFn();
		},
		// tab切换
		handleChangeTab(tab) {
			this.$refs.multipleTable.clearSelection();
			this.tabCode = tab.value;
			this.handleRefresh();
		},
		renderHeader(h, { column }) {
			var titlesName = ["显示字段项", "隐藏字段项"];
			return (
				<div style="position:relative">
					<div onClick={this.showHeader}>
						<i class="el-icon-menu" />
					</div>
					<el-dialog
						append-to-body={true}
						title="设置显示列表"
						showClose={false}
						visible={this.showSelectTitle}
						width="640px"
						center
					>
						<el-transfer
							vModel={this.transferSelectOptions}
							onChange={this.setLeftTitleFun}
							props={this.transferProps}
							titles={titlesName}
							data={this.transferData}
						></el-transfer>
						<div style="margin-top: 25px;text-align: center;">
							<el-button onClick={this.closeHeader}>取消</el-button>
							<el-button type="primary" onClick={this.setHeader}>
								确认
							</el-button>
						</div>
					</el-dialog>
				</div>
			);
		},
		setLeftTitleFun(val) {
			this.transferSelectOptions = val;
		},
		showHeader() {
			this.showSelectTitle = true;
		},
		closeHeader() {
			this.showSelectTitle = false;
		},
	}
}
</script>

<style lang="scss" scoped>
.page-row {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	color: #505465;
	font-size: 13px;
	margin-top: 16px;
}

.bottom_btn {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-right: 20px;
}

.componentsList {
	::v-deep .el-dialog__headerbtn {
		z-index: 99999;
	}

	::v-deep .el-dialog__header {
		padding: 0 !important;
		border-bottom: none !important;
	}

	::v-deep .el-dialog__body {
		padding-top: 10px !important;
	}

	::v-deep .el-dialog--center .el-dialog__body {
		padding: 10px 0 30px !important
	}

	::v-deep .varietiesBan-list-container .varietiesBan-list-tabs-wrapper .varietiesBan-list-tabs {
		padding-left: 20px;
	}

	.im-search-pad {
		margin-left: 20px;
	}
}
</style>