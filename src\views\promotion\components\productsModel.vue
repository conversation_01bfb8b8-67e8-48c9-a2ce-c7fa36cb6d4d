<!--
 * @: 选择商品弹窗,优惠券，团购活动,显示折扣
-->
<template>
  <div>
    <el-dialog width="75%" v-dialogDrag lock-scroll :close-on-click-modal="false" top="5vh" class="product-modal" title="选择商品" :visible.sync="visible" :before-close="closeDia">
      <im-search-pad :as-expand="false" :hasExpand="false" :model="model" @reset="reset" @search="searchLoad">
        <im-search-pad-item prop="erpCode">
          <el-input v-model.trim="model.erpCode" placeholder="请输入ERP商品编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="productName">
          <el-input v-model.trim="model.productName" placeholder="请输入商品名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="whetherUseCoupon">
            <el-select v-model="model.whetherUseCoupon" placeholder="是否可用券">
              <el-option label="是" value="Y" />
              <el-option label="否" value="N" />
            </el-select>
      </im-search-pad-item>
			<im-search-pad-item prop="warehouseIds" v-if="storeList.length">
        <el-select  v-model="model.warehouseIds" class="width160" collapse-tags multiple placeholder="所在仓库" clearable>
          <el-option v-for="item in storeList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
      </im-search-pad>
      <table-pager :rowKey="rowKey" :reserveSelection="true" ref="pager-table" :height="500" :options="allColumn"
        :remote-method="load" :data.sync="tableData" :selection="true" @selection-change="handleSelectionChange"
        @selection-all="onAllSelect" @selectionChangeHandle="selectionChangeHandle" :pageSize="pageSize" :isNeedButton="false">
        <el-table-column slot="pictIdS" label="产品主图" align="center">
          <template slot-scope="scope">
            <el-image style="width:50px;height:50px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
          </template>
        </el-table-column>
        <el-table-column slot="erpCode" width="210" label="ERP商品编码/仓库">
          <template v-slot="{row}">
            <span>编码：{{ row.erpCode || '无' }}</span> <br />
						<span>仓库：{{ row. warehouseName || '无'}}</span>
          </template>
        </el-table-column>
      </table-pager>
      <div slot="footer" class="table-footer">
        <el-button @click="closeDia">取消</el-button>
        <el-button type="primary" @click="submitCheck">确定{{ selectsProduct.length ? `(${selectsProduct.length})` : '' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import { list } from "@/api/products/product/index"
import {
    getAllStore
  } from "@/api/products/store";
const TableColumns = [
  {  prop: 'pictIdS',  name: 'pictIdS',  label: '产品主图',  slot: true},
  { prop: 'erpCode', name: 'erpCode', label: 'ERP商品编码/仓库', slot: true},
  {  prop: 'productName',  name: 'productName',  label: '商品名称',  width: 200},
  {  prop: 'spec',  name: 'spec',  label: '规格',  width: 100},
  {  prop: 'manufacturer',  name: 'manufacturer',  label: '生产厂家',  width: 200},
  {  prop: 'whetherReturnable.desc',  name: 'whetherReturnable.desc',  label: '是否可退',  width: 100},
  {  prop: 'whetherUseCoupon.desc',  name: 'whetherUseCoupon.desc',  label: '是否可用劵',  width: 100},
  {  prop: 'salePrice',  name: 'salePrice',  label: '销售价',  width: 100},
  {  prop: 'costPrice',  name: 'costPrice',  label: '成本价',  width: 100},
  {  prop: 'stockQuantity',  name: 'stockQuantity',  label: '库存',  width: 100},
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  });
}
export default {
   //import引入的组件
  components: {},
  props:{
    productVisible:{ // 弹窗隐藏和展示
      type: Boolean,
      required: true,
      default: false,
    },
    checkList: { // 已勾选列表
      type: Array,
      default: ()=> []
    },
    limit: { // 限制选择的商品数量 9999 默认为不限制
        type: Number,
        default: 9999
    }
  },
  data() {
      return {
        visible:false,
        rowKey: "id",
        model:{
          erpCode: '',
          productName: '',
          whetherUseCoupon:'',
          whetherOnSale:'Y',
					warehouseIds: []
        },
        pageSize: 10,
        tableData: [],
        listData: [],
        allColumn: TableColumnList, // 所有表列
        selectsProduct: [], // 已选择的商品
        selectionMapObject: {}, // 已加载被选中的全部选项
				storeList: [],
        timer: null
      }
  },
  watch: {
    productVisible: {
      handler(newVal, oldVal) {
        this.visible = newVal;
      },
      immediate: true
    },
    selectsProduct: {
      handler(newVal,oldVal) {
        newVal.forEach(selected=>{
          // 只更改本页选中和未选中的
          if(this.tableData.find(item=>selected === item.id)) {
            this.selectionMapObject[selected] = true;
          } else {
            this.selectionMapObject[selected] = false;
          }
        });
        Object.keys(this.selectionMapObject).forEach(i=> {
          if(newVal.find(item=> item === i)) {
            this.selectionMapObject[i] = true;
          } else {
            this.selectionMapObject[i] = false;
          }
        })
      },
      deep: true
    }
  },
	async mounted() {
    try {
			const res = await getAllStore()
			const data = res.data;
          if(data && data.length) {
            data.forEach(item => {
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeList.push(obj)
            })
          }
		} catch (error) {
			console.log(error)
		}
	},
   //方法集合
  methods: {
    async load(params) {
      let listQuery = {
        model:{
          ...this.model,
        },
				...params
      };
      let result = await list(listQuery);
      this.listData = result.data.records || [];
      // 处理初始化已勾选的商品
      this.timer = setTimeout(()=>{
        this.checkProductTrue();
      },10)
      return result;
    },
    checkProductTrue(){
      let ids = this.checkList.map(item => item.id);
      let arr = this.listData.filter(item => ids.includes(item.id));
      console.log("this.$refs['pager-table']",this.$refs['pager-table']);
      arr.forEach(row=>{
        if(row) {
          this.$refs['pager-table'].toggleRowSelection(row, true);
        } else {
          this.$refs['pager-table'].clearSelection()
        }
      })
    },
    onAllSelect(selection) {
      this.onSelect(selection);
    },

    onSelect (val) {
      this.selectsProduct = val.map(item=>item.id);
    },

    handleSelectionChange(selects) {
        // // 判断是否限制勾选个数
        // if(this.limit != 9999 && selects.length > this.limit) {
        //     this.$refs['pager-table'].clearSelection()
        //     this.$nextTick(() => {
        //         selects.slice(0, this.limit).forEach(row=>{
        //           this.$refs['pager-table'].toggleRowSelection(row, true);
        //         });
        //         this.selectsProduct = selects.slice(0, this.limit).map(item=>item.id);
        //         this.$message.warning(`最多只能选择${this.limit}个商品`);
        //         return
        //     })
        // } else {
        //     this.selectsProduct = selects.map(item=>item.id);
        // }
        // console.log('----handleSelectionChange-->',this.selectsProduct);
    },
    selectionChangeHandle(selects){
      // 判断是否限制勾选个数
        if(this.limit != 9999 && selects.length > this.limit) {
            this.$refs['pager-table'].clearSelection()
            this.$nextTick(() => {
                selects.slice(0, this.limit).forEach(row=>{
                  this.$refs['pager-table'].toggleRowSelection(row, true);
                });
                this.selectsProduct = selects.slice(0, this.limit).map(item=>item.id);
                this.$message.warning(`最多只能选择${this.limit}个商品`);
                return
            })
        } else {
            this.selectsProduct = selects.map(item=>item.id);
        }
        console.log('----handleSelectionChange-->',this.selectsProduct);
    },
    reset(){
      this.model = {
        erpCode: '',
        productName: '',
        whetherUseCoupon:"",
        whetherOnSale:'Y',
      }
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    searchLoad(){
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    closeDia(){
      this.visible = false;
      this.$refs['pager-table'].clearSelection()
      this.$emit('closeProductDia');
    },
    // 确定按钮
    submitCheck() {

      let paramsList = [];

      this.checkList.forEach(item=>{
        if(this.selectionMapObject[item.id] == undefined || this.selectionMapObject[item.id]) {
          if(!paramsList.includes(item.id)) {
            paramsList.push(item.id);
          }
        }
      });

      this.selectsProduct.forEach(i=> {
        if(!paramsList.includes(i)) {
          paramsList.push(i);
        }
      })

      this.$emit('change', paramsList);
      this.closeDia();
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>


<style lang='scss' scoped>

</style>
