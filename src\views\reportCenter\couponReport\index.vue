<template>
  <div class="list—index">
    <!-- 搜索form -->
    <im-search-pad :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="during">
        <el-date-picker v-model="during" type="daterange" range-separator="至" value-format="yyyy-MM-dd"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </im-search-pad-item>
      <im-search-pad-item prop="erpCode">
        <el-input v-model.trim="model.erpCode" @keyup.enter.native="searchLoad" placeholder="请输入ERP客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'client'" prop="whetherMember">
        <el-select v-model="model.whetherMember" placeholder="是否会员">
          <el-option label="是" value="Y"></el-option>
          <el-option label="否" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'order'" prop="activityCode">
        <el-input v-model.trim="model.activityCode" @keyup.enter.native="searchLoad" placeholder="请输入活动编码" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'order'" prop="couponName">
        <el-input v-model.trim="model.couponName" @keyup.enter.native="searchLoad" placeholder="请输入优惠券名称" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="whetherVipCoupon">
        <el-select v-model="model.whetherVipCoupon" placeholder="是否专享券">
          <el-option label="是" value="Y" />
          <el-option label="否" value="N" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'client'" prop="couponType">
        <el-select v-model="model.couponType" placeholder="优惠券类型">
          <el-option label="满减券" value="FULL_REDUCTION"></el-option>
          <el-option label="折扣券" value="DISCOUNT"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'order'" prop="couponGetType">
        <el-select v-model="model.couponGetType" placeholder="请选择领券方式">
          <el-option label="自主领取" value="AUTONOMY"></el-option>
          <el-option label="平台赠券" value="GIVE"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand && currentTab == 'order'" prop="orderStatus">
        <el-select multiple collapse-tags v-model="model.orderStatus" placeholder="请选择订单状态">
          <el-option label="待审核" value="WAIT_PROCESS" />
          <el-option label="待发货" value="WAIT_DELIVERY" />
          <el-option label="发货中" value="PART_DELIVERY" />
          <el-option label="已发货" value="HAD_DELIVERY" />
          <el-option label="已完成" value="SUCCESS" />
          <el-option label="已取消" value="CANCEL" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <!-- 搜索form结束 -->
    <!-- tab切换 -->
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="currentTab" @change="handleChangeTab">
        <template slot="button">
          <!-- <el-button @click="handleExport">导出</el-button> -->
          <paged-export v-if="checkPermission(['admin', 'sale-saas-report-coupon:orderExport','sale-platform-report-coupon:orderExport'])" :max-usable="maxUsableExport" controllable :before-close="handleExportBeforeClose"></paged-export>
        </template>
      </tabs-layout>
    </div>
    <!-- tab切换结束 -->

    <div class="statistic" v-if="currentTab == 'order'">
      <el-row :gutter="20">
        <el-col :span="4" v-for="item in statistic" :key="item.prop">
          <h5>{{ item.text }}</h5>
          <p>{{ item.value }}</p>
        </el-col>
      </el-row>
    </div>
    <!-- table 表格 -->
    <div class="table_bg">
      <table-pager ref="pager-table" :is-need-button="currentTab == 'client'" show-summary :summary-method="getSummaries"
        :options="tableColumn" :remote-method="load" :data.sync="tableData" :selection="false" :pageSize="pageSize">
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button v-if="checkPermission(['admin', 'sale-saas-report-coupon:clientDetail','sale-platform-report-coupon:clientDetail'])" type="text" @click="handleDetail(row)">查看明细</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>


<script>
  import columnData from "@/views/reportCenter/couponReport/columnData";
  import checkPermission from "@/utils/permission";
  import {
    couponOrderCountPage, // 优惠券报表 ---->  根据客户统计的分页列表
    couponOrderCountExport, // 优惠券报表 ----> 根据客户统计的导出接口
    couponCountPageOrder, // 优惠券报表  ---> 根据--(订单统计)-- 分页查询
    couponCountExportOrder, // 优惠券报表  ---->  根据--(订单统计)--的导出接口
    couponOrderCountStatistics // 优惠券报表 --->  根据--(订单统计)--的上面几个数字的统计接口
  } from "@/api/reportCenter/couponDetailReport";
  import PagedExport from "@/components/PagedExport/index";
  import {
    exoprtToExcel
  } from "@/utils/commons";
  import {
    formatDataTime,
    MessageConfirmExport
  } from '@/utils/index'
  export default {
    name: 'couponStatement',
    //import引入的组件
    components: {
      PagedExport
    },

    data() {
      return {
        isExpand: false,
        during: [],
        maxUsableExport: 0,
        tableColumn: [],
        tableData: [],
        model: {
          erpCode: '', // ERP客户编码
          purMerchantName: '', // 客户名称
          whetherMember: '', // 是否会员
          activityCode:'', // 活动编码
          couponName:'', // 优惠券名称
          whetherVipCoupon:'', // 是否专享券
          couponType:'', // 优惠券类型
          couponGetType: '', //领券方式
          orderStatus: [], // 订单状态
        },
        statistic: [{
            prop: 'orderTotalMoney',
            text: '订单总额(元)',
            value: 0
          },
          {
            prop: 'orderRealTotalMoney',
            text: '实付总额(元)',
            value: 0
          },
          {
            prop: 'couponTotalMoney',
            text: '优惠券优惠金额(元)',
            value: 0
          }
        ],
        currentTab: "order",
        pageData: {},
        pageSize: 10,
      };
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.initData();
    },
    computed: {
      tabs() {
        return [{
            name: "按订单统计",
            value: "order",
          },
          {
            name: "按客户统计",
            value: "client",
          },
        ];
      },
    },
    created() {
      this.dealDate();
      this.tableColumn = this.initTableColumn(this.currentTab);
    },
    //方法集合
    methods: {
      checkPermission,
      initTableColumn(flag) {
        let list = JSON.parse(JSON.stringify(columnData[flag]));
        let TableColumnList = [];
        list.forEach((element, index) => {
          TableColumnList.push(
            Object.assign({}, element, {
              key: index + 1,
            })
          );
        });
        return TableColumnList;
      },
      dealDate(){
        console.log('--->',formatDataTime('MM'));
        let startMonth = formatDataTime('MM');
        let startYear = formatDataTime('yyyy');
        let startDay = formatDataTime('dd');
        let currentDate = formatDataTime('yyyy-MM-dd');
        if(startMonth == '01') {
          startYear = startYear - 1;
          startMonth = '12';
        } else if(startMonth == '12') {
          startMonth = '11';
        } else {
          startMonth = Number(startMonth) - 1
        }
        let startDate = `${startYear}-${startMonth}-${startDay}`;
        this.during = [startDate,currentDate];
        // 优惠券数据明细跳转进来

        if(localStorage.getItem('COUPON_USER_NAME') && localStorage.getItem('COUPON_USER_NAME') !='null') {

          this.model.activityCode = localStorage.getItem('COUPON_USER_NAME');
          this.during = [];
          localStorage.setItem('COUPON_USER_NAME', null)
        }
      },
      async load(query) {
        let listQuery = {
          model: {
            ...this.model
          },
          map:{

          }
        };
        Object.assign(listQuery, query);
        if(this.during !=null && this.during != 'null' && this.during.length == 2){
          let mapQuery = {
            createTime_st: this.during[0],
            createTime_ed: this.during[1],
          };
          listQuery.map = mapQuery;
        }
        let result = {};
        if (this.currentTab == 'order') {
          console.log('---订单统计--->',this.during );
          result = await couponCountPageOrder(listQuery);
        } else if (this.currentTab == 'client') {
          console.log('---客户统计--->', );
          result = await couponOrderCountPage(listQuery);
        }
        this.maxUsableExport = result.data.total
        return result;
      },
      // 订单统计的数据统计
      initData() {
        let params = {
          current: 1,
          map: {},
          model: this.model,
          order: "descending",
          size: 10,
          sort: "id",
        };
        if(this.currentTab == 'client') {
          return
        }

        if(this.during !=null && this.during != 'null' && this.during.length == 2){
          let mapQuery = {
            createTime_st: this.during[0],
            createTime_ed: this.during[1],
          };
          params.map = mapQuery;
        }
        couponOrderCountStatistics(params).then((res) => {
          if (res.code == 0 && res.msg == "ok") {
            this.statistic = this.statistic.map(item=>{
              item.value = res.data[item.prop] || 0;
              return item
            })
          }
        });
      },
      // 数字判断--->校验只要是数字（包含正负整数，0以及正负浮点数）就返回true
      isNumber(val){
        let regPos = /^[0-9]+.?[0-9]*/; //判断是否是数字
        if(regPos.test(val)){
          return true
        } else {
          return false
        }
      },
      /**
       * 合计
       */
      getSummaries(param) {
        const {
          columns,
          data
        } = param;
        const sums = [];
        let sumKeys = [];
        // console.log('param',param);
        if(this.currentTab == 'order') {
          sumKeys = ['orderMoney','orderRealMoney','couponMoney'];
        } else {
          sumKeys = ['couponReceivedNum','couponReceivedMoney','couponUseNum','couponUseMoney','couponHaveNum','couponHaveMoney'];
        }
        if(columns.length == 0){
          return
        }
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
            return;
          }
          if (sumKeys.some((v) => v === column.property)) {
            sums[index] = data
              .map((v) => ({key:column.property, value: Number(v[column.property])}))
              .reduce((prev, curr) => {
                prev = Number(prev) + curr.value
                return ['couponReceivedNum', 'couponUseNum', 'couponHaveNum'].includes(curr.key) ? prev : prev.toFixed(2);
              }, 0);
          }
        });
        return sums;
      },
      // tab切换改变事件
      handleChangeTab(tab) {
        this.currentTab = tab.value;
        this.tableColumn = this.initTableColumn(tab.value);
        this.reload();
      },
      reload() {
        this.model.activityCode = '';
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize,
        });
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize,
        });
      },
      handleRefresh(pageParams) {
        this.$refs["pager-table"].doRefresh(pageParams);
        this.initData();
      },
      handleDetail(row){
        this.$router.push({
          path:'/reportCenter/couponDetailReport',
          query: {
            purMerchantName: row.purMerchantName,
            purMerchantId: row.id,
            whetherVipCoupon: this.model.whetherVipCoupon,
            couponType: this.model.couponType,
          }
        });
      },
      async handleExportBeforeClose(params) {
        const loading = this.$loading({
          lock: true,
          text: '正在导出中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });
        let paramsQuery = {
          current: 1,
          map: {
            // createTime_st: this.during[0],
            // createTime_ed: this.during[1],
          },
          model: {
            ...this.model,
            ...params
          },
          order: "descending",
          size: 10,
          sort: "id",
        };
        if(this.during !=null && this.during != 'null' && this.during.length == 2){
          let mapQuery = {
            createTime_st: this.during[0],
            createTime_ed: this.during[1],
          };
          paramsQuery.map = mapQuery;
        }
        let result = {};
        if (this.currentTab == 'order') {
          console.log('---订单统计--->', );
          result = await couponCountExportOrder(paramsQuery);
          loading.close();
          // exoprtToExcel(result.data, `按订单统计优惠券报表${formatDataTime('yyyyMMDDHHmmss')}.xlsx`)
          return await MessageConfirmExport();
        } else if (this.currentTab == 'client') {
          console.log('---客户统计--->', );
          result = await couponOrderCountExport(paramsQuery);
          loading.close();
          return await MessageConfirmExport();
        }

      },

    },
  };

</script>


<style lang='scss' scoped>

  .table_bg {
    padding: 0px 20px 20px 20px;
    background-color: #fff;

    .page-row {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      color: #505465;
      font-size: 13px;
      margin-top: 16px;
    }

    .page-row-right {
      display: flex;
    }
  }

  .statistic {
    border-left: 20px solid #fff;
    border-right: 20px solid #fff;
    padding: 30px;

    color: #333333;
    background-color: rgba(248, 248, 248, 1);

    h5 {
      font-size: 16px;
      margin-bottom: 16px;
    }

    p {
      font-size: 26px;
    }

  }

</style>
