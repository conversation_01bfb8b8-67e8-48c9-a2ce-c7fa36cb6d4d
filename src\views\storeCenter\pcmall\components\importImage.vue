<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" class="import-image" @confirm="confirm">
    <div class="dialog-content" :class="type">
      <div class="list">
        <div class="label">背景图片：</div>
        <div class="list-content">
          <el-upload
            action
            list-type="picture-card"
            accept=".jpg,.gif,.png"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-change="handleChange"
            :http-request="upload"
            :limit="1"
          >
            <el-button >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              <span>建议尺寸：1920*120px，支持JPG或PNG格式图片，大小不超过2M</span>
            </div>
          </el-upload>
        </div>
      </div>
      <div class="list">
        <div class="label">店铺背景预览：</div>
        <div class="list-content">
          <div class="img-content">
            <el-image v-if="fileList.length > 0" :src="fileList.length > 0 ? fileList[0].url : ''" class="avatar"></el-image>
            <div v-if="fileList.length > 0" class="remove-img"><i class="icon el-icon-delete" @click="delFile"></i></div>
          </div>
        </div>
      </div>


    </div>
  </im-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'
import { backgroundUpdate } from '@/api/pcmall'
import { getUploadFileUrl } from "@/api/upload"

export default {
  name: 'ImportImage',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      fileList: [],
      dialogVisible: false,
      visibleDialog: false,
      headersProgram: {
        token: getToken(),
        Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
        'Content-Type': 'multipart/form-data'
      },
      insertProgram: {
        folderId: 0
      }
    }
  },
  watch: {
    visibleDialog(newVal) {
      if (!newVal) this.fileList = []
    }
  },
  methods: {
    init(data) {
      if(data) this.fileList = [{url: data}]
      this.visibleDialog = true
    },
    async upload(fileObj) {
      const {data} = await getUploadFileUrl(fileObj.file)
      this.fileList = [data]
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 2MB!',
          type: 'warning'
        })
        return false
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    confirm() {
      if (this.fileList.length <1) {
        this.$message.error('请先选择背景图片')
        return false
      }
      backgroundUpdate({
        backgroundPicUrl: this.fileList[0].url
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('编辑图片成功')
          this.$emit('confirm')
          this.visibleDialog = false
        }
      })
    },
    delFile() {
      this.fileList = []
    }
  }
}
</script>

<style lang="less">
.import-image{

  .dialog-content{
    margin-left: 20px;
    min-height: 90px;
    .list{
      display: flex;
      margin-bottom: 40px;
      .label{
        font-size: 14px;
        color: #1e2439;
        text-align: right;
        width: 100px;
        line-height: 38px;
      }
      .list-content{
        margin-left: 10px;
      }
    }

    .el-upload--picture-card{
      width: 88px;
      height: 32px;
      line-height: 0;
      border: 0;
    }
    &.logo{
      .el-progress-circle{
        width: 78px !important;
        height: 78px !important;
      }
      //.el-upload__tip {
      //  left: 256px;
      //  position: absolute;
      //  top: 48px;
      //}
    }
    .el-upload-list{
      display: none;
    }
  }
  .img-content{
    width: 960px;
    height: 60px;
    position: relative;
    transition: all .3s;
    .el-image{
      width: 960px;
      height: 60px;
    }
    .remove-img{
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0,0,0,.7);
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #fff;
      display: none;
      i{
        cursor: pointer;
      }
    }
    &:hover .remove-img{
      display: flex;
    }
  }
  .el-dialog__body{
    padding: 40px;
  }
}

</style>
