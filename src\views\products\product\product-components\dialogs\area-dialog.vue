<template>
  <el-dialog
    v-loading="loading"
    title="选择区域"
    :visible.sync="visible"
    width="30%"
    :before-close="handleClose">
    <el-tree
      ref="areaTree"
      :data="areaData"
      show-checkbox
      node-key="id"
      @check="handleCheck"
      @check-change="handleCheckChange">
    </el-tree>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { find as _find, filter as _filter, map as _map } from 'lodash'
export default {
  props: {
    areaData: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      visible: false,
      currentTree: '',
      formatAreaData: {
        province: {},
        citys: [],
        districts: []
      }
    }
  },
  mounted () {
    this.load()
  },
  methods: {
    async load () {

    },
    handleClose(done) {
      done()
    },
    handleCheck (node, tree) {
      if (this.currentTree === '') {
        this.currentTree = node.id.slice(0, 2)
      } else {
        if (node.id.slice(0,2) !== this.currentTree) {
          this.currentTree = node.id.slice(0, 2)
          this.$refs.areaTree.setCheckedKeys([])
          this.$refs.areaTree.setCheckedKeys([node.id])
        } else {
          this.$refs.areaTree.setCurrentKey(node.id)
        }
      }
    },
    handleCheckChange () {
      const data = this.$refs.areaTree.getCheckedNodes(false, true)
      this.formatAreaData.province = _find(data, item => item.id.length === 2)
      this.formatAreaData.citys = _filter(data, item => item.id.length === 4)
      this.formatAreaData.districts = _filter(data, item => item.id.length > 5)
    },
    handleConfirm () {
      this.$emit('getData', this.formatAreaData)
      this.visible = false
    }
  }
}
</script>
