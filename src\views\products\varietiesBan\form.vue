<template>
  <div class="varietiesBan-form">
    <div class="fr" style="margin-top: -65px;">
      <el-button @click="$router.push({path: '/productCenter/varietiesBan/list'})">取消</el-button>
      <el-button type="primary" @click="createVarietiesBan">保存</el-button>
    </div>
    <div class="varietiesBan-form-item">
      <form-item-title title="控销商品"/>
      <el-button type="primary" @click="openProductDialog" v-if="editOrAdd" style="margin-bottom: 20px">选择商品</el-button>
      <el-table border :data="productList">
        <el-table-column type="index" label="序号" width="50"/>
        <el-table-column label="商品主图" width="80" class-name="img-cell">
          <template slot-scope="scope">
            <img :src="splitString(scope.row.pictIdS)" width="50px" v-if="scope.row.pictIdS">
            <img :src="pictImg" width="50px" v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''">
          </template>
        </el-table-column>
        <el-table-column label="商品编码" prop="productCode" width="170" show-overflow-tooltip />
        <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
        <el-table-column label="规格" prop="spec" show-overflow-tooltip />
        <el-table-column label="单位" prop="unit" width="60" show-overflow-tooltip />
        <el-table-column label="生产厂家" prop="manufacturer" show-overflow-tooltip />
        <el-table-column label="库存" prop="stockQuantity" width="80" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" v-if="editOrAdd" width="55">
          <template slot-scope="scope">
            <el-button type="text" @click="deleteProductItem(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="varietiesBan-form-item">
      <form-item-title title="控销设置"/>
      <el-form :model="form" label-width="80px">
        <el-form-item label="控销类型">
          <el-select v-model="form.type">
            <el-option label="商品控销" value="RESTRICTION"/>
            <el-option label="商品禁销" value="PROHIBITION"/>
          </el-select>
        </el-form-item>
        <el-form-item label="控销时间">
          <el-date-picker
            v-model="form.during"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            :default-time="['00:00:00', '23:23:59']"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="选择客户">
          <el-radio-group v-model="form.client" @change="clickqw">
            <el-radio label="AREA">按区域<el-button @click="openAreaDialog" v-if="form.client==='AREA'" type="text" icon="el-icon-edit" circle></el-button></el-radio>
            <el-radio label="ENTERPRISE_TYPE">按客户类型<el-button @click="openClientTypeDialog" v-if="form.client==='ENTERPRISE_TYPE'" type="text" icon="el-icon-edit" circle></el-button></el-radio>
            <el-radio label="MERCHANT_GROUP">按客户分组<el-button @click="openClientGroupDialog" v-if="form.client==='MERCHANT_GROUP'" type="text" icon="el-icon-edit" circle></el-button></el-radio>
            <el-radio label="PURCHASE_MERCHANT">指定客户<el-button @click="openClientDialog" v-if="form.client==='PURCHASE_MERCHANT'" type="text" icon="el-icon-edit" circle></el-button></el-radio>
          </el-radio-group>
        </el-form-item>
        <component :is="currentTable" :controlType="form.type" ref="table" @done="doneHandle"></component>
      </el-form>
    </div>
    <product-dialog ref="productDialog" @getData="getProductData"/>
    <area-dialog ref="areaDialog" @getData="getAreaData"  v-bind:checkedIds="checkedIds"/>
    <client-type-dialog ref="clientTypeDialog" @getData="getClientTypeData" v-bind:checkedId="checkedTypeId"/>
    <client-group-dialog ref="clientGroupDialog" @getData="getGroupData" v-bind:checkedId="checkedGroupId" :saleMerchantId="saleMerchantId"/>
    <client-dialog ref="clientDialog" :saleMerchantId="saleMerchantId" @getData="getClientData" v-bind:checkedId="checkedClientId"/>
  </div>
</template>

<script>
import { productControlSaleAdd,productControlSaleEdit } from '@/api/product.js'
import { findByUserIdSale } from '@/api/group'
import {find as _find, map as _map} from 'lodash'
import formItemTitle from '../common-components/form-item-title'
import productDialog from './dialogs/product-dialog'
import areaDialog from './dialogs/area-dialog'
import clientTypeDialog from './dialogs/client-type-dialog'
import clientGroupDialog from './dialogs/client-group-dialog'
import clientDialog from './dialogs/client-dialog'
import productImg from "../../../assets/product.png";
export default {
  components: { formItemTitle, productDialog, areaDialog, clientTypeDialog, clientGroupDialog, clientDialog },
  props:['data','isAdd','client'],
  inject:  ['reload'],
  data() {
    return {
      productList: [],
      form: {
        type: 'RESTRICTION',
        during: [],
        client: 'AREA'
      },
      cityIdS: [],
      districtIds: [],
      productId: '',
      saleMerchantId: '',
      tables: [
        {client: 'AREA', component: require('./tables/area-table').default},
        {client: 'ENTERPRISE_TYPE', component: require('./tables/client-type-table').default,},
        {client: 'MERCHANT_GROUP', component: require('./tables/client-group-table').default},
        {client: 'PURCHASE_MERCHANT', component: require('./tables/client-specific-table').default}
      ],
      enterpriseTypeProductControlSaleRelSaveDTOList: [],
      areaProductControlSaleRelSaveDTOList: [],
      merchantGroupProductControlSaleRelSaveDTOList: [],
      purchaseMerchantProductControlSaleRelSaveDTOList: [],
      detail: {},
      done: false,
      areaData: [],
      editOrAdd: false, //新增显示，编辑不显示商品删除按钮
      pictImg: productImg,
      checkedIds: [],
      checkedTypeId: [],
      checkedGroupId: [],
      checkedClientId: [],
      isOk: true
    }
  },
  computed: {
    currentTable() {
      return _find(this.tables, (o) => o.client === this.form.client).component
    }
  },
  mounted() {

  },
  methods: {
    clickqw(val) {
      this.form.client = val
      this.$nextTick(()=>{
        if (this.form.client === 'AREA') {
          this.$refs.table.setData(2, this.areaProductControlSaleRelSaveDTOList)
        } else if (this.form.client === 'ENTERPRISE_TYPE') {
          this.$refs.table.setData(2, this.enterpriseTypeProductControlSaleRelSaveDTOList)
        } else if (this.form.client === 'MERCHANT_GROUP') {
          this.$refs.table.setData(2, this.merchantGroupProductControlSaleRelSaveDTOList)
        } else {
          this.$refs.table.setData(2, this.purchaseMerchantProductControlSaleRelSaveDTOList)
        }
      })
    },
    reset() {
      this.reload()
    },
    async createVarietiesBan() {
      let tableData = this.$refs.table.tableData
      if (this.form.client === 'AREA') {
        this.areaProductControlSaleRelSaveDTOList = _map(tableData, (item, idx) => {
          const provinceId = item.provinceId
          const cityIdS = item.cityIdS
          const districtIdS = item.districtIdS
          let amount = 0
          if(this.form.type === 'RESTRICTION') {
            amount = item.amount
          }
          if (this.form.type==='RESTRICTION'&&(amount === ''||!amount)) {
            this.$message.warning(item.provinceName + '的控销数量不能为空！')
            this.isOk = false
          } else {
            this.isOk = true
            return {
              provinceId: provinceId,
              cityIdS: cityIdS,
              districtIdS: districtIdS,
              amount: amount,
              innerId: item.innerId
            }
          }
        })
        this.enterpriseTypeProductControlSaleRelSaveDTOList = []
        this.merchantGroupProductControlSaleRelSaveDTOList =[]
        this.purchaseMerchantProductControlSaleRelSaveDTOList = []
      } else if(this.form.client === 'ENTERPRISE_TYPE') {
        this.areaProductControlSaleRelSaveDTOList = []
        this.merchantGroupProductControlSaleRelSaveDTOList =[]
        this.purchaseMerchantProductControlSaleRelSaveDTOList = []
        this.enterpriseTypeProductControlSaleRelSaveDTOList =_map(tableData,(item,index) => {
          let amount = 0
          if(this.form.type === 'RESTRICTION') {
            amount = item.amount
          }
          if (this.form.type==='RESTRICTION'&&(amount === ''||!amount)) {
            this.$message.warning(item.name + '的控销数量不能为空！')
            this.isOk = false
          } else {
            this.isOk = true
            const enterpriseTypeId = item.id
            return {
              amount: amount,
              enterpriseTypeId: enterpriseTypeId,
              innerId: item.innerId
            }
          }
        })
      } else if(this.form.client === 'MERCHANT_GROUP') {
        this.enterpriseTypeProductControlSaleRelSaveDTOList = []
        this.areaProductControlSaleRelSaveDTOList = []
        this.purchaseMerchantProductControlSaleRelSaveDTOList = []
        this.merchantGroupProductControlSaleRelSaveDTOList = _map(tableData,(item,index) => {
          let amount = 0
          if(this.form.type === 'RESTRICTION') {
            amount = item.amount
          }
          if (this.form.type==='RESTRICTION'&&(amount === ''||!amount)) {
            this.$message.warning(item.name + '的控销数量不能为空！')
            this.isOk = false
          } else {
            this.isOk = true
            const merchantGroupId = item.id
            return {
              amount: amount,
              merchantGroupId: merchantGroupId,
              innerId: item.innerId,

            }
          }

        })
      } else {
        this.enterpriseTypeProductControlSaleRelSaveDTOList = []
        this.areaProductControlSaleRelSaveDTOList = []
        this.merchantGroupProductControlSaleRelSaveDTOList =[]
        this.purchaseMerchantProductControlSaleRelSaveDTOList = _map(tableData,(item, index) => {
          let amount = 0
          if(this.form.type === 'RESTRICTION') {
            amount = item.amount
          }
          if (this.form.type==='RESTRICTION'&&(amount === ''||!amount)) {
            this.$message.warning(item.name + '的控销数量不能为空！')
            this.isOk = false
          } else {
            this.isOk = true
            const enterpriseTypeId = item.id
            return {
              amount: amount,
              purMerchantId: item.purMerchantId,
              innerId: item.innerId
            }
          }
        })
      }
      this.loading = true
      let vm = this
      if (this.isOk) {
        if (this.detail.id) {
          const data = await productControlSaleEdit({
            areaProductControlSaleRelUpdateDTOList: this.areaProductControlSaleRelSaveDTOList,
            enterpriseTypeProductControlSaleRelUpdateDTOList: this.enterpriseTypeProductControlSaleRelSaveDTOList,
            merchantGroupProductControlSaleRelUpdateDTOList: this.merchantGroupProductControlSaleRelSaveDTOList,
            purchaseMerchantProductControlSaleRelUpdateDTOList: this.purchaseMerchantProductControlSaleRelSaveDTOList,
            productId: this.productId,
            controlBeginTime: this.form.during[0],
            controlEndTime: this.form.during[1],
            controlType: this.form.type,
            selectCustomer: this.form.client,
            id: this.detail.id
          })
          if(data.code == 0 && data.msg=='ok'){
            this.$message.success('编辑成功！')
            this.$router.push({path: '/productCenter/varietiesBan/list'})
          }


        } else {
          const data = await productControlSaleAdd({
            areaProductControlSaleRelSaveDTOList: this.areaProductControlSaleRelSaveDTOList,
            enterpriseTypeProductControlSaleRelSaveDTOList: this.enterpriseTypeProductControlSaleRelSaveDTOList,
            merchantGroupProductControlSaleRelSaveDTOList: this.merchantGroupProductControlSaleRelSaveDTOList,
            purchaseMerchantProductControlSaleRelSaveDTOList: this.purchaseMerchantProductControlSaleRelSaveDTOList,
            productId: this.productId,
            controlBeginTime: this.form.during[0],
            controlEndTime: this.form.during[1],
            controlType: this.form.type,
            selectCustomer: this.form.client,
          });
          if(data.code == 0 && data.msg=='ok'){
            this.$message.success('新增成功！')
            this.$router.push({path: '/productCenter/varietiesBan/list'})
          }
        }
      }

      this.loading = false

    },
    deleteProductItem (idx) {
      this.productList.splice(idx, 1)
    },
    openProductDialog () {
      this.$refs.productDialog.visible = true
    },
    openAreaDialog () {
      this.$refs.areaDialog.visible = true
      let tableData = this.$refs.table.tableData
      this.checkedIds = _map(tableData, (item, idx) => {
        const provinceId = item.provinceId
        const cityIdS = item.cityIdS
        const districtIdS = item.districtIdS
        return {
          provinceId:provinceId,
          cityIdS:cityIdS,
          districtIdS: districtIdS
        }
      })
    },
    openClientTypeDialog () {
      this.$refs.clientTypeDialog.visible = true
      let tableData = this.$refs.table.tableData
      this.checkedTypeId = _map(tableData, (item, idx) => {
        const id = item.id
        return {
          id
        }
      })
    },
    openClientGroupDialog () {
      this.$refs.clientGroupDialog.visible = true
      let tableData = this.$refs.table.tableData
      this.checkedGroupId = _map(tableData, (item, idx) => {
        const id = item.id
        return {
          id
        }
      })
    },
    openClientDialog () {
      this.$refs.clientDialog.visible = true
      let tableData = this.$refs.table.tableData
      this.checkedClientId = _map(tableData, (item, idx) => {
        const id = item.id
        return {
          id
        }
      })
    },
    getProductData (data) {
      this.productList = []
      this.productList.push(data)
      this.productId = data.id
    },
    getAreaData(data) {
      this.$refs.table.setData(1,data)
    },
    getClientTypeData(data) {
      this.$refs.table.setData(1,data)
    },
    getGroupData(data) {
      this.$refs.table.setData(1,data)
    },
    getClientData(data) {
      this.$refs.table.setData(1,data)
    },
    splitString (val) {
      return val.split(',')[0]
    },
    doneHandle() {
      this.done = true
    },
  },
  watch: {
    client: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.form.client = newVal
        if(this.form.client === '') {
          this.form.client = 'AREA'
        }
      }
    },
    isAdd: {
      immediate: true,
      handler(newVal,old) {
        this.editOrAdd = newVal
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler(newVal,oldVal) {
        this.detail = newVal
        if(this.detail) {
          this.form = {
            type: this.detail.controlType.code,
            during: [this.detail.controlBeginTime, this.detail.controlEndTime],
            client: this.detail.selectCustomer.code
          }
          this.client = this.detail.selectCustomer.code
          this.productList = [this.detail.controlSaleProduct]
          this.enterpriseTypeProductControlSaleRelSaveDTOList = this.detail.enterpriseTypeProductControlSaleRelList
          this.areaProductControlSaleRelSaveDTOList = this.detail.areaProductControlSaleRelList
          this.merchantGroupProductControlSaleRelSaveDTOList = this.detail.merchantGroupProductControlSaleRelList
          this.purchaseMerchantProductControlSaleRelSaveDTOList = this.detail.purchaseMerchantProductControlSaleRelList
          this.productId = this.detail.productId
          this.$nextTick(()=>{
            if (this.form.client === 'AREA') {
              this.$refs.table.setData(2, this.areaProductControlSaleRelSaveDTOList)
            } else if (this.form.client === 'ENTERPRISE_TYPE') {
              this.$refs.table.setData(2, this.enterpriseTypeProductControlSaleRelSaveDTOList)
            } else if (this.form.client === 'MERCHANT_GROUP') {
              this.$refs.table.setData(2, this.merchantGroupProductControlSaleRelSaveDTOList)
            } else {
              this.$refs.table.setData(2, this.purchaseMerchantProductControlSaleRelSaveDTOList)
            }
          })
        }
      }

   }
  }
}
</script>

<style lang="scss" scoped>
.varietiesBan-form{
  &-item{
    margin-bottom: 20px;
  }
}
</style>
