<template>
  <el-dialog
    title="选择客户分组"
    :visible.sync="visible"
    width="50%"
    @open="getList"
    :before-close="handleClose">
    <el-table border :data="tableData" @selection-change="handleSelectionChange" ref="groupTable">
      <el-table-column type="index" label="序号" width="50"/>
      <el-table-column type="selection" width="50" :selectable="selectable"/>
      <el-table-column label="客户分组名称" prop="name"/>
      <el-table-column label="客户数" prop="customerNumber"/>
    </el-table>
    <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination"></pagination>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { merchantGroupList,findByUserIdSale } from "@/api/group";
import Pagination from '@/components/Pagination/index.vue'
export default {
  components: {
    Pagination
  },
  props: ['saleMerchantId','checkedId'],
  data () {
    return {
      visible: false,
      tableData: [],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          saleMerchantId: ''
        },
        order: 'descending',
        sort: 'id'
      },
      groupData: [],
      selected: [],
    }
  },
  mounted() {
    //this.getList()
  },
  methods: {
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    handleClose(done) {
      done()
    },
    handleSelectionChange(val) {
      this.groupData = val
    },
    onSubmit() {
      this.$emit('getData',this.groupData)
      this.$refs.groupTable.clearSelection()
      this.visible = false
    },
    selectable(row, index) {
      if(row.selectable === true) {
        return false
      } else {
        return true
      }
    },
    async getList() {
      this.listQuery.model.saleMerchantId = this.saleMerchantId
      this.listLoading = false;
      const {data} = await merchantGroupList(this.listQuery);
      this.tableData = data.records;
      this.tableData.forEach((item,index)=>{
        this.$set(item,'selectable',false)
        this.selected.forEach(itx=>{
          if (itx.id === item.id) {
            this.tableData[index].selectable = true
          }
        })
      })
    }
  },
  watch: {
    checkedId: {
      immediate: true,
      handler(newVal,oldVal) {
        this.selected = newVal
      }
    }
  }
}
</script>
