<template>
    <div :class="[$style.container, $style.flex, $style['items-center']]">
        <!-- <div :class="[$style['px-10'], $style['flex-none'], $style['cursor-pointer']]">
            <i class="el-icon-arrow-left"></i>
        </div> -->
        <div :class="[$style['flex-1'], $style['w-300']]">
            <el-tabs :value="`_${current}_`" @tab-click="handleTabClick">
              <template v-for="item in tabs">
                <el-tab-pane v-if="!item.hide" :label="item.text" :name="`_${item.value}_`" :key="item.value">
                    <div :class="$style['px-10']" slot="label">
                        <span :class="$style['mr-5']">{{ item.text }}</span>
                        <span v-if="enableCount" :class="$style.tag">{{ item.count || 0 }}</span>
                    </div>
                </el-tab-pane>
              </template>
            </el-tabs>
        </div>
        <div :class="[$style['flex-none'], $style['pb-8']]">
            <slot name="append"></slot>
        </div>
        <!-- <div :class="[$style['px-10'], $style['flex-none'], $style['cursor-pointer']]">
            <i class="el-icon-arrow-right"></i>
        </div> -->

    </div>
</template>
<script>
const MODEL = "UPDATE_MODEL"
export default {
    props: {
        current: {
            type: String,
            default: ''
        },
        tabs: {
            type: Array,
            default: () => []
        },
        enableCount: {
            type: Boolean,
            default: true
        }
    },
    model: {
        prop: 'current',
        event: MODEL
    },
    data() {
        return {}
    },
    methods: {
        handleTabClick(data, e) {
            let value = data.name.replace(/^_/, '').replace(/_$/, '')
            this.$emit(MODEL, value)
            this.$emit('change', value)
        }
    }
}
</script>
<style lang='scss' module>
.container {
    // width: 100%;
    border-bottom: 1px solid #e5e5e5;
    user-select: none;

    :global {
        .el-tabs__nav {
            padding-bottom: 8px;
        }

        .el-tabs__nav-wrap::after {
            display: none;
        }

        .el-tabs__header {
            margin-bottom: 0;
        }

        .el-tabs__item {
            padding: 0 8px;
        }
    }

}

.tag {
    display: inline-block;
    min-width: 16px;
    height: 16px;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: #FF4245;
    color: #fff;
    border-radius: 99px;
    box-sizing: border-box;
    user-select: none;
}

.w-300 {
    width: 300px;
}
.pb-8 {
    padding-bottom: 8px;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.flex-1 {
    flex: 1;
}

.flex-none {
    flex: 0 0 auto;
}

.inline-block {
    display: inline-block;
}

.px-10 {
    padding-left: 10px;
    padding-right: 10px;
}

.mr-5 {
    margin-right: 5px;
}

.cursor-pointer {
    cursor: pointer;
}
</style>