<template>
  <div>
    <el-dialog v-dialogDrag title="查看采集商品" :visible.sync="dialogVisible" width="70%">
      <!--搜索Form-->
      <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
        <im-search-pad-item prop="productCode">
          <el-input v-model="model.productCode" @keyup.enter.native="searchLoad" placeholder="请输入商品编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="productName">
          <el-input v-model="model.productName" @keyup.enter.native="searchLoad" placeholder="请输入商品名称" />
        </im-search-pad-item>
        <im-search-pad-item prop="manufacturer">
          <el-input v-model="model.manufacturer" @keyup.enter.native="searchLoad" placeholder="请输入生产厂家" />
        </im-search-pad-item>
      </im-search-pad>
      <!-- table -->
      <div>
        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column prop="productCode" label="商品编码" width="180">
          </el-table-column>
          <el-table-column prop="productName" label="商品名称" width="180">
          </el-table-column>
          <el-table-column prop="spec" label="规格" width="120">
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="100">
          </el-table-column>
          <el-table-column prop="manufacturer" label="生产厂家" width="150">
          </el-table-column>
          <el-table-column prop="beforePrice" label="上次销售价" width="150">
          </el-table-column>
          <el-table-column prop="price" label="当前销售价" width="150">
          </el-table-column>
          <el-table-column prop="beforeStockQuantity" label="上次库存" width="120">
          </el-table-column>
          <el-table-column prop="stockQuantity" label="当前库存" width="120">
          </el-table-column>
          <el-table-column prop="changeAmount" label="动销量" width="120">
          </el-table-column>
          <el-table-column prop="changePeriod" label="动销周期" width="150">
          </el-table-column>
        </el-table>
        <div class="page-row">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
  import {
    queryByCollectId
  } from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        isExpand: false,
        dialogVisible: false,
        model: {
          productCode: '',
          productName: '',
          manufacturer: ''
        },
        totalCount: 0,
        currentPage: 1,
        pageSize: 10,
        tableData: [],
      }
    },
    //方法集合
    methods: {
      initData(id) {
        this.currentId = id;
        this.dialogVisible = true;
        this.getData();
      },
      getData() {
        let params = {
          current: this.currentPage,
          order: 'descending',
          size: this.pageSize,
          sort: "id",
          model: {
            ...this.model,
            productCollectId: this.currentId,
          }
        };
        queryByCollectId(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.tableData = res.data.records || [];
          }
        })
      },
      handleSizeChange(val) {
        this.pageSize = val;
      },
      handleCurrentChange(val) {
        this.currentPage = val;
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reload() {
        this.model = {
          name: '',
          customerName: '',
          productName: '',
        }
        this.during = '';
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.getData();
      },
    },

  }

</script>


<style lang='scss' scoped>
  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

</style>
