import requestAxios from '@/utils/requestAxios'
const getAgencyId = () => {
  let loginInfo = localStorage.getItem("LOCAL_USER")
  if (loginInfo) loginInfo = JSON.parse(loginInfo)
  return loginInfo.saleMerchantId
}
// 机构服务-分页列表
export function getInstitutionsList(data) {
  if (data && data.model) data.model.agencyId = getAgencyId()
  console.log()
  return requestAxios({
    url: '/org/admin/agencyOrgInfo/page',
    method: 'post',
    data
  })
}

// 机构服务 - 查看账号 - 账号列表
export function getAccountList(data) {
  return requestAxios({
    url: '/org/admin/agencyOrgAccount/page',
    method: 'post',
    data
  })
}

// 机构服务 - 统计
export function getAccountTotal() {
  return requestAxios({
    url: '/org/admin/agencyOrgNo/getagency/'+getAgencyId(),
    method: 'get'
  })
}

// 查看密码
export function getPassword2Id(id) {
  return requestAxios({
    url: '/org/admin/agencyOrgAccount/getAccountpwd/'+id,
    method: 'get'
  })
}

// 业务报表列表
export function getBusinessReportList(data) {
  if (data && data.model) data.model.agencyId = getAgencyId()
  return requestAxios({
    url: '/org/admin/physical/page',
    method: 'post',
    data
  })
}

// 业务报表 -> 汇总
export function getBusinessReportTotal(data) {
  if (data && data.model) data.model.agencyId = getAgencyId()
  return requestAxios({
    url: '/org/admin/physical/total',
    method: 'post',
    data
  })
}

// 业务报表 -> 导出
export function businessReportExport(data) {
  if (data && data.model) data.model.agencyId = getAgencyId()
  return requestAxios({
    url: '/org/admin/physical/export',
    method: 'post',
    data,
  })
}