<template>
  <div>
    <im-search-pad :model="listQuery" @reset="handleReset" :hasExpand="false" @search="onSubmit" style="border-left: 0;padding: 0;">
      <im-search-pad-item prop="code">
        <el-input v-model.trim="listQuery.model.partnerOrInvite" placeholder="请输入推广人/邀请方" />
      </im-search-pad-item>
      <im-search-pad-item prop="code">
        <el-select v-model="listQuery.model.auditStatus" placeholder="是否合伙人">
          <el-option v-for="item in auditStatusData" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="timeRange">
        <el-date-picker
          v-model="listQuery.model.timeRange"
          range-separator="-"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          start-placeholder="开始时间"
          :default-time="['00:00:00', '23:59:59']"
          unlink-panels
          end-placeholder="结束时间"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div style="text-align: right;margin-bottom: 10px;">
      <paged-export :max-usable="total" controllable :before-close="exportExcel" />
    </div>
    <table-pager
      ref="todoTable"
      :options="tableTitle"
      :data.sync="tableData"
      :operation-width="100"
      :remote-method="load"
    >
      <el-table-column label="是否合伙人" width="100" slot="auditStatus">
        <slot slot-scope="{row}">
          {{ row.auditStatus && row.auditStatus.code === 'ACCEPTED' ? '是' : '否' }}
        </slot>
      </el-table-column>
      <div slot-scope="{row}">
        <span class="table-edit-row-item">
          <el-button type="text" @click="showMemberTeamDialog(row.teamId)">查看团队成员</el-button>
        </span>
      </div>
    </table-pager>
    <MemberTeamDialog ref="memberTeamDialogRef" />
  </div>
</template>

<script>
import { getDistributionPartnerList } from '@/api/group'
import { deepClone, MessageConfirmExport } from "@/utils";
import MemberTeamDialog from "./components/memberTeamDialog.vue";
import PagedExport from "@/components/PagedExport/index";
import { exportPartner } from '@/api/retailStore'

const TableColumns = [
  {
    label: "推广员",
    prop: 'partnerName',
    width: 200
  },
  {
    label: "企业类型",
    prop: 'merchantTypeName',
  },
  {
    label: '邀请方',
    prop: 'inviteName',
    width: 200
  },
  {
    label: '是否合伙人',
    name: 'auditStatus',
    slot: true,
  },
  {
    label: '加入时间',
    prop: 'joinTime',
    width: 160
  },
  {
    label: '合伙人时间',
    prop: 'partnerTime',
    width: 160
  },
  {
    label: '一层成员',
    prop: 'firstLevelCount',
  },
  {
    label: '二层成员',
    prop: 'secondLevelCount',
  },
  {
    label: '三层成员',
    prop: 'thirdLevelCount',
  },
  {
    label: '累计团队成交额(元)',
    prop: 'totalRewardMoney',
    width: 150
  },
  {
    label: '累计团队成单数',
    prop: 'totalOrderCount',
    width: 120
  },
  {
    label: '累计本人下单金额(元)',
    prop: 'rewardMoney',
    width: 160
  },
  {
    label: '累计本人奖励积分',
    prop: 'totalIntegral',
    width: 135
  },
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}
export default {
  name: 'membersFissile',
  components: {
    MemberTeamDialog,
    PagedExport
  },
  data() {
    return {
      totalPage: 0,
      tableTitle: TableColumnList,
      tableData: [],
      auditStatusData: [
        { label: '合伙人', value: 'ACCEPTED' },
        { label: '非合伙人', value: 'PENDING' },
      ],
      list: [],
      total: 0,
      listLoading: false,
      partnerFunctionStatus: '',
      listQuery: {
        model: {
          partnerOrInvite: "",
          auditStatus: '',
          timeRange: []
        }
      },
    }
  },
  methods: {
    exportExcel(data) {
      const query = this.getQuery()
      query.model = { ...query.model, ...data }
      exportPartner(query).then(() => {
        MessageConfirmExport()
      })
    },
    getQuery() {
      let listQuery = deepClone(this.listQuery)
      const model = listQuery.model
      if (Array.isArray(model.timeRange) && model.timeRange.length > 0) {
        listQuery.model.startTime = model.timeRange[0]
        listQuery.model.endTime = model.timeRange[1]
      } else {
        listQuery.model.startTime = undefined
        listQuery.model.endTime = undefined
      }
      return listQuery
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      this.listLoading = true
      try {
        const res = await getDistributionPartnerList(this.getQuery())
        this.totalPage = res.data.pages
        this.total = res.data.total
        return res
      } catch (e) {
        this.listLoading = false
      }
    },
    handleReset() {
      this.listQuery.model = {
        partnerOrInvite: '',
        timeRange: []
      }
      this.onSubmit()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    showMemberTeamDialog(id) {
      this.$refs.memberTeamDialogRef.show(id)
    }
  },
}

</script>

<style lang="scss" scoped></style>
