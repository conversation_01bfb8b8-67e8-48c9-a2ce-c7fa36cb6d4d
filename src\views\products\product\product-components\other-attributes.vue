<template>
  <el-form label-width="80px" label-position="right" :disabled="action==='SHOW'">
    <div class="other-attributes">
      <form-item-title title="其他属性"/>
      <el-row :gutter="80">
        <el-col
          :span="6"
          v-for="(item, idx) of form"
          :key="idx">
          <el-form-item label-width="auto" class="new-item" v-if="item.isNew">
            <el-input class="new-item-name" v-model="item.name"></el-input>
            <el-input class="new-item-value" v-model="item.value"></el-input>
            <el-button class="new-item-delete" type="text" @click="removeItem(idx)">删除</el-button>
          </el-form-item>
          <el-form-item :label="item.name" v-else>
            <el-input v-model="item.value"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type="text" @click="addItem">+添加字段</el-button>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script>
import formItemTitle from '@/views/products/common-components/form-item-title'
export default {
  components: { formItemTitle },
  props: {
    action: {
      type: String
    },
    isNumber2: {
      type: Function
    },
    isInt7: {
      type: Function
    }
  },
  data () {
    return {
      form: []
    }
  },
  methods: {
    addItem () {
      this.form.push({ name: '', value: '', isNew: true })
    },
    removeItem (idx) {
      this.form.splice(idx, 1)
    },

    restore (data) {
      this.form = data.productExtensionFieldList.map(item => ({
        name: item.name,
        value: item.value
      }))
    },
    getData () {
      for (let i = 0; i < this.form.length; i++) {
        if (!this.form[i].name || !this.form[i].value) {
          this.$message.error('请填写完整属性信息');
          return false;
        }
      }
      return {
        productExtensionFieldList: this.form
      };
    },
    setForm (data) {
      this.form = (data.productExtensionFieldList || []).map(item => {
        item.isNew = true;
        return item;
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.other-attributes{
  .el-row{
    padding-right: 30px;
    .el-col{
      margin-bottom: 10px;
      .new-item{
        ::v-deep{
          .el-form-item__content{
            display: flex;
            align-items: center;
            .new-item-name{
              width: 80px;
              padding-right: 12px;
            }
            .new-item-value{
              width: calc(100% - 80px);
            }
            .new-item-delete{
              position: absolute;
              right: -40px;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }
      }
      .el-form-item{
				margin-bottom: 0;
			}
    }
  }
}
</style>
