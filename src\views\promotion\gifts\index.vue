<template>
  <div :class="$style.container">

    <im-search-pad :has-expand="false" :model="model" @reset="reload" @search="handleSearch">
      <im-search-pad-item prop="keyword">
        <el-input v-model.trim="model.keyword" placeholder="商品名称/商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="warehouseIds">
        <el-select v-model="model.warehouseIds" collapse-tags multiple placeholder="所在仓库" clearable>
          <el-option v-for="item in storeArr" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>

    <div :class="$style.main">
      <tabs-layout ref="tabs-layout" :tabs="tabs">
        <template slot="button">
          <el-button @click="handleRefersh">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-present:add','sale-platform-promotion-present:add'])" @click="onAdd" type="primary" icon="el-icon-plus">新增赠品</el-button>
        </template>
      </tabs-layout>

      <table-pager ref="todoTable" :pageSize="pageSize" :remote-method="load" :options="tableTitle"
        :data.sync="tableData" :operationWidth="100">
        <el-table-column slot="pictIdS" label="产品主图" align="center" class-name="img-cell">
          <template slot-scope="scope">
            <!-- <img :src="scope.row.pictIdS|imgFilter" width="50px" height="50px"> -->
            <el-image style="width:50px;height:50px" :src="scope.row.pictIdS | imgFilter"
              :preview-src-list="scope.row.pictIdS | imageFilterPreview"></el-image>
          </template>
        </el-table-column>
        <el-table-column label="ERP商品编码/仓库" slot="erpCode" width="200px">
          <template slot-scope="{row}">
            <span>编码：{{row.erpCode || '无'}}</span> <br />
            <span>仓库：{{ row.warehouseName || '无' }}</span>
          </template>
        </el-table-column>

        <div slot-scope="{row}">
          <!-- <el-button v-if="row.limitQuantity === '999999'" type="text">编辑</el-button>
                    <el-button v-else type="text">移除</el-button> -->
          <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-present:edit','sale-platform-promotion-present:edit'])" type="text" @click="onEditGifts(row.id)">编辑</el-button>
          <el-divider direction="vertical" />
          <del-button v-if="checkPermission(['admin', 'sale-saas-promotion-present:del','sale-platform-promotion-present:del'])" @handleDel="handleDeleteGifts" text="确认删除该赠品吗？" btnText="删除" :targetId="row.id">
          </del-button>
        </div>
      </table-pager>
    </div>

  </div>
</template>
<script>
  import DelButton from '@/components/eyaolink/delElButton/index.vue'
  import { getAllStore } from "@/api/products/store";
  import {
    fetchGiftsList,
    deleteGitts
  } from "@/api/gifts"
  import checkPermission from '@/utils/permission';

  const TableColumns = [{
      label: '赠品图片',
      name: 'pictIdS',
      prop: 'pictIdS',
      slot: true
    },
    {
      label: 'ERP编码/仓库',
      name: 'erpCode',
      prop: 'erpCode',
      width: 110,
      slot: true
    },
    {
      label: '赠品名称',
      name: 'productName',
      prop: 'productName',
      width: 200
    },
    {
      label: '生产厂家',
      name: 'manufacturer',
      prop: 'manufacturer'
    },
    {
      label: '规格',
      name: 'spec',
      prop: 'spec'
    },
    {
      label: '单位',
      name: 'unit',
      prop: 'unit'
    },
    {
      label: '换购价（元）',
      name: 'salePrice',
      prop: 'salePrice',
      width: 110
    },
    {
      label: '赠品库存',
      name: 'stockQuantity',
      prop: 'stockQuantity'
    },
    {
      label: '已赠数量',
      name: 'giveawayNum',
      prop: 'giveawayNum'
    },
    {
      label: '操作时间',
      name: 'updateTime',
      prop: 'updateTime'
    },
  ].map((v, i) => ({
    key: i,
    ...v
  }))

  export default {
    name: 'merchandise',
    data() {
      return {
        tableData: [],
        tableTitle: TableColumns,
        tabs: [{
          name: "赠品管理",
          value: 0
        }],
        model: {
          keyword: '',
          warehouseIds: []
        },
        storeArr: [],
        pageSize: 10
      }
    },
    async mounted() {
      await this.getStoreList();
    },
    methods: {
      checkPermission,
      /**
       * @description 获取全部仓库的下拉列表
       */
      getStoreList() {
        getAllStore().then(res => {
          const data = res.data;
          if (data && data.length) {
            data.forEach(item => {
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeArr.push(obj)
            })
          }
        })
      },
      onEditGifts(id) {
        this.$router.push({
          path: '/promotion/gifts/edit',
          query: {
            id
          }
        })
      },
      async handleDeleteGifts(id) {
        const result = await deleteGitts(id)
        if (result.code === 0) {
          this.$message.success('删除成功！')
          this.$refs.todoTable.doRefresh({
            page: 1,
            pageSize: 10
          })
        }
      },
      onAdd() {
        this.$router.push({
          path: '/promotion/gifts/edit'
        })
      },
      handleSearch() {
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      },
      handleRefersh() {
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      },
      reload() {
        this.model = {}
        this.$refs.todoTable.doRefresh()
      },
      async load(params) {
        let listQuery = {
          model: this.model,
          order: "descending"
        };
        Object.assign(listQuery, params)
        let result = await fetchGiftsList(listQuery);
        this.totalPage = result.data.pages;
        this.total = result.data.total;
        return result
      },
    },
    components: {
      DelButton
    }
  }

</script>

<style lang="scss" module>
  .container {
    width: 100%;

    .main {
      background-color: #ffffff;
      padding: 8px 20px 20px;
      margin-top: 16px;
    }
  }

</style>
