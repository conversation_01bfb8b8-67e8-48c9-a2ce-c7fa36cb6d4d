<template>
  <div>
    <el-form ref="defaultForm" :rules="rules" :inline="true"  label-width="150px" size="mini" :model="defaultForm">
          <el-form-item label="默认起配送金额：" prop="startAmount">
            订单总金额达到
            <el-input-number v-model="defaultForm.startAmount" controls-position="right" :min="0" style="width: 150px;display: inline-block;margin:0 10px;" />
            元起配，运费
            <el-tooltip placement="top">
              <div slot="content">运费设置为0元即满足起配条件时包邮</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip></el-form-item>
          <el-form-item prop="freightAmount">
            <el-input-number style="width: 100px;display: inline-block;margin:0 10px;" controls-position="right" :min="0" v-model="defaultForm.freightAmount" @blur="onInputBlur" @change="changeVal"/>元</el-form-item>
          <el-form-item>
            <el-checkbox v-model="defaultForm.checked" :disabled="isCheck" class="isFree">包邮</el-checkbox>
          </el-form-item>
      <el-form-item v-if="defaultForm.checked===true" prop="freeAmount">
        订单满 <el-input-number  width="50px" v-model="defaultForm.freeAmount" controls-position="right" :min="0" style="width: 150px;display: inline-block;margin:0 10px;" :disabled="isCheck"/>元包邮
      </el-form-item>
          <slot></slot>
    </el-form>
  </div>
</template>

<script>
    export default {
      name: "defaultForm",
      props: ['defaultDelivery','defaultForm'],
      data() {
        var validate = (rule, value, callback) => {
          if (value !== '' && value < this.defaultForm.startAmount) {
            return callback(new Error('包邮金额不能小于起配金额'));
          }
          callback();
          }
        return {
         /* checked: false,
          startAmount: '',//起配金额
          freeAmount: '',//包邮金额,
          whetherFreeDelivery: 'N',//0否1是
          freightAmount: '',//运费*/
          rules: {
            startAmount: [{required: true,message: '请输入起配金额', trigger: 'blur'}],
            freightAmount: [{required: true,message: '请输入运费', trigger: 'blur'}],
            freeAmount: [{ validator: validate, trigger: 'blur' }]
          },
          isCheck: false,
          id: ''
        }
      },
      methods: {
        onInputBlur() {
          if(this.defaultForm.freightAmount === 0) {
            this.defaultForm.checked = true
            this.defaultForm.freeAmount = this.defaultForm.startAmount
            this.isCheck = true
          } else {
            this.isCheck = false
            this.defaultForm.freeAmount = ''
          }
        },
        changeVal(val) {
          if(val === 0) {
            this.isCheck = true
          } else {
            this.isCheck = false
          }
        },
        resetForm() {
          this.$refs['defaultForm'].resetFields()
        },
        validateForm() {
          let flag = null
          this.$refs['defaultForm'].validate(valid => {
            if (valid) {
              flag = true

            } else {
              flag = false
            }
          })

          return flag
        }
      },
      watch: {
        defaultDelivery: {
          deep: true,
          immediate: true,
          handler: function(newVal,oldVal) {
            if(!newVal) {
              return
            }
              this.defaultForm.startAmount = newVal.startAmount,//起配金额
              this.defaultForm.freeAmount = newVal.freeAmount,//包邮金额,
              this.defaultForm.whetherFreeDelivery = newVal.whetherFreeDelivery,//0否1是
              this.defaultForm.freightAmount = newVal.freightAmount//运费
              this.id = newVal.id
              if (newVal.freightAmount === 0||newVal.freightAmount==='0.00') {
                this.isCheck = true
              }
              if (newVal.whetherFreeDelivery && newVal.whetherFreeDelivery.code === 'Y') {
                this.defaultForm.checked = true
              } else {
                this.defaultForm.checked = false
              }
          }

        }
      }
    }
</script>

<style scoped>

</style>
