<template>
  <div class="detail-wrapper">
    <p class="detail-title">
      {{title}}
    </p>
    <div class="fr" style="margin-top: -60px;margin-right: 20px;">
      <el-button>取消</el-button>
      <el-button type="primary" @click="onsubmit('roleForm')" >保存</el-button>
    </div>
    <div class="detail-items">
    <el-form label-width="100px" ref="roleForm" :model="roleForm" :rules="rules">
      <el-form-item label="角色名称：" prop="name">
        <el-input placeholder="请输入角色名称" style="width: 400px;" v-model="roleForm.name"></el-input>
      </el-form-item>
      <el-form-item label="角色描述：">
        <el-input placeholder="请输入角色描述，最多50个字" type="textarea" style="width: 400px;" v-model="roleForm.describe" maxlength="50" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="权限设置：">
        <el-tree
          ref="tree"
          :data="menuData"
           show-checkbox
           accordion
          :props="defaultProps"
           node-key="id"
        ></el-tree>
      </el-form-item>
    </el-form>
    </div>
  </div>
</template>
<script>
  import { menuTree,addRole,editRole,roleDetail,getRoleId } from '@/api/settingCenter'
export default {
data() {
  return {
    title: '新增角色',
    roleForm: {
      name: '',
      describe: ''
    },
    rules: {
      name: [{required: true, message: '请输入角色名称',trigger: 'blur'}]
    },
    orgList: [],
    menuData: [],
    id: '',
    defaultProps: {
      children: 'children',
      label: 'label'
    }
  };
},
props: {},
  methods: {
    onsubmit(formName){
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.id) {
            let params = {
              role: {
                "describe": this.roleForm.describe,
                "name": this.roleForm.name,
                "orgList": [],
                "status": true,
                dsType: 'ALL',
                id: this.id
              },
              "roleAuthority": {
                "menuIdList": this.$refs.tree.getCheckedKeys(),
                "resourceIdList": [],
                "roleId": ''
              }
            }
            editRole(params).then(res => {
              this.$message.success('编辑角色成功！')
              this.$router.push({path: './index'})
            }).catch()
          } else {
            let params = {
              role: {
                "describe": this.roleForm.describe,
                "name": this.roleForm.name,
                "orgList": [],
                "status": true,
                dsType: 'ALL',
                id: this.id
              },
              "roleAuthority": {
                "menuIdList": this.$refs.tree.getCheckedKeys(),
                "resourceIdList": [],
                "roleId": ''
              }
            }
            addRole(params).then(res => {
              this.$message.success('新增角色成功！')
              this.$router.push({path: './index'})
            }).catch()
          }
        } else {
          return false;
        }
      });
    },
    async getTree() {
      const { data } = await menuTree()
      this.menuData = data
    },
    async getDetail(id) {
      const {data} = await roleDetail(id)
      this.roleForm = {
        name: data.name,//
        describe: data.describe,
      }
    },
    async getRole(id) {
      const { data } = await getRoleId(id)
      this.orgList = data.menuIdList
      this.$refs.tree.setCheckedKeys(this.orgList);
    }
  },
  mounted() {
    this.getTree()
    this.id = this.$route.query.id
    if(this.id) {
      this.getDetail(this.id)
      this.isEdit = true
      this.title = '编辑角色'
      this.getRole(this.id)
    }
  },
};
</script>
<style lang="less" scoped></style>
