<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="saleManName">
        <el-input v-model.trim="model.saleManName" @keyup.enter.native="searchLoad" placeholder="请输入执行人姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="model.productName" @keyup.enter.native="searchLoad" placeholder="请输入商品名称" />
      </im-search-pad-item>
    </im-search-pad>
    <!--  -->
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{name:'辅助陈列'}]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>
      <!-- table模块 -->

      <!-- 分页tab -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="false" :pageSize="pageSize" :operation-width="150">
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-business-productshowing:view', 'sale-platform-business-productshowing:view'])" @click="hendleDetail(row)">陈列商品</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!-- 客户列表弹窗 -->
    <el-dialog title="陈列商品" v-dialogDrag append-to-body v-if="dialogStatus" width="70%" :visible.sync="dialogStatus" :before-close="closeDialogFun">
      <product-list @closeDia="closeDialogFun" :currentId="currentId"></product-list>
    </el-dialog>
  </div>
</template>


<script>
  import {
    productShowingPage,
    productShowingDetail
  } from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api

  const TableColumns = [{
      label: "执行人编码",
      name: "saleManCode",
      prop: "saleManCode",
      width: "150"
    },
    {
      label: "执行人姓名",
      name: "saleManName",
      prop: "saleManName",
      width: "120"
    },
    {
      label: "执行时间",
      name: "createTime",
      prop: 'createTime',
      width: "150",
    },
    {
      label: "客户名称",
      name: "purMerchantName",
      prop: 'purMerchantName',
      width: '180'
    },
    {
      label: "商品数",
      name: "productArrayCount",
      prop: 'productArrayCount',
      width: "100"
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import productList from '@/views/salesmanCenter/business/components/productList.vue'
  import checkPermission from '@/utils/permission';
  export default {
    //import引入的组件
    components: {
      productList
    },

    data() {
      return {
        isExpand: false,
        model: {
          saleManName: '',
          purMerchantName: '',
          productName: '',
        },
        previewDetail: [],
        tableData: [],
        tableColumns: TableColumnList,
        pageSize: 10,
        currentId:'',
        dialogStatus:false,
      };
    },
    //方法集合
    methods: {
      checkPermission,
      async load(params) {
        let listQuery = {
          model: {
            ...this.model
          }
        };
        Object.assign(listQuery, params);
        return await productShowingPage(listQuery)
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      hendleDetail(row) {
        this.currentId = row.id;
        this.dialogStatus = true;
      },
      closeDialogFun() {
        this.dialogStatus = false;
        this.reload();
      },
    },
  };

</script>


<style lang='scss' scoped>
  .table {
    .table_box {
      line-height: 40px;
      padding-right: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-left: 1px solid #ccc;
      border-top: 1px solid #ccc;
      border-right: 1px solid #ccc;
      background-color: #fafafa;

      .table_left {
        //  border-left: 1px solid #ccc;
        //  border-top: 1px solid #ccc;
        display: flex;

        .table_index {
          width: 50px;
          text-align: center;
          border-right: 1px solid #ccc;
          // border-bottom: 1px solid #ccc;
        }

        .table_item {
          margin: 0 30px;
        }
      }

      // &:hover {
      //   background-color: #ecf5ff;
      // }
    }

    &:nth-last-child(1) {
      border-bottom: 1px solid #ccc;
    }
  }

  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

</style>
