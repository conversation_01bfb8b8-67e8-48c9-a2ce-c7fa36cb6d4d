<template>
  <el-dialog title="积分抵扣设置" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" >
      <el-form-item label="积分抵扣" prop="whetherPointsDeduction">
        <el-radio-group v-model="form.whetherPointsDeduction">
          <div style="margin-top: 0px;">
            <el-radio label="Y">可积分抵扣
              <el-input-number v-model="form.pointsDeductionRatio" controls-position="right" :min="0" :max="100" />%
            </el-radio><br />
          </div>
          <div style="margin-top: 15px;">
            <el-radio label="N">不可积分抵扣</el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { batchUpdatePointsDeduction } from '@/api/retailStore'

export default {
  name: 'pointDeductionDialog',
  data() {
    return {
      visible: false,
      loading: false,
      productName: '',
      form: {
        ids: [],
        whetherPointsDeduction: '',
        pointsDeductionRatio: 0,
      },
      rules: {
        whetherPointsDeduction: [
          { required: true, message: '请选择是否积分抵扣', trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    show(row) {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.form = { ...row }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        batchUpdatePointsDeduction(this.form).then(() => {
          this.$emit('submitSuccess')
          this.hide()
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
