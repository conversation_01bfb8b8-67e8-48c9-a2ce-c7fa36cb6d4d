<template>
	<div>
		<clients-model @tab-change="handleTabChange" ref="model" v-model="multipleSelection" @close="handleCancel"
			@ok="submit" :tabs="tabs" :visible="visible">
			<template slot="button" v-if="tabCode === 'ALREADY'">
				<el-button @click="handleBatchTransfer" :disabled="!multipleSelection.length">批量转移</el-button>
			</template>
		</clients-model>
		<SelectTakeoverSalesman :visible.sync="salesmanVisible" @onsuccess="handleSelectSuccess"
			:salesMerchantId="multipleSelection" :salemanName="salemanName" :salesmanId="currentId" />
	</div>
</template>

<script>
import {
	purMerchants,
	noPurMerchants,
	statisticalQuantity,
	bindingCustomers,
	unbindingCustomers
} from "@/api/salemanCenter/index";

import SelectTakeoverSalesman from './SelectTakeoverSalesman.vue'
import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'

const TableColumns = [
	{
		prop: 'erpCode',
		name: "erpCode",
		label: '客户编码',
		slot: true
	},//新增字段
	{
		prop: 'name',
		name: 'name',
		label: '客户名称',
		width: 80
	},
	{
		prop: 'merchantTypeName',
		name: 'merchantTypeName',
		label: '企业类型',
	},
	{
		prop: 'ceoName',
		name: 'ceoName',
		label: '负责人',
	},
	{
		prop: 'ceoMobile',
		name: 'ceoMobile',
		label: '负责人电话',
	},
	{
		prop: 'region',
		name: 'region',
		label: '所在地区',
		slot: true
	}
];

export default {
	name: 'CustomersDialog',
	components: {
		SelectTakeoverSalesman,
		ClientsModel
	},

	props: {
		visible: {
			type: Boolean,
			default: false
		},
		currentId: {
			type: String,
			default: () => {
				return ''
			}
		},
		salemanName: {
			type: String,
			default: () => {
				return ''
			}
		}
	},
	data() {
		return {
			options: [],
			// 获取row的key值: 
			getRowKeys(row) {
				return row.id;
			},
			isExpand: false,
			model: {
				erpCode: '',
				name: '',
				ceoName: '',
				ceoMobile: '',
				salesmanId: ''
			},
			tabCode: "ALREADY",
			page: 1,
			limit: 10,
			multipleSelection: [],
			tabNum: {
				bindNumber: 0,
				unboundNumber: 0,
			},
			salesmanVisible: false
		}
	},
	mounted() {
		this.currentId && this.fetchStatistical();
	},
	watch: {
		visible(v) {
			if (v) this.fetchStatistical();
		}
	},
	computed: {
		tabs() {
			let salesmanId = this.currentId
			return [
				{
					text: `已关联客户(${this.tabNum.bindNumber})`,
					value: "ALREADY",
					request: purMerchants,
					defaultValues: {
						salesmanId
					},
					button: {
						okText: '移除'
					}
				},
				{
					text: `未关联客户(${this.tabNum.unboundNumber})`,
					value: "NOT",
					request: noPurMerchants,
					defaultValues: {
						salesmanId
					},
					button: {
						okText: '添加'
					}
				},
			];
		},
	},
	methods: {
		handleTabChange(cur, index) {
			this.tabCode = cur.value
		},

		/**
		 * @description 选择业务员的回调
		 */
		handleSelectSuccess() {
			this.$refs.model.refresh();
		},
		/**
		 * @description 批量转移
		 * <AUTHOR>
		 */
		handleBatchTransfer() {
			this.salesmanVisible = true
		},
		handleCancel() {
			this.$emit("update:visible", false);
		},
		submit(multipleSelection, index) {
			this.multipleSelection = multipleSelection;
			if (this.multipleSelection.length == 0) {
				return;
			}
			let list = this.multipleSelection.map((item) => {
				return item.id;
			});
			list = {
				salesmanId: this.currentId,
				purMerchantIds: [...list]
			};
			if (index === 0) {
				this.$confirm("您确定批量移除这些客户吗?", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				}).then((res) => {
					unbindingCustomers({ ...list }).then((data) => {
						if (data.code === 0) {
							this.$refs.model.refresh()
							this.fetchStatistical()
						}
					});
				});
			} else {
				// 批量添加
				this.$confirm("您确定批量添加这些客户吗?", "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				}).then((res) => {
					bindingCustomers({ ...list }).then((data) => {
						if (data.code === 0) {
							this.$refs.model.refresh()
							this.fetchStatistical()
						}
					});
				});
			}
		},
		
		fetchStatistical() {
			const query = { ...this.model, salesmanId: this.currentId }
			Object.keys(query).forEach((key) => {
				if (!query[key]) delete query[key]
			})
			let params = {
				current: this.page,
				map: {},
				model: {
					...query,
				},
				order: "descending",
				size: this.limit,
				sort: "id",
			};
			statisticalQuantity(params).then(res => {
				if (res.code === 0) {
					this.tabNum = res.data
				}
			})
		},
		
	}
}
</script>

<style lang="scss" scoped>
.page-row {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	color: #505465;
	font-size: 13px;
	margin-top: 16px;
}

.bottom_btn {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-right: 20px;
}

.componentsList {
	::v-deep .el-dialog__headerbtn {
		z-index: 99999;
	}

	::v-deep .el-dialog__header {
		padding: 0 !important;
		border-bottom: none !important;
	}

	::v-deep .el-dialog__body {
		padding-top: 10px !important;
	}

	::v-deep .el-dialog--center .el-dialog__body {
		padding: 25px 0 30px !important
	}

	::v-deep .varietiesBan-list-container .varietiesBan-list-tabs-wrapper .varietiesBan-list-tabs {
		padding-left: 20px;
	}

	.im-search-pad {
		margin-left: 20px;
	}
}
</style>
