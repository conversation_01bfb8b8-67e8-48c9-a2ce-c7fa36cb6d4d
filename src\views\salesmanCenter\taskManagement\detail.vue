<template>
  <div class="taskDetail">
    <div class="taskDetail_Header">
      <span class="title">任务达成明细</span>
      <el-button @click="toBack">返回</el-button>
    </div>
    <div class="taskDetail_Content">
      <p>{{ taskTitle }}</p>
      <div class="taskDetail_Content_search">
        <!-- <el-select
          v-model="searchForm.regionDepartId"
          style="width: 180px"
          placeholder="大区"
          @change="getData()"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="searchForm.provinceDepartId"
          style="width: 180px"
          placeholder="省区"
          @change="getData()"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="searchForm.departId"
          style="width: 180px"
          placeholder="主管"
          @change="getData()"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select> -->
        <el-cascader
          style="width: 350px"
          placeholder="大区/省区/主管区"
          v-model="searchForm.orgList"
          :options="orgOptions"
          :props="{ checkStrictly: true, value: 'id', label: 'name' }"
          clearable
          @change="getData()"
        ></el-cascader>
        <el-input
          v-model="searchForm.salesmanErpCodeOrName"
          style="width: 180px"
          placeholder="员工姓名/工号"
          clearable
          @clear="getData()"
          @blur="getData()"
          @keydown.enter.native="getData()"
        ></el-input>
        <el-date-picker
          v-if="type === 3"
          v-model="searchForm.month"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
          @change="handleMonthRange"
          :clearable="false"
        >
        </el-date-picker>
        <el-button type="primary" @click="getData">搜索</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="toDownload">导出</el-button>
      </div>
      <div class="taskDetail_Content_table">
        <table-pager
          ref="pager-table"
          :pageSize="10"
          :options="tableTitle"
          :data.sync="tableData"
          :remote-method="load"
          :isNeedButton="false"
        >
        </table-pager>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getOrgaStructure,
  getDepartmentList,
  taskReportDetail3,
  taskReportDetail4,
  taskReportExport3,
  taskReportExport4,
} from "@/api/salemanCenter/taskManagement.js";
import { MessageConfirmExport } from "@/utils/index";
import dayjs from "dayjs";
export default {
  name: "taskDetail",
  components: {},
  props: {},
  data() {
    return {
      type: null,
      taskTitle: "",
      taskStartDate: "",
      taskEndDate: "",
      searchForm: {
        orgList: null,
        salesmanErpCodeOrName: "",
        month: null,
      },
      tableTitle: [],
      tableData: [],
      // typeOptions: [],
      orgOptions: [],
      tableTitleGroup: {
        // 业绩任务
        3: [
          {
            key: 1,
            prop: "statisticsDay",
            name: "statisticsDay",
            label: "日期",
            width: "",
            align: "center",
          },
          {
            key: 2,
            prop: "regionDepartName",
            name: "regionDepartName",
            label: "大区",
            width: "",
            align: "center",
          },
          {
            key: 3,
            prop: "provinceDepartName",
            name: "provinceDepartName",
            label: "省区",
            width: "",
            align: "center",
          },
          {
            key: 4,
            prop: "departName",
            name: "departName",
            label: "主管区",
            width: "",
            align: "center",
          },
          {
            key: 5,
            prop: "saleManName",
            name: "saleManName",
            label: "员工姓名",
            width: "",
            align: "center",
          },
          {
            key: 6,
            prop: "salesmanErpCode",
            name: "salesmanErpCode",
            label: "工号",
            width: "",
            align: "center",
          },
          {
            key: 7,
            prop: "money",
            name: "money",
            label: "结算金额(元)",
            width: "",
            align: "center",
          },
          {
            key: 8,
            prop: "monthPerformanceIndicators",
            name: "monthPerformanceIndicators",
            label: "当月指标(元)",
            width: "",
            align: "center",
          },
          {
            key: 9,
            prop: "indicatorCompletionRate",
            name: "indicatorCompletionRate",
            label: "指标完成率",
            width: "",
            align: "center",
          },
        ],
        // 客户发展数达成明细
        4: [
          {
            key: 1,
            prop: "firstOrgName",
            name: "firstOrgName",
            label: "大区",
            width: "",
            align: "center",
          },
          {
            key: 2,
            prop: "secondOrgName",
            name: "secondOrgName",
            label: "省区",
            width: "",
            align: "center",
          },
          {
            key: 3,
            prop: "thirdOrgName",
            name: "thirdOrgName",
            label: "主管区",
            width: "",
            align: "center",
          },
          {
            key: 4,
            prop: "salesmanName",
            name: "salesmanName",
            label: "员工姓名",
            width: "",
            align: "center",
          },
          {
            key: 5,
            prop: "erpCode",
            name: "erpCode",
            label: "工号",
            width: "",
            align: "center",
          },
          {
            key: 6,
            prop: "orderCustomerNum",
            name: "orderCustomerNum",
            label: "下单客户总家数",
            width: "110",
            align: "center",
          },
          {
            key: 7,
            prop: "orderKeyCustomerNum",
            name: "orderKeyCustomerNum",
            label: "下单客户·重点客户家数",
            width: "130",
            align: "center",
          },
          {
            key: 8,
            prop: "stageName",
            name: "stageName",
            label: "已达成阶段",
            width: "",
            align: "center",
          },
        ],
      },
      loginInfo: {},
    };
  },
  computed: {},
  watch: {},
  created() {
    this.id = this.$route.query.id;
    this.type = Number(this.$route.query.type);
    this.taskTitle = `${this.$route.query.taskTypeName ?? ""}  ${
      this.$route.query.taskTime ?? ""
    }`;
    this.taskStartDate = this.$route.query.taskTime.split("至")[0];
    this.taskEndDate = this.$route.query.taskTime.split("至")[1];
    this.chooseTableTitle(this.type);
    this.getOrgOptions();
    let loginInfo = localStorage.getItem("LOCAL_USER");
    if (loginInfo) this.loginInfo = JSON.parse(loginInfo);
    this.searchForm.month = this.taskStartDate
      ? [
          dayjs(this.taskStartDate).format("YYYY-MM"),
          dayjs(this.taskEndDate).format("YYYY-MM"),
        ]
      : null;
  },
  mounted() {},
  methods: {
    // 获取组织架构
    async getOrgOptions() {
      let temp = await getOrgaStructure();
      if (temp.code !== 0 || !temp.data.length) return;
      let id = temp.data[0].id;
      let res = await getDepartmentList(id);
      if (res.code === 0 && res.data[0]) {
        this.orgOptions = res.data[0]?.children ?? [];
      }
    },
    // 返回任务管理页面
    toBack() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/salesmanCenter/taskManagement");
    },
    // 组件查询
    async load(params) {
      try {
        let temp = {
          ...(params ?? {}),
          model: {
            ...this.searchForm,
            startTime: this.searchForm.month?.[0] ?? null,
            endTime: this.searchForm.month?.[1] ?? null,
            taskId: this.id ?? null,
            erpCode: this.searchForm.salesmanErpCodeOrName ?? null,
            orgId: this.searchForm.orgList?.length
              ? this.searchForm.orgList[this.searchForm.orgList.length - 1]
              : null,
            regionDepartId: this.searchForm.orgList?.[0] ?? null,
            provinceDepartId: this.searchForm.orgList?.[1] ?? null,
            departId: this.searchForm.orgList?.[2] ?? null,
            saleMerchantId: this.loginInfo.saleMerchantId ?? null,
          },
        };
        let res = null;
        switch (this.type) {
          case 3: // 业绩任务
            res = await taskReportDetail3(temp);
            break;
          case 4: // 客户发展数达成明细
            res = await taskReportDetail4(temp);
            break;
        }
        return res || { data: { records: [] } };
      } catch (error) {
        console.log(error);
      }
    },
    // 查询
    getData() {
      this.$refs["pager-table"].doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    // 重置
    reset() {
      this.searchForm.orgList = null;
      this.searchForm.salesmanErpCodeOrName = "";
      this.searchForm.month = this.taskStartDate
        ? [
            dayjs(this.taskStartDate).format("YYYY-MM"),
            dayjs(this.taskEndDate).format("YYYY-MM"),
          ]
        : null;
      this.getData();
    },
    // 下载
    async toDownload() {
      let temp = {
        ...this.searchForm,
        startTime: this.searchForm.month?.[0] ?? null,
        endTime: this.searchForm.month?.[1] ?? null,
        taskId: this.id ?? null,
        erpCode: this.searchForm.salesmanErpCodeOrName ?? null,
        orgId: this.searchForm.orgList?.length
          ? this.searchForm.orgList[this.searchForm.orgList.length - 1]
          : null,
        regionDepartId: this.searchForm.orgList?.[0] ?? null,
        provinceDepartId: this.searchForm.orgList?.[1] ?? null,
        departId: this.searchForm.orgList?.[2] ?? null,
        saleMerchantId: this.loginInfo.saleMerchantId ?? null,
      };
      let res = {};
      switch (this.type) {
        case 3: // 业绩任务
          res = await taskReportExport3(temp);
          break;
        case 4: // 客户发展数达成明细
          res = await taskReportExport4(temp);
          break;
      }
      if (res?.code == 0 && res?.data) {
        MessageConfirmExport();
      }
    },
    // 根据type选择表头
    chooseTableTitle(type) {
      this.tableTitle = this.tableTitleGroup[type];
    },
    // 日期限制
    handleMonthRange(value) {
      if (!value) {
        this.searchForm.month = value;
        return;
      }
      let f = false;
      let start = value[0];
      let end = value[1];
      if (dayjs(start).isBefore(dayjs(this.taskStartDate), "months")) {
        f = true;
        start = dayjs(this.taskStartDate).format("YYYY-MM");
      }
      if (dayjs(this.taskEndDate).isBefore(dayjs(end), "months")) {
        f = true;
        end = dayjs(this.taskEndDate).format("YYYY-MM");
      }
      f ? this.$message.error("筛选日期不得超出任务周期之外！") : null;
      this.$nextTick(() => {
        this.searchForm.month = [start, end];
        this.getData();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.taskDetail {
  background: #fff;
  // height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  .taskDetail_Header {
    height: 56px;
    min-height: 56px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
    }
  }
  .taskDetail_Content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;
    > * + * {
      margin-top: 20px;
    }
    .taskDetail_Content_search {
      > * + * {
        margin-left: 10px;
      }
    }
    .taskDetail_Content_table {
      flex: 1;
      height: 0;
    }
  }
}
</style>
