<template>
  <div :class="$style.container">
    <!-- <div :class="$style.btn" v-if="!disabled">
            <el-button type="primary" @click="onSelectClick()">选择商品</el-button>
        </div> -->
    <!-- 已选择商品列表 start -->
    <!-- <table-pager :paging="false" :options="tableColumns" :data="data" :is-need-button="!disabled">
            <template v-for="item in tableColumns" :slot="item.prop">
                <el-table-column v-if="item.slot" :label="item.label" :width="item.width" :key="item.prop">
                    <template slot-scope="{ row }">
                        <img v-if="item.prop === 'pictIdS'" :src="row[item.name] | imgFilter" width="50px"
                            height="50px" />
                        
                        <template v-if="item.prop === 'erpCode'">
                            <span>编码：{{row.erpCode || '无'}}</span> <br />
                            <span>仓库：{{ row.warehouseName || '无' }}</span>
                        </template>
                    </template>
                </el-table-column>
            </template>

            <template slot-scope="{ row }">
                <del-button @handleDel="handleDelete" text="确认删除该商品吗？" btnText="删除" :targetId="row[matchField]">
                </del-button>
            </template>
        </table-pager> -->
    <front-end-page ref="frontEndPage" @openProductModel="onSelectClick" :data.sync="data" :options="tableColumns"
      :pageSize="pageSize" :operation-width="120" :total="totalNum" :operationWidth="100" :currentPage="currentPage"
      :selection="true" @patchDelete="handleDelete" :disableHandle="disabled" :is-need-button="!disabled">
      <el-table-column slot="pictIdS" label="产品主图" align="center" width="80">
        <template v-slot="scope">
          <el-image style="width:40px;height:40px" :src="scope.row.pictIdS | imgFilter"
            :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
        </template>
      </el-table-column>
      <el-table-column slot="erpCode" width="180" label="ERP商品编码/仓库">
        <template v-slot="{ row }">
          <span>编码：{{ row.erpCode || '无' }}</span><br />
          <span>仓库：{{ row.warehouseName || '无' }}</span>
        </template>
      </el-table-column>
      <div slot-scope="scope" align="center">
        <el-row class="table-edit-row">
          <span class="table-edit-row-item">
            <el-button type="text" @click="handleDelete([scope.row])">
              删除</el-button>

          </span>
        </el-row>
      </div>
    </front-end-page>
    <!-- 已选择商品列表 end -->
    <div v-if="visible">
      <products-modal :isBackIdOrInfo="true" ref="products-modal" :productVisible="visible"
        @closeProductDia="closeProductDia" :check-list="data" @change="handleAddProducts" />
    </div>
    <!-- 弹窗选择商品 end -->
  </div>
</template>

<script>
  import _ from "lodash"
  import DelButton from '@/components/eyaolink/delElButton/index.vue'
  import ProductsModal from '@/components/eyaolink/productModel/index'

  const TableColumns = [{
      label: '主图',
      name: 'pictIdS',
      prop: 'pictIdS',
      slot: true,
      width: 72
    },
    {
      label: 'ERP商品编码/仓库',
      name: 'erpCode',
      prop: 'erpCode',
      width: 180,
      slot: true
    },
    {
      label: '商品名称',
      name: 'productName',
      prop: 'productName'
    },
    {
      label: '生产厂家',
      name: 'manufacturer',
      prop: 'manufacturer'
    },
    {
      label: '规格',
      name: 'spec',
      prop: 'spec'
    },
    {
      label: '单位',
      name: 'unit',
      prop: 'unit'
    },
    {
      label: '品牌',
      name: 'brandName',
      prop: 'brandName'
    }
  ].map((v, i) => ({
    key: i,
    ...v
  }))

  export default {
    props: {
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false
      },
      // 商品列表
      data: {
        type: Array,
        default: () => []
      },
      // 匹配字段
      matchField: {
        type: String,
        default: 'id'
      }
    },
    watch: {
      // 初始化赋值给 已选择数组（select）供内部使用
      data: {
        handler(data) {
          this.selects = _.cloneDeep(data);
          this.totalNum = data.length;
        },
        immediate: true,
        deep: true
      }
    },
    data() {
      return {
        // 显示弹窗
        visible: false,
        // 弹窗商品列表数据
        dialogTableData: [],
        // 已选择的商品列表
        tableColumns: TableColumns,
        // 已选择的商品列表表头
        selects: [],
        pageSize: 10,
        totalNum: 0,
        currentPage: 1,
      }
    },
    methods: {
      /**
       * 点击【选择商品】
       */
      onSelectClick() {
        this.visible = true;
      },
      handleDelete(row) {
        const arrId = [];
        if (row.length) {
          row.forEach(item => {
            arrId.push(item.id)
          })
        }
        this.selects = this.selects.filter(item => !arrId.includes(item.id))
        if (JSON.parse(JSON.stringify(this.$refs.frontEndPage.tableData)).length === 1 && JSON.parse(JSON.stringify(this.selects)).length > 1) {
          this.$refs.frontEndPage.page = 1
        }
        //   this.handleChange(this.list)
        this.$emit('update:data', this.selects)
        this.total = this.data.length;
      },
      /**
       * 点击关闭选择商品弹窗
       * */
      closeProductDia() {
        this.visible = false;
      },
      /**
       * 切换列表选中状态
       */
      toggleSelection() {
        this.$nextTick(() => {
          let matchField = this.matchField;
          this.selects.forEach(item => {
            let row = this.dialogTableData.find(v => v[matchField] === item[matchField]);
            row && this.$refs.table.toggleRowSelection(row, true)
          })
        })
      },
      /**
       * 确认选择商品
       */

      handleAddProducts(list) {
        this.$emit('update:data', list)
      },

    },
    components: {
      DelButton,
      ProductsModal,
    }
  }

</script>
<style lang="scss" module>
  .container {
    width: 100%;

    .btn {
      margin-bottom: 10px;
    }

    .pagination {
      margin-top: 10px;
      text-align: right;
    }

    li {
      width: fit-content;
    }
  }

</style>
