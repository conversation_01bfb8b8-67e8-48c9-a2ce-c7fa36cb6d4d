<template>
  <el-dialog
    title="选择品牌"
    :visible.sync="visible"
    width="80%"
    :before-close="handleClose" v-loading="loading">
    <div class="search-wrapper-row">
      <el-input placeholder="请输入品牌名称" v-model="brandName" clearable></el-input>
      <div class="buttons">
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
    </div>

    <el-table :data="tableData" border>
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="选择" width="50" align="center">
        <template slot-scope="scope">
          <el-radio v-model="radio" :label="scope.$index"></el-radio>
        </template>
      </el-table-column>
      <el-table-column label="品牌logo" width="80" class-name="img-cell">
        <template slot-scope="{ row }">
          <img :src="row.brandLogo" width="50px" height="50px" />
        </template>
      </el-table-column>
      <el-table-column label="品牌" prop="brandName"/>
      <el-table-column label="生产厂家" prop="manufacturer"/>
    </el-table>
    <el-pagination
      @size-change="load"
      @current-change="load"
      :current-page.sync="page"
      :page-size.sync="pageSize"
      background
      layout="->, prev, pager, next, sizes, jumper"
      :total="total"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import request from '@/utils/request'

  export default {
    data () {
      return {
        brandName: '',
        loading: false,
        visible: false,
        search: '',
        tableData: [],
        radio: 0,
        page: 1,
        pageSize: 10,
        total: 10
      }
    },
    mounted () {
      this.load()
    },
    methods: {
      async load () {
        this.loading = true
        const { data } = await request.post('/product/admin/brand/page', {
          current: this.page,
          size: this.pageSize,
          map: {},
          model: {
            brandName: this.brandName
          },
          order: 'descending',
          sort: 'id'
        })
        this.tableData = data.records
        this.page = data.current
        this.total = data.total
        this.loading = false
      },
      handleClose(done) {
        done()
      },
      splitString (val) {
        return val.split(',')
      },
      handleConfirm () {
        this.$emit('getData', this.tableData[this.radio])
        this.visible = false
      },
      onSearch () {
        this.resetPage();
        this.load();
      },
      // 重置分页信息
      resetPage () {
        this.page = 1;
        this.total = 0;
      },
      onReset () {
        this.brandName = '';
        this.load()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .search-wrapper-row{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .el-input{
      width: 200px;
      margin-right: 10px;
    }
  }
  .el-table{
    margin-bottom: 20px;
    ::v-deep{
      .el-radio{
        .el-radio__label{
          display: none;
        }
      }
    }
  }
</style>
