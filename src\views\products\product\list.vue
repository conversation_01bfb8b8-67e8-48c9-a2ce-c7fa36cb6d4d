<template>
  <div v-loading="loading" class="product-list">
    <!-- 搜索模块  -->
    <im-search-pad :is-expand.sync="isExpand" :model="queryParams" @reset="resetFilter" @search="submitSearch">
      <im-search-pad-item prop="erpCode">
        <el-input style="width: 285px;" v-model.trim="queryParams.keyword" placeholder="请输入ERP商品编码/商品名称/通用名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model.trim="queryParams.manufacturer" placeholder="生产厂家/品牌" />
      </im-search-pad-item>
      <im-search-pad-item prop="warehouseIds" v-if="storeArr.length">
        <el-select  v-model="queryParams.warehouseIds" class="width160" collapse-tags multiple placeholder="所在仓库" @change="handleChangeWarehouse" clearable>
          <el-option v-for="item in storeArr" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="categoryCode">
        <el-cascader v-model="queryParams.categoryCode" placeholder="所在分类" clearable :options="categoryOpts"
          :props="{ checkStrictly: true, emitPath: false, label: 'name', value: 'code' }" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="labelIds">
        <el-select  v-model="queryParams.labelIds" class="width160" collapse-tags multiple placeholder="商品标签" clearable>
          <el-option v-for="item in labelList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="labelIds">
        <el-select  v-model="queryParams.skuSyncType" class="width160" collapse-tags placeholder="是否同步ERP库存" clearable>
          <el-option v-for="item in skuSyncTypeList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="stockCondition">
        <el-select v-model="queryParams.stockCondition" placeholder="库存" clearable>
          <el-option value="ALL" label="全部" />
          <el-option value="WITH" label="有库存" />
          <el-option value="WITHOUT" label="没有库存" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="pictIdsIsEmpty">
        <el-select v-model="queryParams.pictIdsIsEmpty" placeholder="商品图片有无" clearable>
          <el-option value="Y" label="无" />
          <el-option value="N" label="有" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="sourceMerchantName">
        <el-input v-model.trim="queryParams.sourceMerchantName" placeholder="请输入来源对象" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据来源" clearable>
          <el-option value="LOCAL" label="本地创建" />
          <el-option value="IMPORT" label="本地导入" />
          <el-option value="ERP" label="erp对接" />
          <el-option value="SHARE" label="分销共享" />
          <el-option value="SYNC" label="供方同步" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="sourceType">
        <el-select v-model="queryParams.whetherAdjustPrice" placeholder="请选择是否可调价" clearable>
          <el-option value="Y" label="可调价" />
          <el-option value="N" label="不可调价" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="">
        <template>
          <div class="searchBox">
            <el-input v-model.trim="queryParams.beginPrice" placeholder="最低" style="width:220px">
              <el-select slot="prepend" v-model="queryParams.priceType" style="width:120px" placeholder="筛选项">
                <el-option label="统一销售价" value="SALE_PRICE"></el-option>
                <el-option label="会员价格" value="MEMBER_PRICE"></el-option>
                <el-option label="成本价格" value="COST_PRICE"></el-option>
                <el-option label="毛利率" value="GROSS_PROFIT"></el-option>
              </el-select>
            </el-input>
            <span style="margin:0 5px">-</span>
            <el-input v-model.trim="queryParams.endPrice" placeholder="最高" style="width:100px" />
          </div>
        </template>
      </im-search-pad-item>
    </im-search-pad>
    <!--页面主体-->
    <div class="product-list-container">
      <!--tab栏-->
      <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="currentTab" @change="handleChangeTab">
        <template slot="button">
          <div>
            <el-button v-if="currentTab === 1&&checkPermission(['admin','sale-saas-self-product:putOnSale','sale-platform-self-product:putOnSale'])"
              :disabled="!selects.length" @click="handleBatchPutOnSale">批量上架
            </el-button>
            <el-button v-if="currentTab === 0&&checkPermission(['admin','sale-saas-self-product:pullOffShelves','sale-platform-self-product:pullOffShelves'])"
              :disabled="!selects.length" @click="handleBatchPullOffShelves">批量下架
            </el-button>
            <el-button @click="batchImport" v-if="checkPermission(['admin','sale-saas-self-product:import','sale-platform-self-product:import'])">批量导入</el-button>
            <el-button v-if="checkPermission(['admin','sale-saas-self-product:export','sale-platform-self-product:export'])" @click="handleOutExcel">批量导出</el-button>
            <el-dropdown style="margin: 0 10px" @command="setBatchAdjustPrice">
              <el-button>批量设置<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in adjustPriceOptions" :key="item.command" :command="item.command" :disabled="!selects.length">{{item.text}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button @click="reload">刷新</el-button>
            <el-button v-if="checkPermission(['admin','sale-saas-self-product:create','sale-platform-self-product:create'])" type="primary"
              @click="handleAdd">+ 发布新品
            </el-button>
          </div>
        </template>
      </tabs-layout>
      <!--商品表格主体-->
      <table-pager :rowKey="rowKey" :reserveSelection="true" ref="pager-table" :height="600" :options="allColumn"
        :remote-method="load" :data.sync="tableData" :selection="true" @selection-change="handleSelectionChange"
        @selection-all="onAllSelect" :pageSize.sync="pageSize" :currentPage.sync="page" :operation-width="200">
        <el-table-column slot="pictIdS" label="产品主图" align="center" class-name="img-cell">
          <template slot-scope="scope">
            <el-image style="width:50px;height:50px" :src="scope.row.pictIdS|imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
          </template>
        </el-table-column>
        <el-table-column slot="erpCode" label="商品编码/状态" width="160">
          <template slot-scope="{row}">
            <div>编码：{{ row.erpCode }}</div>
            <div>仓库：{{ row.warehouseName }}</div>
            <div>状态：{{ row.whetherOnSale.code === 'Y'?'已上架':'已下架' }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="productName" label="商品信息" width="220">
          <template slot-scope="{row}">
            <div>{{ row.productName }}</div>
            <div>{{ row.spec }}</div>
            <div>{{ row.manufacturer }}</div>
            <div>{{ row.approvalNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="drugName" label="其他信息" width="180">
          <template slot-scope="{row}">
            <div>通用名：{{ row.drugName }}</div>
            <div>剂型：{{ row.agentiaType }}</div>
            <div>品牌：{{ row.brandName }}</div>
            <div>产地：{{ row.area }}</div>
            <div>条形码：{{ row.barCode }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="stockQuantity" label="商品库存" width="180">
          <template slot-scope="{row}">
            <el-link :underline="false" type="primary" @click="showErpSkuDialog(row)">
              {{ row.skuSyncType.desc }} <i class="el-icon-edit-outline" />
            </el-link>
            <div>
              实际库存：
              <span :class="[getIsRed(row.realStockQuantity) ? 'red' : '']">{{ row.realStockQuantity }}</span>
            </div>
            <div>
              可卖库存：
              <span :class="[getIsRed(row.stockQuantity) ? 'red' : '']">{{ row.stockQuantity }}</span>
            </div>
            <div>冻结库存：{{ row.freezeQuantity }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="salePrice" label="销售价" width="180">
          <template slot-scope="{row}">
            <div>销售价：{{ row.salePrice }}</div>
            <div>会员价：{{ row.memberPrice }}</div>
            <div>成本价：{{ row.costPrice }}</div>
            <div>建议批发价：{{ row.wholesalePrice }}</div>
            <div>建议零售价：{{ row.retailPrice }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="grossProfit" label="单品毛利" width="130">
          <template slot-scope="{row}">
            <div>毛利额：{{ row.grossProfit }}</div>
            <div>毛利率：{{ row.grossProfitRate }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="otherSetting" label="其他设置" width="150">
          <template slot-scope="{row}">
            <el-link :underline="false" type="primary" @click="showRetailStoreDialog(row)">
              {{ getWhetherRewardLabel(row.whetherReward && row.whetherReward.code) }} <i class="el-icon-edit-outline" />
            </el-link>
            <el-link :underline="false" type="primary" @click="showPointDeductionDialog(row)">
              {{ row.whetherPointsDeduction && row.whetherPointsDeduction.code === 'Y' ? `积分抵扣 ${row.pointsDeductionRatio || 0}%` : '不可积分抵扣' }}
              <i class="el-icon-edit-outline" />
            </el-link>
          </template>
        </el-table-column>
        <el-table-column slot="updateTime" label="操作时间" width="225">
          <template slot-scope="scope">
            <div>创建时间：{{ scope.row.createTime }}</div>
            <div>最新操作：{{ scope.row.updateTime }}</div>
          </template>
        </el-table-column>
        <div slot-scope="scope" align="center">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin','sale-saas-self-product:varietiesBan', 'sale-platform-self-product:varietiesBan'])" @click="settingControl(scope.row.id)">控销设置</el-button>
              <el-button type="text" v-if="checkPermission(['admin','sale-saas-self-product:detail','sale-platform-self-product:detail'])"
                @click="$router.push({ path: '/productCenter/productsManagement/show', query: { id: scope.row.id ,marketing:'DIRECT'} })">
                查看详情</el-button>
              <el-button type="text" v-if="checkPermission(['admin','sale-saas-self-product:edit','sale-platform-self-product:edit'])"
                @click="$router.push({ path: '/productsManagement/edit', query: { id: scope.row.id ,marketing:'DIRECT'} })">
                编辑</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!-- 控销弹窗 -->
    <el-dialog title="设置控销" append-to-body v-if="controlPinStatus" width="70%" :visible.sync="controlPinStatus"
      :before-close="closeDialogFun">
      <control-pin ref="controlPin" :currentId="currentId"></control-pin>
      <div slot="footer" class="dialog-footer">
        <el-button @click="controlPinStatus = false">取 消</el-button>
        <el-button type="primary" @click="submitControl">确定</el-button>
      </div>
    </el-dialog>
    <!-- 导出数据选择弹窗 -->
    <el-dialog title="导出数据" append-to-body v-if="exportStatus" width="50%" :visible.sync="exportStatus" :before-close="closeExportDia">
      <exportpage ref="exportPage" :total="total" :totalPage="totalPage"></exportpage>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportStatus = false">取消</el-button>
        <el-button type="primary" @click="submitExport">确定</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <Import-Dialog ref="importDialogRef" @closeImportDia="resetFilter"></Import-Dialog>
    <!-- 设置库存弹窗 -->
    <SetSkuDialog ref="setSkuDialogRef" @setSkuSuccess="submitSearch" />
    <!-- 设置分销弹窗 -->
    <RetailStoreDialog ref="retailStoreDialogRef" @submitSuccess="submitSearch" />
    <!-- 积分抵扣弹窗 -->
    <PointDeductionDialog ref="pointDeductionDialogRef" @submitSuccess="submitSearch" />
  </div>
</template>

<script>
  import _ from 'lodash'
  import tableInfo from "@/views/products/product/tableInfo";
  import request from '@/utils/request'
  import transferTable from '@/components/TransferTable/index'
  import checkPermission from '@/utils/permission'
  import {
    MessageConfirmExport
  } from '@/utils/index';
  import {
    downloadFile,
  } from "@/utils/commons";

  import controlPin from "@/views/products/product/product-components/controlPin.vue"
  import exportpage from "@/views/products/product/exportPage"
  import ImportDialog from "@/views/products/product/components/importDialog"
  import SetSkuDialog from './components/setSkuDialog.vue';
  import RetailStoreDialog from './components/retailStoreDialog.vue';
  import PointDeductionDialog from './components/pointDeductionDialog.vue';
  import {
    list, batchAdjustPrice
  } from "@/api/products/product/index"
  import { fetchLabslList } from '@/api/products/label'

  import {
    addForbid,
    editForbid,
    exportProductExcel
  } from "@/api/products/product/index";
  import {
    getAllStore
  } from "@/api/products/store";
  import { getWhetherRewardLabel } from './components/data';

  const TableColumns = [{
      prop: 'pictIdS',
      name: "pictIdS",
      label: '产品主图',
      slot: true
    },
    {
      prop: 'erpCode',
      name: 'erpCode',
      slot: true,
      label: '商品编码/状态',
      width: 200
    },
    {
      prop: 'productName',
      name: 'productName',
      slot: true,
      label: '商品信息',
      width: 200
    },
    {
      prop: 'drugName',
      name: 'drugName',
      slot: true,
      label: '其他信息',
      width: 120
    },
    {
      prop: 'stockQuantity',
      name: 'stockQuantity',
      label: '商品库存',
      slot: true,
    },
    {
      prop: 'salePrice',
      name: 'salePrice',
      slot: true,
      label: '销售价',
      width: 100,
      className: 'salePrice'
    },
    {
      prop: 'grossProfit',
      name: 'grossProfit',
      slot: true,
      label: '单品毛利',
      className: 'grossProfit',
      width: 120
    },
    {
      prop: 'otherSetting',
      name: 'otherSetting',
      slot: true,
      label: '其他设置'
    },
    {
      prop: 'categoryPathName',
      label: '商品分类',
      width: 180
    },
    {
      prop: 'updateTime',
      name: 'updateTime',
      slot: true,
      label: '操作时间',
      width: 200
    },
    {
      prop: 'operator',
      label: '操作',
      slot: true,
      show: true
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  export default {
    name:"productList",
    components: {
      transferTable,
      controlPin,
      exportpage,
      ImportDialog,
      SetSkuDialog,
      RetailStoreDialog,
      PointDeductionDialog
    },
    data() {
      return {
        rowKey: "id",
        isExpand: false,
        exportStatus: false,
        allColumn: TableColumnList, // 所有表列
        columns: {}, // 要显示的列
        loading: false,
        controlPinStatus: false,
        busOption: '',
        queryParams: {
          manufacturer: '',
          categoryCode: '',
          stockCondition: 'ALL',
          keyword: '',
          skuSyncType: '',
          sourceMerchantName: '',
          sourceType: '',
          pictIdsIsEmpty: "",
          priceType:'SALE_PRICE', // 价格类型
          beginPrice:'', // 起始价格
          endPrice:'', // 结束价格
          warehouseIds: [],
          labelIds: [],
          whetherAdjustPrice: '' // 是否可调价
        },
        storeArr: [],
        labelList: [],
        skuSyncTypeList: [
          { label: '同步', value: 'ERP_SYNC' },
          { label: '不同步', value: 'LOCAL_SYNC' }
        ],
        categoryOpts: [],
        currentTab: 0,
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 1,
        total: 0,
        selects: [],
        adjustPriceOptions: [
          { command: 'y', text: '可调价' },
          { command: 'n', text: '不可调价' }
        ],
        busOptions: [{
            label: '可现金交易',
            value: 'whetherCashTransaction:Y'
          },
          {
            label: '不可现金交易',
            value: 'whetherCashTransaction:N'
          },
          {
            label: '可退货',
            value: 'whetherReturnable:Y'
          },
          {
            label: '可共享分销',
            value: 'whetherShare:Y'
          },
          {
            label: '不可共享分销',
            value: 'whetherShare:N'
          }
        ],
        tableTitle: [],
        currentId: '',
        flag: false
      }
    },
    computed: {
      tabs() {
        return [{
            name: '已上架',
            value: 'ONSALE',
            hide: !checkPermission(['admin', 'sale-saas-self-product:putOnSaleView', 'sale-platform-self-product:putOnSaleView'])
          },
          {
            name: '已下架',
            value: 'NOTONSALE',
            hide: !checkPermission(['admin', 'sale-saas-self-product:pullOffShelvesView', 'sale-platform-self-product:pullOffShelvesView'])
          },
          {
            name: '待审核',
            value: 'PENDING',
            hide: !checkPermission(['admin', 'sale-saas-self-product:penddingView', 'sale-platform-self-product:penddingView'])
          },
          {
            name: '已驳回',
            value: 'REJECTED',
            hide: !checkPermission(['admin', 'sale-saas-self-product:rejectedView', 'sale-platform-self-product:rejectedView'])
          }
        ]
      }
    },
    watch: {
      currentTab() {
        this.$nextTick(() => {
          // 重置
          this.page = 1
          this.pageSize = 10
          this.totalPage = 1
          this.total = 0
          // 重新请求
          this.submitSearch()
        })
      }
    },
    async mounted() {
      await this.getStoreList()
      await this.loadCategories()
      await this.initTbaleTitle()
      this.getlabelList();
      if(this.$route.query.warehouseId) {
        this.queryParams  ={...this.queryParams, warehouseIds: [this.$route.query.warehouseId]}
      }
    },
    activated() {
      if(sessionStorage.getItem('productList')) {
        this.resetFilter()
        sessionStorage.removeItem('productList')
      }
    },
    methods: {
      checkPermission,
      // 显示设置erp库存弹窗
      showErpSkuDialog(row) {
        this.$refs.setSkuDialogRef.show(row)
      },
      // 展示设置分销奖励dialog
      showRetailStoreDialog(row) {
        this.$refs.retailStoreDialogRef.show(row)
      },
      // 展示积分抵扣弹窗
      showPointDeductionDialog(row) {
        const form = {
          ids: [row.id],
          whetherPointsDeduction: row.whetherPointsDeduction && row.whetherPointsDeduction.code || 'N',
          pointsDeductionRatio: row.pointsDeductionRatio || 0,
        }
        this.$refs.pointDeductionDialogRef.show(form)
      },
      getWhetherRewardLabel(value) {
        return getWhetherRewardLabel(value)
      },
      getErpText(val) {
        if (val === 'ERP_SYNC') return '同步ERP库存'
        if (val === 'LOCAL_SYNC') '不同步ERP库存'
        return ''
      },
      getIsRed(val) {
        if (val == 0) return true
        if (!val) return false
        return parseFloat(val) < 0
      },
      // 获取标签列表
      getlabelList(){
        let params = {
          current: 1,
          model: {},
          size: 1000,
        };
        fetchLabslList(params).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.labelList = res.data.records.map(item=>{
              return Object.assign(
                {},
                {
                  label:item.labelName,
                  value: item.id
                }
              )
            })
          }
        })
      },
      /**
       * @description 获取全部仓库的下拉列表
       * <AUTHOR>
       */
      getStoreList() {
        getAllStore().then(res => {
          const data = res.data;
          if(data && data.length) {
            data.forEach(item => {
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeArr.push(obj)
            })
          }
        })
      },
      handleChangeWarehouse(val) {
        this.flag = true
      },
      resetData() {
        this.page = 1
        this.pageSize = 10
        this.totalPage = 1
        this.total = 0
        this.selects = []
        this.tableData = []
        this.queryParams.keyword = ''
        this.queryParams.manufacturer = ''
        this.queryParams.categoryCode = ''
        this.queryParams.stockCondition = 'ALL'
        this.queryParams.sourceMerchantName = ''
        this.queryParams.sourceType = '';
        this.queryParams.pictIdsIsEmpty = "";
        this.queryParams.priceType = 'SALE_PRICE', // 价格类型
        this.queryParams.whetherAdjustPrice = ''
        this.queryParams.beginPrice = '', // 起始价格
        this.queryParams.endPrice = '', // 结束价格
        this.queryParams.skuSyncType = ''
        this.categoryId = ''
        this.busOption = '';
        this.flag = true
        this.queryParams.warehouseIds = [];
        this.queryParams.labelIds = [];
      },
      async reload() {
        this.$refs['pager-table'].doRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load(params) {
        if (this.tabs.every(item => item.hide)) return
        if(this.queryParams.beginPrice != '' && this.queryParams.endPrice != '') {
          if(Number(this.queryParams.beginPrice) > Number(this.queryParams.endPrice)) {
            this.$message.warning('请正确选择价格区间');
            return
          }
        }
        if(this.$route.query.warehouseId) {
          if(this.queryParams.warehouseIds.length === 0 && !this.flag) {
            this.queryParams = {...this.queryParams,warehouseIds: [this.$route.query.warehouseId]}
          }
        }
        const model = {
          ...this.queryParams,
          marketing: "DIRECT"
        }
        if (this.tabs[this.currentTab].value === 'ONSALE') {
          model.whetherOnSale = 'Y'
        } else if (this.tabs[this.currentTab].value === 'NOTONSALE') {
          model.whetherOnSale = 'N'
        } else {
          model.approvalStatus = this.tabs[this.currentTab].value
        }
        if (this.busOption) {
          const arr = this.busOption.split(':')
          model[arr[0]] = arr[1]
        }

        let listQuery = {
          model,
          sort: 'createTime'
        };
        Object.assign(listQuery, params)
        let result = await list(listQuery)
        this.totalPage = result.data.pages;
        this.total = result.data.total;
        this.page = result.data.current;
        return result
      },
      dealSource(type) {
        let text = '';
        switch (type) {
          case 'LOCAL':
            text = "本地创建"
            break;
          case 'IMPORT':
            text = "本地导入"
            break;
          case 'ERP':
            text = "erp对接"
            break;
          case 'SHARE':
            text = "分销共享"
            break;
          case 'SYNC':
            text = "供方同步"
            break;
        }
        return text
      },
      handleChangeTab(tab, index) {
        this.currentTab = index;
        this.$refs['pager-table'].clearSelection();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        });
      },
      submitSearch() {
        this.$refs['pager-table'].clearSelection();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },

      splitString(val) {
        if (!val) {
          return ''
        }
        return val.split(',')
      },

      async loadCategories() {
        try {
          const {
            data
          } = await request.post('product/admin/category/query', {})
          const map = _.groupBy(data, 'parentId')

          this.categoryOpts = this._buildTree(0, 0, map)
        } catch (e) {

        }
      },

      _buildTree(parentId, level, map) {
        const list = map[parentId]

        if (!list) {
          return null
        }

        return list.map(item => ({
          id: item.id,
          code: item.categoryCode,
          name: item.label,
          sort: item.sortValue,
          frontShow: _.get(item, 'whetherShowFrontend.code') === 'Y',
          parentId: item.parentId,
          level,
          children: this._buildTree(item.id, level + 1, map)
        })).sort((a, b) => a.sort - b.sort)
      },

      resetFilter() {
        this.resetData()
        this.$refs['tabs-layout'].reset()
        // this.submitSearch()
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      handleSelectionChange(selects) {
        this.selects = selects
      },

      async handleBatchReject() {
        this.loading = true
        try {
          await request.post('product/admin/product/batchRejectProduct', {
            list: this.selects.map(item => ({
              id: item.id,
              approvalStatus: 'REJECTED',
              rejectReason: ''
            }))
          })
        } catch (e) {

        }
        this.loading = false

        this.submitSearch()
      },

      async handleBatchPending() {
        this.loading = true
        try {
          await request.post('product/admin/product/batchRejectProduct', {
            list: this.selects.map(item => ({
              id: item.id,
              approvalStatus: 'PENDING',
              rejectReason: ''
            }))
          })
        } catch (e) {

        }
        this.loading = false

        this.submitSearch()
      },

      async handleBatchPutOnSale() {
        this.$confirm("您确定批量上架这些商品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.loading = true;
          try {
            await request.post('product/admin/product/batchPutOnSale', null, {
              params: {
                ids: this.selects.map(item => item.id)
              }
            })
          } catch (e) {

          }
          this.loading = false;

          this.submitSearch();
        })
      },
      initTbaleTitle() {
        let arr = []
        tableInfo["ACCEPTED"].forEach(item => {
          if (item.name != "id") {
            arr.push(item)
          }
        })
        this.tableTitle = arr

      },
      //  导出档案
      async outExcel() {
        if (this.selects.length > 0) {
          const tHeader = ["id"];
          const filterVal = ["id"];
          this.tableTitle.forEach(function (item) {
            tHeader.push(item.label);
            filterVal.push(item.name);
          });
          let exportData = this.formatJson(this.selects, filterVal);
          downloadFile({
            tHeader: tHeader,
            fileName: "商品列表",
            exportData: exportData,
          });
        } else {
          this.$message.error("请选择数据");
        }
      },
      formatJson(dataList, filterVal) {
        return dataList.map((v) =>
          filterVal.map((j) => {
            // if (j == "marketing") {
            //   return v[j].desc
            // }
            if (j == "whetherShare") {
              return v[j].desc
            }
            if (j == "approvalStatus") {
              return v[j].desc
            }
            if (j == "whetherOnSale") {
              return v[j].desc
            }
            return v[j]
          })
        );
      },
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect: function (val) {
        this.selects = val;
      },
      async handleBatchPullOffShelves() {
        this.$confirm("您确定批量下架这些商品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.loading = true;
          try {
            await request.post('product/admin/product/batchPullOffShelves', null, {
              params: {
                ids: this.selects.map(item => item.id)
              }
            })
          } catch (e) {

          }

          this.loading = false;

          this.submitSearch();
        })
      },
      // 控销设置
      settingControl(id) {
        this.currentId = id;
        this.controlPinStatus = true;
      },
      closeDialogFun() {
        this.controlPinStatus = false;
      },
      submitControl() {
        const loading = this.$loading({
          lock: true,
          text: '正在设置中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });
        let params = this.$refs['controlPin'].getData();
        let {
          productForbid,
          isAdd,
          productId,
          editId,
          customerTagIds
        } = params;
        let paramsQuery = {
          ...productForbid,
          productId,
          customerTagIds
        };
        if (isAdd) {
          addForbid(paramsQuery).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('新增控销成功');
              this.resetFilter();
              this.closeDialogFun();
            }
          }).finally(() => {
            loading.close();
          })
        } else {
          paramsQuery.id = editId;
          editForbid(paramsQuery).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('修改控销成功');
              this.resetFilter();
              this.closeDialogFun();
            }
          }).finally(() => {
            loading.close();
          })
        };
      },
      // 导出数据选择弹窗关闭
      closeExportDia() {
        this.exportStatus = false;
      },
      handleOutExcel() {
        this.exportStatus = true;
      },
      // 确定导出
      submitExport() {
        let params = this.$refs['exportPage'].getData();
        const loading = this.$loading({
          lock: true,
          text: '正在导出中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });

        const model = {
          ...this.queryParams,
          ...params,
          marketing: "DIRECT"
        }
        if (this.tabs[this.currentTab].value === 'ONSALE') {
          model.whetherOnSale = 'Y'
        } else if (this.tabs[this.currentTab].value === 'NOTONSALE') {
          model.whetherOnSale = 'N'
        } else {
          model.approvalStatus = this.tabs[this.currentTab].value
        }
        if (this.busOption) {
          const arr = this.busOption.split(':')
          model[arr[0]] = arr[1]
        };

        let listParams = {
          current: this.page,
          size: this.pageSize,
          map: {},
          model,
          order: 'descending',
          sort: 'createTime'
        };
        exportProductExcel(listParams).then(res => {
          loading.close();
          MessageConfirmExport().then(res=>{
            if(res) {
              this.exportStatus = false;
            }
          })
        })
      },
      // 打开批量导入的弹窗
      batchImport(){
        this.$refs['importDialogRef'].openDiaFn();
      },
      /**
       * @description 发布新品
       * <AUTHOR>
       */
      handleAdd() {
        this.$router.push({ path: '/productsManagement/create' })
      },
      // 批量可调价/不可调价
      setBatchAdjustPrice(val) {
        const text = val === 'y' ? '可调价' : '不可调价'
        this.$confirm(`您确定批量设置这些商品为“${text}”吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const formData = new FormData()
          formData.append('ids[]', this.selects.map(item => item.id).toString())
          formData.append('adjustPrice', val);
          batchAdjustPrice(formData).then(res => {
            if (res.isSuccess) {
              this.$message.success('设置成功')
              this.submitSearch()
            }
          })
        }).catch(() => {});
      }
    }
  }

</script>

<style lang="scss" scoped>
  .product-list {
    &-container {
      background-color: #ffffff;
      padding: 8px 20px 20px;
      margin-top: 16px;

      .product-list-tabs-wrapper {
        position: relative;

        .product-list-tabs {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #dcdde0;
          font-size: 14px;
          color: #303133;
          margin-bottom: 16px;

          .tab {
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            margin-right: 9px;
            position: relative;
            cursor: pointer;

            &:after {
              content: '';
              display: block;
              width: 100%;
              height: 2px;
              background-color: #1890ff;
              position: absolute;
              bottom: -1px;
              left: 0;
              display: none;
            }

            &.active:after {
              display: block;
            }
          }
        }

        .operations {
          position: absolute;
          right: 0;
          bottom: 8px;
        }
      }
    }

    .el-table {
      .red {
        color: red;
      }
      ::v-deep {
        td.salePrice {
          color: #FF6600;
        }

        td.costPrice {
          color: #339900;
        }

        td.grossProfit {
          color: #339900;
        }
      }
    }

    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #505465;
      font-size: 14px;
      margin-top: 20px;
    }
  }

  .searchBox{
    display: flex;
    align-items: center;

  }
</style>

<style lang="scss">
  td.salePrice {
    color: #FF6600;
  }

  td.costPrice {
    color: #339900;
  }

  td.grossProfit {
    color: #339900;
  }
  .width160 {
    width: 235px;
  }
</style>
