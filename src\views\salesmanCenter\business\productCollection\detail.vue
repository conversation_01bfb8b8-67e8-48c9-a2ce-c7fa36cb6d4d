<template>
  <div>
    <div class="detailTable">
      <page-title :title="detail.productName" />
      <page-module-card>
        <p><b>规格</b>： {{detail.spec}}</p>
        <p style="margin: 10px 0;"><b>批准文号</b>：国药准{{detail.approvalNumber}}</p>
        <p><b>生产厂家</b>：{{detail.manufacturer}}</p>
      </page-module-card>
    </div>

    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{name:'历史采集'}]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <!-- <el-button @click="reload">刷新</el-button> -->
        </template>
      </tabs-layout>

      <el-table :data="list" border style="width: 100%">
        <el-table-column prop="createTime" label="采集时间" width="200">
        </el-table-column>
        <el-table-column prop="saleManName" label="采集人" width="200">
        </el-table-column>
        <el-table-column prop="price" label="采集销售价" width="200">
        </el-table-column>
        <el-table-column prop="stockQuantity" label="采集库存" width="200">
        </el-table-column>
        <el-table-column prop="changeAmount" label="动销量" width="200">
        </el-table-column>
        <el-table-column prop="changePeriod" label="动销周期">
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="page-row">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="current" :page-sizes="[10, 20, 50, 100]" :page-size.sync="size"
        layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
      </el-pagination>
    </div>
  </div>
</template>


<script>
  import {
    getHistoryByProductId,
    getHistoryByProductIdList
  } from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        tableData: [],
        detail: {
          productCollectItemList: []
        },
        current: 1,
        list: [],
        size: 10,
        totalCount: 0
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    activated() {
      this.load();
      this.getList();
    },
    //方法集合
    methods: {
      async load() {
        getHistoryByProductId(this.$route.query.productId).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.detail = res.data || {}
          }
        })
      },
      getList() {
        let parasm = {
          "current": this.current,
          "map": {},
          "model": {
            "productId": this.$route.query.productId
          },
          "order": "descending",
          "size": this.size,
          "sort": "id"
        }
        getHistoryByProductIdList(parasm).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.list = res.data.records || [];
            this.totalCount = res.data.total;
          }
        })
      },
      handleSizeChange(val) {
        this.size = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.current = val;
        this.getList();
      },
    },

  }
</script>


<style lang='scss' scoped>
  .detailTable {
    background: #ffffff;
    padding: 0 20px 20px;
  }

  .tab_bg {
    margin-top: 20px;
  }
</style>
