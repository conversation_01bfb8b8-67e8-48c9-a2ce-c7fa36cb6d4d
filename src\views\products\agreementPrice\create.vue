<template>
  <div class="create" v-loading="loading">
    <div class="create-title">
      <div class="title">{{ isRealPagination ? '编辑协议价' : '新增协议价' }}</div>
      <div class="btns">
        <el-button @click="backFn">返回</el-button>
        <template v-if="edit">
          <el-button  @click="onSubmit" type="primary" v-if="checkPermission(['admin', 'sale-platform-agreementPrice:add', 'sale-saas-agreementPrice:add'])">提交</el-button>
        </template>
      </div>
    </div>
    <!-- 中心内容区域 -->
    <div class="create-body">
      <div class="create-select">
        <div>选择客户：</div>
        <div class="select-box">
          <div class="name">{{ `${clientInfo.name && clientInfo.name.length > 16 ? clientInfo.name.substring(0, 16) + '...' : clientInfo.name || ''}` }}</div>
          <div class="select-trigger" v-if="!isRealPagination" @click="displayClients">
            <i class="el-icon-more" />
          </div>
        </div>
      </div>
      <!-- 客户信息 -->
      <div class="detail-items">
        <page-module-card title="客户信息">
          <div>
            <el-form  label-width="100px" :model="query">
              <div style="display:flex;flex-wrap: wrap;">
                <el-form-item class="formItem" label="客户编码：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.code }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="客户名称：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.name }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="客户类型：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.merchantType }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="客户资质：">
                  <div class="info_right check-intelligence"  style="width: 300px;" @click="handleCheckPreView(clientInfo.merchantLicenseFileList)">查看客户资质</div>

                </el-form-item>
                <el-form-item class="formItem" label="联系人：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.ceoName }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="联系电话：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.ceoMobile }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="所在区域：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.region }}</div>
                </el-form-item>
                <el-form-item class="formItem" label="关联业务员：">
                  <div class="info_right" style="width: 300px;">{{ clientInfo.salesmanName }}</div>
                </el-form-item>
              </div>
            </el-form>
            <el-image ref="refIntelligenceImage" style="width:0;height:0" :src="previewCheckImages[0]"  :preview-src-list="previewCheckImages"></el-image>
        </div>
        </page-module-card>
      </div>
      <!-- 商品信息 -->
      <div class="detail-items">
        <page-module-card title="商品信息">
					<front-end-page ref="frontEndPage" @openProductModel="openProductModel" :data.sync="goodsList" :isRealPagination="isRealPagination"
					:options="options" :pageSize="pageSize" :operation-width="200" :total="total" :operationWidth="100" :currentPage="currentPage" :selection="true"
          @patchDelete="handleDelete">
						<el-table-column slot="pictIdS" label="产品主图" align="center">
							<template v-slot="scope">
								<el-image style="width:50px;height:50px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
							</template>
						</el-table-column>
						<el-table-column slot="erpCode" width="180" label="ERP商品编码/仓库">
              <template v-slot="{ row }">
							  <span>编码：{{ row.erpCode || '无' }}</span><br />
								<span>仓库：{{ row.warehouseName || '无' }}</span>
							</template>
						</el-table-column>
						<el-table-column slot="agreePrice" width="180" label="协议价">
              <template v-slot="{ row }">
							  <el-input v-model.trim="row.agreePrice" @change="handleChange(row)"></el-input>
							</template>
						</el-table-column>
						<div slot-scope="scope" align="center">
							<el-row class="table-edit-row">
								<span class="table-edit-row-item">
									<el-button type="text"
										@click="handleDelete([scope.row])">
										删除</el-button>

								</span>
							</el-row>
						</div>
            <el-button slot="moreHandleBtn" @click="handleImportProduct" v-if="checkPermission(['admin','ImportProductDetailImportProduct'])">导入商品</el-button>
					</front-end-page>
				</page-module-card>
      </div>
      <!-- 其他信息 -->
      <div class="detail-items">
        <el-form ref="otherForm"
                :model="formValidate"
                :rules="rules"
                label-width="100px">
          <div class="create-block">
            <div class="block-title">其他信息</div>
            <div class="other-info">
              <el-form-item label="协议有效期至">
                <el-date-picker v-model="formValidate.expireDate"
                                type="date"
                                :disabled="!edit"
                                placeholder="选择日期"
                                style="width: 540px">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="协议备注">
                <el-input type="textarea"
                          :rows="4"
                          :disabled="!edit"
                          placeholder="请输入备注信息"
                          style="width: 540px"
                          v-model="formValidate.remark">
                </el-input>
              </el-form-item>
              <el-form-item label="协议附件">
                  <el-upload
                    :disabled="!edit"
                    list-type="picture-card"
                    :action="uploadParams.action"
                    :headers="uploadParams.headers"
                    :data="uploadParams.data"
                    :on-preview="handleUpLoad"
                    :file-list="images"
                    :limit="5"
                    :on-exceed="handleExceed"
                    accept=".jpg,.png,.bmp,.jpeg"
                    :on-change="handleUploadChange"
                    :on-remove="handleUploadChange">
                    <i class="el-icon-plus"></i>
                  </el-upload>
              </el-form-item>
            </div>
          </div>
          <div class="create-block" v-if="!edit">
            <div class="block-title">协议状态</div>
            <el-form-item label="协议状态">
              <el-radio-group v-model="formValidate.status" @change="handleStateChange">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="2">冻结</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 选择客户弹窗 -->
    <client-modal ref="clientModal" @on-select="onSelectClient" />
    <!-- <products-modal ref="products-modal" @change="handleAddProducts" /> -->
    <div v-if="productVisible">
      <products-modal ref="products-modal"
      :productVisible="productVisible"
      @closeProductDia="closeProductDia"
      :check-list="goodsList"
      @change="handleAddProducts"
      :selectPurId="selectPurId"
      :negotiatedPriceId="negotiatedPriceId"
      typeVal="agree"
      />
    </div>
    <el-image :ref="`refImage`" style="width:0;height:0" :src="previewImages[0]"  :preview-src-list="previewImages"></el-image>
    <!--  导入商品弹窗  -->
    <importDialog ref="importDialogRef" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="false" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
  </div>
</template>


<script>
import {getToken} from '@/utils/auth'
import moment from 'moment';
import {getLocalUser} from '@/utils/local-user'
import clientModal from "@/views/products/agreementPrice/components/clientModal";
import productsModal from "@/components/eyaolink/productModel/index"
import checkPermission from "@/utils/permission";
import PageModuleCard from "@/components/PageModuleCard/index";
import { postGoodsList } from '@/api/promotion'
import _ from "lodash";
import { addAgreePrice, agreePriceDetail,editAgreePrice } from '@/api/products/agreemenPrice'
import importDialog from "@/components/eyaolink/importDialog/index"

const TableColumns =[
  {
    prop: 'pictIdS',
    name: "pictIdS",
    label: '产品主图',
    slot: true
  },//新增字段
  {
    prop: 'erpCode',
    name: 'erpCode',
    label: 'ERP商品编码/仓库',
    width: 170,
		slot: true
  },
  {
    prop: 'productName',
    name: 'productName',
    label: '商品名称',
  },
  {
    prop: 'manufacturer',
    name: 'manufacturer',
    label: '生产厂家',
  },
  {
    prop: 'spec',
    name: 'spec',
    label: '规格',
  },
  {
    prop: 'unit',
    name: 'unit',
    label: '单位',
  },
  {
    prop: 'stockQuantity',
    name: 'stockQuantity',
    label: '库存',
  },
  {
    prop: 'costPrice',
    name: 'costPrice',
    label: '成本价',
  },
	{
    prop: 'salePrice',
    name: 'salePrice',
    label: '销售价',
  },
	{
    prop: 'agreePrice',
    name: 'agreePrice',
    label: '协议价',
		slot: true
  },
];
const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
}
export default {
   //import引入的组件
  components: {
    clientModal,
    productsModal,
    PageModuleCard,
    importDialog
  },
  data() {
      return {
        query: {},
        loading: false, // 全局加载
        edit: true, // 新增和编辑状态为true
        dialogVisible: false, // 驳回理由弹窗
        previewImages: [], // 图片预览
        previewCheckImages: [], //客户资质图片预览
        clientInfo: {}, // 当前选择的客户信息
        products: [], // 当前已选择的商品
        rejectReason: '', // 驳回理由
        productList: [], // 已选择的商品列表
        isRealPagination: false, // 商品分页列表是真分页还是假分页
        productVisible: false, // 商品弹窗是否展示
				model: {},
				form: {productIds:[]},
				goodsList: [],
				options: TableColumnList,
				pageSize: 10,
				total: 0,
				currentPage: 1,
        formValidate: {},
        rules: {},
        uploadParams: {
          action: process.env.VUE_APP_BASE_API + '/file/file/upload',
          headers: {
            Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
            token: `Bearer ${getToken()}`
          },
          data: {
            pur: 0,
            sale: 0,
            tenant: 0,
            userid: getLocalUser().userId,
            folderId: 0
          }
        },
        images: [],
        approvalStatus: 'PENDING',
        storeageList: [], //缓存已经输入的协议价
        storeageIds: [],
        selectPurId: '',//协议客户id
        negotiatedPriceId: '', //协议活动id
        actionUploadUrl: '/api/product/admin/productImport/importProductActivity',
        templateKey: 'IMPORT_PRODUCT_EXCEL_TEMP',
        queryParams:{
          productType: 'IMPORT_PRODUCT_NEGOTIATED_PRICE',
          activityId:''
        }
      }
  },
   //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.isRealPagination = Boolean(this.$route.query.id);
    if(this.$route.query.id) {
        this.getLoadDetails();
        this.queryParams.activityId = this.$route.query.id;
    }
  },
   //方法集合
  methods: {
    checkPermission,
    /**
     * @description 获取用户详情的数据
     */
    async getLoadDetails() {
      this.loading = true;
      this.storeageList = [];
      try {
        const res = await agreePriceDetail(this.$route.query.id);
        const data = res.data;
        this.selectPurId = data.purMerchantId;
        this.clientInfo = {...data.purMerchantVo,purMerchantId:data.purMerchantId,salesmanName: data.salesmanInfoList && data.salesmanInfoList[0].salesManName};
        this.goodsList = (data.negotiatedPriceItemVoList || []).map(item => {
          return {...item, agreePrice: item.price, id: item.productId}
        });
        this.goodsList.forEach(item => {
          const obj = {};
          obj.id = item.id;
          obj.agreePrice = item.agreePrice;
          this.storeageList.push(obj);
          this.storeageIds.push(item.id)
        });
        this.total = this.goodsList.length;
        this.formValidate = {
          expireDate: data.negotiatedTime || '',
          remark: data.description || '',
          status: data.activeStatus.code === 'FREEZE' ? 2 : 1
        };
        this.images = data.pictIdS ? data.pictIdS.split(',').map(item => {
            return {
              url: item,
              response: {
                data: {
                  url: item
                }
              }
            }
          }) : [];

        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    // 返回
    backFn() {
      this.$confirm('确定取消编辑，取消后已编辑的内容将不保存', '提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push('/productCenter/agreementPrice/list');
      })
    },
    //  打开导入商品弹窗
    handleImportProduct() {
      if(!this.selectPurId) {
        this.$message.error('请先选择客户');
        return;
      }
      this.$refs.importDialogRef.initOpen();
    },
    // 打开商品选择弹窗
    openProductModel(){
      if(!this.selectPurId) {
        this.$message.error('请先选择客户');
        return;
      }
      this.storeageList = [];
      this.storeageIds = []
      this.goodsList.forEach(item => {
        const obj = {};
        obj.id = item.id;
        obj.agreePrice = item.agreePrice;
        this.storeageList.push(obj);
        this.storeageIds.push(item.id)
      });
      this.productVisible = true;
    },
    closeProductDia(){
      this.productVisible = false;
    },
    // 选择客户弹窗
    displayClients() {
      this.$refs.clientModal.show();
    },
    // 选择客户回调
    onSelectClient(item) {
      this.goodsList = [];
      this.selectPurId = item.id
      this.clientInfo = {
        ...item
      };
    },
    // 导入商品回调
    uploadChangeData(list = []) {
      list = list.map(item=>{
        return Object.assign(
          {},
          item,
          {
            agreePrice: 0
          }
        )
      })
      let arr = _.cloneDeep(this.goodsList || []);
      let newList = arr.concat(list);
      this.goodsList = _.uniqBy(newList,'id');
      this.totalNum = this.goodsList.length;
    },
    // 选择商品回调弹窗
    async handleAddProducts(list) {
			const formData = new FormData()
      const idArr = list || this.form.productIds
      formData.append('ids', idArr.toString())
      const { data } = await postGoodsList(formData)
      this.goodsList = []
      let arrList = data.filter((items) => {
        if (idArr.includes(items.id)) {
          return items
        }
      });
      arrList.forEach(item => {
        if(this.storeageIds.includes(item.id)) {
          this.storeageList.forEach(e => {
            if(item.id === e.id) {
              item = {...item, agreePrice: e.agreePrice}
              this.goodsList.push(item)
            }
          })
        }else {
          item = {...item, agreePrice: '0'};
          this.goodsList.push(item)
        }
      })
      this.total = this.goodsList.length
    },
    /**
     * @description 处理提交逻辑
     * <AUTHOR>
     */
    async onSubmit(){
      if (!this.clientInfo.purMerchantId) {
        return this.$message.error('请选择客户');
      }
      if (this.formValidate.expireDate == 'null' || this.formValidate.expireDate == null ){
        this.$message.error('请选择协议有效期');
        return
      }
      let noPrice = false;
      if(this.isRealPagination) {
        const params = {
          approvalStatus: this.approvalStatus,
          description: this.formValidate.remark,
          negotiatedPriceItemSaveDTOList: this.goodsList.map(p => {
            if (!p.agreePrice || parseFloat(p.agreePrice) <= 0) {
              noPrice = true;
            }
            return {
                price: p.agreePrice,
                productId: p.id,
                skuId: p.skuId
              }
          }),
          negotiatedTime: moment(this.formValidate.expireDate).format('yyyy-MM-DD'),
          pictIdS: this.images.map(img => img.response.data.url).join(','),
          purMerchantId: this.clientInfo.purMerchantId,
          id: this.$route.query.id
        }
        params.negotiatedTime = params.negotiatedTime.split(' ').length==2 ? params.negotiatedTime : `${params.negotiatedTime} 23:59:59`;
        if (!this.goodsList.length) {
          return this.$message.error('请选择商品');
        }
        if (noPrice) {
          return this.$message.error('请填写协议价并且协议价需要大于0');
        }
        this.loading = true
        try {
          const res = await editAgreePrice(params)
          if(res.code === 0) {
            this.$message.success('编辑成功!')
            this.$router.push('/productCenter/agreementPrice/list')
          }
        } catch (e) {
        }
        this.loading = false
      }else {
        const params = {
          approvalStatus: this.approvalStatus,
          description: this.formValidate.remark,
          negotiatedPriceItemSaveDTOList: this.goodsList.map(p => {
            if (!p.agreePrice || parseFloat(p.agreePrice) <= 0) {
              noPrice = true;
            }
            return {
                price: p.agreePrice,
                productId: p.id,
                skuId: p.skuId
              }
          }),
          negotiatedTime: moment(this.formValidate.expireDate).format('yyyy-MM-DD'),
          pictIdS: this.images.map(img => img.response.data.url).join(','),
          purMerchantId: this.clientInfo.purMerchantId,
        }
        params.negotiatedTime = params.negotiatedTime.split(' ').length==2 ? params.negotiatedTime : `${params.negotiatedTime} 23:59:59`;
        if (!this.goodsList.length) {
          return this.$message.error('请选择商品');
        }
        if (noPrice) {
          return this.$message.error('请填写协议价并且协议价需要大于0');
        }
        this.loading = true
        try {
          const res = await addAgreePrice(params)
          if(res.code === 0) {
            this.$message.success('新增成功!')
            sessionStorage.setItem('agreementPrice','123')
            this.$router.push('/productCenter/agreementPrice/list')
          }

        } catch (e) {
        }
        this.loading = false
      }
		},
		/**
		 * @description 删除
		 * <AUTHOR>
		 */
		handleDelete(row){
      const arrId = []
      if(row.length) {
        row.forEach(item => {
          arrId.push(item.id)
        })
      }
			this.goodsList = this.goodsList.filter(item => {
        return !arrId.includes(item.id)
      })

      if (JSON.parse(JSON.stringify(this.$refs.frontEndPage.tableData)).length === 1 && JSON.parse(JSON.stringify(this.goodsList)).length > 1) {
        this.$refs.frontEndPage.page = 1
      }
      this.storeageList = [];
      this.storeageIds = []
      this.goodsList.forEach(item => {
          const obj = {};
          obj.id = item.id;
          obj.agreePrice = item.agreePrice;
          this.storeageList.push(obj);
          this.storeageIds.push(item.id)
        });
      this.total = this.goodsList.length
		},
		handleChange(row) {
      row.agreePrice = parseFloat(row.agreePrice).toFixed(2);
      let list = _.cloneDeep(this.goodsList);
      this.goodsList = [];
      list.forEach(item => {
        if(item.id === row.id) {
        item = {...item, agreePrice: row.agreePrice}
            this.goodsList.push(item)
          }else {
            this.goodsList.push(item)
          }
        })
		},
    handleUpLoad(){
      this.previewImages = this.images.map(item=>{
        return item.url;
      });
      this.$refs[`refImage`].showViewer = true;
    },
    handleExceed(files, images){
      this.$message.warning(`只能上传5张图片`);
    },
    handleUploadChange(file, fileList) {
      this.images = fileList
    },
    /**
     * @description 预览客户资质
     * <AUTHOR>
     */
    handleCheckPreView(list) {
      if(list == null) {
        this.$message.warning('该客户没有上传客户资质');
        return;
      }
      this.previewCheckImages = [...list]
      this.$refs[`refIntelligenceImage`].showViewer = true;
    }
  },
}
</script>


<style lang='scss' scoped>
.create {
  background-color: #ffffff;
  color: #555;
  font-size: 14px;

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E4E7ED;
    padding: 0 16px;

    .title {
      font-size: 18px;
      line-height: 56px;
    }
  }

  &-body {
    padding: 24px;
  }

  &-select {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    .select-box {
      width: 320px;
      height: 32px;
      border: 1px solid #E4E7ED;
      display: flex;
      align-items: center;

      .name {
        padding: 0 10px;
        line-height: 32px;
        flex: 1;
      }
    }

    .select-trigger {
      flex-shrink: 0;
      width: 30px;
      height: 100%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #b1b1b1;
    }
  }

  &-block {
    padding: 32px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #E4E7ED;
    }
    .block-title {
      font-weight: bold;
      font-size: 14px;
      color: #000;
      padding-left: 12px;
      position: relative;
      margin-bottom: 24px;

      &:before {
        content: '';
        width: 4px;
        height: 100%;
        background-color: #409EFF;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }

  .client-info {
    width: 80%;
  }

  .create-btn {
    line-height: 40px;
    color: #409EFF;
    text-align: center;
    border: 1px solid #dfe6ec;
    border-top: 0;
    cursor: pointer;
  }

  .other-info {
    // width: 540px;

    .img-list {
      display: flex;
      flex-wrap: wrap;

      .img {
        width: 100px;
        height: 100px;
        position: relative;
        margin-right: 16px;
        border: 1px solid #f2f2f2;

        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .icons {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          background-color: rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:first-child {
              margin-right: 10px;
            }
          }

          i {
            color: #ffffff;
            font-size: 16px;
          }
        }
      }
    }

    .upload-icon {
      width: 100px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dotted rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.4);

      i {
        font-size: 18px;
      }

      .text {
        line-height: 1em;
      }
    }
  }
}

.info_item {
  display: flex;
  align-items: center;
  .info_left {
    margin-right: 10px;
  }
}
.check-intelligence {
  cursor: pointer;
  &:hover {
    color: #0056E5;
    text-decoration:underline;
  }
}

::v-deep .el-form--inline .el-form-item {
    display: inline-block;
    margin-right: 10px;
    vertical-align: top;
}

</style>
</style>
