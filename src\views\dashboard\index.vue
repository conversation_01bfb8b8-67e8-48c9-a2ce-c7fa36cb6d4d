<template>
  <div class="home">
    <div class="cardContain">
      <div class="title">数据概况</div>
      <profileData></profileData>
    </div>
    <div class="cardContain">
      <div class="title">订单看板</div>
      <orderCard></orderCard>
    </div>
    <div class="cardContain">
      <div class="title">代办事项</div>
      <div class="lists">
        <el-row :gutter="20">
          <el-col :span="6"
            ><div class="item" @click="toOrder(1)">
              <div class="content">
                <div class="type">全部订单</div>
                <div class="number">{{ item.allOrderCount }}</div>
                <div class="msg">订单金额：￥{{ item.allOrderMoney }}</div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show1"></div></div
          ></el-col>
          <el-col :span="6"
            ><div class="item" @click="toOrder(2)">
              <div class="content">
                <div class="type">待发货</div>
                <div class="number">{{ item.waitShipmentsOrderCount }}</div>
                <div class="msg">
                  订单金额：￥{{ item.waitShipmentsOrderMoney }}
                </div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show2"></div></div
          ></el-col>
          <el-col :span="6"
            ><div class="item" @click="toOrder(3)">
              <div class="content">
                <div class="type">发货中</div>
                <div class="number">{{ item.shipmentsIngOrderCount }}</div>
                <div class="msg">
                  订单金额：￥{{ item.shipmentsIngOrderMoney }}
                </div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show3"></div></div
          ></el-col>
          <el-col :span="6"
            ><div class="item">
              <div class="content">
                <div class="type">待退款</div>
                <div class="number">{{ item.pendingSalesRefundInfo }}</div>
                <div class="msg">订单金额：￥{{ item.pendingMoney }}</div>
              </div>
              <img class="bgimg" src="@/assets/home/<USER>" alt="" />
              <div
                class="bg_r"
                :style="
                  'background-image: url(' + bgImg + ');background-size:cover'
                "
              >
                <img src="@/assets/home/<USER>" alt="" />
              </div>
              <div class="show4"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="cardContain">
      <div class="title">商品看板</div>
      <goodsCard> </goodsCard>
    </div>
    <div class="cardContain">
      <div class="title">客户看板</div>
      <customerCard></customerCard>
    </div>
  </div>
</template>

<script>
import profileData from "@/views/dashboard/cards/profileData";
import goodsCard from "@/views/dashboard/cards/goodsCard";
import OrderCard from "@/views/dashboard/cards/orderCard.vue";
import customerCard from "@/views/dashboard/cards/customerCard.vue";
import { getBacklog } from "@/api/dashboard/index";
import { setContextData, setToken } from "@/utils/auth.js";
export default {
  data() {
    return {
      item: {},
      bgImg: require("@/assets/home/<USER>"),
    };
  },
  methods: {
    toOrder(id) {
      // if (id == 1) {
      //   setContextData("tradeCenter_sales", 'WAIT_DELIVERY');
      //   this.$router.push("/tradeCenter/sales");
      // }
      // if (id == 2) {
      //   setContextData("tradeCenter_sales", {
      //     current: 1,
      //     size: 10,
      //     model: {
      //       publishStatusEnum: "WAIT_DELIVERY",
      //     },
      //   });
      // }
      // if (id == 3) {
      //   setContextData("tradeCenter_sales", {
      //     current: 1,
      //     size: 10,
      //     model: {
      //       publishStatusEnum: "WAIT_DELIVERY",
      //     },
      //   });
      // }
    },
    async getBacklog() {
      let { data } = await getBacklog();
      this.item = data;
    },
  },
  created() {
    this.getBacklog();
  },
  components: {
    profileData,
    OrderCard,
    goodsCard,
    customerCard,
  },
};
</script>

<style lang="scss" scoped>
.home {
  margin-top: 0;
  background-color: #f2f3f4;
  .cardContain:nth-of-type(1) {
    margin-top: 0;
  }
  .cardContain {
    min-width: 1200px;
    margin-top: 18px;
    background-color: #fff;
    padding: 20px 18px;
    border-radius: 10px;
    .title {
      border-left: 4px solid #0056e5;
      font-size: 20px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 600;
      color: #0f1831;
      padding-left: 10px;
      margin-bottom: 20px;
    }
    .lists {
      padding-bottom: 10px;
      .item {
        width: 100%;
        position: relative;
        cursor: pointer;
        transition: all 0.5s;
        .bgimg {
          width: 100%;
          height: 154px;
          z-index: 5;
          position: relative;
          display: block;
        }
        .bg_r {
          position: absolute;
          top: 0;
          right: 0;
          z-index: 5;
          height: 100%;
          width: 200px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding-right: 30px;
          img {
            width: 45px;
            height: 45px;
          }
        }
        .content {
          top: 38px;
          color: #fff;
          position: absolute;
          left: 30px;
          z-index: 6;
          .type {
            font-size: 16px;
          }
          .msg {
            font-size: 14px;
          }
          .number {
            font-size: 32px;
            padding: 10px 0;
          }
        }
        .show1,
        .show2,
        .show3,
        .show4 {
          width: 80%;
          height: 10px;
          margin: 0 auto;
          position: absolute;
          bottom: 5px;
          left: 10%;
          z-index: 0;
          transition: all 0.5s;
        }
      }
      .item:hover {
        transform: translateY(-5px);
      }
      .item:hover .show1 {
        box-shadow: 0 10px 30px 0px #2864ff;
      }
      .item:hover .show2 {
        box-shadow: 0 10px 30px 0px #ff5d66;
      }
      .item:hover .show3 {
        box-shadow: 0 10px 30px 0px #1695bb;
      }
      .item:hover .show4 {
        box-shadow: 0 10px 30px 0px #6c32fa;
      }
    }
  }
}
</style>
