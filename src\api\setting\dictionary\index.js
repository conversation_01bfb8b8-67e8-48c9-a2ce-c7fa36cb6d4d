import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

export function list(data) {
    return requestAxios({
        url: '/api/authority/dictionary/page',
        method: 'post',
        data
    })
}


export function editApi(data) {
    return requestAxios({
        url: '/api/authority/dictionary',
        method: data.id == 0 ? 'post' : 'put',
        data
    })
}

export function deleteApi(id) {
    return requestAxios({
        url: '/api/authority/dictionary?ids[]=' + id,
        method: 'delete'
    })
}