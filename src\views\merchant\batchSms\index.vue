<template>
  <div class="manual_quality_control">
    <div class="list">
      <div class="top">
        <el-tabs v-model="query.model.taskStatus" @tab-click="search">
          <el-tab-pane v-for="item in taskStatusList" :label="item.label" :name="item.value" />
        </el-tabs>
        <el-button class="refresh" size="mini" @click="search">刷新</el-button>
      </div>
      <el-button style="width: 100%;margin-bottom: 10px" type="primary" size="small" @click="showCreateTaskDialog">新建任务</el-button>
      <el-input v-model="query.model.taskName" placeholder="搜索任务名称" size="small" style="margin-bottom: 10px;" />
      <el-date-picker
        style="width: 100%;"
        value-format="yyyy-MM-dd"
        v-model="query.model.createTime"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="small"
      />
      <div class="task_list" v-loading="taskLoading" v-infinite-scroll="load">
        <TaskItem
          v-for="item in taskList"
          :activated="item.id === currentTaskId"
          :key="item.id" :item="item"
          @addPerson="showImportPersonDialog"
          @click="updateCurrentTaskId"
        />
        <div v-if="taskList.length === 0 && !taskLoading" style="text-align: center;color: #999;">暂无数据</div>
      </div>
    </div>
    <div class="content">
      <TaskTable ref="taskTable" @handleTaskSuccess="search" :taskStatus="query.model.taskStatus" />
    </div>
    <AddTaskDialog ref="addTaskDialogRef" @createSuccess="search" />
    <ImportPersonDialog ref="importPersonDialogRef" :taskId="currentTaskId" />
  </div>
</template>

<script>
import TaskItem from './components/taskItem.vue';
import { getSmsTaskList } from "@/api/retailStore";
import { deepClone } from "@/utils";
import TaskTable from "./components/taskTable.vue";
import AddTaskDialog from "./components/addTaskDialog.vue";
import ImportPersonDialog from "./components/importPersonDialog.vue";

export default {
  name: 'importPersonDialog',
  components: {
    TaskItem,
    TaskTable,
    AddTaskDialog,
    ImportPersonDialog
  },
  data() {
    return {
      taskStatusList: [ // 任务状态列表
        { label: '待开始', value: 'NOT_STARTED' },
        { label: '进行中', value: 'PROGRESS' },
        { label: '已结束', value: 'COMPLETED' },
      ],
      currentTaskSendStatus: undefined, // 当前发送状态
      taskSendStatusList: [ // 发送状态列表
        { label: '待发送', value: '' },
        { label: '发送成功', value: '' },
        { label: '发送失败', value: '' }
      ],
      currentTaskId: '', // 当前任务id
      taskList: [], // 任务列表
      taskTotal: 0,
      taskLoading: false,
      query: {
        current: 1,
        size: 10,
        model: {
          startTime: '',
          endTime: '',
          taskName: '',
          createTime: [],
          taskStatus: 'NOT_STARTED'
        }
      },
    }
  },
  mounted() {
    this.query.model.taskStatus = 'NOT_STARTED'
    this.search()
  },
  methods: {
    // 触底加载
    load() {
      if (this.taskTotal === this.taskList.length || this.taskList.length === 0) return;
      this.query.current++
      this.setSmsTaskList()
    },
    // 搜索
    search() {
      this.query.current = 1
      this.taskList = []
      this.setSmsTaskList()
    },
    // 展示新建任务dialog
    showCreateTaskDialog() {
      this.$refs.addTaskDialogRef.show()
    },
    // 展示导入名单dialog
    showImportPersonDialog(id) {
      this.$refs.importPersonDialogRef.show(id)
    },
    // 更新当前任务id
    updateCurrentTaskId(id) {
      if (this.currentTaskId === id) return
      this.currentTaskId = id
      this.$refs.taskTable.init(this.currentTaskId)
    },
    // 设置短信任务列表
    setSmsTaskList() {
      this.taskLoading = true
      let query = deepClone(this.query)
      const createTime = query.model.createTime
      let startTime = undefined
      let endTime = undefined
      if (Array.isArray(createTime) && createTime.length === 2) {
        startTime = createTime[0]
        endTime = createTime[1]
      }
      query.model = { ...query.model, startTime, endTime }
      getSmsTaskList(query).then(res => {
        this.taskTotal = res.total
        this.taskList.push(...res.records)
        if (res.records.length > 0) {
          if (this.taskList.findIndex(item => item.id === this.currentTaskId) === -1) {
            this.currentTaskId = this.taskList[0].id
            this.$refs.taskTable.init(this.currentTaskId)
          }
        }
      }).finally(() => {
        this.taskLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.manual_quality_control {
  $backgroundColor: #fff;
  $padding: 16px;
  display: flex;
  box-sizing: border-box;
  height: calc(100vh - 86px - 32px);
  .list {
    width: 300px;
    background-color: $backgroundColor;
    border-radius: $padding / 2;
    box-sizing: border-box;
    padding: $padding;
    display: flex;
    flex-direction: column;
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      .refresh {
        cursor: pointer;
        font-size: 14px;
        position: relative;
        margin-left: 10px;
        top: -3px;
      }
    }
    .task_list {
      margin-top: $padding;
      flex: 1;
      overflow-y: auto;
    }
  }
  .content {
    flex: 1;
    box-sizing: border-box;
    margin-left: $padding;
    border-radius: $padding / 2;
    overflow: hidden;
    overflow-y: auto;
  }
}
</style>