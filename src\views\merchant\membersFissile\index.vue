<template>
  <div class="tab_bg">
    <tabs-layout v-model="currentTabValue" ref="tabs-layout" :tabs="tabs">
      <template slot="button">
        <div>
          <span style="color: #333">
            <span>合伙人功能</span>
            <span :style="{color: partnerFunctionStatus === 'Y' ? '#0056e5' : 'red'}">
              已{{ partnerFunctionStatus === 'Y' ? '启用' : '关闭' }}
            </span>, 点击
          </span>
          <el-button plain :loading="loading" :type="partnerFunctionStatus === 'N' ? 'primary' : ''" @click="setEnablePartnerFunction">
            {{ partnerFunctionStatus === 'Y' ? '关闭' : '启用' }}功能
          </el-button>
        </div>
      </template>
    </tabs-layout>
    <MembersTeam v-if="currentTabValue === '1'" />
    <RewardSetting v-if="currentTabValue === '2'" />
    <FissionSetting v-if="currentTabValue === '3'" />
    <LevelSettings v-if="currentTabValue === '4'" />
  </div>
</template>

<script>
import MembersTeam from "./membersTeam.vue";
import RewardSetting from "./rewardSetting.vue";
import FissionSetting from "./fissionSetting.vue";
import LevelSettings from '@/views/merchant/membersFissile/levelSettings.vue'
import { enablePartnerFunction, getPartnerFunction } from '@/api/retailStore'

export default {
  name: 'membersFissile',
  components: {
    LevelSettings,
    MembersTeam,
    RewardSetting,
    FissionSetting
  },
  data() {
    return {
      currentTabValue: '1',
      partnerFunctionStatus: '',
      loading: false,
      tabs: [
        {
          name: '合伙人',
          value: '1'
        },
        {
          name: '奖励设置',
          value: '2'
        },
        {
          name: '裂变设置',
          value: '3'
        },
        {
          name: '等级设置',
          value: '4'
        }
      ]
    }
  },
  created() {
    this.setPartnerFunctionStatus()
  },
  methods: {
    setPartnerFunctionStatus() {
      getPartnerFunction().then(res => {
        this.partnerFunctionStatus = res.code
      })
    },
    setEnablePartnerFunction() {
      this.loading = true
      enablePartnerFunction(this.partnerFunctionStatus === 'Y' ? 'N' : 'Y').then(() => {
        this.$message.success('操作成功')
        this.setPartnerFunctionStatus()
      }).finally(() => {
        this.loading = false
      })
    },
  }
}

</script>

<style lang="scss" scoped></style>
