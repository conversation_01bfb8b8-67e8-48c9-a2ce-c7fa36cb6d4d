<template>
  <div class="archivesEditContent">
    <im-search-pad :is-expand.sync="isExpand" @reset="resetForm" @search="onSearchSubmitFun">
      <im-search-pad-item prop="timeRange">
      <el-date-picker
        v-model="listQuery.model.timeRange"
        range-separator="-"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="daterange"
        start-placeholder="开始时间"
        :default-time="['00:00:00', '23:59:59']"
        unlink-panels
        style="width: 100%"
        end-placeholder="结束时间"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="depIds">
        <el-cascader style="width: 400px;" placeholder="请选择部门" v-model="listQuery.model.depIds" :options="organizationTree" :props="cascaderProps" clearable filterable />
      </im-search-pad-item>
      <im-search-pad-item prop="personName">
        <el-input v-model="listQuery.model.personName" placeholder="业务员姓名" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="approvalStatusList" v-model="listQuery.model.approvalStatus">
        <template slot="button">
          <paged-export :max-usable="maxUsableExport" controllable :before-close="handleExportBeforeClose" />
        </template>
      </tabs-layout>
      <div style="background-color: #fff; margin-bottom: 20px">
        <div class="statistics">
          <div class="statistics_item">
            <span class="text">拜访客户数（个）</span>
            <span class="num">{{ listTotal.sigInPurCount }}</span>
          </div>
          <div class="statistics_item">
            <span class="text">拜访次数（人）</span>
            <span class="num">{{ listTotal.sigInCount }}</span>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          @select="onSelect"
          @select-all="onAllSelect"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column align="center" width="65" show-overflow-tooltip :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '180px'"
            :width="item.name == 'pictIdS' ? item.width : ''"
            :label="item.label"
          >
            <template slot-scope="{ row }">
              <span v-if="item.name === 'accountTotal'" style="color: #0056e5">
                {{ row[item.name] }}
              </span>
              <span v-else-if="item.name === 'status'">
                {{ row[item.name] ? row[item.name].desc : '' }}
              </span>
              <span v-else-if="item.name === 'beginTime'">
                {{ row['beginTime'] + ' 至 ' + row['endTime'] }}
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="150" class="itemAction">
              <template slot-scope="{row}">
                <el-button type="text" @click="gotoDetail(row.personName)">查看明细</el-button>
              </template>
            </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { getSgInReportList, getSgInReporStatistics, sginReporExport } from '@/api/salemanCenter/index'
import { getOrganizationList, getOrganizationTreeById } from "@/api/organization/index";
import tableInfo from './tableInfo'
import Pagination from '@/components/Pagination'
import TabsLayout from '@/components/TabsLayout'
import { MessageConfirmExport, deepClone } from '@/utils/index';
import PagedExport from "@/components/PagedExport/index";

export default {
  name: 'checkInReport',
  components: {
    Pagination,
    TabsLayout,
    PagedExport
  },
  watch: {
    listQuery: {
      handler(val) {
        const model = val.model
        if (Array.isArray(model.timeRange) && model.timeRange.length > 0) {
          this.listQuery.model.startTime = model.timeRange[0]
          this.listQuery.model.endTime = model.timeRange[1]
        } else {
          this.listQuery.model.startTime = ''
          this.listQuery.model.endTime = ''
        }
        sessionStorage.setItem('listQuery', JSON.stringify(model))
      },
      deep: true
    }
  },
  data() {
    return {
      organizationTree: [],
      cascaderProps: {
        // multiple: true,
        checkStrictly: true,
        label: 'name',
        value: 'id', 
        emitPath: false
      },
      maxUsableExport: 0,
      isExpand: false,
      tableTitle: [],
      list: [],
      listTotal: {
        persions: 0,
        schoolTotal: 0
      },
      total: 0,
      listLoading: false,
      tableSelectTitle: [0, 1, 2, 3],
      listQuery: {
        model: {
          depIds: '',
          endPage: '',
          endTime: '',
          exceptionReason: '',
          personName: '',
          purMerchantName: '',
          saleMerchantId: '',
          saleMerchantName: '',
          startPage: '',
          startTime: ''
        },
        current: 1,
        size: 10
      },
    }
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '签到报表',
          value: 'ALL'
        }
      ]
    }
  },
  methods: {
    async setDepSelection() {
      return new Promise((resolve, reject) => {
        getOrganizationList().then(res => {
          if (res.code !== 0) return
          const organizationList = res.data
          if (Array.isArray(organizationList) && organizationList.length > 0) {
            const organizationItem = organizationList[0]
            getOrganizationTreeById(organizationItem.id).then(resTree => {
              if (resTree.code !== 0 || !Array.isArray(resTree.data)) return
              this.organizationTree = resTree.data
              resolve()
            }).catch(() => {
              reject()
            })
          }
        }).catch(() => {
          reject()
        })
      })
    },
    getChildrenIds(tree, id) {
      var result = [];
      for (var i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          if (tree[i].children) {
            for (var j = 0; j < tree[i].children.length; j++) {
              result.push(tree[i].children[j].id);
              var childResult = this.getChildrenIds(tree[i].children, tree[i].children[j].id);
              result = result.concat(childResult);
            }
          }
          break;
        } else if (tree[i].children) {
          result = result.concat(this.getChildrenIds(tree[i].children, id));
        }
      }
      return result;
    },
    getListQuery() {
      const listQueryStorage = sessionStorage.getItem('listQuery')
      let listQuery = deepClone(this.listQuery)
      if (listQueryStorage) {
        listQuery = { ...listQuery, model: JSON.parse(listQueryStorage) }
        this.listQuery = deepClone(listQuery)
      }
      let depIds = listQuery.model.depIds
      let depIdList = []
      if (!depIds || Array.isArray(depIds) && depIds.length === 0) {
        listQuery.model.depIds = []
      } else {
        // 递归，拿到子节点的id
        if (Array.isArray(depIds) && depIds.length > 0) depIds = depIds[0]
        depIdList = [depIds, ...this.getChildrenIds(this.organizationTree, depIds)]
        listQuery.model.depIds = depIdList
      }
      return listQuery
    },
    handleExportBeforeClose(val) {
      sginReporExport({...this.getListQuery().model, ...val}).then(res => {
        if (res.code !== 0) return
        MessageConfirmExport()
      })
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            append-to-body
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              onChange={this.setleftTitleFun}
              data={titles}
              titles={titlesName}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    setHeaer: function () {
      var titles = tableInfo[0]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    },
    showHeaer: function () {
      this.showSelectTitle = true
    },
    closeHeaer: function () {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    getListTotal() {
      getSgInReporStatistics(this.getListQuery()).then((res) => {
        if (res.code !== 0) return
        this.listTotal = res.data
      })
    },
    async getList() {
      this.listLoading = true
      getSgInReportList(this.getListQuery()).then(res => {
        const data = res.data
        this.list = data.records
        this.total = data.total
        this.maxUsableExport = data.total
        this.getListTotal()
      }).finally(() => {
        this.listLoading = false
      })
    },
    onSearchSubmitFun: function () {
      this.listQuery.current = 1
      this.list = []
      this.getList()
    },
    resetForm() {
      Object.assign(this.$data.listQuery, this.$options.data().listQuery)
      sessionStorage.removeItem('listQuery')
      this.getList()
    },
    initTbaleTitle() {
      this.tableSelectTitle = [99]
      this.tableTitle = tableInfo.ALL
      this.tableTitle = this.tableTitle.filter((item) => {
        return item.key != 99
      })
    },
    // table 选中
    onAllSelect(selection) {
      this.onSelect(selection)
    },
    onSelect: function (val) {
      this.multipleSelection = val
    },
    gotoDetail(name) {
      const { startTime, endTime } = this.listQuery.model
      this.$router.push(`/salesmanCenter/business/photographSingUp?name=${name}&beginTime=${startTime}&endTime=${endTime}`)
    }
  },
  async mounted() {
    this.initTbaleTitle()
    await this.setDepSelection()
    this.getList()
  }
}
</script>
<style lang="less" scoped>
.archivesEditContent {
  .statistics {
    display: flex;
    background-color: #f8f8f8;
    box-sizing: border-box;
    padding: 20px 30px;
    .statistics_item {
      display: flex;
      flex-direction: column;
      margin-right: 80px;
      .text {
        color: #666;
      }
      .num {
        margin-top: 15px;
        font-size: 24px;
      }
    }
  }
}
</style>
