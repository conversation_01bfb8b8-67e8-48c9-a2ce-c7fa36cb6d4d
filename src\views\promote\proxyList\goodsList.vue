<template>
  <div>
    <div class="">
      <el-form
        :inline="true"
        ref="searchForm"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model.trim="listQuery.model.productCode"
            placeholder="请输入商品编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="factory">
          <el-input
            v-model.trim="listQuery.model.productName"
            placeholder="请输入商品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="factory">
          <el-input
            v-model.trim="listQuery.model.drugName"
            placeholder="请输入商品通用名称"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchSubmitFun">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        v-loading="listLoading"
        @row-click="handleRowClick"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
        max-height="450px"
      >
        <el-table-column align="center" width="80" label="序号">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>

        <el-table-column align="center" width="80" label="选择">
          <template slot-scope="{ row }">
            <el-checkbox
              @change="selectTableItemFun(row)"
              v-model="goodsCheckId"
              :true-label="row.id"
              :false-label="''"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <img
              v-if="item.name == 'pictIdS'"
              :src="splitString(row.pictIdS)[0]"
              style="width:50px;height:50px"
            />
            <span
              v-else-if="item.name == 'retailPrice'"
              style="color: #ff782b"
              >{{ row[item.name] }}</span
            >
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex_between_center">
        <div></div>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
    <div style="text-align: right" class="dialog-footer">
      <el-button @click="selectCancel">取 消</el-button>
      <el-button type="primary" @click="addrConfirm" :disabled="!goodsCheckId">确 定</el-button>
    </div>
  </div>
</template>

<script>
const tableTitle = [
  {
    label: "商品主图",
    name: "pictIdS",
    width: "70px",
  },
  {
    label: "商品编码",
    name: "productCode",
    width: "170px",
  },
  {
    label: "商品名称",
    name: "productName",
    width: "120px",
  },
  {
    label: "通用名称",
    name: "drugName",
    width: "120px",
  },
  {
    label: "规格",
    name: "spec",
    width: "120px",
  },
  {
    label: "生产厂家",
    name: "manufacturer",
    width: "120px",
  },
  {
    label: "销售价",
    name: "salePrice",
    width: "120px",
  },
];
import request from "@/utils/request";
import requestAxios from '@/utils/request';
import Pagination from "@/components/Pagination";
export default {
  data() {
    return {
      list: [],
      tableTitle,
      listLoading: false,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          productCode: "",
          productName: "",
          manufacturer: "",
          "whetherOnSale":"Y"
        },
      },
      goodsCheckId: "",
    };
  },
  methods: {
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    addrConfirm() {
      // if(this.goodsCheckId == '' || !this.goodsCheckId){
      //   this.$message.error('请选择需要推广的商品')
      //   return
      // }
      let obj = this.list.find((item) => {
        if (item.id == this.goodsCheckId) {
          return item;
        }
      });
      this.$emit("update:row", obj);
      this.selectCancel();
    },
    selectCancel() {
      this.$emit("update:showFlag", false);
    },
    resetForm() {
      this.list = [];
      this.total = 0;
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          productCode: "",
          productName: "",
          manufacturer: "",
        },
      };
      this.getlist();
    },
    async chekId(id) {
      let {data} = await  requestAxios({
        url: '/agent/agentProduct/merchant/insert-agency-product/isExist/' + id,
        method: 'post'
      })
      if(data) {
        this.$message.error('该商品已添加推广，请选择其他商品')
        this.goodsCheckId = ''
      } else {
        this.goodsCheckId = id
      }
    },
    selectTableItemFun(row) {
      if (this.goodsCheckId == row.id) {
        this.goodsCheckId = "";
      } else {
        this.chekId(row.id)
      }
    },
    handleRowClick(row) {
      if (this.goodsCheckId == row.id) {
        this.goodsCheckId = "";
      } else {
        this.chekId(row.id)
      }
    },
    async getlist() {
      this.listLoading = true;
      const { data } = await request({
        url: "/agent/product/admin/product/page",
        data: this.listQuery,
        method: "post",
      });
      this.listLoading = false;
      this.total = data.total;
      this.list = data.records;
    },
  },
  created() {
    this.getlist();
  },
  props: {
    showFlag: {
      type: Boolean,
    },
    row: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    Pagination,
  },
};
</script>

<style lang="less" scoped>
.dialog-footer {
  margin: 0 -20px;
  margin-bottom: -20px;
  border-top: 1px solid #ddd;
  padding: 10px 20px;
  margin-top: 30px;
}
/deep/ .pagination-container{
  height: 60px;
  margin-top: 0;
}
</style>
