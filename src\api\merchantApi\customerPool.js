import request from "@/utils/request";

// 客户池分页列表查询
export function getMerchantPoolList(data) {
  return request({
    url: `/merchant/admin/purMerchantPoolInfo/page`,
    method: "post",
    data,
  });
}

// 根据id查询客户池信息
export function getMerchantPoolDetail(id) {
  return request({
    url: `/merchant/admin/purMerchantPoolInfo/${id}`,
    method: "get",
  });
}

// 客户池编辑
export function merchantPoolEdit(data) {
  return request({
    url: `/merchant/admin/purMerchantPoolInfo`,
    method: "put",
    data,
  });
}

// 根据id查询客户池信息
export function checkPoolName(name) {
  return request({
    url: `/merchant/admin/purMerchantPoolInfo/CheckByName/${name}`,
    method: "get",
  });
}

// 获取业务员列表
export function getSaleManList(data) {
  return request({
    url: `/crm/merchant/salesman/getSalesManByNameOrMobile`,
    method: "post",
    data
  });
}

// 客户池明细分页列表查询
export function getMerchantPoolDetailList(data) {
  return request({
    url: `/merchant/admin/purMerchantPool/page`,
    method: "post",
    data,
  });
}

// 客户池批量分配
export function merchantPoolAllot(data) {
  return request({
    url: `/merchant/admin/purMerchantPool/feign/purMerchantPooldistribution`,
    method: "post",
    data,
  });
}

// 客资流转记录分页列表查询
export function getMerchantPoolCirculationLogList(data) {
  return request({
    url: `/merchant/admin/purMerchantPoolCirculationLog/page`,
    method: "post",
    data,
  });
}