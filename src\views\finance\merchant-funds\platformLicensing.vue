<template>
  <div class="tab_bg">
    <tabs-layout
      v-model="activeName"
      :tabs="[{ name: '商家服务中心', value: '1' }, { name: '缴费记录', value: '2' }]"
      @change="handleClick"
    />
    <template v-if="activeName === '1'">
      <el-table
        ref="table"
        v-if="checkPermission(['admin','deposit:view'])"
        v-loading="loading"
        :data="tableData"
        style="width: 100%;"
        stripe
        border
      >
        <el-table-column type="index" />
        <el-table-column prop="name" label="收费项目" width="200" show-overflow-tooltip />
        <el-table-column prop="configAmount" label="所需缴纳费用" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span v-if="row.configAmount !== 0">￥</span><span>{{ row.configAmount|getDecimals }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="已缴金额" width="100" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span v-if="row.amount !== 0">￥</span><span>{{ row.amount|getDecimals }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="有效期" min-width="200" show-overflow-tooltip />
        <el-table-column prop="name" label="操作" width="115">
          <template slot-scope="{row}">
            <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button type="text" @click="openCaisher(row)">缴纳平台使用费</el-button>
                </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="activeName === '2'">
      <table-pager v-if="checkPermission(['admin','deposit-record:view'])" ref="todoTable" :options="tableTitle" :remote-method="loadPay" :isNeedButton="false" :data.sync="tablePay">
        <template slot="financePaymentFlow.certificatePath">
          <el-table-column label="付款凭证"  width="130">
            <slot slot-scope="{row}">
              <el-image
                style="height: 60px;width:100px"
                :src="row.financePaymentFlow.certificatePath"
                :preview-src-list="[row.financePaymentFlow.certificatePath]"
                fit="contain"
              ></el-image>
            </slot>
          </el-table-column>
        </template>
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </template>
    <cashier-dialog :cashierFlag="cashierVisible" @changeShow="changeShow" :data="cashier" :type="type"></cashier-dialog>
  </div>
</template>

<script>
  import {merchantsPlatformMoneyPage,merchantsPlatformMoney,refundDeposit,cancel } from '@/api/finance'
  const TableColumns = [
    { label: "业务类型", name: "type.desc",prop: "type.desc",width: "120"},
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "银行流水号", name: "financePaymentFlow.serialNumber", prop:"financePaymentFlow.serialNumber",width: "190" },
    { label: "付款凭证", name: "financePaymentFlow.certificatePath", prop:"financePaymentFlow.certificatePath",slot: true,width:'100' },
    { label: "支付时间", name: "financePaymentFlow.paymentTime", prop:"financePaymentFlow.paymentTime",width: '170' },
    { label: "保证金名称", name: "name", prop:"name",width: '150' },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: '120' },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName",width: '200'},
    { label: "金额（元）", name: "amount", prop:"amount",slot: 'true' },
    { label: "制单人", name: "createUserName", prop:"createUserName",width: '180' },
    { label: "制单时间", name: "createTime", prop:"createTime",width: '170' },
  ]
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "@/utils/permission";
  import CashierDialog from './cashier-dialog'
  export default {
    name: "deposit",
    components: {
      CashierDialog
    },
    data() {
      return {
        tableTitle: TableColumnList,
        activeName: '1',
        tableData: [],
        tablePay: [],
        loading: true,
        cashierVisible: false,
        cashier: '',
        type: 'platform'
      }
    },
    mounted() {
      this.load()
    },
    methods: {
      checkPermission,
      openCaisher(row) {
        this.cashierVisible=true
        this.cashier = row
      },
      //申请退还
      refund(row) {
        refundDeposit({id:row.id}).then(res=>{
          if(res.code===0) {
            this.$message.success('申请成功！')
            this.load()
          }
        })
      },
      //取消
      cancel(row) {

      },
      changeShow(data) {
        if (data === 'false') {
          this.cashierVisible = false
          this.load()
        } else {
          this.cashierVisible = true
        }
      },
      async loadPay(params) {
        const listQuery = {
          model: {}}
        Object.assign(listQuery, params)
        this.loading = false
        return await merchantsPlatformMoneyPage(listQuery)
      },
      async load() {
        this.loading = false
        const {data} = await merchantsPlatformMoney({})
        this.tableData = data
      },
      handleClick(tab, index) {
        if(tab.value === '2') {
          this.handleRefresh({
            page: 1,
            pageSize: 10
          })
        }

      },
      handleRefresh(pageParams) {
        this.$nextTick(() => {
          this.$refs.todoTable && this.$refs.todoTable.doRefresh(pageParams)
        })
      },

    }
  }
</script>

<style lang="scss">
</style>
