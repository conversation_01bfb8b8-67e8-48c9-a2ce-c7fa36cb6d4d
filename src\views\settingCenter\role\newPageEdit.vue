<template>
  <div class="roleEditContent">
    <page-title :title="$route.query.id == null ? '新增角色' : '编辑角色'">
      <template>
        <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
      </template>
    </page-title>
    <el-form class="form" :model="query" ref="ruleForm" label-width="90px">
      <!-- <el-form-item class="formItem" prop="role.code" label="角色编码:" :rules="[{ required: true, message: '请填写角色编码',trigger: 'blur' }]">
        <el-input :disabled="query.role.id>0" clearable  v-model="query.role.code" placeholder="请填写角色编码"></el-input>
      </el-form-item> -->
      <el-form-item class="formItem" prop="role.name" label="角色名称:" :rules="[{ required: true, message: '请填写角色名称',trigger: 'blur' }]">
        <el-input clearable v-model="query.role.name" placeholder="请填写角色名称"></el-input>
      </el-form-item>
      <el-form-item class="formItem" prop="role.status" label="状态:" :rules="[{ required: true, message: '请选中状态',trigger: 'blur' }]">
        <el-radio-group  v-model="query.role.status">
          <el-radio-button :label="true">启用</el-radio-button>
          <el-radio-button :label="false">禁用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="formItem" prop="role.describe" label="描述:"
        :rules="[
          { required: true, message: '请填写角色描述',trigger: 'blur' },
          { max: 50, message: '角色描述，最多50个字',trigger: ['blur','change'] }
        ]"
      >
        <el-input
          maxlength="50"
          show-word-limit
          type="textarea"
          clearable
          style="width:100%;"
          rows="2"
          v-model="query.role.describe"
          placeholder="请输入角色描述，最多50个字"
        ></el-input>
      </el-form-item>
      <el-form-item label="权限设置：" v-if="query.roleAuthority!=null">
          <roleAuthority :roleAuthorityInfo.sync="query.roleAuthority"></roleAuthority>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import roleAuthority from '@/views/settingCenter/role/roleAuthority'
import {editRoleAndAuthority,findAuthorityIdByRoleId,getApi,checkUserRoleCodeApi } from '@/api/setting/permission/userRole'
import PageTitle from "../../../components/PageTitle/index";
export default {
  data() {
    return {
      query: {
        role:{
          	// code: "",
            describe: "",
            dsType: "ALL",
            name: "",
            orgList: [],
            status: true
        },
        roleAuthority:null,
      },
      check: [],
      data: [],
      role: {},
      id: '',
      appList: []
    };
  },
  components:{
    PageTitle,
    roleAuthority
  },
  methods: {
    goBackFun(){
      this.$router.go(-1)
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    clearFun: function() {
      var _this=this;
      _this.goBackFun()
    },
    submitFun: function(businessScopeForm) {
      let _this = this
      this.$refs[businessScopeForm].validate(async valid => {
        if (valid) {

          let res = await editRoleAndAuthority(_this.query);
          if(res.code == 0){
            this.$message.success('操作成功');
            _this.goBackFun();
          }

          // if(_this.$route.query.id == null) {
          //   const data = await checkUserRoleCodeApi(this.query.role.code)
          //   if(data.code==0 && data.data==false) {
          //     await editRoleAndAuthority(_this.query)
          //     _this.goBackFun()
          //   } else {
          //     this.$message.error('编码已存在')
          //   }
          // } else {
          //   await editRoleAndAuthority(_this.query)
          //   _this.goBackFun()
          // }

        } else {
          return false;
        }
      })
    },
    async getFun() {
      const { data } = await getApi(this.$route.query.id);
      this.query.role =Object.assign(this.query.role,data);
      this.getFindAuthorityIdByRoleId();
    },
    async getFindAuthorityIdByRoleId(){
      let {data} = await findAuthorityIdByRoleId(this.$route.query.id)
      this.query.roleAuthority=data
    }
  },
  async mounted() {
    if (this.$route.query.id > 0) {
      await this.getFun();
    }else{
      this.query.roleAuthority={}
    }

  },
};
</script>
<style lang="less" scoped>
.roleEditContent {
  // margin: -30px -20px;
   border-top: 1px solid #ebecee;
  padding: 0px 20px 20px;
  background-color: #fff;
  .formItem{text-align: left;}
}
</style>
