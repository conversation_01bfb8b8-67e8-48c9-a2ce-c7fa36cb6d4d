<template>
    <!-- 弹窗选择商品 start -->
    <el-dialog title="选择商品" width="1000px" @close="onClose" :visible.sync="visible_">
        <!-- 搜索表单 start -->
        <im-search-pad :has-expand="false" :model="queryParams.model" @reset="handleReset" @search="handleSearch">

            <im-search-pad-item prop="productCode">
                <el-input v-model="queryParams.model.brandName" placeholder="请输入品牌名称" />
            </im-search-pad-item>

        </im-search-pad>
        <!-- 搜索表单 end -->

        <!-- 商品列表 start -->
        <el-table ref="table" v-loading="dialogLoading" border style="width: 100%" :data="dialogTableData">
            <el-table-column width="50" align="center" label="序号">
                <template slot-scope="scope">
                    <span>{{ scope.$index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column width="50" align="center" label="选择">
                <template slot-scope="scope">
                    <el-radio @change="onChange" v-model="current" :label="scope.$index"></el-radio>
                </template>
            </el-table-column>
            <template v-for="item in dialogTableColumns">
                <el-table-column v-if="item.slot && item.name === 'brandLogo'" :label="item.label" :prop="item.prop"
                    :width="item.width">
                    <template slot-scope="{ row }">
                        <img :src="row[item.name] | imgFilter" width="50px" height="50px">
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip v-else :label="item.label" :prop="item.prop">
                </el-table-column>
            </template>
        </el-table>
        <!-- 商品列表 end -->

        <!-- 分页器 start -->
        <div class="pagination">
            <el-pagination :disabled="dialogLoading" background @size-change="handleSizeChange"
                @current-change="handleCurrentChange" :current-page="queryParams.current" :pageSizes="[10, 20, 50, 100]"
                :page-size="queryParams.size" layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div>
        <!-- 分页器 end -->


        <template slot="footer">
            <el-button @click="visible_ = false">取消</el-button>
            <el-button type="primary" @click="onConfirmSelectClick">确定</el-button>
        </template>
    </el-dialog>
    <!-- 弹窗选择商品 end -->
</template>
<script>
const DialogTableColumns = [
    { label: '品牌logo', name: 'brandLogo', prop: 'brandLogo', slot: true, width: 80 },
    { label: '品牌', name: 'brandName', prop: 'brandName', width: 110 },
    { label: '生产厂家', name: 'manufacturer', prop: 'manufacturer', width: 100 }
].map((v, i) => ({ key: i, ...v }))
import { fetchBrandList } from "@/api/product"
const MODEL = 'UPDATE_MODEL'
export default {
    props: {
        value: {
            type: Object,
            default: () => {
                id: null
            }
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    model: {
        porp: 'value',
        event: MODEL
    },
    watch: {
        value: {
            handler(val) {
                if (!val || !val.id) return
                if (this.dialogTableData.length) {
                    // this.currentBrand = val;
                    let index = this.dialogTableData.findIndex(v => v.id === val.id);
                    if (~index) {
                        this.brand = this.dialogTableData[index]
                        this.current = index;
                    }
                }

            },
            immediate: true
        },
        visible: {
            handler(val) {
                this.visible_ = val
            },
            immediate: true
        },
    },
    data() {
        return {
            brand: {},
            visible_: false,
            current: null,
            total: 0,
            // 弹窗商品列表表头
            dialogTableColumns: DialogTableColumns,
            dialogTableData: [],
            queryParams: {
                current: 1,
                size: 10,
                map: {},
                model: {
                    brandName: ''
                },
                order: 'descending',
                sort: 'id'
            }
        }
    },
    created() {
        this.fetch();
    },
    methods: {
        
        onClose() {
            this.$emit('update:visible', false)
        },
        onChange(index) {
            this.brand = this.dialogTableData[index]
        },
        /**
         * 搜索栏重置按钮
         */
        handleReset() {
            this.queryParams = {
                ...this.queryParams,
                size: this.queryParams.size,
                current: 1,
                model: {
                    brandName: ''
                }
            }
            this.fetch();
        },

        /**
         * 搜索栏搜索按钮
         * */
        handleSearch() {
            this.queryParams = {
                ...this.queryParams,
                current: 1
            }
            this.fetch()
        },

        /**
         * 页码大小变化
         */
        handleSizeChange(size) {
            this.queryParams = {
                ...this.queryParams,
                current: 1,
                size,

            }
            this.fetch();

        },

        /**
         * 页码变化
         */
        handleCurrentChange(current) {
            this.queryParams = {
                ...this.queryParams,
                current
            }
            this.fetch();
        },


        /**
         *  列表[单选/取消单选]事件
         */
        handleSelect(selection, row) {
            let matchField = this.matchField;
            let isAdd = selection.some(v => v[matchField] === row[matchField]);
            if (isAdd) {
                this.selects.push(row)
            } else {
                let index = this.selects.findIndex(v => v[matchField] === row[matchField]);
                if(~index) {
                    this.selects.splice(index, 1)
                }
            }
        },

        /**
         *  列表[全选/取消全选]事件
         */
        handleSelectAll(selection) {
            let isAdd = !!selection.length;
            let matchField = this.matchField;
            if (isAdd) {
                selection.forEach(item => {
                    if (this.selects.every(v => v[matchField] !== item[matchField])) {
                        this.selects.push(item)
                    }
                })
            } else {
                this.dialogTableData.forEach(item => {
                    let index = this.selects.findIndex(v => v[matchField] === item[matchField])
                    if (index >= 0) {
                        this.selects.splice(index, 1)
                    }
                })
            }
        },
        /**
         * 确认选择商品
         */
        onConfirmSelectClick() {
            if (!this.brand.id) {
                this.$message.warning('请选择品牌')
                return
            }
            this.visible_ = false;
            
            this.$emit(MODEL, this.brand)
            this.$emit('change', this.brand)
        },

        /**
         * 请求接口获取弹窗商品列表数据
         */
        async fetch() {
            if (this.dialogLoading) return
            try {
                this.dialogLoading = true;
                let { code, data } = await fetchBrandList(this.queryParams)
                if (code !== 0) return
                let { records, total } = data;
                this.dialogTableData = records;
                if (this.value && this.value.id) {
                    let index = records.findIndex(v => v.id === this.value.id);
                    if (~index) {
                        this.brand = records[index]
                        this.current = index;
                    } else {
                        this.current = null;
                    }
                }

                this.total = total;
                // this.toggleSelection();
            } catch (error) {
                console.log("🚀 ~ file: SelectBrand.vue ~ line 197 ~ fetch ~ error", error)
            } finally {
                this.dialogLoading = false;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.pagination {
    margin-top: 10px;
    text-align: right;
}

.el-table {
    margin-bottom: 20px;

    ::v-deep {
        .el-radio {
            .el-radio__label {
                display: none;
            }
        }
    }
}
</style>
