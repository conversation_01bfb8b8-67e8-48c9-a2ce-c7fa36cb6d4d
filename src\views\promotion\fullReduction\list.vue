<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reload"
      @search="onSubmit"
    >
      <im-search-pad-item prop="activityName">
        <el-input v-model="model.activityName" placeholder="请输入活动名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-select v-model="model.activityStatus" placeholder="活动状态" >
          <el-option label="全部" value="" />
          <el-option label="未开始" value="NOT_START" />
          <el-option label="进行中" value="PROCEED" />
          <el-option label="已结束" value="FINISHED" />
          <el-option label="废弃" value="OBSOLETE" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-date-picker
          type="daterange"
          range-separator="至"
          v-model="during"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="activeName"
        :tabs="[{ name: '满减', value: 'first' }]">
        <template slot="button">
          <el-button @click="onSubmit">刷新</el-button>
          <el-button type="primary" @click="editCoupon()" v-if="checkPermission(['admin','fullReduce-add'])">+ 新增满减</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData">
        <template slot="activityStartTime">
          <el-table-column label="活动时间">
            <slot slot-scope="{row}">
              {{row.activityStartTime}}至{{row.activityEndTime}}
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-button type="text" @click="editCoupon(props.row.id)" v-if="props.row.activityStatus.code == 'NOT_START'&&checkPermission(['admin','fullReduce-edit'])">编辑</el-button>
          <el-button type="text" @click="editCoupon(props.row.id)" v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE'||props.row.activityStatus.code == 'PROCEED') && checkPermission(['admin','fullReduce-check'])">查看</el-button>
          <el-divider direction="vertical" v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE')&&checkPermission(['admin','fullReduce-delete'])"></el-divider>
          <el-button type="text" @click="delCoupon(props.row.id)" v-if="(props.row.activityStatus.code == 'FINISHED'||props.row.activityStatus.code == 'OBSOLETE') && checkPermission(['admin','fullReduce-delete'])">删除</el-button>
          <el-divider direction="vertical" v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START') &&checkPermission(['admin','fullReduce-off'])"></el-divider>
          <el-button type="text" @click="handleOnOff(props.row.id)" v-if="(props.row.activityStatus.code == 'PROCEED'||props.row.activityStatus.code == 'NOT_START')&&checkPermission(['admin','fullReduce-off'])">作废</el-button>
        </div>
      </table-pager>
<!--      <product-dialog ref="products-dialog" :checkList="goodsList" :detail="itemDetail"></product-dialog>-->
    </div>
  </div>
</template>

<script>
  import { getList,updatePromotionPackageState,handleDel } from "@/api/fullReduction"
  import checkPermission from '../../../utils/permission';
  import TabsLayout from "../../../components/TabsLayout/index";
  // import productDialog from '../components/product-dialog'

  const TableColumns = [
    { label: '活动编码', name: 'activityCode', prop: 'activityCode' },
    { label: "活动名称", name: "activityName", prop: "activityName" },
    { label: '活动状态', name: 'activityStatus.desc', prop:'activityStatus.desc' },
    { label: '活动时间', name: 'activityStartTime', prop: 'activityStartTime',slot: true},
  ];

  const TableColumnList = [];

  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  export { TableColumnList };

  export default {
    components: {TabsLayout},
    data () {
      return {
        // table配置
        showSelectTitle: false,
        tableTitle: TableColumnList,
        tableVal: [],
        loading: false,
        showCoupon: false,
        showAdd: false,
        activeName: 'first',
        model: {
          'activityName': '',
          'activityStatus': '',
        },
        during: '',
        tableData: [],
        goodsList: [],
        itemDetail: ''
      }
    },
    created () {
    },
    methods: {
      checkPermission,
      async postCouponList (params) {
        let listQuery = {
          model: {
            ...this.model,
            activityStartTime: this.during[0],
            activityEndTime: this.during[1]
          }

        }
        Object.assign(listQuery, params)
        this.loading = true
        return await getList(listQuery)
      },
      async handleOnOff(id) {
        const data = await updatePromotionPackageState(id)
        if(data.code === 0) {
          this.$message.success('该满减活动已作废！')
          this.onSubmit()
        }
      },
      //删除作废和结束的
      async delCoupon(id) {
        const data = await handleDel([id])
        if(data.code === 0) {
          this.$message.success('删除成功！')
          this.$refs.todoTable.doRefresh({
            page: 1,
            pageSize: 10
          })
        }
      },
      // 新增或者编辑
      editCoupon (id, type) {
        this.$router.push({
          path: '/promotion/fullReduce/edit',
          query: {
            saleMerchantId: this.saleMerchantId,
            id: id,
            type: type
          }
        })
      },
      onSubmit () {
        // this.postCouponList()
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load (params) {
        Object.assign(this.listQuery, params)
        return await this.postCouponList()
      },
      reload () {
        this.model = {}
        this.during=''
        this.$refs.todoTable.doRefresh()
      },
      pagination(val) {
        this.listQuery.current = val.page
        this.listQuery.size = val.limit
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
