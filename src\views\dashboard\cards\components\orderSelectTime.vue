<template>
  <div class="btn">
    <el-select
      style="width: 180px;margin-right: 6px;"
      @change="timeTypeChange"
      v-model="selectOptions"
      placeholder="请选择"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-date-picker style="width: 140px;" :disabled="datePickerDisabled" :picker-options="disabledStartFun" v-model="startTime" type="date" placeholder="开始日期" value-format="yyyy-MM-dd" @change="search" />
    <span> - </span>
    <el-date-picker style="width: 140px;" :disabled="datePickerDisabled" :picker-options="disabledEndFun" v-model="endTime" type="date" placeholder="结束日期" value-format="yyyy-MM-dd" @change="search" />
    <el-select
      style="width: 160px;margin-left: 6px;"
      @change="search"
      v-model="orderStatus"
      placeholder="请选择"
    >
      <el-option
        v-for="item in orderType"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script>
import moment from 'moment';
moment.updateLocale('en', {
  week: { dow : 1 }
});

export default {
  name: 'orderSelectTime',
  props: {
    Query: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      options: [
        { value: 1, label: "本周", },
        { value: 2, label: "本月", },
        { value: 3, label: "自定义（跨度一年内）" },
      ],
      orderType: [
        { value: '0', label: '全部（不含取消）' },
        { value: "1", label: "待审核" },
        { value: "100", label: "待付款" },
        { value: "120", label: "待发货" },
        { value: "140", label: "已发货" },
        { value: "200", label: "已完成" },
        { value: "210", label: "已取消" },
      ],
      orderStatus: '0',
      selectOptions: 1,
      orderTimeList: [],
      startTime: '',
      endTime: '',
    };
  },
  computed: {
    disabledStartFun() {
      const endTimeStamp = moment(this.endTime).valueOf() || 0
      const minTimeStamp = endTimeStamp - 60 * 60 * 24 * 365 * 1000
      return {
        disabledDate(date) {
          const timeStamp = date.getTime()
          return timeStamp > Date.now() || endTimeStamp > 0 && timeStamp > endTimeStamp || timeStamp < minTimeStamp
        }
      }
    },
    disabledEndFun() {
      const startTimeStamp = moment(this.startTime).valueOf() || 0
      const maxTimeStamp = startTimeStamp + 60 * 60 * 24 * 365 * 1000
      return {
        disabledDate(date) {
          const timeStamp = date.getTime()
          return timeStamp > Date.now() || startTimeStamp > 0 && timeStamp < startTimeStamp || timeStamp > maxTimeStamp
        }
      }
    },
    datePickerDisabled() {
      return this.selectOptions !== 3
    }
  },
  methods: {
    // 设置本周时间
    setCurrentWeekDate() {
      // 获取今天的日期
      const today = moment()
      // 获取本周的开始日期和结束日期
      const startOfWeek = moment().startOf('week');
      let endOfWeek = moment().endOf('week');
      // 如果本月的结束日期超过今天的日期，则将结束日期设置为今天
      if (endOfWeek.isAfter(today)) endOfWeek = today
      // 格式化日期
      const startOfWeekFormatted = startOfWeek.format('YYYY-MM-DD');
      const endOfWeekFormatted = endOfWeek.format('YYYY-MM-DD');
      this.startTime = startOfWeekFormatted
      this.endTime = endOfWeekFormatted
    },
    // 设置本月时间
    setCurrentMonthDate() {
      // 获取今天的日期
      const today = moment()
      // 获取本月的开始日期和结束日期
      const startOfMonth = moment().startOf('month')
      let endOfMonth = moment().endOf('month')
      // 如果本月的结束日期超过今天的日期，则将结束日期设置为今天
      if (endOfMonth.isAfter(today)) endOfMonth = today
      // 格式化日期
      const startOfMonthFormatted = startOfMonth.format('YYYY-MM-DD')
      const endOfMonthFormatted = endOfMonth.format('YYYY-MM-DD')
      this.startTime = startOfMonthFormatted
      this.endTime = endOfMonthFormatted
    },
    search() {
      let query = {
        orderStatus: this.orderStatus,
        startTime: this.startTime,
        endTime: this.endTime
      }
      if (!query.startTime || !query.endTime) return
      this.$emit("getDetail", query);
    },
    timeTypeChange(val) {
      switch (val) {
        // 本周
        case 1:
          this.setCurrentWeekDate()
          this.search();
          break
        // 本月
        case 2:
          this.setCurrentMonthDate()
          this.search();
          break
        // 自定义，跨度是今天~上一年的今天
        default:
          this.orderTimeList = []
      }
    },
  },
  created() {
    this.timeTypeChange(1)
  }
};
</script>