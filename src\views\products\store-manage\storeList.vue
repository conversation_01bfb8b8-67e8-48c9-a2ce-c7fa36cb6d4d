<template>
<div v-loading="loading" class="product-list">
  <im-search-pad
      :has-expand="false"
      :model="queryParams"
      @reset="resetFilter"
      @search="submitSearch"
    >
      <im-search-pad-item prop="name">
        <el-input v-model.trim="queryParams.name" placeholder="请输入仓库名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[ { name: '仓库管理' } ]">
      <template slot="button">
        <el-button type="primary" v-if="checkPermission(['admin','sale-saas-store:create','sale-platform-store:create'])" icon="el-icon-plus" @click="handleAddStore">新建仓库</el-button>
        <el-button  icon="el-icon-refresh" @click="resetFilter">刷新</el-button>
      </template>
    </tabs-layout>
    <div class="table" style="margin-top: 0">
      <table-pager :rowKey="rowKey" :reserveSelection="true" ref="pager-table" :height="600" :options="allColumn"
        :remote-method="load" :data.sync="tableData" :selection="false"
        :pageSize="pageSize" :operation-width="200">
        <el-table-column width="170" slot="name" label="仓库名称">
          <template v-slot="{row}">
            <span>{{ row.name }} <span class="default-store" v-if="row.defaultWarehouse.code === 'Y'">(默认仓)</span></span>
          </template>
        </el-table-column>
        <el-table-column width="170" :show-overflow-tooltip='true' slot="location" label="所在地区">
          <template v-slot="{row}">
            <span>{{ row.location }} </span>
          </template>
        </el-table-column>
        <el-table-column width="260" :show-overflow-tooltip='true' slot="location" label="详细地址">
          <template v-slot="{row}">
            <span>{{ row.detailedAddress }} </span>
          </template>
        </el-table-column>
        <el-table-column slot="productCount" label="商品数">
          <template v-slot="{row}">
            <span class="total-col" @click="handleClientList(row)">{{ row.productCount }}</span>
          </template>
        </el-table-column>
        <el-table-column slot="purCount" label="客户数">
          <template v-slot="{row}">
            <span v-if="checkPermission(['admin', 'sale-saas-store:clientRelation', 'sale-platform-store:clientRelation'])" class="total-col" @click="handleProductList(row)">{{ row.purCount }}</span>
            <span v-else>{{ row.purCount }}</span>
          </template>
        </el-table-column>
        <el-table-column slot="updateUser" width="150" label="操作人">
          <template v-slot="{row}">
            {{ row.updateUser }}
          </template>
        </el-table-column>
        <div slot-scope="scope" align="center">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleImport(scope.row.id)" v-if="checkPermission(['admin','sale-saas-store:clientImport','sale-platform-store:clientImport'])">导入客户</el-button>
              <el-button type="text" v-if="checkPermission(['admin','sale-saas-store:edit','sale-platform-store:edit'])"
                @click="handleEdit(scope.row)">
                编辑</el-button>
              <el-button type="text"  v-if="scope.row.defaultWarehouse.code === 'N' && checkPermission(['admin','sale-saas-store:del','sale-platform-store:del'])"
                @click="handleDelete(scope.row)">
                删除</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    </div>
    <AddStore  :visible.sync="isAddStoreDialog" :title="title" @onsuccess="handleSuccess" :typeVal="typeVal" :id="id"  />
    <StoreCustomAccount :visible.sync="isStoreCustomDialog" @onsuccess="handleSuccess" :clientId="clientId"/>
  <!--    导入商品弹窗-->
  <importDialog ref="importDialogRef" :tipsList="tipsList" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="true" :queryParams="queryData" @uploadChangeData="uploadChangeData"></importDialog>
</div>
</template>

<script>
import AddStore from './components/AddStore.vue';
import StoreCustomAccount from './components/StoreCustomAccount.vue';
import importDialog from "@/components/eyaolink/importDialog/index";
import checkPermission from '@/utils/permission'
import {
  getStoreList,
  deleteStoreJoggle,
  settingDefaultStore
} from "@/api/products/store";
const TableColumns =[
  {
    prop: 'name',
    name: "name",
    label: '仓库名称',
    slot: true
  },//新增字段
  {
    prop: 'code',
    name: 'code',
    label: '仓库编码',
    width: 80
  },
  {
    prop: 'location',
    name: 'location',
    label: '所在地区',
    slot: true
  },
  {
    prop: 'detailedAddress',
    name: 'detailedAddress',
    label: '详细地址',
    slot: true
  },
  {
    prop: 'productCount',
    name: 'productCount',
    label: '商品数',
    slot: true
  },
  {
    prop: 'purCount',
    name: 'purCount',
    label: '客户数',
    slot: true
  },
  {
    prop: 'updateUser',
    name: 'updateUser',
    label: '操作人',
    slot: true,
    width: 150
  },
  {
    prop: 'updateTime',
    name: 'updateTime',
    label: '操作时间',
    width: 120
  },
];
const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
}
export default {
  name: 'storeManagement',
  components: {
    AddStore,
    StoreCustomAccount,
    importDialog
  },
  data() {
    return {
      title:'新建仓库',
      rowKey: "id",
      allColumn: TableColumnList, // 所有表列
      loading: false,
      listQuery: {
        model:{},
      },
      queryParams: {
        name: ''
      },
      list:[],
      isAddStoreDialog: false,
      isStoreCustomDialog: false,
      pageSize: 10,
      totalPage: 1,
      tableData: [],
      typeVal: 'add',
      id: '',
      clientId: '',
      actionUploadUrl: '/api/merchant/admin/merchantImport/importMerchantExcel',
      templateKey: 'IMPORT_MERCHANT_EXCEL_TEMP',
      queryData:{
        purMerchantType: 'WAREHOUSE_PUR_IMPORT',
        importId:''
      },
      tipsList:[
        "指定客户导入是根据ERP客户编码导入，请留意客户是否已维护ERP客户编码。",
        "仅支持xlsx格式文件，文件大小1M以内且数据500条以内。"
      ]
    }
  },
  methods: {
    checkPermission,
    resetData(){
      this.page = 1
        this.pageSize = 10
        this.totalPage = 1
        this.total = 0
        this.tableData = []
        this.queryParams.name = ''
    },
    async load(params) {
      let listQuery = {
          model: {
            ...this.queryParams
          },
          ...params
        };
      try {
        let result = await getStoreList(listQuery);
        this.totalPage = result.data.pages;
        this.total = result.data.total;
        return result
      } catch (error) {
        console.log(error)
      }

    },
    submitSearch() {
      this.$refs['pager-table'].clearSelection();
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
    resetFilter() {
        this.resetData()
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
    handleEdit(row) {
      this.id = row.id;
      this.title="编辑仓库"
      this.typeVal = 'edit'
      this.isAddStoreDialog = true
    },
    handleAddStore(){
      this.id = '';
      this.title="新建仓库"
      this.typeVal = 'add'
      this.isAddStoreDialog = true
    },
    handleSuccess() {
      this.submitSearch()
    },
    handleClientList(row){
      this.$router.push({
        path: '/productCenter/productsManagement/list',
        query: {
          warehouseId: row.id
        }
      })
    },
    handleProductList(row){
      this.clientId = row.id
      this.isStoreCustomDialog = true;
    },
    handleDelete(row) {
      this.$confirm('您确定删除该仓库吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteStoreJoggle(row.id).then(res => {
            if(res.code === 0) {
              this.$message.success('删除成功！')
              this.submitSearch()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    //设为默认仓库
    handleSettingDefaultStore(row) {
      this.$confirm('您确认设置该仓库为默认仓库吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          settingDefaultStore(row.id).then(res => {
            if(res.code === 0) {
              this.$message.success('设置成功!')
              this.submitSearch()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消设置默认仓库'
          });
        });
    },
    handleImport(id) {
      this.queryData.importId = id;
      this.$refs.importDialogRef.initOpen();
    },
    //  导入商品回调
    uploadChangeData(){
      this.resetFilter();
    },
  }
}
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}
.total-col {
  color: #0056E5;
  cursor: pointer;
}
.default-store {
  color: #D9001B
}
</style>
