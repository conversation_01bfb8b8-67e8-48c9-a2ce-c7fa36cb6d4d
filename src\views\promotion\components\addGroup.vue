<template>
  <div class="app-container">
    <el-dialog
      title="选择客户"
      :visible.sync="addUservisible"
      width="50%"
      @close="handleClose"
    >
      <im-search-pad
        :has-expand="false"
        :model="listQuery"
        @reset="reset"
        @search="search"
      >
<!--        <im-search-pad-item prop="purMerchantName">-->
<!--          <el-input placeholder="请输入内容" v-model="input" class="input-with-select">-->
<!--            <el-select v-model="select" slot="prepend" placeholder="请选择" @change="getSelect" style="width: 120px;">-->
<!--              <el-option label="客户编码" value="1"></el-option>-->
<!--              <el-option label="名称" value="2"></el-option>-->
<!--            </el-select>-->
<!--          </el-input>-->
<!--        </im-search-pad-item>-->
        <im-search-pad-item prop="purMerchantName">
          <el-input v-model="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
        </im-search-pad-item>
      </im-search-pad>
      <table-pager ref="todoTable" :isNeedButton="false" :options="tableTitle" :data.sync="tableData" :remote-method="load" :selection="true" @selection-change="handleSelectionChange" @selection-all="handleSelectionAll" :select-data="selectData">
        <!-- <div slot-scope="props">
          <el-link @click="add(props.row)">添加</el-link>
        </div> -->
      </table-pager>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="adds">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { merchantGroupListNew } from "@/api/group";

const TableColumns = [
  { label: "客户分组", prop: "name" },
  { label: "客户数量", prop: "customerNumber" }
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export default {
  name: "DragTable",
  props: ["visible",'saleMerchantId','row','selectData'],
  data() {
    return {
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      sortable: null,
      tableData: [],
      ids: [],
      listQuery: {
        model:{
          "purMerchantCode": '',
          "purMerchantName": '',
          "saleMerchantId": '',
          merchantGroupId: 1
        }
      },
      addUservisible: false,
      merchantGroupId: ''
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    handleClose () {
      this.$parent.showAdd = false;
    },

    search() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.load()
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.listQuery.model.purMerchantCode = ''
      this.listQuery.model.purMerchantName = ''
      this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listLoading = true;
      const query = {
        model: {}
      }
      Object.assign(query, params)
      return await merchantGroupListNew(query);
    },
    async adds() {
      this.$emit('changeShow', JSON.parse(JSON.stringify(this.ids)))
      this.$parent.showCoupon = true
    },
    handleSelectionChange(val) {
      this.ids = val
    },
    // 客户分组多选
    handleSelectionAll(val) {
      this.ids = val
    }
  },
  watch: {
    visible() {

      this.addUservisible = this.visible
    },
    row() {
      this.merchantGroupId = this.row.id
    }
  },
};
</script>
