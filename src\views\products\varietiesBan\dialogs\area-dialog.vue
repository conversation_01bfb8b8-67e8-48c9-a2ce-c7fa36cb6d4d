<template>
  <el-dialog
    title="选择区域"
    :visible.sync="visible"
    width="30%"
    @open="load"
    :before-close="handleClose">
    <el-tree
      style="height: 350px;overflow: auto"
      v-loading="loading"
      :data="areaData"
      show-checkbox
      node-key="id"
      ref="tree"
      accordion
      @check="curCheck"
      :default-expanded-keys="curNodes"
      highlight-current>
    </el-tree>
    <!--<el-tree
      ref="areaTree"
      :data="areaData"
      show-checkbox
      accordion
      node-key="id"
      :default-checked-keys ="checkArr"
      @check-change="handleCheckChange">&ndash;&gt;
    </el-tree>-->
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { find as _find, filter as _filter, map as _map } from 'lodash'
import request from '@/utils/request'
import {trees} from '@/api/group'
import { analysisByCheck } from '@/api/settingCenter'
export default {
  props: ['checkedIds'],
  data () {
    return {
      loading: false,
      visible: false,
      areaData: [],
      currentTree: '',
      formatAreaData: [],
      checkArr: [],
      curNodes: [],
      grandFather: '',
      checkedIdS: []
    }
  },
  created () {
    //this.load()
  },
  methods: {
    async load () {
      this.loading = true
      const { data } = await trees()
      this.areaData = data
      this.areaData.forEach((item,index)=>{
        this.checkedIdS.map(v => {
          if (v.provinceId === item.id) {
            if (v.cityIdS === 'ALL' && v.districtIdS === 'ALL:ALL') {
              this.$set(item, 'disabled', true)
              item.children.forEach(itm => {
                this.$set(itm, 'disabled', true)
                itm.children.forEach(itms => {
                  this.$set(itms, 'disabled', true)
                })
              })
            } else {
              const districtIdS = v.districtIdS.split(',')
              districtIdS.forEach(j=>{
                item.children.forEach(itm => {
                  const ind = item.children.findIndex(itm=>itm.id===j.substring(0, 6))
                  if(j.substring(7) ==='ALL') {
                    item.children[ind].disabled = true
                    itm.children.forEach(itms => {
                      this.$set(itms, 'disabled', true)
                    })
                  } else {
                    item.children[ind].children.forEach(itms=>{
                      const idx = item.children[ind].children.findIndex(itm=>itm.id===j.substring(7))
                      item.children[ind].children[idx].disabled = true
                    })
                  }
                })
              })
            }
          }
        })
      })
        this.loading = false

    },
    //@check事件
    curCheck(data,state) {
      const curNode = this.$refs.tree.getNode(data)
      if (curNode.level === 1) {
        if(state.checkedKeys.length===0) {
          this.$refs.tree.setCheckedKeys([])
        } else {
          this.$refs.tree.setCheckedKeys([curNode.data.id])
          this.grandFather = curNode.parent.data.id
        }

      } else if (curNode.level === 2) {
        if (this.grandFather !== curNode.parent.data.id) {
          this.grandFather = curNode.parent.data.id
        }
      } else {
        if (this.grandFather !== curNode.parent.parent.data.id) {
          this.grandFather = curNode.parent.parent.data.id
        }
      }
    },
    handleClose(done) {
      done()
    },
    handleCheckChange() {
      const data = (this.$refs.tree).getCheckedNodes(false, true)
      this.formatAreaData = _map(_filter(data, item => item.id.length === 2), (item, idx) => {
        const currentTree = item.id.slice(0, 2)
        const citys = _filter(data, item => item.id.length === 4 && currentTree === item.id.slice(0, 2))
        const districts = _filter(data, item => item.id.length === 6 && currentTree === item.id.slice(0, 2))
        return {
          province: item,
          citys: citys,
          districts: districts
        }
      })
    },
    handleConfirm () {
      const checkData = (this.$refs.tree).getCheckedNodes(false, true)
      if (checkData.length <= 0) {
        this.$message.error('请选择区域！')
        return
      }
      const params = {
        checkedKeys: this.$refs.tree.getCheckedKeys(),
        checkedKeysTrue: this.$refs.tree.getCheckedKeys(true),
        halfCheckedKeys: this.$refs.tree.getHalfCheckedKeys()
      }
      analysisByCheck(params).then(res => {
        const checkData = res.data
        this.formatAreaData.push({
          checkedKeys: checkData.checkedKeys,
          cityIdS: checkData.cityIdS,
          cityNames: checkData.cityNames,
          districtIdS: checkData.districtIdS,
          districtNames: checkData.districtNames,
          provinceId: checkData.provinceId,
          provinceName: checkData.provinceName,
        })
        this.$emit('getData', this.formatAreaData)
        this.formatAreaData = []
        this.$refs.tree.setCheckedKeys([])
        this.visible = false
      })
    }
  },
  watch: {
    checkedIds: {
      immediate: true,
      handler(newVal,oldVal){
        this.checkedIdS = newVal
      }

    }
  }
}
</script>
