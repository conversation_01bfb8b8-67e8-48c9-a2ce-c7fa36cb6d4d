<template>
  <el-form :model="form" label-width="80px" label-position="right" :disable="action==='SHOW'">
    <div class="content-information">
      <form-item-title title="内容信息"/>
      <el-form-item label="产品图片">
        <div class="upload-wrapper">
          <div class="images-wrapper">
            <upload-image
              v-for="(image, index) in form.images"
              :key="image.url"
              :src="image.url"
              @on-set="onSetImage(index)"
              @on-delete="onDeleteImage(index)"
            />
          </div>
          <el-upload
            :show-file-list="false"
            list-type="picture-card"
            :action="uploadParams.action"
            :headers="uploadParams.headers"
            :data="uploadParams.data"
            :on-success="handleUploadSuccess"
            :on-remove="handleRemove"
            :file-list="form.images"
            :disabled="isDetail || form.images.length > 4"
            accept=".jpg,.png,.bmp,.jpeg"
          >
            <div class="upload-content" v-if="form.images.length < 5">
              <i class="el-icon-plus"></i><br/>
              <span class="upload-tip">上传({{ form.images.length }}/5)</span>
            </div>
          </el-upload>
        </div>
        <div class="el-upload__tip">建议尺寸：600x600像素以上JPG或PNG格式图片，大小不超过2M</div>
      </el-form-item>
      <el-form-item label="产品详情">
        <tinymce :height="200" v-model="form.productDescription" :readonly="isDetail"/>
      </el-form-item>
      <el-form-item label="说明书">
        <el-table :data="form.instructionList" border>
          <el-table-column label="标题" width="300">
            <template slot-scope="scope">
              <div v-if="!scope.row.edit">{{ scope.row.cache.title }}</div>
              <el-input v-else :disabled="!scope.row.edit" v-model="scope.row.cache.title" placeholder="请输入标题"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="描述">
            <template slot-scope="scope">
              <div v-if="!scope.row.edit">{{ scope.row.cache.description }}</div>
              <el-input v-else v-model="scope.row.cache.description" placeholder="请输入描述"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button v-if="!scope.row.edit" @click="handleEdit(scope.row)" type="text" >编辑</el-button>
              <el-button v-else @click="handleCancel(scope.row, scope.$index)" type="text"  style="color: #7f7f7f;">取消</el-button>
              <el-button v-if="!scope.row.edit" @click="handleDelete(scope)" type="text" >删除</el-button>
              <el-button v-else @click="handleConfirm(scope.row)" type="text" >确定</el-button>
            </template>
          </el-table-column>
          <el-button @click="handleAdd" slot="append" type="text">+ 添加</el-button>
        </el-table>
      </el-form-item>
      <preview-dialog ref="previewDialog"/>
    </div>
  </el-form>
</template>

<script>
import { cloneDeep as _cloneDeep } from 'lodash'
import formItemTitle from '@/views/products/common-components/form-item-title'
import previewDialog from './dialogs/preview-dialog'
import tinymce from '@/components/Tinymce'
import { getToken } from '@/utils/auth'
import { getLocalUser } from '@/utils/local-user'
import uploadImage from './upload-image';

export default {
  components: { formItemTitle, previewDialog, tinymce, uploadImage },
  props: {
    action: {
      type: String
    },
    isNumber2: {
      type: Function
    },
    isInt7: {
      type: Function
    }
  },
  data () {
    return {
      form: {
        images: [],
        productDescription: '',
        instructionList: [
          /*{
            real: { title: '药品名称', description: '' },
            cache: { title: '', description: '' },
            edit: false
          },*/
          // {
          //   real: { title: '通用名称', description: '' },
          //   cache: { title: '', description: '' },
          //   edit: false
          // },
          // {
          //   real: { title: '汉语拼音', description: '' },
          //   cache: { title: '', description: '' },
          //   edit: false
          // },
          // {
          //   real: {title: '主要成分', description: ''},
          //   cache: { title: '', description: '' },
          //   edit: false
          // }
        ]
      },
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + '/file/file/upload',
        headers: {
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
          token: `Bearer ${getToken()}`
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0
        }
      },
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  computed: {
    isDetail () {
      return this.action === 'SHOW'
    }
  },
  methods: {
    onSetImage (index) {
      if (this.isDetail) {
        return;
      }
      let image = this.form.images[index];
      this.form.images.splice(index, 1);
      this.form.images = [image, ...this.form.images];

    },
    onDeleteImage (index) {
      if (this.isDetail) {
        return;
      }
      this.form.images.splice(index, 1);
    },
    getData() {
      let pictIdS = this.form.images.map(item => {
        return item.url
      })
      return {
        pictIdS: pictIdS.join(','),
        productDescription: this.form.productDescription,
        instructions: JSON.stringify(this.form.instructionList.map(item => {
          return {
            [item.cache.title]: item.cache.description
          }
        }))
      };

    },
    setForm (data) {
      if(data.pictIdS!=undefined && data.pictIdS !=null &&data.pictIdS != "" ){
        this.form.images = (data.pictIdS || '').split(',').map(item => {
          return {url: item}
        })
      }
      this.form.productDescription = data.productDescription
      try {
        let instructions = JSON.parse(data.instructions)
        this.form.instructionList = instructions.map(item => {
          let ret = {
            real: { title: '', description: '' },
            cache: { title: '', description: '' },
            edit: false
          }
          for (let k in item) {
            ret.real.title = item.title
            ret.real.description = item.detail
            ret.cache.title = k
            ret.cache.description = item[k]
          }
          return ret
        })
      } catch (e) {

      }
    },
    handlePreview (file) {
      this.$refs.previewDialog.url = file.url
      this.$refs.previewDialog.visible = true
    },
    handleAdd () {
      if (this.isDetail) {
        return;
      }
      this.form.instructionList.push({
        real: {title: '', description: '' },
        cache: {title: '', description: '' },
        newItem: true,
        edit: true
      })
    },
    handleEdit (item) {
      if (this.isDetail) {
        return;
      }
      item.edit = true
      // item.cache = _cloneDeep(item.real)
    },
    handleCancel (item, idx) {
      if (this.isDetail) {
        return;
      }
      if (item.newItem) {
        this.form.instructionList.splice(idx, 1)
      }
      item.cache = {title: '', description: '' }
      item.edit = false
    },
    handleDelete (item) {
      if (this.isDetail) {
        return;
      }
      this.form.instructionList.splice(item.$index, 1)
    },
    handleConfirm (item) {
      if (this.isDetail) {
        return;
      }
      item.edit = false
      item.newItem = false;
      console.log('this.form.instructionList',this.form.instructionList);
      item.real = _cloneDeep(item.cache)
    },
    handleUploadSuccess(file) {
      this.form.images.push({
        name: file.data.filename,
        id: file.data.id,
        url: file.data.url
      })
    },
    handleRemove (file, filelist) {
      if (this.isDetail) {
        return;
      }
      this.form.images = filelist.map(item => {
        return {
          name: item.data.filename,
          id: item.data.id,
          url: item.data.url
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-information{
  .el-form-item{
    .el-upload__tip{
      color: #aaaaaa;
    }
    .el-table{
      width: 100%;
      ::v-deep{
        .el-table__body-wrapper{
          .el-table__append-wrapper{
            text-align: center;
          }
        }
      }
    }
  }
}
.upload-wrapper {
  display: flex;
}
  .images-wrapper {
    display: flex;
  }
  .upload-content {
    display: inline-block;
    line-height: 24px;
  }
</style>
