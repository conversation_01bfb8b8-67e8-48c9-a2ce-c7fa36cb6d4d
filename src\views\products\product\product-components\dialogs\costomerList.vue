<template>
  <div>
    <el-radio-group style="margin-bottom: 10px" v-model="radio" @change="radioChange">
      <el-radio :label="1">全部客户</el-radio>
      <el-radio :label="2">指定客户</el-radio>
    </el-radio-group>
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="name">
        <el-input v-model.trim="model.name" @keyup.enter.native="searchLoad" placeholder="客户名称" />
      </im-search-pad-item>
    </im-search-pad>
    <!-- table分页 -->
    <el-table ref="multipleTable" :row-key="getRowKeys" border :data="tableData" tooltip-effect="dark"
      style="width: 100%" @selection-change="handleSelectionChange" height="449">
      <el-table-column type="selection" :reserve-selection="true" width="55" fixed></el-table-column>
      <el-table-column prop="name" label="客户名称"></el-table-column>
      <el-table-column prop="merchantTypeName" label="客户类型" width="150"></el-table-column>
      <el-table-column prop="ceoName" label="联系人" width="120"></el-table-column>
      <el-table-column prop="ceoMobile" label="联系人电话" width="130"></el-table-column>
      <el-table-column prop="region" label="所在地区" width="180">
        <template slot-scope="{ row }">
          <div>
            {{ row.provinceName }} - {{ row.cityName }} - {{ row.countyName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="groupName" label="联系人电话" width="150"></el-table-column>
    </el-table>
    <div class="page-row">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
        layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
      </el-pagination>
    </div>
    <div class="bottom_btn">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submit">
        确定{{
          multipleSelection.length == 0 ? "" : `(${multipleSelection.length})`
        }}</el-button>
    </div>
  </div>
</template>


<script>
  import {
    businessType
  } from "@/api/registercheck";
  import {
    merchantGroupList
  } from "@/api/group";
  import {
    areas
  } from "@/api/enterprise";
  import {
    pageSalesman
  } from "@/api/products/product/index";

  export default {
    //import引入的组件
    components: {},

    data() {
      return {
        getRowKeys(row) {
          return row.id;
        },
        isExpand: false,
        page: 1,
        limit: 10,
        totalCount: 0,
        tableData: [],
        multipleSelection: [],
        model: {
          name: ""
        },
        cityValue: [],
        tGroups: [],
        cGroups: [],
        radio: 1,
        listQuery:{
          provinceId: 0, // 省id 全国默认0
          cityIds: [], // 市id
          districtIds: [], // 区id
          merchantTypeIds: [], // 客户类型集合
          groupIds: [] // 客户分组集合
        },
        timer: null
      };
    },
    props: {
      dialogStatus: {
        type: Boolean,
        default: false,
      },
      allType: {
        type: String,
        default: "1",
      },
      costomerChack: {
        type: Array,
        default: [],
      },
      currentRow: {
        type: Object,
        default: {}
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      // 省id
      this.listQuery.provinceId = Number(this.currentRow.id);
      if(this.currentRow.id != 0) {
        // 不是全国
        // 市id
        this.currentRow.cityList.forEach(item=>{
          this.listQuery.cityIds.push(item.id);
        });
        // 区id
        this.currentRow.districtList.forEach(item=>{
          this.listQuery.districtIds.push(item.id);
        });
      } else {
        // 全国
        this.listQuery.cityIds = [];
        this.listQuery.districtIdsn = [];
      }
      // 客户类型
      if(this.currentRow.customerType.includes("ALL")) {
        this.listQuery.merchantTypeIds = [];
      } else {
        this.listQuery.merchantTypeIds = this.currentRow.customerType || [];
      }
      // 客户分组
      if(this.currentRow.customerGroup.includes("ALL")) {
        this.listQuery.groupIds = [];
      } else {
        this.listQuery.groupIds = this.currentRow.customerGroup || [];
      }
      if (this.dialogStatus) {
        if (this.allType == "0") {
          this.radio = 2;
          this.load();
        }
        if (this.costomerChack.length > 0) {
          this.$nextTick(() => {
            this.timer = setTimeout(() => {
              this.costomerChack.forEach(item => {
                let row = this.tableData.find(i => i.id == item);
                this.$refs.multipleTable.toggleRowSelection(row, true);
              })
            }, 1000)

          });
        }
      } else {
        this.radio = 1;
        this.$refs.multipleTable.clearSelection();
      }
    },
    //方法集合
    methods: {
      async load() {
        let params = {
          current: this.page,
          map: {},
          model: {
            name: this.model.name,
            ...this.listQuery,
            // merchantTypeIds: [this.model.customerType],
            // groupIds: [this.model.customerGroup]
          },
          order: "descending",
          size: this.limit,
          sort: "id",
        };
        if (this.radio == 1) {
          return;
        }
        delete params.model.customerType;
        delete params.model.customerGroup;
        pageSalesman(params).then((res) => {
          if (res.code == 0 && res.msg == "ok") {
            this.tableData = res.data.records || [];
            this.totalCount = res.data.total;
          }
        });
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      handleSizeChange(val) {
        this.limit = val;
        this.load();
      },
      handleCurrentChange(val) {
        this.page = val;
        this.load();
      },
      radioChange(e) {
        this.radio = e;
        if (e == 2) {
          this.load();
        } else {
          this.tableData = [];
          this.totalCount = 0;
        }
      },
      searchLoad() {
        this.load();
      },
      reload() {
        this.model = {
            name: "",
            provinceId: "",
            cityId: "",
            countyId: "",
            customerType: "",
            customerGroup: "",
          },
          this.cityValue = [];
        this.load();
      },
      handleCancel() {
        this.$emit("closeDialogFun");
      },
      submit() {
        if (this.radio == 1) {
          this.$emit("getSubmit", [], 1);
          //   全部
        } else {
          //   指定客户
          if (this.multipleSelection.length == 0) {
            this.$emit("closeCustomer");
            return;
          }
          let list = this.multipleSelection.map((item) => {
            return item.id;
          });
          this.$emit("getSubmit", list, 0);
        }
      },
    },
    beforeDestroy() {
      clearTimeout(this.timer)
    }
  };

</script>


<style lang='scss' scoped>
  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

  .bottom_btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

</style>
