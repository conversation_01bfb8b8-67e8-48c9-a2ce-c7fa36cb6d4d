
<template>
  <div>
    <im-search-pad :has-expand="false" :model="listQuery" @reset="reload" @search="onSubmit">
      <im-search-pad-item prop="couponName">
        <el-input v-model.trim="listQuery.model.couponName" placeholder="优惠券名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="couponType">
        <el-select v-model="listQuery.model.couponType" placeholder="优惠券类型">
          <el-option label="全部" value="" />
          <el-option label="满减券" value="FULL_REDUCTION" />
          <el-option label="折扣券" value="DISCOUNT" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="couponStatus">
        <el-select v-model="listQuery.model.couponStatus" placeholder="活动状态">
          <el-option label="全部" value="" />
          <el-option label="未开始" value="NOT_START" />
          <el-option label="进行中" value="PROCEED" />
          <el-option label="已作废" value="OBSOLETE" />
          <el-option label="已结束" value="FINISHED" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout v-model="activeName" :tabs="[{ name: '优惠券', value: 'first' }]">
        <template slot="button">
          <el-button @click="load">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-coupon:add','sale-platform-promotion-coupon:add'])" type="primary" @click="addCoupon">+ 新增优惠券</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData"
        :operation-width="240">
        <template slot="illustrate">
          <el-table-column label="优惠券内容" width="200">
            <slot slot-scope="{row}">
              {{ row.whetherReward && row.whetherReward.code === 'Y' ? '裂变奖励，' : '' }}
              {{ row.whetherPointsDeduction && row.whetherPointsDeduction.code === 'Y' ? '积分抵扣，' : '' }}
              {{ row.thresholdType.desc || "" }}{{ row.thresholdType.code == 'FULL' ? `${row.fullMoney}元,` : '，' }}{{
                  row.couponType.code == 'FULL_REDUCTION' ? `减${row.reduceMoney}元` : `打${row.discount || 0}折`
              }}
            </slot>
          </el-table-column>
        </template>
        <template slot="total">
          <el-table-column label="剩余" width="80">
            <slot slot-scope="{row}">
              {{ row.total - row.receiveTotal }}
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item" v-if="props.row.receiveTotal > 0 && props.row.receiveTotal > 0">
              <!-- 已领取和已使用都大于 0 显示数据按钮 -->
              <el-button v-if="checkPermission(['admin', 'sale-saas-promotion-coupon:report','sale-platform-promotion-coupon:report'])" type="text" @click="useDetail(props.row.id)">数据</el-button>
            </span>
            <span v-if="props.row.couponStatus.code == 'NOT_START' && checkPermission(['admin', 'sale-saas-promotion-coupon:edit','sale-platform-promotion-coupon:edit'])" class="table-edit-row-item">
              <el-button type="text" @click="editCoupon(props.row.id)">编辑</el-button>
            </span>
            <span
              v-if="checkPermission(['admin', 'sale-saas-promotion-coupon:detail','sale-platform-promotion-coupon:detail']) && props.row.couponStatus.code == 'FINISHED' || props.row.couponStatus.code == 'OBSOLETE' || props.row.couponStatus.code == 'PROCEED'"
              class="table-edit-row-item">
              <el-button type="text" @click="editCoupon(props.row.id, 'check')">查看</el-button>
            </span>

            <span v-if="props.row.couponStatus.code == 'PROCEED' && checkPermission(['admin', 'sale-saas-promotion-coupon:manyGive','sale-platform-promotion-coupon:manyGive']) " class="table-edit-row-item">
              <el-button type="text" @click="send(props.row.id, props.row.couponName)">赠券</el-button>
            </span>
            <span v-if="props.row.couponStatus.code == 'NOT_START' || props.row.couponStatus.code == 'PROCEED' && checkPermission(['admin', 'sale-saas-promotion-coupon:abolish','sale-platform-promotion-coupon:abolish'])"
              class="table-edit-row-item">
              <el-button type="text" @click="deleteMethods(props.row)">作废</el-button>
            </span>
            <span class="table-edit-row-item" v-if="checkPermission(['admin', 'sale-saas-promotion-coupon:del','sale-platform-promotion-coupon:del'])">
              <el-button type="text" @click="handleDel(props.row)">删除</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!--赠券-->
    <!-- <add-user :visible="showAdd" :sale-merchant-id="saleMerchantId" @changeShow="changeAddUser" /> -->
    <clients-model :visible.sync="showAdd" @ok="changeAddUser"></clients-model>
    <el-dialog title="赠券" :visible.sync="showCoupon" width="400px" @close="showCoupon = false" v-dialogDrag>
      <p style="margin: 0;margin-top: -14px;padding-bottom: 14px;">{{ receiveParams.name }}</p>
      <el-input v-model="receiveParams.number" placeholder="请输入赠送数量" />
      <div slot="footer" class="dialog-footer" style="margin-top: -20px;">
        <el-button @click="showCoupon = false">取 消</el-button>
        <el-button type="primary" @click="sendCoupon()">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import Pagination from '@/components/Pagination/index.vue'
import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'
import AddUser from '../components/addUser.vue'
import { findByUserIdSale } from '@/api/group'
import { postCouponList, delCouponList } from '@/api/promotion'
import checkPermission from '@/utils/permission'

const TableColumns = [
  // { label: "商品主图", name: "pictIdS" },
  { label: '活动编码', name: 'couponCode', prop: 'couponCode', width: 160 },
  { label: '优惠券名称', name: 'couponName', prop: 'couponName', width: 180 },
  { label: '类型', name: 'couponType', prop: 'couponType.desc', width: 120 },
  { label: '优惠券内容', name: 'illustrate', prop: 'illustrate', slot: 'true', width: 200 },
  { label: '活动状态', name: 'couponStatus.desc', prop: 'couponStatus.desc', width: 100 },
  { label: '已领取', name: 'receiveTotal', prop: 'receiveTotal', width: 80 },
  { label: '剩余', name: 'total', prop: 'total', slot: 'true', width: 80 },
  { label: '已使用', name: 'useTotal', prop: 'useTotal', width: 80 },
  { label: '操作时间', name: 'createTime', prop: 'createTime', width: 160 }
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  name: 'coupon',
  components: {
    Pagination,
    AddUser,
    ClientsModel
  },
  data() {
    return {
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      loading: false,
      showCoupon: false,
      showAdd: false,
      saleMerchantId: '', //经销商id
      activeName: 'first',
      listQuery: {
        model: {
          'couponName': '',
          'couponType': '',
          'couponStatus': ''
        }
      },
      tableData: [],
      receiveParams: {
        name: '',
        couponId: '',
        purMerchantIds: '',
        number: ''
      }
    }
  },
  created() {
    this.getSaleId()
    // this.postCouponList()
  },
  watch: {
    showCoupon(newVal, oldVla) {
      if (!newVal && oldVla) {
        this.receiveParams.number = '';
      }
    }
  },
  activated() {
    if (sessionStorage.getItem('couponList')) {
      this.reload()
      sessionStorage.removeItem('couponList')
    }
  },
  methods: {
    checkPermission,
    async postCouponList(params) {
      Object.assign(this.listQuery, params)
      this.loading = true
      return await postCouponList(this.listQuery)
    },
    // 获取经销商id
    async getSaleId() {
      let userId = JSON.parse(localStorage.getItem('LOCAL_USER')).userId
      const { data } = await findByUserIdSale(userId)
      this.saleMerchantId = data.id
    },
    changeAddUser(data) {
      console.log('data', data)
      this.receiveParams.purMerchantIds = data.map(({ id }) => id)
      this.showCoupon = true;
    },
    // 新增或者编辑优惠券
    editCoupon(id, type) {
      this.$router.push({
        path: '/promotion/coupon/edit',
        query: {
          saleMerchantId: this.saleMerchantId,
          id: id,
          type: type
        }
      })
    },
    addCoupon() {
      this.$router.push({
        path: '/promotion/coupon/edit',
        query: {
          saleMerchantId: this.saleMerchantId,
          type: 'add'
        }
      })
    },
    // 删除优惠券
    handleDel(row) {
      this.$confirm("您确定删除此优惠券吗？", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delCouponList(row.id).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('删除成功');
              this.reload();
            }
          })
        })
        .catch((req) => { });
    },
    // 赠券
    send(id, name) {
      this.receiveParams.couponId = id
      this.receiveParams.name = name
      this.showAdd = true
    },
    async sendCoupon() {
      if (!this.receiveParams.number) {
        this.$message.error('优惠券数量不能为空')
        return false
      }

      let params = {
        ...this.receiveParams
      }
      delete params.name

      let res = await request.post('product/merchant/coupon/receiveByMany', params)

      if (res.code == 0) {
        this.reload();
        this.$message.success('发放成功');
        this.showCoupon = false;
        this.showAdd = false;  
        
      } else {
        this.showCoupon = false;
        this.$message.error(data.msg)
      }
      

    },
    // 作废
    deleteMethods(row) {
      this.$confirm('确定将该优惠券作废，作废后已领取的优惠券在活动有效期内仍可使用', '优惠券作废', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.get(`product/merchant/coupon/proceed/${row.id}`).then((res) => {
          if (res.code == 0) {
            this.$message({
              type: 'success',
              message: '作废成功!'
            });
            row.couponStatus = { code: "OBSOLETE", desc: "废弃" }
            // this.postCouponList()
          }
        });
      })
        .catch(req => {

        })
    },
    onSubmit() {
      // this.postCouponList()
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      this.$refs.todoTable.doRefresh()
    },
    reload() {
      this.listQuery.model = {}
      this.$refs.todoTable.doRefresh()
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
    },
    // 优惠券明细
    useDetail(id) {
      this.$router.push({
        path: '/promotion/coupon/useDetail',
        query: {
          id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-link {
  margin-right: 8px;
}
</style>
