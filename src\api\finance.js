import request from '@/utils/request'
//商家账单列表
export function merchantsBillOrderDetail(query) {
  return request({
    url: `/finance/admin/merchantsBillOrderDetail/page`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//提现记录
export function caseOutList(query) {
  return request({
    url: `/finance/admin/merchantsFinanceCashOut/page`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//业务员结算单tab状态统计
export function merchantsBillOrderCount(query) {
  return request({
    url: `/finance/admin/merchantsBillOrderDetail/countStatus`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据当前登录用户获取销售商信息
export function getMerchantDetails() {
  return request({
    url: `/merchant/admin/saleMerchant/getMerchantDetails`,
    method: 'get'
  })
}
//提现
export function caseOut(query) {
  return request({
    url: '/finance/admin/merchantsFinanceCashOut/cashOut',
    method: 'post',
    params: query
  })
}
//商家可用余额
export function financeCapitalAccount() {
  return request({
    url: '/finance/admin/merchants/merchantsAccountDetail/statistics',
    method: 'post',
  })
}
//发送验证码
export function send(query) {
  return request({
    url: `/msgs/verification/send`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//验证验证码
export function codeVerify(query) {
  return request({
    url: `msgs/verification`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//保证金缴费记录
export function merchantsEarnestMoneyPage(query) {
  return request({
    url: `/finance/admin/merchantsEarnestMoney/page`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//保证金管理

export function merchantsEarnestMoneyList(query) {
  return request({
    url: `/finance/admin/merchantsEarnestMoney/list`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//收银台->提交保证金
export function earnestMoney(query) {
  return request({
    url: '/finance/admin/merchantsEarnestMoney/earnestMoney',
    method: 'post',
    params: query
  })
}
//系统参数
export function getValue(data) {
  return request({
    url: '/oauth/parameter/value',
    method: 'get',
    params:data
  })
}
//申请退还保证金
export function refundDeposit(query) {
  return request({
    url: '/finance/admin/merchantsEarnestMoney/refundDeposit',
    method: 'post',
    params: query
  })
}
//平台使用费
export function merchantsPlatformMoney(query) {
  return request({
    url: `/finance/admin/merchantsPlatformMoney/list`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//缴费记录
export function merchantsPlatformMoneyPage(query) {
  return request({
    url: `/finance/admin/merchantsPlatformMoney/page`,
    method: 'post',
    data: query,
    transformRequest: [function() {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}//收银台->提交保证金
export function platformearnEstMoney(query) {
  return request({
    url: '/finance/admin/merchantsPlatformMoney/earnestMoney',
    method: 'post',
    params: query
  })
}



