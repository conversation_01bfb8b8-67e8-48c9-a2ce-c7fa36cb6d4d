<template>
  <!-- 修改成功 -->
  <div class="archivesEditContent" v-if="!isLoding">
    <el-dialog title="资质预览" append-to-body :visible.sync="dialogStatus" v-if="showlisenceItem.filePath" :before-close="closeDialogFun">
      <el-carousel arrow="always" height="50vh" :autoplay="false">
        <el-carousel-item v-for="(item, index) in getsrc(showlisenceItem.filePath)" :key="index">
          <el-image style="width: 100%; height: 100%" :fit="'contain'" :src="item.url"></el-image>
        </el-carousel-item>
      </el-carousel>
      <div slot="footer">
        <el-button @click="closeDialogFun">取 消</el-button>
        <DowloadButton :buttonType="'primary'" :key="showlisenceItem.id" :imgList="getimgList(showlisenceItem.filePath)"></DowloadButton>
      </div>
    </el-dialog>
    <page-title title="查看采购商档案">
      <template>
        <template v-if="$route.query.tabType == 'PENDING'">
          <el-popover v-if="checkPermission(['admin', 'registercheck:rejection'])" v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回</el-button>
            <el-form ref="rejectform" :model="rejectText">
              <el-form-item prop="text" :rules="[{required: true, message: '请填写驳回理由',trigger: 'blur'},{required: true,min:5, message: '请至少填写5个字！',trigger: 'blur'}]">
                <el-input type="textarea" :rows="3" placeholder="请输入驳回理由" v-model="rejectText.text">
                </el-input>
              </el-form-item>
            </el-form>
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button size="mini" @click="rejectFlag = false">取消</el-button>
              <el-button type="primary" size="mini" @click="rejected">确定</el-button>
            </div>
          </el-popover>
          <el-button :loading="auditLoading" v-if="checkPermission(['admin', 'registercheck:audit'])" type="primary" :disabled="isBtn" @click="accepted">生成档案</el-button>
        </template>
        <template v-else-if="$route.query.tabType == 'REJECTED'">
          <el-popover v-model="rejectFlag" placement="bottom-end" title="驳回理由" width="300" trigger="click">
            <el-button slot="reference">驳回理由</el-button>
            {{query.rejectReason}}
            <div style="text-align: right; margin: 0;padding-top:14px">
              <el-button type="primary" size="mini" @click="rejectFlag = false">知道了</el-button>
            </div>
          </el-popover>
        </template>
        <template v-else-if="$route.query.tabType == 'ACCEPTED'">
        </template>
      </template>
    </page-title>
    <el-form :inline="true" label-width="140px" :model="query" ref="editForm" :rules="rules">
      <div class="item">
        <page-module-title title="基础信息" />
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="客户编码:">
              <span class="graytext">{{query.code}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="客户名称:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
              <span class="graytext">{{query.name}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="客户识别码:" :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]">
              <span class="graytext"> {{query.identifyCode}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="社会统一信用代码:">
              <span class="graytext"> {{query.socialCreditCode}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="法定代表人:">
              <span class="graytext">{{query.legalPerson}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人:" :rules="[{ required: true, message: '请填写负责人',trigger: 'blur' }]">
              <span class="graytext"> {{query.ceoName}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="负责人手机:" :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]">
              <span class="graytext">{{query.ceoMobile}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="质量负责人:" :rules="[{ required: true, message: '请选择经营类目',trigger: 'blur' }]">
              <span class="graytext"> {{query.qualityPersonInCharge}}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="所在区域:">
              <span class="graytext">{{query.region}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册地址:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
              <span class="graytext">{{query.registerAddress}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="productNumber" label="注册资金:" :rules="[{ required: true, message: '请填写助记码',trigger: 'blur' }]">
              <span class="graytext">{{query.registerCapital}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="address-selector">
          <el-form-item prop="detailLocation" label="企业地址">
            <el-input disabled clearable style="width: 400px" v-model.trim="query.detailLocation" placeholder="可手动填写地址或者地图点选">
            </el-input>
          </el-form-item>
          <el-form-item class="formItem" prop="latitude" label="">
            - <el-input clearable style="width: 380px" :value="coordinate" placeholder="经纬度坐标" disabled />
          </el-form-item>
        </div>
      </div>

      <div class="item">
        <page-module-title title="账户信息" />
        <el-row>
          <el-col :span="6">
            <el-form-item class="formItem" label="登录账号:">
              <span class="graytext"> {{query.loginAccount}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="formItem" prop="userMobile" label="手机号码:" :rules="[{ required: true, message: '请填写产品名称',trigger: 'blur' }]">
              <span class="graytext"> {{query.userMobile}}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="item">
        <page-module-title title="经营类目" />
        <div style="padding: 0 30px" class="cateGory">
          <el-row :gutter="20" v-if="cateGory">
            <el-col v-for="(ids, keys) in cateGory" :key="keys" :span="8" :style="'width:'+getFlexNum()+'%;flex:1;display:flex;flex-wrap:wrap;'">
              <span>{{keys + ':'}}</span> <span style="white-space: nowrap;color:#aaaaaa" v-for="ite in ids" :key="ite.id">{{ite.label + '、'}}</span>
            </el-col>
          </el-row>
          <span style="padding-left:30px;color:#a9a9ac" v-else>无</span>
        </div>
      </div>
      <div class="item">
        <page-module-title title="客户资质" />
        <div>
          <template>
            <el-form-item class="formItem" prop="merchantTypeId" label="企业类型:" :rules="[
                {
                  required: true,
                  message: '请选择企业类型',
                  trigger: 'blur',
                },
              ]">
              <el-select disabled v-model="query.merchantTypeId" placeholder="请选择企业类型">
                <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <div class="graytext">
              上传材料为复印件加盖企业原印公章且均在有效期内，支持JPG、JPEG、PNG、BMP格式，大小不超过2M
            </div>
            <template>
              <el-table :data="lisenceTableDate" style="width: 100%" border>
                <el-table-column prop="label" label="证件类型"></el-table-column>
                <el-table-column prop="licenseNumber" label="证件号">
                  <template slot-scope="{ row }">
                    <el-input v-if="row.isEdit" placeholder="请输入证件号" v-model="row.licenseNumber"></el-input>
                    <span v-else>{{ row.licenseNumber }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="licenseEndTime" label="过期时间">
                  <template slot-scope="{ row }">
                    <el-date-picker
                      v-if="row.isEdit"
                      :disabled="row.isForever"
                      v-model="row.licenseEndTime"
                      type="datetime"
                      style="width: 220px; margin-right: 10px"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    ></el-date-picker>
                    <span v-if="!row.isEdit && !row.isForever">{{ row.licenseEndTime }}</span>
                    <el-checkbox v-if="row.isEdit || row.isForever" :disabled="!row.isEdit" v-model="row.isForever">长期</el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="filePath" label="附件">
                  <template slot-scope="{ row }">
                    <el-upload v-if="row.isEdit" :class="{hide: !row.isEdit}" ref="uploadlisence" :limit="3" :file-list="row.filePathList" :action="$uploadUrl" :data="insertProgram"
                      :headers="headersProgram" list-type="picture-card" :on-remove="handleRemove" :on-success="uploadSuccess" :before-upload="beforeUpload">
                      <i class="el-icon-plus"></i>
                    </el-upload>

                    <span v-else>
                      <el-image v-for="file in row.filePathList" :preview-src-list="[file.url]" :key="file.url" style="width:40px;height:40px;margin-right:5px"
                        :src="file.url" fit="cover" />
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="{ row }">
                    <el-button type="text" v-show="row.isEdit" @click="cancelLisenceEdit(row)" style="color: rgb(127, 127, 127);"> 取 消 </el-button>
                    <el-button type="text" v-show="row.isEdit" @click="confirmLisenceEdit(row)"> 确 定 </el-button>
                    <el-button type="text" @click="showBigPic(row)" v-if="!!row.filePath" v-show="!row.isEdit">预 览</el-button>
                    <DowloadButton v-show="row.id" :size="'small'" :buttonType="'text'" :key="row.licenseBaseId" :imgList="getimgList(row.filePath)"></DowloadButton>
                    <el-image :ref="`ref${row.licenseBaseId}`" style="width:0;height:0" :src="previewImages[0]" :preview-src-list="previewImages"></el-image>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </template>
        </div>
      </div>

      <div class="item">
        <page-module-title title="发货地址" />
        <template>
          <el-table :data="addrtableDate" style="width: 100%" border>
            <el-table-column prop="name" label="收货人姓名" min-width="120">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入收货人" v-model="row.name"></el-input>
                <span v-else>{{row.name}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mobilPhone" label="联系手机" min-width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系手机" v-model="row.mobilPhone"></el-input>
                <span v-else>{{row.mobilPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="fixedPhone" label="联系电话" min-width="170">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入联系电话" v-model="row.fixedPhone"></el-input>
                <span v-else>{{row.fixedPhone}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" label="收货区域" min-width="320">
              <template slot-scope="{ row }">
                <el-cascader :disabled="!row.isEdit" ref="addr" v-model="row.address" style="width: 240px" placeholder="请选择所在区域"  :props="{ value: 'id', label: 'label'}"
                  :options="areasTree" clearable>
                </el-cascader>
              </template>
            </el-table-column>
            <el-table-column prop="detailedAddress" label="详细地址" width="220">
              <template slot-scope="{ row }">
                <el-input v-if="row.isEdit" placeholder="请输入详细地址" v-model="row.detailedAddress"></el-input>
                <span v-else>{{row.detailedAddress}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="region" align="center" width="250" label="是否启用">
              <template slot-scope="{row}">
                <!-- <span v-if="row.isEdit">
                  <el-radio v-model="row.defaultOrNot"  label="Y">启用</el-radio>
                  <el-radio v-model="row.defaultOrNot"  label="N">禁用</el-radio>
                </span> -->
                <span> {{row.status && row.status.code == 'ENABLED'? '启用' : '禁用'}} </span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>

      <div class="item">
        <page-module-title title="发票信息" />
        <template>
          <div>
            <el-form-item class="formItem" prop="fpinvoiceType" label="发票类型:" :rules="[{ required: true, message: '请选择发票类型', trigger: 'blur' },]">
              <el-radio-group v-model.trim="query.fpinvoiceType" @input="changeFpinvoiceType">
                <!-- <el-radio label="VATINVOICE">纸质普通发票</el-radio>
                <el-radio label="SPECIALINVOICE">纸质专用发票</el-radio> -->
                <el-radio label="ELECTRON_INVOICE">电子普通发票</el-radio>
                <el-radio label="ELECTRON_SPECIALINVOICE">电子专用发票</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fpname" label="发票抬头:" :rules="[{ required: true, message: '请填写发票抬头', trigger: 'blur' },]">
              <span class="graytext">{{query.fpname}}</span>
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="fptaxNumber" label="税号:" :rules="isSpecialInvoice ? [{ required: true, message: '请填写税号', trigger: 'blur' },] : []">
              <span class="graytext">{{query.fptaxNumber}}</span>
            </el-form-item>
          </div>

          <div>
            <el-form-item class="formItem" prop="fpregisterMobile" label="注册地址:" :rules="isSpecialInvoice ? [{ required: true, message: '请填写注册地址', trigger: 'blur' },] : []">
              <span class="graytext">{{query.fpregisterAddress}}</span>
            </el-form-item>
          </div>

          <div>
            <el-form-item class="formItem" prop="fpregisterMobile" label="注册电话:" :rules="isSpecialInvoice ? [{ required: true, message: '请填写注册电话', trigger: 'blur' },] : []">
              <span class="graytext">{{query.fpregisterMobile}}</span>
            </el-form-item>
          </div>

          <div>
            <el-form-item class="formItem" prop="fpbankNumber" label="银行账号:" :rules="isSpecialInvoice ? [{ required: true, message: '请填写银行账号', trigger: 'blur' },] : []">
              <span class="graytext">{{query.fpbankNumber}}</span>
            </el-form-item>
          </div>

          <div>
            <el-form-item class="formItem" prop="fpdepositBank" label="开户银行:" :rules="isSpecialInvoice ? [{ required: true, message: '请填写开户银行', trigger: 'blur' },] : []">
              <span class="graytext">{{query.fpdepositBank}}</span>
            </el-form-item>
          </div>
          <div>
            <!-- 电子普通发票和电子专用发票中邮箱必填 -->
            <el-form-item class="formItem" prop="email" label="邮箱:" :rules="isSpecialInvoice ? [
              { required: true, message: '请填写邮箱', trigger: 'blur' },
            ] : []">
              <el-input clearable style="width: 400px" v-model.trim="query.email" placeholder="用于接收电子发票的邮箱" />
            </el-form-item>
          </div>
          <div>
            <el-form-item class="formItem" prop="mobile" label="手机号码:">
              <el-input clearable style="width: 400px" v-model.trim="query.mobile" placeholder="请输入手机号码" />
            </el-form-item>
          </div>
        </template>
      </div>
    </el-form>

  </div>
</template>
<script>
import checkPermission from "@/utils/permission";
import { getContextData, getToken, setContextData } from "@/utils/auth";
import { checkNumPot2 } from "@/utils/rules";
import rule from "@/utils/rules";
import {
  add,
  listByLicenseBaseType,
  getitems,
  enable,
  accepted,
  rejected,
  frozen,
  edititem,
  findPurMerchantDetailName
} from "@/api/registercheck";
import {
  findSaleScope,
  areas,
} from "@/api/businessCenter/businessList";
import {
  adaddr,
  deladdr,
  editaddr
} from "@/api/businessList";
import {
  merchantType,
  MerchantTypeDetailsById,
} from "@/api/archivesList";
import DowloadButton from "@/components/eyaolink/DowloadButton";

export default {
  components: {
    DowloadButton
  },
  data() {
    let that = this;
    var checkNumPot3 = (rule, value, callback) => {
      const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
      if (!value) {
        return callback(new Error("请填写数字"));
      } else if (!reg.test(value)) {
        return callback(new Error("请填写数字,最多2位小数"));
      } else if (value <= 0 || value > 100) {
        return callback(new Error("请填写0-100以内的数"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value !== this.query.password) {
        callback(new Error("两次输入密码不一致!"));
      }
      callback();
    };
    return {
      rules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur" },
        ],
        registerCapital: [
          { validator: checkNumPot2, trigger: "blur", required: true },
        ],
        ceoMobile: rule.phone,
        orderAmountRate: [
          { validator: checkNumPot3, trigger: "blur", required: true },
        ],

        // rule
      },
      auditLoading: false,
      addrRules: {
        mobilPhone: rule.phone,
      },
      previewImages:[],
      arealist: [],
      props: {},
      isEdit: false,
      checkList: [],
      businessScope: {},
      adAddr: {},
      editBusiness: false,
      tableDate: [],
      businessTableLabel: [
        {
          name: "label",
          label: "证件类型",
          width: "300px",
        },
        {
          name: "licenseNumber",
          label: "证件号",
          width: "300px",
        },
        {
          name: "licenseEndTime",
          label: "过期时间",
          width: "300px",
        },
        {
          name: "filePath",
          label: "附件",
          width: "300px",
        },
        {
          name: "operation",
          label: "操作",
        },
      ],
      editCertificates: false,
      adAddressflag: false,
      dialogImageUrl: "",
      isShowBigPic: false,
      certificatesForm: {
        id: 1,
        certificatesType: "",
        outTime: "",
        annex: "",
      },
      regionId: "",
      addrtableDate: [],
      query: {
        approvalStatus: "",
        approvalTime: "",
        approvalUser: 0,
        businessCategoryId: [],
        ceoMobile: "",
        ceoName: "",
        cityId: 0,
        code: "",
        countyId: 0,
        identifyCode: "",
        legalPerson: "",
        loginAccount: "",
        merchantTypeId: 0,
        name: "",
        password: "",
        provinceId: 0,
        publishStatus: "",
        qualityPersonInCharge: "",
        registerAddress: "",
        registerCapital: "",
        socialCreditCode: "",
        userId: 0,
        mobile: '',
        email: ''
      },
      rejectText: {},
      rejectFlag: false,
      tablist: [],
      lisenceClickItem: {},
      lisenceFlag: false,
      adAddrsss: {},
      insertProgram: {
        folderId: 0,
      },
      headersProgram: {
        token: getToken(),
        Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
      },
      adAddrSelect: {},
      lisenceTableDate: [],
      lisenceEdit: [],
      areasTree: [],
      oldAddrtableDate: [],
      listmerchantType: [],
      isLoding: false,
      cateGory: false,
      showlisenceItem: {},
      dialogStatus: false,
      isBtn: false
    };
  },
  computed: {
    isSpecialInvoice() {
      return ['SPECIALINVOICE', 'ELECTRON_SPECIALINVOICE'].includes(this.query.fpinvoiceType)
    },
    coordinate() {
      if (this.query.latitude && this.query.longitude) {
        return this.query.latitude + ',' + this.query.longitude
      }
      return ''
    }
  },
  methods: {
    // 改变发票类型=>普通发票需要去掉部分字段校验
    changeFpinvoiceType(e) {
      if (e !== 'VATINVOICE') return
      this.$refs.editForm.clearValidate(
        ['fptaxNumber', 'fpregisterAddress', 'fpregisterMobile', 'fpbankNumber', 'fpdepositBank']
      )
    },
    checkPermission,
    getFlexNum(){
      let num = 0
      this.cateGory
      for (const key in this.cateGory) {
        num++
      }
      if(num == 1){
        return 100
      } else if(num ==2) {
        return 50
      } else {
        return 33
      }
    },
    async getMerchantTypeDetailsById() {
      let { data } = await MerchantTypeDetailsById(this.query.merchantTypeId);
      let tableDate = data.licenseBases || [];
      let arr = [];
      tableDate.forEach((item) => {
        console.log("item=>", item);
        const ids = this.query.merchantLicenses.find(itemLicenses => itemLicenses.licenseBaseId === item.id)
        if (ids) {
          arr.push({
            licenseBaseId: item.id,
            isEdit: false,
            limit: item.multiple.code == "Y" ? 5 : 1,
            licenseEndTime: ids.isForever.code === 'Y' ? '' :ids.licenseEndTime,
            filePath: ids.filePath,
            filePathList: this.getsrc(ids.filePath),
            licenseNumber: ids.licenseNumber,
            label: item.name,
            merchantId: ids.merchantId,
            id: ids.id,
            isForever: ids.isForever.code === 'Y'
          })
        }
      });
      this.lisenceTableDate = arr;
    },
    showBigPic(row) {
      console.log('row----->',row);
      this.showlisenceItem = row;
      // this.dialogStatus = true;
      this.previewImages = row.filePathList.map(item=>{
        return item.url;
      });
      this.$refs[`ref${row.licenseBaseId}`].showViewer = true;
    },
    getimgList(row) {
      let arr = [];
      row.split(",").forEach((item) => {
        arr.push({
          fileIds: item,
        });
      });
      return arr;
    },
    back() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    async getmerchantType() {
      let { data } = await merchantType();
      data.forEach((item) => {
        if (item.id == this.query.merchantTypeId) {
          this.query.merchantTypeText = item.name;
        }
      });
    },
    address() {
      let obj = {
        cityId: "",
        countyId: "",
        detailedAddress: "",
        fixedPhone: "",
        defaultOrNot: "Y",
        merchantId: this.$route.query.id || "",
        isEdit: true,
        mobilPhone: "",
        name: "",
        provinceId: "",
        address: [],
      };
      this.addrtableDate.push(obj);
    },
    async delAddr(row, index) {
      if (row.id) {
        let { data } = await deladdr({ ids: [row.id] });
        if (data) {
          this.$message.success("已删除该地址！");
          this.addrtableDate.forEach((item, index) => {
            // console.log(item);
            if (item.id == row.id) {
              this.addrtableDate.splice(index, 1);
            }
          });
        }
      } else {
        this.addrtableDate.splice(index, 1);
      }
      this.oldAddrtableDate = this.addrtableDate.slice(0);
    },
    canceladdrEdit(row, index) {
      if (this.oldAddrtableDate.length == 0) {
        this.addrtableDate = [];
        return;
      }
      this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
    },
    async confirmaddrEdit(row, index) {
      if (row.address.length == 0 || row.name == "" || row.mobilPhone == "") {
        this.addrtableDate = JSON.parse(JSON.stringify(this.oldAddrtableDate));
        return;
      }
      row.provinceId = row.address[0];
      row.cityId = row.address[1];
      row.countyId = row.address[2];
      row.isEdit = false;
      if (this.row.id) {
        if (row.id) {
          let { data } = await editaddr(row);
          if (data.id) {
            this.$message.success("修改地址成功");
            row = data;
          }
        } else {
          let { data } = await adaddr(row);
          if (data.id) {
            this.$message.success("添加地址成功");
            data.address = [data.provinceId, data.cityId, data.countyId];
            this.$set(this.addrtableDate, index, data);
          }
        }
      }
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.addrtableDate));
    },
    editAddrItem(row, index) {
      row.isEdit = true;
    },
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    editLisenceFun(row) {
      row.isEdit = true;
      this.editLisenceItem = JSON.parse(JSON.stringify(row));
    },
    cancelLisenceEdit(row) {
      row.isEdit = false;
      this.lisenceTableDate = this.lisenceEdit;
    },
    async confirmLisenceEdit(row) {
      row.isEdit = false;
      row.filePathList.forEach((item) => {
        item.url = item.response.data.url;
      });
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    uploadSuccess(res, file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    handleRemove(file, fileList) {
      this.lisenceTableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filepath = this.getFilePath(fileList);
          item.filePathList = fileList;
        }
      });
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      return str.substr(0, str.length - 1);
    },
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    resetSucss(type, msg, tabType) {
      if (type) {
        this.$message.success(msg);
        if (tabType) {
          this.$emit("update:tabType", tabType);
        }
        this.$emit("update:visible", false);
        this.$emit("update:isReload", true);
      }
    },
    cancel(e) {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.go(-1);
    },
    cityChange(e) {
      this.query.provinceId = e[0];
      this.query.cityId = e[1];
      this.query.countyId = e[2];
      this.regionId = e;
    },
    // 通过审核
    async accepted() {
      this.isBtn = true
      let { data, code } = await accepted(this.$route.query.id);
      if (code == 0) {
        this.$message.success("已通过该采购商");
        this.auditLoading = true
        findPurMerchantDetailName(this.query.name).then(res=>{
          if(res.code == 0) {
            this.$router.push({
              path:'/merchant/list/editItem',
              query:{
                id: res.data,
                from: 'registerCheck'
              }
            })
          } else {
            this.noGetId()
          }
        }).catch(()=>{
          this.noGetId()
        }).finally(() => {
          this.auditLoading = false
        })
      }
    },
    // 没有查到id或者报错
    noGetId(){
      this.$store.dispatch("tagsView/delView", this.$route);
      let listQuery = getContextData("registerCheck_list");
      setContextData("registerCheck_list", {
        current: 1,
        size: 10,
        model: {
          approvalStatus: { code: "ACCEPTED" },
        },
      });
      this.$router.go(-1);
    },
    // 驳回审核
    async rejected() {
      this.$refs.rejectform.validate(async (valid) => {
        if (valid) {
          let { data } = await rejected({
            id: this.$route.query.id,
            rejectReason: this.rejectText.text,
          });
          if (data) {
            this.$message.success("已驳回该采购商");
            this.$store.dispatch("tagsView/delView", this.$route);
            let listQuery = getContextData("registerCheck_list");
            setContextData("registerCheck_list", {
              current: 1,
              size: 10,
              model: {
                approvalStatus: { code: "REJECTED" },
              },
            });
            this.$router.go(-1);
          }
        } else {
          return false;
        }
      });

    },
    // 冻结
    async frozen() {
      let { data } = await frozen(this.$route.query.id);
      if (data) {
        this.cancel();
        this.$message.success("已冻结该销售商！");
      }

    },
    // 启用
    async enable() {
      let { data } = await enable(this.$route.query.id);

      if (data) {
        this.cancel();
        this.$message.success("已启用该销售商！");
      }
    },
    checkListFun(e) {
      this.query.businessCategoryId = e;
    },
    // 获取经营范围
    async getjyfw() {
      let { data } = await findSaleScope();
      let obj = {};
      data.forEach((item) => {
        if (obj[item.parentName + ":"]) {
          obj[item.parentName + ":"].push(item);
        } else {
          obj[item.parentName + ":"] = [];
          obj[item.parentName + ":"].push(item);
        }
      });
      this.businessScope = obj;
    },
    // 获取详情
    async getitem() {
      this.isLoding = true;
      if (!this.$route.query.id) {
        let tableDate = (await listByLicenseBaseType()).data;
        tableDate.forEach((item) => {
          let obj = {
            licenseBaseId: item.id,
            licenseEndTime: "",
            filePath: "",
            licenseNumber: "",
            label: item.name,
            isEdit: false,
            filePathList: this.getsrc(item.filePath),
          };
          this.lisenceTableDate.push(obj);
        });
        this.listLoading = false;
        this.query.deliveryAddressList = [];
        return;
      }
      let { data } = await getitems(this.$route.query.id);
      if (!data.deliveryAddressList) {
        data.deliveryAddressList = [];
      }
      data.deliveryAddressList.forEach((item) => {
        item.address = [item.provinceId, item.cityId, item.countyId];
      });
      this.query = Object.assign(data);
      this.isLoding = false;
      this.query.regionId = [data.provinceId, data.cityId, data.countyId];
      this.addrtableDate = data.deliveryAddressList;
      this.checkList = data.businessCategoryId;
      let obj = {};
      if (!data.businessCategoryDetailList) {
        this.cateGory = false;
      } else {
        data.businessCategoryDetailList.forEach((item) => {
          if (!obj[item.parentName]) {
            obj[item.parentName] = [];
            obj[item.parentName].push(item);
          } else {
            obj[item.parentName].push(item);
          }
        });
        this.cateGory = obj;
      }
      if (data.invoiceInfo) {
        this.$set(
          this.query,
          "fpinvoiceType",
          data.invoiceInfo.invoiceType.code
        );
        this.$set(this.query, "fpname", data.invoiceInfo.name);
        this.$set(
          this.query,
          "fpdepositBank",
          data.invoiceInfo.depositBank
        );
        this.$set(this.query, "fptaxNumber", data.invoiceInfo.taxNumber);
        this.$set(
          this.query,
          "fpregisterMobile",
          data.invoiceInfo.registerMobile
        );
        this.$set(
          this.query,
          "fpbankNumber",
          data.invoiceInfo.bankNumber
        );
        this.$set(
          this.query,
          "fpregisterAddress",
          data.invoiceInfo.registerAddress
        );
        this.$set(this.query, "email", data.invoiceInfo.email);
        this.$set(this.query, "mobile", data.invoiceInfo.mobile);
      }

      delete this.query.invoiceInfo;
      this.getmerchantType();
      this.listmerchantType = [
        {
          name: data.merchantType,
          id: data.merchantTypeId,
        },
      ];
      this.getMerchantTypeDetailsById();
    },
    addrCancel() {
      this.adAddressflag = false;
      this.adAddr = {};
    },
    getaddrSelectText(item) {
      if (!item) return;
      this.addrSelectText = item.label + this.addrSelectText;
      if (item.parent) {
        this.getaddrSelectText(item.parent);
      }
      return this.addrSelectText;
    },
    addrcityChange(e) {
      this.adAddr.region = this.getaddrSelectText(
        this.$refs.addr.getCheckedNodes()[0]
      );
      this.adAddr.provinceId = e[0];
      this.adAddr.cityId = e[1];
      this.adAddr.countyId = e[2];
    },
    clearFun: function () {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    edit(content) {
      this.$refs[content].validate(async (valid) => {
        if (valid) {
          if (!this.$route.query.id) {
            this.query.merchantLicenses = [];
            this.tableDate.forEach((item) => {
              if (item.filePath != "") {
                this.query.merchantLicenses.push(item);
              }
            });
            if (!this.query.merchantLicenses.length) {
              this.$message.error("请填写采购商资质");
            }
            this.query.invoiceInfoSaveDTO = {
              invoiceType: this.query.fpinvoiceType,
              name: this.query.fpname,
              taxNumber: this.query.fptaxNumber,
              registerMobile: this.query.fpregisterMobile,
              bankNumber: this.query.fpbankNumber,
              depositBank: this.query.fpdepositBank,
            };
            this.query.id = this.query.merchantId;
            let { data } = await add(this.query);
            if (data.id) {
              this.$message.success("创建采购商成功");
              this.query = {};
            }
          } else {
            this.query.merchantLicenses = [];
            this.lisenceTableDate.forEach((item) => {
              if (item.filePath != "") {
                this.query.merchantLicenses.push(item);
              }
            });

            this.query.deliveryAddressSaveDTOList = this.addrtableDate;

            this.query.invoiceInfoSaveDTO = {
              invoiceType: this.query.fpinvoiceType,
              name: this.query.fpname,
              taxNumber: this.query.fptaxNumber,
              registerMobile: this.query.fpregisterMobile,
              bankNumber: this.query.fpbankNumber,
              depositBank: this.query.fpdepositBank,
            };

            this.query.id = this.$route.query.id;
            let obj = {
              id: this.$route.query.id,
              purMerchantSaveDTO: this.query,
            };
            let { data } = await edititem(obj);
            this.resetSucss(data, "修改成功！", "PENDING");
          }

        } else {
          console.log("error submit!!");
          return false;
        }
      });
      this.isEdit = false;
    },
    closeDialogFun() {
      this.dialogStatus = false;
    },
  },
  created() {
    this.query = this.row;
    this.getareas();
    this.getitem();
    this.getjyfw();
  },
  mounted() {
    if (this.$route.query.id) {
      this.isEdit = true;
    }
  },
  watch: {
    adAddrsss(newl, old) {
      this.query.regionId = newl;
      this.query.cityId = newl.cityId;
      this.query.provinceId = newl.provinceId;
      this.query.countyId = newl.countyId;
    },
    adAddrSelect(newl, old) {
      this.adAddr.regionId = newl;
      this.adAddr.cityId = newl.cityId;
      this.adAddr.provinceId = newl.provinceId;
      this.adAddr.countyId = newl.countyId;
    },
  },
};
</script>
<style lang="less" scoped>
.archivesEditContent {
  border-top: 1px solid #ebecee;
  padding: 0px 20px;
  background-color: #fff;
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .cateGory{
      display: flex;
      .el-col-8{
        word-break:break-all;
        font-size: 14px;
        line-height: 20px;
        padding-bottom: 10px;
        text-overflow: wrap;
      }
    }
    /deep/ .el-col {
      line-height: unset;
    }
  }
  .uploadPic {
    padding-bottom: 100%;
    margin-bottom: -100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    > div {
      min-width: 100%;
      height: 25px;
    }
  }
  .productPicContent .text p {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    font-size: 13px;
    margin: 0;
  }
  .detailMsg {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
      "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: #aaaaaa;
    line-height: 20px;
    padding-bottom: 20px;

    font-size: 13px;
  }
  .graytext {
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif,
    "PingFangSC-Regular", "PingFang SC", sans-serif-400;
    font-weight: 400;
    color: rgba(0, 0, 0, .6);
    line-height: 20px;
    font-size: 13px;
    margin: 0;
    padding-bottom: 20px;
  }
  // /deep/ .el-input.is-disabled .el-input__inner {
  //   color: #04060c;
  //   background-color: #eaf7e7;
  // }
  // /deep/ .is-checked .is-disabled .el-checkbox__inner {
  //   color: #2fa338;
  //   background-color: #1b9e38;
  // }
  // /deep/ .is-checked .el-checkbox__label {
  //   color: #04060c;
  // }
  // /deep/ .is-disabled .is-checked .el-radio__inner {
  //   background-color: #1b9e38;
  // }
  // /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
  //   color: #04060c;
  // }
  // /deep/ .el-col {
  //   line-height: 40px;
  // }
}
// /deep/ .el-input.is-disabled .el-input__inner {
//   color: #04060c;
//   background-color: #eaf7e7;
// }
// /deep/ .is-checked .is-disabled .el-checkbox__inner {
//   color: #2fa338;
//   background-color: #1b9e38;
// }
// /deep/ .is-checked .el-checkbox__label {
//   color: #04060c;
// }
// /deep/ .is-disabled .is-checked .el-radio__inner {
//   background-color: #1b9e38;
// }
// /deep/ .is-disabled.is-checked.el-radio > .el-radio__label {
//   color: #04060c;
// }

// /deep/.el-checkbox__input.is-disabled + span.el-checkbox__label{

// }

/deep/ .el-upload {
  width: 40px;
  height: 40px;
  position: relative;
}
/deep/ .el-upload > i {
  font-size: 16px;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
/deep/ .el-upload-list .el-upload-list__item {
  width: 40px;
  height: 40px;
}
/deep/ .hide .el-upload--picture-card {
  display: none;
}
</style>
