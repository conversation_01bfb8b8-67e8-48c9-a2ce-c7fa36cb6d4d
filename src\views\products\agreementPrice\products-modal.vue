<template>
<el-dialog class="products-modal" title="选择商品" :visible.sync="visible" width="70%">
  <div v-loading="loading">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="search"
      @reset="reset"
      @search="load"
    >
      <im-search-pad-item prop="erpCode">
        <el-input v-model="search.erpCode" placeholder="请输入ERP商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model="search.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="warehouseIds" v-if="storeList.length">
        <el-select  v-model="search.warehouseIds" class="width160" collapse-tags multiple placeholder="所在仓库" clearable>
          <el-option v-for="item in storeList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer" v-show="isExpand">
        <el-input v-model="search.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item prop="stockCondition" v-show="isExpand">
        <el-select v-model="search.stockCondition" placeholder="库存" style="width: 110px">
          <el-option label="全部" value="ALL" />
          <el-option label="有货" value="WITH" />
          <el-option label="缺货" value="WITHOUT" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <el-table :data="list" border @selection-change="handleSelectionChange" height="600">
      <el-table-column label="序号" type="index" width="50"></el-table-column>
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="产品主图" width="80" class-name="img-cell">
        <template slot-scope="scope">
          <img :src="scope.row.pictIdS | imgFilter" width="50px" height="50px" />
        </template>
      </el-table-column>
      <el-table-column label="ERP商品编码/仓库" prop="productCode" width="180" show-overflow-tooltip>
        <template v-slot="{row}">
          <span>编码：{{ row.erpCode || '无' }}</span><br />
          <span>仓库：{{ row.warehouseName || '无' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" prop="productName" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column label="规格" prop="spec" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="单位" prop="unit" width="60" show-overflow-tooltip></el-table-column>
      <el-table-column label="生产厂家" prop="manufacturer" show-overflow-tooltip></el-table-column>
      <el-table-column label="销售价" width="100" show-overflow-tooltip>
        <template v-slot="scope">
          <span style="color: #F56C6C">{{ scope.row.salePrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成本价" width="100" show-overflow-tooltip  >
        <template v-slot="scope">
          <span style="color: #67C23A">{{ scope.row.costPrice }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :total="total"
      :current-page.sync="page"
      :page-size.sync="pageSize"
      @current-change="load"
      @size-change="load"
      layout="->, prev, pager, next, sizes, jumper"/>
  </div>

  <div slot="footer">
    <el-button @click="close">取消</el-button>
    <el-button type="primary" @click="ok">确定</el-button>
  </div>
</el-dialog>
</template>

<script>
import request from '@/utils/request'
import {
    getAllStore
  } from "@/api/products/store";
export default {
  data () {
    return {
      visible: false,
      list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      loading: false,
      selection: [],
      isExpand:false,
      search: {
        stockCondition: 'ALL',
        erpCode: '',
        productName: '',
        manufacturer: ''
      },
      storeList: []
    }
  },
  async mounted() {
    try {
			const res = await getAllStore()
			const data = res.data;
          if(data && data.length) {
            data.forEach(item => {
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeList.push(obj)
            })
          }
		} catch (error) {
			console.log(error)
		}
	},
  methods: {
    splitString (val) {
      if (!val) {
        return ''
      }
      return val.split(',')
    },
    close () {
      this.visible = false
    },

    ok () {
      this.$emit('change', this.selection)
      this.close()
    },

    open () {
      this.visible = true
      this.page = 1
      this.selection = []
      this.load()
    },

    async load () {
      this.loading = true
      try {
        const { data } = await request.post('product/admin/product/page', {
          current: this.page,
          map: {},
          model: this.search,
          order: 'descending',
          size: this.pageSize,
          sort: 'id'
        })
        this.total = data.total
        this.list = data.records
      } catch (e) {

      }
      this.loading = false

    },
    reset() {
      this.search = {
        stockCondition: 'ALL',
        erpCode: '',
        productName: '',
        manufacturer: '',
        warehouseIds:[]
      }
      // this.search.stockCondition = 'ALL'
      this.load()
    },

    handleSelectionChange (val) {
      this.selection = val
    }
  }
}
</script>

<style lang="scss" scoped>

</style>>
