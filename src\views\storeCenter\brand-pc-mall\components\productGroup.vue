<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" @confirm="confirm">
    <el-table
      ref="tableData"
      :data="tableData"
      border
    >
      <el-table-column label="序号" type="index" width="54" align="center" />
      <el-table-column label="分组大标题" prop="name" width="170">
        <template slot-scope="scope">
          <el-input v-model="scope.row.name" placeholder="请输入分组大标题" class="el-input-none-border" />
        </template>
      </el-table-column>
      <el-table-column label="分组副标题" prop="name1" width="170">
        <template slot-scope="scope">
          <el-input v-model="scope.row.name1" placeholder="请输入分组副标题" class="el-input-none-border" />
        </template>
      </el-table-column>
      <el-table-column label="商品数量" prop="num" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.selectedProducts.length }}
          <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="selectProduct(scope.$index)">选择商品</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
            </span>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-button" @click="add"><span>添加分组</span></div>
    <im-dialog title="选择商品" :visible.sync="visible" width="1200px" append-to-body class="pro-select-dialog" @confirm="confirmPro">
      <div v-if="visible" class="pro-list">
        <div class="pro-search">
          <im-search-pad
            :model="searchForm"
            class="client-file-search-pad"
            :has-expand="false"
            @reset="handleReset"
            @search="handleSearch"
          >
            <im-search-pad-item prop="productCode">
              <el-input v-model="searchForm.productCode" placeholder="请输入商品编码" @keyup.enter.native="handleSearch" />
            </im-search-pad-item>
            <im-search-pad-item prop="productName">
              <el-input v-model="searchForm.productName" placeholder="请输入商品名称" @keyup.enter.native="handleSearch" />
            </im-search-pad-item>
            <im-search-pad-item prop="manufacturer">
              <el-input v-model="searchForm.manufacturer" placeholder="请输入生产厂家" @keyup.enter.native="handleSearch" />
            </im-search-pad-item>
          </im-search-pad>
          <im-table-page
            ref="tablePage"
            
            :data.sync="tableDataPage"
            :remote-method="queryData"
            height="450"
            style="margin-right: 16px;"
            :more-check-box="handleSelectionChange"
            :selectAll="selectAll"
            :select="select"
          >
            <el-table-column type="selection" fixed align="center" />
            <el-table-column label="序号" type="index" width="54" fixed align="center" />
            <el-table-column label="主图" width="64" prop="pictIdS" class-name="img-cell">
              <template slot-scope="scope">
                <img :src="scope.row.pictIdS | imgFilter" class="productImg">
              </template>
            </el-table-column>
            <el-table-column label="商品编码" width="180" prop="productCode" show-overflow-tooltip />
            <el-table-column label="商品名称" width="240" prop="productName" show-overflow-tooltip />
            <el-table-column label="规格" width="240" prop="spec" show-overflow-tooltip />
            <el-table-column label="生产厂家" width="240" prop="manufacturer" show-overflow-tooltip />
            <el-table-column label="单位" width="96" prop="unit" show-overflow-tooltip />
            <el-table-column label="产地" width="96" prop="area" show-overflow-tooltip />
            <el-table-column label="销售价" width="96" prop="salePrice" show-overflow-tooltip />
            <el-table-column label="成本价" width="96" prop="costPrice" show-overflow-tooltip />
            <el-table-column label="库存" width="96" prop="realStockQuantity" show-overflow-tooltip />
          </im-table-page>
        </div>
        <div class="pro-selected">
          <div class="selected-title">
            已选商品({{ selectedList.length }})<span>鼠标拖拽可调整分组顺序</span>
          </div>
          <div class="selected-content">
            <div
              v-for="(item, index) in selectedList"
              :key="item.id"
              v-dragging="{ item: item, list: selectedList, group: 'selectedListInfo' }"
              class="selected-list"
            >
              <img :src="item.pictIdS | imgFilter">
              <div class="infor">
                <div class="name over-ellipsis">{{ item.productName }}</div>
                <div class="spec over-ellipsis">厂家：{{ item.manufacturer }}</div>
                <div class="spec over-ellipsis">规格：{{ item.spec }}</div>
                <div class="sort">
                  <span style="white-space: nowrap;">排序：</span><el-input-number v-model="item.sortValue" :controls="false" :precision="0"  />
                </div>
              </div>
              <img src="../../../../assets/img/index/btn_close_qualification.png" class="delete" @click="delSelect(index)">
            </div>
          </div>
        </div>
      </div>
    </im-dialog>
  </im-dialog>
</template>

<script>
import { groupUpdate, getProductList } from './index'
export default {
  name: 'ProductGroup',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '720px'
    }
  },
  data() {
    return {
      visibleDialog: false,
      visible: false,
      tableData: [],
      tableDataPage: [],
      fileList: [],
      scopeIndex: '',
      searchForm: {
        productCode: '',
        productName: '',
        manufacturer: '',
        whetherOnSale:"Y"
      },

      selectedList: [],
      selectIndex: 0
    }
  },
  watch: {
    tableDataPage() {
      const _that = this
      setTimeout(function() {
        _that.tableDataPage.map((val, index) => {
          _that.selectedList.map(item => {
            if (val.id === item.id) {
              _that.$refs.tablePage.toggleRowSelection(_that.tableDataPage[index], true)
            }
          })
        })
      }, 10)
    }
  },
  methods: {
    init(data) {
      this.tableData = data.map(val => {
        return {
          name: val.name,
          name1: val.name1,
          num: val.pageDataProductGroupRelVoList.length,
          selectedProducts: val.pageDataProductGroupRelVoList
        }
      })
      this.visibleDialog = true
    },
    confirm() {
      groupUpdate(this.tableData).then(res => {
        this.$emit('confirm')
        this.$message.success('编辑商品分组成功')
        this.visibleDialog = false
      })
    },
    confirmPro() {
      this.tableData[this.selectIndex].selectedProducts = this.selectedList.map(val => {
        val.productId = val.id
        return val
      })
      this.visible = false
    },
    handleSelectionChange(selection) {
      // console.log('handleSelectionChange',selection);
      // let list = selection.concat(this.selectedList);
      // let arr = this.unqiArray(list,'id');
      // this.selectedList = arr;
    },
    selectAll(selection){
      console.log('selectAll',selection);
      console.log('tableDataPage',this.tableDataPage);
      if(selection.length>0){
        let list = selection.concat(this.selectedList);
        let arr = this.unqiArray(list,'id');
        this.selectedList = arr;
      } else {
        this.tableDataPage.forEach(item=>{
          let index = this.selectedList.findIndex(i=>i.id == item.id);
          if(index != -1){
            this.selectedList.splice(index, 1)
          }
        })
      }
    },
    unqiArray(arr = [],type){
      var hash = {};
      arr = arr.reduce(function (item, next) {
          hash[next[type]] ? "" : (hash[next[type]] = true && item.push(next));
          return item;
      }, []);
      return arr;
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    },
    select(selection, row) {
      let valid = true
      let rowIndex = 0
      this.selectedList.length > 0 ? this.selectedList.map((val, index) => {
        if (val.id === row.id) {
          valid = false
          rowIndex = index
        }
      }) : ''
      if (valid) {
        this.selectedList.push(row)
      } else {
        this.selectedList.splice(rowIndex, 1)
      }
    },
    add() {
      this.tableData.push({
        name: '',
        name1: '',
        sortValue: 0,
        selectedProducts: []
      })
    },
    selectProduct(index) {
      this.selectIndex = index
      this.selectedList = this.tableData[index].selectedProducts.map(val => {
        if (val.product) {
          val.product.productId = val.product.id
          val.product.sortValue = val.sortValue
          return val.product
        } else {
          val.productId = val.id
          return val
        }
      })
      this.visible = true
    },
    handleReset() {
      this.searchForm = {
        productCode: '',
        productName: '',
        manufacturer: '',
        whetherOnSale:'Y'
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.tablePage.doRefresh({ currentPage: 0, pageSize: 10 })
    },
    delSelect(index) {
      this.selectedList.splice(index, 1)
      this.$refs.tablePage.clearSelection()
      this.tableDataPage.map((val, index) => {
        this.selectedList.map(item => {
          if (val.id === item.id) {
            this.$refs.tablePage.toggleRowSelection(this.tableDataPage[index], true)
          }
        })
      })
    },
    queryData(pageParam) {
      return new Promise((then, reject) => {
        getProductList({
          current: pageParam.pageNum,
          size: pageParam.pageSize,
          model: {
            ...this.searchForm,
            // approvalStatus: 'ACCEPTED'
          }
        }).then(res => {
          then(res)
        })
      })
    }
  }
}
</script>

<style lang="less">
  .add-button{
    border: 1px solid #EBECEE;
    border-top: 0;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 14px;
      color: #0056e5;
      position: relative;
      &:after{
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        background-color: #0056E5;
        width: 7px;
        height: 1px;
        margin-top: 0;
      }
      &:before{
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -3px;
        background-color: #0056E5;
        width: 1px;
        height: 7px;
      }
    }
  }
  .pro-select-dialog{
    .el-dialog__body{
      padding-right: 4px;
    }
  }
  .upload-delete{
    display: none
  }
  .pro-list{
    display: flex;
    .pro-search{
      width: 792px;
      .im-search-pad{
        .el-input__inner{
          height: 32px;
          line-height: 32px;
        }
      }
    }
    .pro-selected{
      padding: 8px 0 0 15px;
      border-left: 1px solid #EBECEE;
      .selected-title{
        font-size: 16px;
        font-weight: 500;
        color: #1e2439;
        span{
          font-size: 14px;
          font-weight: 400;
          color: #b4b6bd;
          padding-left: 8px;
        }
      }
      .selected-content{
        max-height: 572px;
        overflow-y: auto;
        padding-right: 12px;
        .selected-list{
          margin-top: 16px;
          padding: 16px;
          border: 1px solid #ebecee;
          display: flex;
          position: relative;
          cursor: pointer;
          max-width: 350px;
          img{
            width: 88px;
            height: 88px;
          }
          .infor{
            padding-left: 12px;
            font-size: 14px;
            color: #505465;
            .name{
              font-size: 16px;
              color: #1e2439;
            }
            .spec{
              padding-top: 16px;
            }
            .sort{
              padding-top: 16px;
              display: flex;
              align-items: center;
            }
            .over-ellipsis{
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .delete{
            position: absolute;
            right: -10px;
            top: -10px;
            width: 20px;
            height: 20px;
            display: none;
          }
          &:hover{
            box-shadow: 0px 2px 10px 0px rgba(5,12,36,0.08);
            .delete{
              display: block;
            }
          }
        }
      }
    }
  }
  // .table-header-select-none{
  //   .el-table__fixed-header-wrapper{
  //     thead{
  //       tr{
  //         th:first-child{
  //           .cell{
  //             // display: none;
  //           }
  //         }
  //       }
  //     }
  //   }
  // }

</style>
