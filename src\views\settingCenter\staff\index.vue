<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="onSearchSubmitFun"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="model.account">
        <el-input v-model.trim="listQuery.model.account" placeholder="输入员工账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="model.name">
        <el-input v-model.trim="listQuery.model.name" placeholder="输入员工姓名" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="listQuery.model.positionStatus"
        :tabs="[
          { name: '在职', value: 'WORKING', hide: !checkPermission(['admin','sale-saas-setting-manage-staff:on', 'sale-platform-setting-manage-staff:on']) }, 
          { name: '离职', value: 'QUIT', hide: !checkPermission(['admin','sale-saas-setting-manage-staff:off', 'sale-platform-setting-manage-staff:off']) }
        ]"
        @change="changeTabsFun"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin','sale-saas-setting-manage-staff:add', 'sale-platform-setting-manage-staff:add'])"  type="primary" @click="newFun">+新增员工</el-button>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="60"
            show-overflow-tooltip
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index+1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="(item.width?item.width:'350px')"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span v-if="item.name=='positionStatus'">{{ row[item.name].desc }}</span>
              <span v-else-if="item.name=='roleList'">
                <template v-for="item,index in row[item.name]">
                  {{ index>0?","+item.name:item.name }}
                </template>
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="180"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin','sale-saas-setting-manage-staff:assign', 'sale-platform-setting-manage-staff:assign'])" class="table-edit-row-item">
                  <el-button type="text" @click="editRoleClickFun(scope.row)">角色分派</el-button>
                </span>
                <span v-if="checkPermission(['admin','sale-saas-setting-manage-staff:edit', 'sale-platform-setting-manage-staff:edit'])" class="table-edit-row-item">
                  <el-button type="text" @click="editClickFun(scope.row)">编辑</el-button>
                </span>
                <span v-if="listQuery.model.positionStatus=='WORKING'&&checkPermission(['admin','sale-saas-setting-manage-staff:leave', 'sale-platform-setting-manage-staff:leave'])" class="table-edit-row-item">
                  <el-button type="text" @click="setUserQuit(scope.row)">离职</el-button>
                </span>
                <span v-if="listQuery.model.positionStatus=='QUIT'&&checkPermission(['admin','sale-saas-setting-manage-staff:reinstate', 'sale-platform-setting-manage-staff:reinstate'])" class="table-edit-row-item">
                  <el-button type="text" @click="setUserWorking(scope.row)">复职</el-button>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getList" />
      </div>
      <!-- 设置 编辑 -->
      <edit v-if="showEdit" :visible.sync="showEdit" :is-reload.sync="submitReload" :row.sync="row" />
      <!-- 设置 编辑 -->
      <!-- 设置 角色 -->
      <setRoleTable v-if="showRoleEdit" :visible.sync="showRoleEdit" :is-reload.sync="submitReload" :row.sync="row" />

      <!-- 设置 角色 -->
    </div>

  </div>
</template>
<script>
import checkPermission from '@/utils/permission'
import { page, editNewApi } from '@/api/setting/staff'
import edit from '@/views/settingCenter/staff/edit'
import setRoleTable from '@/views/settingCenter/staff/setRoleTable'
import Pagination from '@/components/Pagination'
import TabsLayout from "../../../components/TabsLayout/index";
export default {
  data() {
    return {
      // roleList:[],
      showEdit: false,
      showRoleEdit: false,
      row: {

      },

      tableTitle: [
        {
          key: 0,
          label: '在职状态',
          name: 'positionStatus',
          width: '120px'
        },
        {
          key: 1,
          label: '员工账号',
          name: 'account',
          width: '150px'
        },
        {
          key: 2,
          label: '员工姓名',
          name: 'name',
          width: '150px'
        },
        {
          key: 3,
          label: '员工联系号码',
          name: 'contactNumber',
          width: '180px'
        },
        {
          key: 4,
          label: '角色',
          name: 'roleList',
          width: '180px'
        },
        {
          key: 5,
          label: '员工入职时间',
          name: 'createTime'
        }
      ],
      submitReload:false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model:{
         positionStatus:"WORKING",
          name:'',
          account:''
        },
        current: 1,
        size: 10,
        order: "descending",
	      sort: "id"
      },
      titles: ['显示字段项','隐藏字段项'],
      showSelectTitle: false
    };
  },
  watch:  {
    submitReload:function(newVal,oldVal){
      if(newVal){
        this.submitReload=false;
        this.getList()
      }
    }
  },
 components: {
   Pagination,edit,setRoleTable },
 methods: {
   renderHeader(h, { column }) {
     // h即为cerateElement的简写，具体可看vue官方文档
     return (<div style="position:relative">
       <div onClick={this.setHeaer}>
       <i class="el-icon-menu" />
       </div>
       <el-dialog
     title="设置显示列表"
     showClose={false}
     visible={this.showSelectTitle}
     width="640px"
     center
     >
     <el-transfer
     vModel={this.tableVal}
     data={this.tableTitle}
     titles={this.titles}
       ></el-transfer>
       <div style="margin-top: 25px;text-align: center;">
       <el-button  onClick={this.cancel}>
       取消
       </el-button>
       <el-button type="primary" onClick={this.setHeaer}>
       确认
       </el-button>
       </div>
       </el-dialog>
       </div>);
   },
   cancel:function() {
     this.showSelectTitle = false
   },
   setHeaer:function(){
     this.showSelectTitle=!this.showSelectTitle
   },
   checkPermission,
    newFun:function(){
        this.row={};
        this.showEdit=true
    },
    editClickFun(row) {
        this.row=row
        this.showEdit=true
    },
    editRoleClickFun(row) {
        this.row=row
        this.showRoleEdit=true
    },
    async getList() {
      this.listLoading = true
      page(this.listQuery).then((res) => {
        if (res.code !== 0) return
        this.list = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.listLoading = false
      })
    },
    changeTabsFun() {
      this.list = []
      this.listQuery.pag=1
      this.getList()
    },
    onSearchSubmitFun() {

      this.list = []
      this.getList()
    },
    // async getRoleList() {
    //   const { data } = await query({dsType: "ALL"})
    //   this.roleList = data
    // },
    // 设置离职
     setUserQuit(row) {
      var _this=this;
      row.positionStatus= "QUIT"


      this.$confirm('是否确认该员工离职?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const data = await editNewApi(row);
          console.log('data--离职-->',data);
          if(data.code==0){
            this.$message({
              type: 'success',
              message: '设置员工离职成功!'
            });
            _this.getList();
            _this.chageTabsFun();
          }

        })
    },
    async setUserWorking(row) {
      // 设置复职
      var _this=this;
      row.positionStatus="WORKING";

      this.$confirm('是否确认该员工复职?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const data = await editNewApi(row);
          console.log('data--复职-->',data);
          if(data.code==0){
            this.$message({
              type: 'success',
              message: '设置员工复职成功!'
            });
            _this.getList();
            _this.chageTabsFun()
          }

        })
    }
  },
  mounted() {
      if(this.checkPermission(['admin','sale-saas-setting-manage-staff:on', 'sale-platform-setting-manage-staff:on'])){
        this.listQuery.positionStatus = "WORKING"
        this.listQuery= {
          model:{
          positionStatus:"WORKING",
            name:'',
            account:''
          },
          current: 1,
          size: 10,
          order: "descending",
          sort: "id"
        }
        this.getList()
      }else if(this.checkPermission(['admin','sale-saas-setting-manage-staff:off', 'sale-platform-setting-manage-staff:off'])){
        this.listQuery.positionStatus = "QUIT"
        this.listQuery= {
          model:{
          positionStatus:"QUIT",
            name:'',
            account:''
          },
          current: 1,
          size: 10,
          order: "descending",
          sort: "id"
        }
        this.getList()
      }
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.staffPageTable {
  position: relative;
}
.staffPageContent {
  padding: 0;
  padding-bottom: 16px;
  background-color: #fff;
  .temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
  .form-inline{height:60px; overflow:hidden;}
  .title{
        border-bottom:2px solid #EBECEE;
        margin-bottom:12px;
        padding:0 12px;
        padding-top: 10px;
      span{
        margin-bottom: -2px;
        padding:0 15px;
        height: 40px;
        line-height: 30px;
        display:block;
        background: rgba(255,255,255,0);
        border-bottom:2px solid rgb(64, 158, 255);
        font-size: 16px;
        font-family: 'PingFangSC-Regular', 'PingFang SC', 'PingFangSC-Regular', 'PingFang SC'-400;
        font-weight: 400;
        color:rgb(64, 158, 255);
      }
  }

  .formItem{width:586px;}
  .line{color:#dfe6ec; margin:0 6px;}
  .typeTabs{height: 40px;margin: 0 15px -2px;width: 100%;}
  .frs {
    position: absolute;
    right: 15px;
    top: 10px;
  }

}
</style>
