<template>
  <div class="table_wrapper">
    <im-search-pad :model="query.model" @reset="reset" :hasExpand="false" @search="search">
      <im-search-pad-item prop="code">
        <el-input v-model="query.model.purMerchantName" placeholder="请输入客户名称" style="width: 280px" />
      </im-search-pad-item>
      <im-search-pad-item prop="code">
        <el-select v-model="query.model.levelNumList" placeholder="所属等级" multiple>
          <el-option v-for="item in statisticLevelTotalData" :key="item.levelNum" :label="item.levelName" :value="item.levelNum" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <el-table :data="list" style="width: 100%;" v-loading="loading" border>
      <el-table-column type="index" />
      <el-table-column prop="name" label="客户名称">
        <template slot-scope="{row}">{{ row.purMerchantName || '-' }}</template>
      </el-table-column>
      <el-table-column prop="phone" label="所属等级">
        <template slot-scope="{row}">
          <span>{{ row.levelName || '-' }}</span>
          <span class="tag" v-if="row.isFirst">首次</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="最新修改时间">
        <template slot-scope="{row}">{{ row.updateTime || '-' }}</template>
      </el-table-column>
<!--      <el-table-column label="操作" width="100px" align="center">-->
<!--        <el-button type="text">变更等级</el-button>-->
<!--      </el-table-column>-->
    </el-table>
    <div class="page" v-if="list.length > 0">
      <el-pagination
        style="display: inline-block;"
        background
        @current-change="pageChange"
        :current-page="query.current"
        layout="total, prev, pager, next, jumper"
        :page-size="query.size"
        :total="listTotal"
      />
    </div>
    <ChangeLevel ref="changeLevelRef" />
  </div>
</template>

<script>
import { getLevelDetailList } from "@/api/retailStore";
import ChangeLevel from './changeLevel.vue'

const initModel = () => {
  return {
    levelListId: '',
    purMerchantName: ''
  }
}
export default {
  name: 'levelListTable',
  components: {
    ChangeLevel
  },
  props: {
    statisticLevelTotalData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      query: {
        current: 1,
        size: 10,
        model: initModel()
      },
      levelNumList: [],
      loading: false,
      list: [],
      listTotal: 0
    }
  },
  methods: {
    reset() {
      this.clear()
      this.setTableList()
    },
    clear() {
      this.query.current = 1
      this.query.model = {
        ...initModel(),
        levelListId: this.query.model.levelListId
      }
      this.list = []
      this.listTotal = 0
    },
    search() {
      this.query.current = 1
      this.setTableList()
    },
    init(levelListId) {
      this.query.model.levelListId = levelListId
      this.reset()
    },
    setTableList() {
      if (!this.query.model.levelListId) {
        this.list = []
        this.listTotal = 0
        return
      }
      this.loading = true
      getLevelDetailList(this.query).then(res => {
        this.list = res.records
        this.listTotal = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    pageChange(page) {
      this.query.current = page
      this.setTableList()
    },
  }
}
</script>

<style lang="scss" scoped>
.table_wrapper {
  background-color: #fff;
  padding: 15px;
  box-sizing: border-box;
}
.page {
  text-align: center;
  margin-top: 10px;
}
.tag {
  border: 1px solid #f59a23;
  color: #f59a23;
  background-color: #fef5e9;
  padding: 2px 8px;
  margin-left: 4px;
  border-radius: 2px;
  font-size: 12px;
}
</style>
