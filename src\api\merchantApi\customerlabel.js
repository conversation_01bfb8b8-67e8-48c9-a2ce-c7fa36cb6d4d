import request from "@/utils/request";
var qs = require("qs");

//  分页
export function page(data) {
  if (data.page) data.size = data.page
  return request({
    url: `/merchant/admin/customerTag/page`,
    method: "post",
    data,
  });
}

// 新增客户标签
export function getDetail(id) {
  return request({
    url: `/merchant/admin/customerTag/${id}`,
    method: "get",
  });
}
// 新增客户标签
export function addLabel(data) {
  return request({
    url: `/merchant/admin/customerTag`,
    method: "post",
    data,
  });
}

// 修改 客户标签
export function updateLabel(data) {
  return request({
    url: `/merchant/admin/customerTag`,
    method: "put",
    data,
  });
}

// 删除 客户标签
export function delLabel(id) {
  return request({
    url: `/merchant/admin/customerTag?ids[]=${id}`,
    method: "delete",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });
}

// 关联客户相关接口

/***
 * @description 关联总数
 * @param {*} customerTagId  客户标签id
 * @param {*} keyword  客户编码、名称
 * @return  { "bingSum": 已关联客户数量, "noBingSum": 未关联客户数量}
 * */
export function customerCount(data) {
  return request({
    url: `/merchant/admin/customerTag/sum`,
    method: "post",
    data,
  });
}

/***
 * @description 已关联客户 分页查询
 * @param {*} data   分页查询传参
 * @return  list  {
 *               "id":客户id,
 *               "customerCode":客户编码,
 *               "name":客户名称,
 *               "merchantType":客户类型,
 *               "ceoName":联系人,
 *               "ceoMobile":联系人电话,
 *               "region":所在地区,
 *               "keyword":"客户名称/客户编码模糊搜索"
 *           }
 * */
export function bindingPage(data) {
  return request({
    url: `/merchant/admin/customerTag/relevance`,
    method: "post",
    data,
  });
}

/***
 * @description 未关联客户 分页查询
 * @param {*} data   分页查询传参
 * @return  list  {
 *               "id":客户id,
 *               "customerCode":客户编码,
 *               "name":客户名称,
 *               "merchantType":客户类型,
 *               "ceoName":联系人,
 *               "ceoMobile":联系人电话,
 *               "region":所在地区,
 *               "keyword":"客户名称/客户编码模糊搜索"
 *           }
 * */
export function noRelevancePage(data) {
  return request({
    url: `/merchant/admin/customerTag/noRelevance`,
    method: "post",
    data,
  });
}

/***
 * @description 添加
 * @param {*}  {
 *                  customerTagId:'111221323', 标签Id
 *                  purMerchantIds :1,2   客户IDs
 *              }
 * */
export function bindingRemoveAction(data) {
  return request({
    url: `/merchant/admin/customerTag/binding`,
    method: "post",
    data,
  });
}
// 移除
/***
 * @description 移除
 * @param {*}  {
 *                  customerTagId:'111221323', 标签Id
 *                  purMerchantIds :1,2   客户IDs
 *              }
 * */
export function noBindingRemoveAction(data) {
  return request({
    url: `/merchant/admin/customerTag/noBinding`,
    method: "post",
    data,
  });
}

/**
 * @description 获取导入指定-排除客户数量
 * <AUTHOR>
 */

export const customerTagNum = data => {
  return request({
    url: `/merchant/admin/customerTag/sum/specify/setting`,
    method: "post",
    params: {
      ...data
    },
  });
}
// 查询根据业务员指定客户数量的接口
export const specifySumBySaleMan = data => {
  return request({
    url: '/merchant/admin/customerTag/sum/specify/specifySumBySaleMan',
    method: "post",
    data
  })
}
/**
 * @description 分页查询指定-排除导入客户
 * <AUTHOR>
 * @param {*} data
 * @returns
 */
export const pageImportCustomerList = data => {
  return request({
    url: `/merchant/admin/customerTag/page/specify/setting`,
    method: "post",
    data,
  });
}

/**
 * @description 批量删除指定-排除导入客户
 * <AUTHOR>
 * @param {*} ids
 * @returns
 */
export const patchDeleteImportCustomer = ids => {
  console.log('ids',ids);
  return request({
    url: `/merchant/admin/customerTag/delete/specify/setting?ids[]=${ids.toString()}`,
    method: "post",
    // params: {
    //   'ids[]': ids
    // }
  });
}

/**
 * @description 关联客户标签设置页
 * <AUTHOR>
 * @param {*} id
 */
export const getAssociationDetails = id => {
  return request({
    url: `merchant/admin/customerTag/getSetting/${id}`,
    method: "get"
  });
}

/**
 * @description 确认设置标签
 * <AUTHOR>
 * @param {*} data
 */
export const AssociateCustomerSetting = data => {
  return request({
    url: `/merchant/admin/customerTag/confirmSetting`,
    method: "post",
    data,
  });
}

// 恢复已删除指定-排除导入客户
export const specifySetting = data => {
  return request({
    url: '/merchant/admin/customerTag/recover/specify/setting',
    method: "post",
    data
  })
}
