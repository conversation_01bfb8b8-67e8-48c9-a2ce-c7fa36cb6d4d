<template>
  <div class="proxySetings" v-loading="listLoading">
    <div class="top_title flex_between_center">
      <div>
        编辑推广商品
        <img
          v-if="publishStatusEnum.code == 'PENDING'"
          src="@/assets/promoteImg/PENDING.png"
          alt=""
        />
        <img
          v-if="publishStatusEnum.code == 'REJECTED'"
          src="@/assets/promoteImg/REJECTED.png"
          alt=""
        />
        <img
          v-if="publishStatusEnum.code == 'HAVE'"
          REPEAL
          src="@/assets/promoteImg/promotion.png"
          alt=""
        />
        <img
          v-if="publishStatusEnum.code == 'REPEAL'"
          REPEAL
          src="@/assets/promoteImg/Revoked.png"
          alt=""
        />
      </div>
      <div style="padding-right: 20px">
        <el-button v-if="!$route.query.id" @click="$router.go(-1)"
          >取消</el-button
        >
        <el-button v-if="$route.query.id" @click="backPre">返回</el-button>
        <el-button v-if="!$route.query.id" type="primary" @click="subFun"
          >提交</el-button
        >
      </div>
    </div>
    <div class="archivesEditContent">
      <div class="item">
        <div class="title">
          <span>品种信息</span>
        </div>
        <el-button
          v-if="!$route.query.id"
          type="primary"
          @click="addGoodsFlag = true"

          style="margin: 10px 0 20px 0"
          :disabled="list.length != 0"
          >+ 选择商品</el-button
        >
        <!-- @selection-change="selectTableItemFun" -->
        <div class="table">
          <el-table
            ref="table"
            :data="list"
            row-key="id"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column align="center" width="80" label="序号">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }} </span>
              </template>
            </el-table-column>

            <el-table-column
              v-for="(item, index) in tableTitle"
              :key="index"
              :min-width="item.width ? item.width : '350px'"
              :label="item.label"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="{ row }">
                <img
                  v-if="item.name == 'pictIdS'"
                  :src="splitString(row.pictIdS)[0]"
                  style="width: 50px; height: 50px"
                />
                <span
                  v-else-if="item.name == 'retailPrice'"
                  style="color: #ff782b"
                  >{{ row[item.name] }}</span
                >
                <span v-else>{{ row[item.name] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              width="120"
              class="itemAction"
              v-if="!$route.query.id"
            >
              <template slot-scope="{ row }">
                <el-button type="text"  @click="delFun(row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>地区设置</span>
          <i class="textgray">指定地区范围内推广费</i>
        </div>
        <el-button
          :disabled="list.length == 0"
          type="primary"
          @click="newAddrFun"

          style="margin: 10px 0 20px 0"
          >+ 新增地区</el-button
        >

        <el-table :data="addrtableDate" style="width: 100%" border>
          <el-table-column width="50" align="center">
            <template slot="header">
              <span><i class="el-icon-menu" /></span>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="provice" label="省份" min-width="130">
            <template slot-scope="{ row }">
              <span>{{ row.agentPolicyAreas[0].provinceName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="citys" label="城市" min-width="200">
            <template slot-scope="{ row }">
              <span>{{ row.agentPolicyAreas[0].cityNames }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="country" label="区 / 区县" min-width="200">
            <template slot-scope="{ row }">
              <span>{{ row.agentPolicyAreas[0].districtNames }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="promotionExpenses"
            label="推广费"
            min-width="100"
          >
            <template slot-scope="{ row }">
              <span>{{ row.promotionExpenses }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="publishStatus"
            label="是否启用"
            min-width="60"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-input
                v-if="row.isEdit"
                placeholder="请输入联系电话"
                v-model="row.publishStatus"
              ></el-input>
              <span
                :style="row.publishStatus.code == 'N' ? 'color:#ff0066' : ''"
                v-else
                >{{ row.publishStatus.code == "Y" ? "是" : "否" }}</span
              >
            </template>
          </el-table-column> -->

          <el-table-column
            prop="expectSale"
            label="期望月度销售额"
            min-width="100"
            align="left"
          >
            <template slot-scope="{ row }">
              <span>{{ row.expectSale }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="200"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="addAddrItem(scope.row, scope.$index)"
                v-show="!scope.row.isEdit"

                >新增</el-button
              >
              <el-button
                type="text"
                @click="backoutItem(scope.row, scope.$index)"
                v-show="!scope.row.isEdit"

                >撤销</el-button
              >
              <el-button
                type="text"
                v-show="!scope.row.isEdit"
                @click="delAddr(scope.row, scope.$index)"

                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      v-if="addrFlag"
      title="添加地区"
      :visible.sync="addrFlag"
      width="70%"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="addrclose"
    >
      <!-- :render-content="setAddrTree" -->
      <div class="addrtree">
        <el-tree
          class="filter-tree"
          ref="addrTree"
          :data="addrTree"
          show-checkbox
          node-key="id"
          accordion
          :default-expanded-keys="defaultCheck"
          :default-checked-keys="defaultCheck"
          :props="defaultProps"
          :filter-node-method="filterNode"
        >
        </el-tree>
        <div class="proxyPrice">
          <el-form ref="price" :rules="rules" :model="row" label-width="180px">
            <el-form-item
              label="推广费:"
              prop="promotionExpenses"
              style="width: 500px"
            >
              <el-input
                v-model.trim="row.promotionExpenses"
                placeholder="请输入推广费:"
                type="number"
                :disabled="disableSub"
              ></el-input>
            </el-form-item>

            <el-form-item
              label="期望月度销售额:"
              prop="expectSale"
              style="width: 500px"
            >
              <el-input
                v-model.trim="row.expectSale"
                placeholder="请输入期望月度销售额"
                type="number"
                :disabled="disableSub"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="addrclose">取 消</el-button>
        <el-button
          type="primary"
          v-if="addAndRemove == ''"
          @click="addrConfirm"
          :loading="confirmFlag"
          >确 定</el-button
        >
        <el-button
          type="primary"
          v-if="addAndRemove == 'add'"
          @click="addAddrItemFun"
          :loading="confirmFlag"
          >确 定</el-button
        >
        <el-button
          type="primary"
          v-if="addAndRemove == 'remove'"
          @click="backoutItemFun"
          :loading="confirmFlag"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 设置 编辑 -->
    <el-dialog
      :close-on-click-modal="false"
      v-if="addGoodsFlag"
      :title="'选择商品'"
      :visible.sync="addGoodsFlag"
      width="80%"
      :show-close="true"
    >
      <goods-list :showFlag.sync="addGoodsFlag" :row.sync="list[0]">
      </goods-list>
    </el-dialog>
    <!-- 设置 编辑 -->
  </div>
</template>

<script>
const tableTitle = [
  {
    label: "商品主图",
    name: "pictIdS",
    width: "70px",
  },
  {
    label: "商品编码",
    name: "productCode",
    width: "170px",
  },
  {
    label: "商品名称",
    name: "productName",
    width: "120px",
  },
  {
    label: "规格",
    name: "spec",
    width: "120px",
  },
  {
    label: "生产厂家",
    name: "manufacturer",
    width: "170px",
  },
  {
    label: "销售价",
    name: "salePrice",
    width: "120px",
  },
];
import request from "../request";
import requestAxios from "@/utils/request";
import goodsList from "./goodsList";
// import { areas } from "@/api/businessCenter/businessList";
// import {checkAddr} from "@/api/businessCentric/proxySettings"
const checkNumPot2 = function (rule, value, callback) {
  const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
  if (!value) {
    return callback(new Error("请填写数字"));
  } else if (!reg.test(value)) {
    return callback(new Error("请填写数字,最多2位小数"));
  } else {
    callback();
  }
};
export default {
  data() {
    let that = this;
    return {
      dubleClick: false,
      rules: {},
      tableTitle,
      listLoading: false,
      list: [],
      row: {},
      expectSale: "",
      promotionExpenses: "",
      addrtableDate: [],
      query: {},
      addrFlag: false,
      addrTree: {},
      defaultProps: {
        children: "children",
        label: "label",
        disabled: that.setDisabled,
        // disabled(data, node) {
        //   if (that.allCheckIds.indexOf(data.id) !== -1) {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // },
      },
      checkdNodeIds: [],
      defaultCheck: [],
      currentIndex: "",
      currentTree: "", //当前选中的树
      addGoodsFlag: false,
      isEdit: false,
      allCheckIds: [],
      editItem: {},
      publishStatusEnum: {},
      disableSub: false,
      disAbelFlag: false,
      addAndRemove: "",
      grandFather: "",
      rules: {
        expectSale: [
          {
            required: true,
            message: "请输入期望月度销售额",
          },
          {
            validator: checkNumPot2,
          },
        ],
        promotionExpenses: [
          { required: true, message: "请输入推广费" },
          {
            validator: checkNumPot2,
          },
        ],
      },
      confirmFlag: false,
    };
  },
  methods: {
    setDisabled(data, node) {
      if (this.disAbelFlag) {
        if (this.allCheckIds.indexOf(data.id) !== -1) {
          return false;
        } else {
          return true;
        }
      } else {
        if (this.allCheckIds.indexOf(data.id) !== -1) {
          return true;
        } else {
          return false;
        }
      }
    },
    async getcheckAddr() {
      let checkedKeys = this.$refs["addrTree"].getCheckedKeys();
      let checkedKeysTrue = this.$refs["addrTree"].getCheckedKeys(true);
      let halfCheckedKeys = this.$refs["addrTree"].getHalfCheckedKeys();
      if (checkedKeys.length == 0) {
        this.$message.error("请选择代理区域");
        return;
      }
      let { data } = await requestAxios({
        url: "/authority/area/anno/analysisByCheck",
        method: "post",
        data: {
          checkedKeys,
          checkedKeysTrue,
          halfCheckedKeys,
        },
      });
      try {
        return {
          checkedKeys: data.checkedKeys,
          cityIds: data.cityIdS,
          cityNames: data.cityNames,
          districtIds: data.districtIdS,
          districtNames: data.districtNames,
          provinceId: data.provinceId,
          provinceName: data.provinceName,
        };
       } catch (error) {
        return {}
      }
    },
    backPre() {
      if (this.addrtableDate.length == 0) {
        this.$confirm(
          "该推广商品已没有指定地区范围内的推广设置，确认保存编辑吗?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.$router.go(-1);
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      } else {
        this.$router.go(-1);
      }
    },
    filterNode(val, data, node) {
      if (!val) return false;
      return val.toString().indexOf(data.id) !== -1;
    },
    async subFun() {
      // /agentProduct/merchant/insert-agency-product/{productId}
      if (this.list.length == 0) {
        this.$message.error("请选择品种信息");
        return;
      }
      if (this.addrtableDate.length == 0) {
        this.$message.error("请选择代理区域");
        return;
      }
      let { data } = await requestAxios({
        url:
          "/agent/agentProduct/merchant/insert-agency-product/" +
          this.list[0].id,
        method: "post",
        data: this.addrtableDate,
      });
      if (data) {
        this.$message.success("添加推广商品成功");
        this.$router.replace("/promoteCenter/promotionCheck");
      }
    },
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    delFun() {
      this.$confirm("您确定要删除该商品吗?", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.list = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addrChange(node, tree) {
      // if (this.currentTree === "") {
      //   this.currentTree = node.id.slice(0, 2);
      // } else {
      //   if (node.id.slice(0, 2) !== this.currentTree) {
      //     this.currentTree = node.id.slice(0, 2);
      //     this.$refs.addrTree.setCheckedKeys([]);
      //     this.$refs.addrTree.setCheckedKeys([node.id]);
      //   } else {
      //     this.$refs.addrTree.setCurrentKey(node.id);
      //   }
      // }
    },
    async addAddrItemFun() {
      this.$refs.price.validate(async (valid) => {
        if (valid) {
          this.confirmFlag = true;
          let obj = await this.getcheckAddr();
          let currentObj = JSON.parse(JSON.stringify(this.row));
          currentObj.agentPolicyAreas = [obj];
          if (this.row.id) {
            requestAxios({
              url: "/agent/agentProduct/areaPolicy/add",
              method: "post",
              data: currentObj,
            })
              .then((res) => {
                if (res.data) {
                  this.$message.success("新增代理区域成功");
                  this.addrFlag = false;
                  this.$set(this.addrtableDate, this.currentIndex, currentObj);
                  this.addAndRemove = "";
                  this.row = {};
                  this.confirmFlag = false;
                }
              })
              .catch((req) => {})
              .finally(() => {
                this.confirmFlag = false;
              });
          }
          this.confirmFlag = false;
        }
      });
    },
    addAddrItem(row, index) {
      this.disAbelFlag = false;
      this.row = row;
      this.publishStatusEnum.code == "PENDING"
        ? (this.disableSub = false)
        : (this.disableSub = true);
      this.addAndRemove = "add";
      this.currentIndex = index;
      let str = "";
      let idArr = [];
      this.addrtableDate.forEach((item) => {
        idArr = idArr.concat(item.agentPolicyAreas[0].checkedKeys);
      });
      this.allCheckIds = idArr.toString();
      this.addrFlag = true;
      this.defaultCheck = row.agentPolicyAreas[0].checkedKeys;
    },
    curCheck(data, state) {
      const curNode = this.$refs.addrTree.getNode(data);
      if (curNode.level === 1) {
        this.$refs.addrTree.setCheckedKeys([curNode.data.id]);
        this.grandFather = curNode.parent.data.id;
      } else if (curNode.level === 2) {
        if (this.grandFather !== curNode.parent.data.id) {
          /*          this.$refs.addrTree.setCheckedKeys([curNode.data.id])*/
          this.grandFather = curNode.parent.data.id;
        }
      } else {
        if (this.grandFather !== curNode.parent.parent.data.id) {
          /*          this.$refs.addrTree.setCheckedKeys([curNode.data.id])*/
          this.grandFather = curNode.parent.parent.data.id;
        }
      }
    },
    editAddrItem(row, index) {
      let idArr = [];
      this.addrtableDate.forEach((item, ind) => {
        if (ind == index) {
        } else {
          idArr = idArr.concat(item.agentPolicyAreas[0].checkedKeys);
        }
      });
      this.allCheckIds = idArr;
      this.defaultCheck = row.agentPolicyAreas[0].checkedKeys;
      this.addrFlag = true;
      this.currentIndex = index;
      this.row = row;
    },
    backoutItemFun() {
      this.$refs.price.validate(async (valid) => {
        if (valid) {
          this.confirmFlag = true;
          let obj = await this.getcheckAddr();
          let currentObj = JSON.parse(JSON.stringify(this.row));
          currentObj.agentPolicyAreas = [obj];
          try {
            let { data } = await requestAxios({
              url: "/agent/agentProduct/areaPolicy/repeal",
              method: "post",
              data: currentObj,
            });
            if (data) {
              this.$message.success("撤销代理区域成功");
              this.$set(this.addrtableDate, this.currentIndex, currentObj);
              this.addrFlag = false;
              this.row = {};
              this.confirmFlag = false;
              this.addAndRemove = "";
            } 
          } catch (error) {
            this.confirmFlag = false;
          }
          this.confirmFlag = false;
        }
      });
    },
    backoutItem(row, index) {
      this.row = row;
      this.publishStatusEnum.code == "PENDING"
        ? (this.disableSub = false)
        : (this.disableSub = true);
      this.addrFlag = true;
      this.disAbelFlag = true;
      this.addAndRemove = "remove";
      this.currentIndex = index;
      let str = "";
      this.addrTree.forEach((item) => {
        str += item.id + ",";
      });
      this.allCheckIds = str + row.agentPolicyAreas[0].checkedKeys.toString();
      this.defaultCheck = row.agentPolicyAreas[0].checkedKeys;
    },
    async addrConfirm() {
      this.confirmFlag = true;
      this.$refs.price.validate(async (valid) => {
        if (valid) {
          try {
            let checkedKeys = this.$refs["addrTree"].getCheckedKeys();
            let checkedKeysTrue = this.$refs["addrTree"].getCheckedKeys(true);
            let halfCheckedKeys = this.$refs["addrTree"].getHalfCheckedKeys();
            if (checkedKeys.length == 0) {
              this.$message.error("请选择代理区域");
              return;
            }
            let { data } = await requestAxios({
              url: "/authority/area/anno/analysisByCheck",
              method: "post",
              data: {
                checkedKeys,
                checkedKeysTrue,
                halfCheckedKeys,
              },
            });
            this.confirmFlag = false;
            console.log('=========');
            let obj={};
            try {
              obj = {
                checkedKeys: data.checkedKeys,
                cityIds: data.cityIdS,
                cityNames: data.cityNames,
                districtIds: data.districtIdS,
                districtNames: data.districtNames,
                provinceId: data.provinceId,
                provinceName: data.provinceName,
              };
            } catch (error) {
              console.log('error');
            }
            // let obj = {
            //   checkedKeys: data.checkedKeys,
            //   cityIds: data.cityIdS,
            //   cityNames: data.cityNames,
            //   districtIds: data.districtIdS,
            //   districtNames: data.districtNames,
            //   provinceId: data.provinceId,
            //   provinceName: data.provinceName,
            // };
            let currentObj = JSON.parse(JSON.stringify(this.row));
            currentObj.agentPolicyAreas = [obj];
            // if (this.row.id) {
            //   let { data } = await requestAxios({
            //     url: "/agentProduct/areaPolicy/update",
            //     method: "post",
            //     data: currentObj,
            //   });
            //   if (data) {
            //     this.$set(this.addrtableDate, this.currentIndex, currentObj);
            //     this.addrFlag = false;
            //     this.row = {};
            //     this.defaultCheck = [];
            //     this.currentIndex = "";
            //     this.$message.success("修改指定范围内推广费成功");
            //     return;
            //   }
            // }
            // if (this.currentIndex && !this.row.id) {
            //   alert(this.row.id);
            //   this.addrtableDate[this.currentIndex] = this.row;
            // }
            this.row.agentPolicyAreas = [obj];
            if (this.$route.query.id) {
              this.row.agencyProductId = this.$route.query.id;
              let { data } = await requestAxios({
                url: "/agent/agentProduct/areaPolicy/insert",
                method: "post",
                data: this.row,
              });
              this.confirmFlag = false;
              if (data) {
                this.$message.success("新增代理区域成功");
                if (this.publishStatusEnum.code == "HAVE") {
                  this.$router.push("/promoteCenter/promotionCheck");
                } else {
                  this.getitem();
                  this.row = {};
                }
              }
            }
            this.confirmFlag = false;
            this.addrFlag = false;
            this.row = {};
            this.defaultCheck = [];
            this.currentIndex = "";
          } catch (error) {
            this.confirmFlag = false;
            this.addrFlag = false;
            this.row = {};
            this.defaultCheck = [];
            this.currentIndex = "";
          }
        }
      });
    },

    setAddrTree(h, { node, data, store }) {


      if (this.allCheckIds.indexOf(data.id) === -1) {
        return <span>{node.label}</span>;
      } else {
        // node.visible = false;
        return (
          <span style="background-color:#f00;padding-left:18px;margin-left:-40px;z-index:100000;background-color:#EDF2FC">
            <el-checkbox
              disabled
              checked
              style="margin-right:10px"
            ></el-checkbox>
            <span>{node.label}</span>
          </span>
        );
      }
    },
    newAddrFun() {
      this.disableSub = false;
      this.disAbelFlag = false;
      this.addAndRemove = "";
      this.row = {};
      let idArr = [];
      this.addrtableDate.forEach((item) => {
        idArr = idArr.concat(item.agentPolicyAreas[0].checkedKeys);
      });
      this.allCheckIds = idArr.toString();
      this.addrFlag = true;
      this.$nextTick(() => {
        this.$refs.addrTree.setCheckedKeys([]);
      });
    },
    async getitem() {
      this.listLoading = true;
      let { data } = await requestAxios({
        url: "/agent/agentProduct/merchant/query/" + this.$route.query.id,
        method: "post",
      });
      this.list = [data.productVo];
      this.publishStatusEnum = data.publishStatusEnum;
      this.listLoading = false;
      let arr = [];
      if (data.list) {
        data.list.forEach((item) => {
          let obj = {
            id: item.id,
            agentPolicyAreas: [
              {
                checkedKeys: JSON.parse(item.checkedKeys),
                cityIds: item.cityIds,
                cityNames: item.cityNames,
                districtIds: item.districtIds,
                districtNames: item.districtNames,
                provinceId: item.provinceId,
                provinceName: item.provinceName,
              },
            ],
            expectSale: item.expectSale,
            promotionExpenses: item.promotionExpenses,
          };
          arr.push(obj);
        });
      }
      this.addrtableDate = arr;
    },
    async getAddrTree() {
      requestAxios({
        url: "/authority/area/anno/tree",
        method: "get",
      })
        .then((res) => {
          this.addrTree = res.data;
        })
        .catch((req) => {
        });
    },
    addrclose() {
      this.addrFlag = false;
    },
    delAddr(row) {
      this.$confirm("您确定要删除该条地区设置吗?", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          if (row.id) {
            let { data } = await requestAxios({
              url: "/agent/agentProduct/areaPolicy/delete/" + row.id,
              method: "post",
            });
            if (data) {
              this.addrtableDate.forEach((item, index) => {
                if (item.id == row.id) {
                  this.addrtableDate.splice(index, 1);
                  this.$message.success("已删除该条地区设置");
                }
              });
              return;
            }
          }
          this.addrtableDate.forEach((item, index) => {
            if (item.id == row.id) {
              this.addrtableDate.splice(index, 1);
              this.$message.success("已删除该条地区设置");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
  components: {
    goodsList,
  },
  created() {
    this.getAddrTree();
    if (this.$route.query.id) this.getitem();
  },
};
</script>

<style lang="less" scoped>
.proxySetings {
  border-top: 1px solid #ebecee;
  background-color: #fff;
  .top_title {
    height: 56px;
    line-height: 56px;
    font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
    font-size: 18px;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;
    padding-left: 20px;
    .el-button {
      margin-left: 10px;
    }
    img {
      display: inline-block;
      width: 70px;
      position: relative;
      top: 10px;
    }
  }
  .archivesEditContent {
    padding: 0px 20px;
    .item {
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 1px solid #eeeeee;
      .title {
        padding: 0 0 15px;
        span {
          padding: 0 10px;
          border-left: 4px solid rgba(64, 158, 255, 1);
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: 650;
          font-style: normal;
          font-size: 15px;
        }
        i {
          font-style: normal;
          font-size: 14px;
        }
      }
      /deep/.el-form-item--medium .el-form-item__content {
        text-align: left;
        color: #cdced3;
      }
      /deep/ .el-col {
        line-height: unset;
      }
    }
    .textgray {
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-weight: 400;
      font-style: normal;
      font-size: 14px;
      color: #505465;
      text-align: left;
      line-height: 22px;
    }
  }
  .addrtree {
    .filter-tree {
      width: 300px;
      max-height: 50vh;
      overflow-y: scroll;
    }
    display: flex;
  }
  .el-table {
    margin-bottom: 30px;
  }
  /deep/ .el-dialog__footer {
    border-top: 1px solid #ddd;
  }
}
</style>
