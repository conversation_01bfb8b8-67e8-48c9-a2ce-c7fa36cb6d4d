 <!-- 审核弹窗 -->
<template>
    <div :class="[$style.container, 'el-button']">
        <el-button @click="onControl" type="primary">批量驳回</el-button>
        <el-dialog :custom-class="$style.dialog" submitting modal-append-to-body :close-on-click-modal="false"
            :visible.sync="visible" :show-close="!submitting" :close-on-press-escape="!submitting" title="批量驳回" width="500px" @close="handleClose">
            <el-form :disable="submitting" ref="form" :model="model" :rules="rules" label-width="100px" label-suffix="：">
                <el-form-item prop="reason" label="驳回原因">
                    <el-input v-model="model.reason" type="textarea" resize="none" maxlength="30" show-word-limit
                        :rows="5" placeholder="请输入驳回原因"></el-input>
                </el-form-item>
            </el-form>
            <template slot="footer">
                <el-button :disable="submitting" @click="onCancel">取消</el-button>
                <el-button :loading="submitting" @click="onOk" type="primary">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { updateContractStatusToReject } from '@/api/salemanCenter/index'
export default {
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            visible: false,
            submitting: false,
            model: {
                reason: ''
            },
            rules: {
                reason: [
                    { required: true, message: '驳回原因不可为空' }
                ]
            }
        }
    },
    methods: {
        onControl() {
            if (!this.data.length) {
                this.$message.warning('至少选择一条数据');
                return
            }
            this.visible = true;
        },
        onCancel() {
            this.visible = false
        },
        handleClose() {
            this.$refs.form.resetFields()
        },
        async onOk() {
            const valid = await this.$refs.form.validate();
            console.log('valid', valid)
            if (!valid) return
            const params = {
                'ids[]': this.data.map(({ id }) => id).join(','),
                rejectReason: this.model.reason
            }
            this.submitting = true
            try {
                const { code } = await updateContractStatusToReject(params)
                if (code !== 0) throw new Error(msg)
                this.$message.success('驳回成功')
                this.visible = false;
                this.$emit('ok')
            } catch (error) {
                console.log("🚀 ~ file: RejectPopup.vue ~ line 72 ~ onOk ~ error", error)
            } finally {
                this.submitting = false;
            }

        }
    }
}
</script>
<style lang='scss' module>
.container {
    padding: 0;
    border: none;
}

.dialog {
    :global(.el-dialog__body) {
        padding-bottom: 0;
    }
}

.h-300 {
    height: 300px;
}
</style>