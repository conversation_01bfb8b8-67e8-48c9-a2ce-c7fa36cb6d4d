import requestAxios from '@/utils/requestAxios'

export function list(data) {
  return requestAxios({
    method: 'post',
    url: '/api/product/admin/brand/page',
    data
  })
}


export function getApi(id) {
  return requestAxios({
    url: '/api/product/admin/brand/'+id,
    method:"get"
  })
}

export function editApi(data) {
  return requestAxios({
    url: '/api/product/admin/brand',
    method: data.id > 0 ? 'put' : 'post',
    data
  })
}

export function deleteApi(id) {
  return requestAxios({
    url: '/api/product/admin/brand?ids[]=' + id,
    method: 'delete'
  })
}



export function update(data) {
  return requestAxios({
    url: '/vue-element-admin/products/brand/update',
    method: 'post',
    data
  })
}