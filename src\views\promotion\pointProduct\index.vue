<template>
  <div class="point_product">
    <!-- 分组管理 -->
    <div class="group_wrapper">
      <GroupManager @updateGroupId="updateGroupId" @update="" />
    </div>
    <!-- 商品列表 -->
    <div class="product_list">
      <div>
        <tabs-layout ref="tabs-layout" :tabs="tabs">
        </tabs-layout>
        <div class="search" style="display: flex;justify-content: space-between;align-items: flex-start;">
          <im-search-pad style="border: none;padding: 0;" :has-expand="false" :model="query.model" @reset="search" @search="search">
            <im-search-pad-item prop="whetherOnSale">
              <el-select v-model="query.model.whetherOnSale" placeholder="上架状态" clearable>
                <el-option value="Y" label="上架" />
                <el-option value="N" label="下架" />
              </el-select>
            </im-search-pad-item>
            <im-search-pad-item prop="keyword">
              <el-input v-model.trim="query.model.keyword" placeholder="商品名称/商品编码" />
            </im-search-pad-item>
          </im-search-pad>
          <div style="text-align: right;">
            <el-button @click="listingOrDelisting('Y')" :disabled="ids.length === 0">上架</el-button>
            <el-button @click="listingOrDelisting('N')" :disabled="ids.length === 0">下架</el-button>
            <el-button @click="onAdd" type="primary" icon="el-icon-plus">新增商品</el-button>
          </div>
        </div>
        <table-pager
          ref="todoTable"
          selection
          :pageSize="query.size"
          :remote-method="load"
          :options="tableTitle"
          :data.sync="tableData"
          :operationWidth="100"
          @selection-change="tableSelectionChange"
        >
          <el-table-column slot="pictIdS" label="商品图片" align="center" class-name="img-cell">
            <template slot-scope="scope">
              <el-image class="product_img" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS | imageFilterPreview" />
            </template>
          </el-table-column>
          <el-table-column slot="productName" label="商品名称">
            <template slot-scope="{row}">
              <div>{{ row.productName }}</div>
              <div>{{ row.spec }}</div>
              <div>{{ row.manufacturer }}</div>
            </template>
          </el-table-column>
          <el-table-column slot="unit" label="其他信息" width="150">
            <template slot-scope="{row}">
              <div>单位: {{ row.unit }}</div>
              <div>品牌: {{ row.brandName || '无' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="编码/状态" slot="erpCode" width="150">
            <template slot-scope="{row}">
              <div>编码：{{ row.erpCode || '无' }}</div>
              <div>
                <span>状态：</span>
                <span :class="[row.whetherOnSale && row.whetherOnSale.code === 'N' ? 'red' : '']">
                  {{ row.whetherOnSale && row.whetherOnSale.code === 'Y' ? '已上架' : '已下架' }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column slot="productName" label="商品库存" width="150">
            <template slot-scope="{row}">
              <div>可兑换量: {{ parseInt(row.stockQuantity) }}</div>
              <div>已兑换量: {{ parseInt(row.giveawayNum) }}</div>
            </template>
          </el-table-column>
          <div slot-scope="{row}">
            <el-button type="text" @click="onEditGifts(row.id)">编辑</el-button>
            <el-divider direction="vertical" />
            <del-button @handleDel="handleDeleteGifts" text="确认删除该商品吗？" btnText="删除" :targetId="row.id" />
          </div>
        </table-pager>
      </div>
    </div>
  </div>
</template>
<script>
import DelButton from '@/components/eyaolink/delElButton/index.vue'
import GroupManager from './components/groupManager.vue';
import { fetchGiftsList, deleteGitts } from "@/api/gifts"
import { batchUpdatePointProduct } from '@/api/retailStore'

const TableColumns = [{
  label: '商品图片',
  name: 'pictIdS',
  prop: 'pictIdS',
  slot: true
},
{
  label: 'ERP编码/仓库',
  name: 'erpCode',
  prop: 'erpCode',
  width: 110,
  slot: true
},
{
  label: '商品名称',
  name: 'productName',
  prop: 'productName',
  slot: true
  },
  {
  label: '其他信息',
  name: 'unit',
  prop: 'unit',
  slot: true
},
{
  label: '积分值',
  name: 'points',
  prop: 'points',
  width: 120
},
{
  label: '商品库存',
  name: 'stockQuantity',
  prop: 'stockQuantity',
  slot: true,
},
{
  label: '最新操作时间',
  name: 'updateTime',
  prop: 'updateTime',
  width: 120
}].map((v, i) => ({
  key: i,
  ...v
}))

export default {
  name: 'pointProduct',
  components: {
    DelButton,
    GroupManager
  },
  data() {
    return {
      tableData: [],
      ids: [],
      tableTitle: TableColumns,
      tabs: [{
        name: "积分商品",
        value: 0
      }],
      groupList: [],
      currentGroupId: '0',
      query: {
        current: 1,
        size: 10,
        model: {
          keyword: '',
          groupingIds: [],
          whetherOnSale: '',
          giveawayType: 'UN_PRODUCT',
          unProductType: 'POINTS_PRODUCT',
        }
      },
    }
  },
  methods: {
    // 编辑
    onEditGifts(id) {
      this.$router.push({
        path: '/promotion/editPointProduct',
        query: { id }
      })
    },
    // 删除
    async handleDeleteGifts(id) {
      const result = await deleteGitts(id)
      if (result.code === 0) {
        this.$message.success('删除成功！')
        this.search()
      }
    },
    // 新增
    onAdd() {
      this.$router.push({
        path: '/promotion/editPointProduct'
      })
    },
    // 刷新
    search() {
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    // 更新当前分组id
    updateGroupId(id, isUpdate) {
      if (isUpdate) {
        this.search()
        return
      }
      // 同一个id,不重复执行
      if (this.query.model.groupingIds.length > 0 && this.query.model.groupingIds[0] === id) {
        return
      }
      // 重复点击全部, 也不重复执行
      if (!id && this.query.model.groupingIds.length === 0) return
      this.query.model.groupingIds = id ? [id] : []
      this.search()
    },
    tableSelectionChange(val) {
      this.ids = val.map(item => item.id)
    },
    // 批量上下架
    listingOrDelisting(whetherOnSale) {
      if (this.ids.length === 0) {
        this.$message.error('请选择商品')
      }
      const text = whetherOnSale === 'Y' ? '上架' : '下架'
      this.$confirm(`确定要${text}吗`, `${text}商品`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        batchUpdatePointProduct({ ids: this.ids, whetherOnSale }).then(() => {
          this.search()
        })
      }).catch(() => {})
    },
    async load(params) {
      Object.assign(this.query, params)
      let result = await fetchGiftsList(this.query);
      this.totalPage = result.data.pages;
      this.total = result.data.total;
      return result
    },
  }
}

</script>

<style lang="scss" scoped>
.point_product {
  display: flex;
  .group_wrapper {
    min-width: 300px;
    max-width: 300px;
    margin-right: 10px;
  }
  .product_list {
    flex: 1;
    box-sizing: border-box;
    padding: 10px;
    background-color: #fff;
    min-width: 0;
    .red {
      color: red;
    }
    .product_img {
      width: 50px;
      height: 50px;
    }
  }
}
</style>
