<template>
  <el-dialog title="驳回原因" width="600px" v-bind="$attrs" v-on="$listeners" v-dialogDrag :close-on-click-modal="false" @close="clearData">
	  <el-input type="textarea" v-model.trim="rejectReason" :rows="4">
    </el-input>
		<div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">关 闭</el-button>
      <el-button type="primary" @click="handlePrimary">提 交</el-button>
    </div>
	</el-dialog>
</template>

<script>
import { rejectedAgreemen } from '@/api/products/agreemenPrice'
export default {
	name: 'RejectResonDialog',
	props: {
		agreeId: {
			type: String,
			default: () => {
				return ''
			}
		}
	},
	data() {
		return {
			rejectReason: ''
		}
	},
	methods: {
		clearData() {
			this.rejectReason = ''
		},
		handlePrimary(){
			if(!this.rejectReason) {
        this.$message.error('驳回理由不能为空');
				return false
			}
			rejectedAgreemen({ id:this.agreeId, rejectReason: this.rejectReason }).then(res => {
				if(res.code === 0) {
          this.$message.success('操作成功!')
					this.$emit('onsuccess')
					this.$emit('update:visible', false)
				}
			})
		}
	}
}
</script>