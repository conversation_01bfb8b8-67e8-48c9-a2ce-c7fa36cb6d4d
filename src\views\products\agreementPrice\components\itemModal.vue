<template>
<el-dialog title="协议商品"
          width="62%"
          :visible.sync="showModal">
  <v-item-table :list="list" />
</el-dialog>
</template>

<script>
import itemTable from './itemTable'

export default {
  data () {
    return {
      showModal: false,
      list: []
    }
  },
  components: {
    'v-item-table': itemTable
  },
  methods: {
    show (list) {
      this.showModal = true
      this.list = list
    }
  }
}
</script>
