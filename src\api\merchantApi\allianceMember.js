import request from '@/utils/request'
var qs = require('qs')


// 批量新增联盟会员
export function batchAdd(data) {
    return request({
      url: `/merchant/admin/memberManagement`,
      method: 'post',
      data
    })
}

// 修改联盟会员的有效期
export function editMemberManagement(data) {
    return request({
        url:'/merchant/admin/memberManagement',
        method:'put',
        data,
    })
}

// 删除联盟会员
export function deleteMemberManagement(id) {
    return request({
        url:`/merchant/admin/memberManagement?ids[]=${id}`,
        method:'delete',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

// 联盟会员列表
export function memberPage(data) {
    return request({
        url:'/merchant/admin/memberManagement/page',
        method:'post',
        data
    })
}

// 联盟会员查询
export function memberDetail(id) {
    return request({
        url: `/merchant/admin/memberManagement/${id}`,
        method: 'get',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

// 客户弹窗
export function purMerchantPage(data){
    return request({
        url:'/merchant/admin/purMerchant/page',
        method:'post',
        data
    })
}

// 获取会员管理状态数量
export function getMemberManagementCount(){
    return request({
        url:'/merchant/admin/memberManagement/getMemberManagementCount',
        method:'get',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

// 通过销售商id进行查询
export function getManagementRelBySaleMerchantId(){
    return request({
        url: '/merchant/admin/memberManagementRel/getManagementRelBySaleMerchantId',
        method: 'get'
    })
}

// 新增或修改会员价系数
export function saveOrUpdate(data){
    return request({
        url: '/merchant/admin/memberManagementRel/saveOrUpdate',
        method: 'post',
        data
    })
}