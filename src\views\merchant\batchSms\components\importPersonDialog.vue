<template>
  <el-dialog title="批量导入" :visible.sync="visible" width="500px">
    <div class="top">
      <div style="display: flex;justify-content: center;">
        <el-upload drag action="#" :file-list="fileList" ref="upload" :multiple="false" :http-request="requestUpload" :before-upload="beforeUpload">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </el-upload>
      </div>
    </div>
    <div class="center" style="line-height: 2;">
      <div>1、上传的客户号码与系统中客户号码重复时则更新重复号码的客户信息</div>
      <div>2、请保持导入Excel的表头与模板一致，避免导入失败</div>
      <div>
        <span>3、仅支持xlsx格式文件，文件大小10M以内且数据20000条以内，若超过限制，请分批导入</span>
        <el-button type="text" class="download" @click="downloadFile">下载模板</el-button>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
    </span>
    <ImportTipDialog ref="importTipDialog" />
  </el-dialog>
</template>

<script>
import { importSmsTask } from "@/api/retailStore";
import ImportTipDialog from "@/components/importTipDialog.vue";

export default {
  name: 'batchImportRosterDialog',
  components: {
    ImportTipDialog
  },
  props: {
    taskId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      fileList: []
    }
  },
  methods: {
    show() {
      this.visible = true
      this.fileList = []
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    hide() {
      this.visible = false
    },
    requestUpload(file) {},
    beforeUpload(file) {
      this.fileList = [file]
    },
    submit() {
      if (this.fileList.length === 0) {
        this.$message.error("请上传文件")
        return
      }
      const formData = new FormData()
      formData.append('file', this.fileList[0])
      formData.append('taskId', this.taskId)
      this.loading = true
      importSmsTask(formData).then(_ => {
        this.$message.success("添加成功")
        this.$refs.importTipDialog.show()
        setTimeout(() => {
          this.hide()
        }, 1000)
      }).finally(() => {
        this.loading = false
      })
    },
    downloadFile() {
      let a = document.createElement("a");
      a.download = '批量发送短信导入模板.xlsx';
      a.href = `${process.env.BASE_URL}批量发送短信导入模板.xlsx?response-content-type=application/octet-stream`;
      a.click();
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  position: relative;
  .download {
    position: absolute;
    right: 4px;
    top: 0px;
  }
}
.center {
  border-top: 1px solid #ddd;
  padding-top: 10px;
  margin-top: 10px;
}
</style>