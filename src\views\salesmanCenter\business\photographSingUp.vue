<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="during">
        <el-date-picker type="daterange" range-separator="至" v-model="model.during" value-format="yyyy-MM-dd"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
      <im-search-pad-item prop="personName">
        <el-input v-model.trim="model.personName" @keyup.enter.native="searchLoad" placeholder="请输入打卡人姓名" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="model.purMerchantName" @keyup.enter.native="searchLoad" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="exceptionReason">
        <el-select v-model="model.exceptionReason" placeholder="是否超范围打卡">
          <el-option label="正常范围" value="0"></el-option>
          <el-option label="超范围打卡" value="1"></el-option>
          <el-option label="未打卡" value="2"></el-option>
          <el-option label="迟到打卡" value="3"></el-option>
          <el-option label="早退" value="4"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <CheckInSettingsDialog ref="checkInSettingsDialogRef" />
    <!--  -->
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{name:'拍照签到'}]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <paged-export controllable :max-usable="maxUsableExport" text-type="primary" :max="10000" text="导出" :before-close="handleExport" />
          <el-button @click="showCheckInSettingsDialog">签到设置</el-button>
          <el-button @click="reload">刷新</el-button>
        </template>
      </tabs-layout>

      <!-- 分页tab -->
      <table-pager ref="pager-table" :options="tableColumns" :loadOnCreated="false" :remote-method="load" :data.sync="tableData"
        :selection="false" :pageSize="pageSize" :operation-width="150" :isNeedButton="true">

        <el-table-column label="打卡类型" width="120" slot="siginType">
          <slot slot-scope="{ row }">
            <div>{{ row.siginType == 1 ? '签到' : '签退' }}</div>
          </slot>
        </el-table-column>

        <el-table-column label="打卡偏差" width="120" slot="offset">
          <slot slot-scope="{ row }">
            <div>{{ row.offset }} 米</div>
          </slot>
        </el-table-column>
        <el-table-column label="类别" width="120" slot="category">
          <slot slot-scope="{ row }">
            <div>{{ row.category && row.category === 1 ?'拓客跟进':'采购商拜访'}} </div>
          </slot>
        </el-table-column>


        <el-table-column label="图片说明" width="130" slot="imageIds">
          <slot slot-scope="{row}">
            <div class="img-cell">
              <div v-if="row.imageIds.split(',').length < 2">
                <img style="width:50px;height:50px;margin-right:5px" v-for="(i,indexs) in row.imageIds.split(',')"
                  :key="indexs" :src="i" alt="">
              </div>
              <div class="img_box" v-else>
                <img style="width:50px;height:50px;margin-right:5px" :src="row.imageIds.split(',')[0]" alt="">
                <div class="img-posi">
                  <img style="width:50px;height:50px;margin-right:5px" :src="row.imageIds.split(',')[1]" alt="">
                  <div class="img_mask"> +{{ row.imageIds.split(',').length - 1 }}</div>
                </div>
              </div>
            </div>
          </slot>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <div class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-business-sign:photographPreView', 'sale-platform-business-sign:photographPreView'])" @click="handleBigImage(row)">预览图片</el-button>
              <el-button type="text" v-loading='row.markLoading' v-if="row.siginType == 2 && row.coachedType == 1" @click="handleMark(row)">查看评价</el-button>
              <el-image :ref="`ref${row.id}`" style="width:0;height:0" :src="previewDetail[0]"
                :preview-src-list="previewDetail"></el-image>
            </div>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog title="查看评价" v-dialogDrag append-to-body v-if="dialogStatus" width="60%" :visible.sync="dialogStatus">
      <el-table
        :data="markTableData"
        border
        class="mark-table"
        style="width: 100%"
        :row-class-name="({row,rowIndex})=>rowIndex == 6 ? 'sum-row' : ''"
        >
        <el-table-column
          prop="title"
          label="维度"
          align='center'
          width="250">
        </el-table-column>
        <el-table-column
          prop="value"
          align='center'
          label="评分">
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>


<script>
  const TableColumns = [{
      label: "打卡人编码",
      name: "personCode",
      prop: "personCode",
      width: "150"
    },
    {
      label: "打卡人姓名",
      name: "personName",
      prop: "personName",
      width: "110"
    },
    {
      label: "协访对象",
      name: "coachedPersonName",
      prop: "coachedPersonName",
      width: "110"
    },
    {
      label: "打卡类型",
      name: "siginType",
      prop: 'siginType',
      width: "130",
      slot: true
    },
    {
      label: "客户名称",
      name: "purMerchantName",
      prop: 'purMerchantName',
      width: "170"
    },
    {
      label: "类别",
      name: "category",
      prop: 'category',
      width: "120",
      slot: true
    },
    {
      label: "目的地",
      name: "destination",
      prop: 'destination',
      width: "150"
    },
    {
      label: "打卡时间",
      name: "updateTime",
      prop: 'updateTime',
      width: "160"
    },
    {
      label: "打卡位置",
      name: "address",
      prop: 'address',
      width: "180"
    },
    {
      label: "打卡偏差",
      name: "offset",
      prop: 'offset',
      width: "140",
      slot: true
    },
    {
      label: "补充说明",
      name: "remarks",
      prop: 'remarks',
      width: "140",
    },
    {
      label: "图片说明",
      name: "imageIds",
      prop: 'imageIds',
      width: "200",
      slot: true
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import { punchSiginlist, exportPunchRecord, markById } from '@/api/salemanCenter/index' // 替换成对应用的列表api
  import checkPermission from '@/utils/permission';
  import PagedExport from "@/components/PagedExport/index.vue"
  import { MessageConfirmExport } from '@/utils';
  import CheckInSettingsDialog from './components/checkInSettingsDialog.vue';
  export default {
    name: 'photographSingUp',
    //import引入的组件
    components: {
      PagedExport,
      CheckInSettingsDialog
    },
    data() {
      return {
        maxUsableExport: 0,
        isExpand: false,
        model: {
          personName: '',
          purMerchantName: '',
          exceptionReason: '',
          during: []
        },
        tableData: [],
        previewDetail: [],
        tableColumns: TableColumnList,
        pageSize: 10,
        dialogStatus: false,
        markTableData: [
          {
            label: 'professionalLevel',
            title: '专业水平',
            value: '',
          },
          {
            label: 'serviceAttitude',
            title: '服务态度',
            value: '',
          },
          {
            label: 'productPromotion',
            title: '产品宣传',
            value: '',
          },
          {
            label: 'eventCommunication',
            title: '活动传达',
            value: '',
          },
          {
            label: 'customerSatisfaction',
            title: '客户满意度',
            value: '',
          },
          {
            label: 'evaluate',
            title: '详细评价',
            value: '',
          },
          {
            label: 'sum',
            title: '综合评分',
            value: '',
          },
        ],
      };
    },
    //方法集合
    methods: {
      checkPermission,
      showCheckInSettingsDialog() {
        this.$refs.checkInSettingsDialogRef.show()
      },
      handleExport(query) {
        const loading = this.$loading({
          lock: true,
          text: '正在导出中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.4)'
        });
        exportPunchRecord({
          ...query,
          ...this.getListQuery().model
        }).then(res => {
          if (res.code !== 0) return;
          MessageConfirmExport()
        }).finally(() => {
          loading.close();
        })
      },
      getListQuery() {
        let durationFormat = {}
        const during = this.model.during
        if (during.length == 2 && during[0] && during[1]) {
          durationFormat = {
            startTime: during[0] + ' 00:00:00',
            endTime: during[1] + ' 23:59:59'
          }
        }
        let listQuery = {
          model: {
            ...this.model,
            ...durationFormat
          }
        }
        return listQuery;
      },
      async load(params) {
        let listQuery = this.getListQuery();
        Object.assign(listQuery, params);
        const result = await punchSiginlist(listQuery);
        this.maxUsableExport = result.data.total
        return result;
      },
      // 预览图片
      async handleBigImage(row) {
        this.previewDetail = [];
        if (row.imageIds != null && row.imageIds != undefined && row.imageIds != 'null') {
          if (row.imageIds.split(',').length == 0) {
            this.$message.warning('无图片可查看');
            return
          }
          row.imageIds.split(',').forEach(item => {
            this.previewDetail.push(item);
          });
          this.$refs[`ref${row.id}`].showViewer = true;
        }
        console.log('this.previewDetail', this.previewDetail);
      },
      // 查看评分
      handleMark(row){
        let temp = { id: row.id};
        this.dialogStatus = false;
        this.markLoading = true;
        markById(temp).then(res=>{
          if(res.code == 0){
            this.markLoading = false;
            let sum = 0;
            this.markTableData.forEach((item)=>{
              item.value = res?.data?.[item.label] ?? 0;
              if(!['evaluate','sum'].includes(item.label)){
                item.value = Number(item.value)/2 ;
                sum += Number(item.value);
              }
              // console.log(item,sum);
            })
            this.markTableData[6].value = sum / 5;
            this.dialogStatus = true;
          }
        }).catch(()=>{
          this.markLoading = false;
        });

        
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
    },
    mounted() {
      const routeQuery = this.$route.query
      if (Object.keys(routeQuery).length > 0) {
        const beginTime = routeQuery.beginTime || ''
        const endTime = routeQuery.endTime || ''
        const personName = routeQuery.name || ''
        this.model.during = [beginTime, endTime]
        this.model.personName = personName
      }
      this.searchLoad()
    }
  };

</script>


<style lang='scss' scoped>
  .img-cell {
    display: flex;
    align-items: center;
    .img_box {
      display: flex;
      align-items: center;
      position: relative;
      .img-posi {
        width: 50px;
        height: 50px;
      }
      .img_mask {
        position: absolute;
        width: 50px;
        height: 50px;
        top: 0;
        left: 55px;
        line-height: 50px;
        text-align: center;
        background-color: rgba($color: #000000, $alpha: 0.6);
        color: #fff;
      }
    }
  }
  ::v-deep .mark-table .sum-row {
     background: #f5f7fa !important;
  }
  .table-edit-row-item{
    .el-button + .el-button{
      margin-left: 4px;
      padding-left: 5px;
      border-left: solid 1px #dbdde0;
    }
  }
</style>
