<template>
  <div class="systemPageContent">
    <tabs-layout :tabs="[ { name: '导入管理' } ]">
      <template slot="button">
        <!-- <el-button type="primary"  icon="el-icon-plus" @click="showImportFun">新建导入</el-button> -->
        <el-button  icon="el-icon-refresh" @click="onSearchSubmitFun">刷新</el-button>
      </template>
    </tabs-layout>
    <div class="table" style="margin-top: 0">
      <el-table
        ref="tableDom"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
      >
        <el-table-column align="center" width="60" show-overflow-tooltip :render-header="renderHeader">
          <template slot-scope="scope">
            <span>{{ scope.$index+1 }}</span>
          </template>
        </el-table-column>
        <el-table-column :min-width="'180px'" prop="createTime" label="导入时间" show-overflow-tooltip />
        <el-table-column :min-width="'180px'" prop="productType" label="导入类型" show-overflow-tooltip>
          <template slot-scope="{row}">
            <div>{{ row.productType && row.productType.desc }}</div>
          </template>
        </el-table-column>
        <el-table-column width="180px" show-overflow-tooltip label="导入状态" column-key="uploadStatus">
          <template slot-scope="{ row }">
            <el-tag v-if="row['uploadStatus'].code == 'SUCCESS'" type="success">成功</el-tag>
            <el-tag v-if="row['uploadStatus'].code == 'FAILURE'" type="danger">失败</el-tag>
            <el-tag v-if="row['uploadStatus'].code == 'PROCESSING'">处理中</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" width="250" show-overflow-tooltip label="操作人" />
        <el-table-column fixed="right" align="center" label="操作" width="200">
          <template slot-scope="scope">
            <el-row class="table-edit-row">
              <span v-if="scope.row['uploadStatus'].code != 'PROCESSING'" class="table-edit-row-item">
                <el-button type="text"  @click="downloadFun(scope.row)">下载失败结果</el-button>
              </span>
              <span v-if="scope.row['uploadStatus'].code != 'PROCESSING'" class="table-edit-row-item">
                <el-button type="text"  @click="deleteFun(scope.row)">删除</el-button>
              </span>
            </el-row>
            <!-- <el-link :underline="false" type="primary" href="https://element.eleme.io" target="_blank">下载失败结果</el-link> -->
<!--            <el-link v-if="scope.row['uploadStatus'].code != 'PROCESSING'" :underline="false" type="primary" @click="downloadFun(scope.row)">下载失败结果</el-link>-->
<!--            <el-link v-if="scope.row['uploadStatus'].code != 'PROCESSING'" style="margin-left:15px;" :underline="false" type="primary" target="_blank">删除</el-link>-->
            <!-- <el-button style="margin-left:15px;"  @click.native="deleteFun(scope.row)" type="text">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          @pagination="getList"
        />
      </div>
    </div>

    <el-dialog
      title="新建导入"
      :visible.sync="showImport"
      width="428px"
      :append-to-body="true"
      :before-close="closeDialogFun"
    >
      <div class="uploadContent">
        <el-radio-group v-model="radioType" style="margin-bottom:10px" @change="handleUploadType">
          <el-radio :label="'1'">商品导入</el-radio>
          <el-radio :label="'2'">图片导入</el-radio>
          <el-radio :label="'3'">价格导入</el-radio>
        </el-radio-group>
        <el-upload
          ref="upload"
          class="avatar-uploader"
          drag
          :action="actionUploadUrl"
          :headers="headersProgram"
          :on-change="beforeUpload"
          :on-remove="handleRemove"
          :file-list="fileList"
          :on-success="uploadSuccess"
          :on-progress="uploadProgram"
          :auto-upload="false"
          :multiple="false"
        >
          <!-- :limit="1" -->
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖拽至此区域或选择文件上传</div>
          <div v-if="radioType != '2'" slot="tip" class="el-upload__tip">当前仅支持xlsx格式文件，文件大小1M以内且在5000条 数据以内，若超出限制，请分批导入
            <el-link type="primary" :href="tempUrl" target="_blank">下载模板</el-link>
          </div>
          <div v-else slot="tip" class="el-upload__tip">当前商品图片上传仅支持zip格式的压缩包，文件大小限制在150M以内</div>
        </el-upload>
        <el-progress :stroke-width="2" v-if="showProcess" :percentage="processLen"></el-progress>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showImport = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import checkPermission from '@/utils/permission'
import {getParameterByKey} from '@/api/settingCenter'
import { importProductList, importProductDelete, exportProductLog } from '@/api/product'
export default {
  name: 'importManagement',
  data() {
    return {
      radioType:'1',
      actionUploadUrl:this.$excelImportUrl,
      headersProgram: {
        token: `Bearer ${getToken()}`,
        Authorization: 'Basic c2FsZV91aTpzYWxlX3VpX3NlY3JldA=='
      },
      // insertProgram: {
      //   folderId: 0
      // },
      fileList: [],
      showEdit: false,
      showImport: false,
      showItemTable: false,
      row: {},
      submitReload: false,
      showProcess: false, // 上传进度条
      isSubmit:false,
      processLen: 0,
      unitTime: 0, // 上传进度条每次加1的单位时间
      list: [],
      total: 0,
      totalPage:0,
      listLoading: true,
      tempUrl:"https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/04/dea696ef-a598-4d7e-9dd3-ba080bd8dae0.xlsx",
      listQuery: {
        size: 10,
        model: {
          'productType': '',
          'uploadStatus': ''
        },
        current: 1
      }
    }
  },
  watch: {
    submitReload: function(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false
        this.getList()
      }
    }
  },
  mounted() {
    this.getList()
    this.getParameterByKeyFun()
  },
  methods: {
    checkPermission,
    async getParameterByKeyFun(){
      let key = "";
      if(this.radioType == '1') {
        key = 'MERCHANT_IMPORT_PRODUCT_EXCEL_TEMP';
      } else if(this.radioType == '3') {
        key = "MERCHANT_IMPORT_PRODUCT_EXCEL"
      }
     let {code,data} = await getParameterByKey({
       key
     });
     if(code==0){
       this.tempUrl=data.value
     }
    },
    closeDialogFun() {
      this.showImport = false;
      this.fileList = [];
    },
    renderHeader(h, { column }) {
      return (
        <div style='position:relative'>
          <div>
            <i class='el-icon-menu' />
          </div>
        </div>
      )
    },
    onSearchSubmitFun() {
      this.listQuery.size = 10
      this.getList()
    },
    handleCurrentChange(val){
      this.listQuery.current = val;
      this.getList()
    },
    handleSizeChange(val){
      this.listQuery.size = val;
      this.getList()
    },
    async getList() {
      this.listLoading = true
      const { data } = await importProductList(this.listQuery)
      this.list = data.records
      this.total = data.total
      this.listLoading = false
    },
    showImportFun() {
      this.showImport = true;
      this.radioType = '1';
      this.fileList = [];
      this.isSubmit = false;
      this.processLen = 0;
      this.showProcess = false;
    },
    editFun(row) {
      this.row = row
      this.showEdit = true
    },
    initQuery() {
      this.listQuery = {
        size: 10,
        model: {
          'productType': '',
          'uploadStatus': ''
        },
        current: 1
      }
    },
    async actionDeleteFun(row) {
      const data = await importProductDelete(row.id)
      if (data.code == 0) {
        this.initQuery()
        this.getList()
      }
    },
    deleteFun(row) {
      var _this = this
      this.$confirm('此操作将永久删除该信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.actionDeleteFun(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 修改导入的类型
    handleUploadType(e){
      if(e=='1'){
        // 商品导入
        this.actionUploadUrl = this.$excelImportUrl;
        this.getParameterByKeyFun();
      } else if(e == '2') {
        // 图片导入
        this.actionUploadUrl = this.$productImageImportUrl;
      } else if(e == '3') {
        // 价格导入
        this.actionUploadUrl = this.$priceImportUrl;
        this.getParameterByKeyFun();
      }
      this.fileList = []
    },
    beforeUpload(file) {
      console.log('file',file);
      this.unitTime = parseInt(file.size/1024/1024/30) * 600;
      let isZIP = true;
      if(this.radioType == '2'){
        let list = ['application/zip','application/x-compressed','application/x-zip-compressed','multipart/x-zip'];
        if(list.includes(file.raw.type)) {
          isZIP = true;
        } else {
          isZIP = false;
        }
      }
      
      var uploadFiles = this.$refs.upload.uploadFiles
      if (uploadFiles.length > 1) {
        uploadFiles.splice(0, 1)
      }
      if(!isZIP){
        this.$message.error('只能上传ZIP格式的压缩包');
        uploadFiles = [];
        this.fileList = [];
      }
      console.log('isZip',isZIP);
      return isZIP
      // return isLt1M
    },
    uploadProgram(event, file, fileList) {

    },
    processFnShow(){
      let interval;
      if(this.isSubmit) {
        this.showProcess = true;
        interval = setInterval(()=>{
          if(this.processLen >= 99){
            clearInterval(interval);
            return
          };
          this.processLen +=1;
        },this.unitTime);
      } else{
        this.processLen = 100;
        clearInterval(interval);
      }
    },
    handleRemove(file, fileList){
      console.log('文件列表移除文件时的钩子', file, fileList);
      if(fileList.length == 0){
        this.isSubmit = false;
        this.processLen = 0;
        this.showProcess = false;
      }
    },
    uploadSuccess(res, file) {
      if (res.code == 0) {
        this.fileList = []
        this.showImport = false
        this.initQuery()
        this.getList()
      } else {
        this.$message.error('上传失败!')
      }
      this.isSubmit = false;
      this.processFnShow();
      this.showProcess = false;
    },
    submitUpload() {
      if(this.$refs.upload.uploadFiles.length == 0){
        this.$message.warning('请先上传文件');
        return;
      }
      this.isSubmit = true;
      this.processFnShow();
      this.$refs.upload.submit();
    },
    downloadFun: async function(row) {
      const data = await exportProductLog({
        'current': 1,
        'map': {},
        'model': {
          'productUploadId': row.id
        },
        'order': 'descending',
        'size': 10,
        'sort': 'id'
      })
      const blob = new Blob([data.data], { // 取响应回来的数据
        // type: "application/vnd.ms-excel;",  xls
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'
      })
      const href = window.URL.createObjectURL(blob) // 创建下载的链接
      const downloadElement = document.createElement('a')
      downloadElement.href = href
      downloadElement.download = decodeURI(data.headers['filename'])
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.table {
  margin-top: 16px;
  .pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #505465;
    font-size: 14px;
    margin-top: 20px;
  }
}
.temp_searchBox{height: 64px;overflow: hidden; margin-bottom: 0; }
.form-inline{height:60px; overflow:hidden;}
.systemPageContent {
  padding: 8px 20px 20px;
  background: #fff;
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .el-dropdown-link {
    margin-left: 12px;
    cursor: pointer;
    font-size: 12px;
    color: #0056e5;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
}

</style>

<style lang="less" scoped>
/deep/ .avatar-uploader .el-upload{width: 100%; height: auto;}
/deep/ .uploadContent .avatar-uploader .el-upload-dragger{
    width:100%;
    height:180px;
}
/deep/ .uploadContent .avatar-uploader .el-upload__tip{
  width: 100%;
  margin-top: 10px;
  line-height: 20px;
}

/deep/ .uploadContent .avatar-uploader .el-upload-list .el-progress {
  display: none;
}

</style>
