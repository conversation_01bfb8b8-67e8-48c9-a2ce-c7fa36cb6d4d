<template>
  <div class="archivesPageContent">
    <div class="temp_searchBox">
      <el-form
        :inline="true"
        ref="searchForm"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="listQuery.model.shopname"
            placeholder="请输入店铺名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="factory">
          <el-input
            v-model="listQuery.model.name"
            placeholder="请输入商家名称"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-form-item label="">
            <el-select
              v-model="listQuery.model.publishStatus"
              placeholder="请选择商家状态"
            >
              <!-- <el-option label="全部" value=""></el-option> -->
              <el-option label="已启用" value="Y"></el-option>
              <el-option label="未启用" value="N"></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" @click="onSearchSubmitFun">搜索</el-button>
          <el-button @click="resetForm('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="title flex_between_center">
      <div>
        <el-tabs v-model="tabType" class="typeTabs">
          <el-tab-pane label="推广费用明细" name="list"></el-tab-pane>
        </el-tabs>
      </div>
      <div></div>
    </div>

    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        @selection-change="selectTableItemFun"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="80"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex_between_center">
        <div></div>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/promote/promotionExpenses/tableInfo";
export default {
  data() {
    return {
      listLoading: false,
      list: [1, 2, 3],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      // props: {
      //   lazy: true,
      //   async lazyLoad(node, resolve) {
      //     const { level } = node;
      //     let id = node.data ? node.data.id : "";
      //     let res = await areas({ parentId: id });
      //     let list = res.data;
      //     list.forEach((item) => {
      //       item.value = item.id;
      //       item.leaf = level >= 2;
      //     });
      //     resolve(list);
      //   },
      // },
    };
  },
  methods: {
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {},
    getlist() {},

    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
  },
  components: {
    Pagination,
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  background-color: #fff;
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-top: 16px solid #f2f3f4;
    border-bottom: 2px solid #ebecee;
    margin-bottom: 35px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
    padding: 0 12px;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
