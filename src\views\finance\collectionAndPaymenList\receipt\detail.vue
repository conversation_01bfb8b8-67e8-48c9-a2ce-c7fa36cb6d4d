<template>
  <div class="detail-wrapper" v-loading="loading">
    <div class="detail-title flex_between_center" style="margin-bottom: 30px">
      收款单详情
      <div>
        <el-button
          v-if="notReceipt"
          type="primary"
          :loading="acceptedLoading"
          @click="confirmReceipt"
          >确认收款</el-button
        >
        <el-button @click="backlist">返回列表</el-button>
      </div>
    </div>
    <div class="detail-items">
      <div class="item">
        <div class="title"><span>收款单信息</span></div>
        <div class="ntable">
          <div class="nitem">
            <div>收款单类型</div>
            <div>{{ detail.type && detail.type.desc }}</div>
            <div>收款单状态</div>
            <div>{{ detail.paymentStatus && detail.paymentStatus.desc }}</div>
          </div>
          <div class="nitem">
            <div>收款单单号</div>
            <div>{{ detail.id }}</div>
            <div>关联业务单号</div>
            <div>{{ detail.businessNo }}</div>
          </div>
          <div class="nitem">
            <div>付款方类型</div>
            <div>{{ detail.payerType && detail.payerType.desc }}</div>
            <div>付款方</div>
            <div>{{ detail.payerName }}</div>
          </div>

          <div class="nitem">
            <div>收款金额（元）</div>
            <div>{{ detail.paymentAmount }}</div>
            <div>支付方式</div>
            <div>
              <span>{{ flowMethod }}</span>
              <el-checkbox v-if="notReceipt" style="margin: 0 15px;" v-model="changeMethod">变更方式</el-checkbox>
              <el-select v-if="changeMethod" v-model="item.method" placeholder="请选择" size="small" style="width: 120px;">
                <el-option v-for="item in methodList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>

          <div class="nitem">
            <div>制单人</div>
            <div>{{ detail.createUserName }}</div>
            <div>制单时间</div>
            <div>{{ detail.createTime }}</div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="detail-items"
      v-if="
       detail.flowOnline && detail.flowOnline.code == 'ON' && detail.paymentStatus && detail.paymentStatus.code != 'WAIT'
      "
    >
      <div class="item">
        <div class="title"><span>收款回单 （银行回调信息）</span></div>
        <div class="ntable">
          <div class="nitem">
            <div>业务类型</div>
            <div>{{ item.flowRemarks }}</div>
            <div>支付日期</div>
            <div>{{item.createTime && item.createTime.substr(0,10) }}</div>
          </div>
          <div class="nitem">
            <div>付款方账户号</div>
            <div>{{ item.payAccount }}</div>
            <div>付款方户名</div>
            <div>{{ item.payAccountName }}</div>
          </div>
          <div class="nitem">
            <div>金额（小写）</div>
            <div>{{ item.smallAmount }}</div>
            <div>金额（大写）</div>
            <div>{{ item.bigAmount }}</div>
          </div>
          <div class="nitem">
            <div>收款方账户号</div>
            <div>{{ item.collectAccount }}</div>
            <div>收款方开户行</div>
            <div>{{ item.collectAccountBank }}</div>
          </div>
          <div class="nitem">
            <div>收款方户名</div>
            <div>{{ item.collectAccountName }}</div>
            <div>交易单号</div>
            <div>{{ detail.flowPayOrderNo }}</div>
          </div>
          <div class="nitem">
            <div>流水号</div>
            <div>{{ item.serialNumber }}</div>
            <div>回单编号</div>
            <div>{{ item.receiptNumber }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="detail-items" v-else>
      <div class="item">
        <div class="title"><span>收款回单 （人工审核）</span></div>
        <div class="ntable">
          <div class="nitem">
            <div>付款日期</div>
            <div>{{ item.paymentTime? item.paymentTime.substr(0,10) : '' }}</div>
            <div>流水号</div>
            <div>{{ item.serialNumber }}</div>
          </div>
          <div class="nitem">
            <div>付款账号</div>
            <div>{{ item.payAccount }}</div>
            <div>付款名称</div>
            <div>{{ item.payAccountName }}</div>
          </div>

          <div class="nitem">
            <div>付款凭证</div>
            <div>
              <el-image
                v-if="item.certificatePath"
                style="height: 100px;width:100px"
                :src="item.certificatePath"
                :preview-src-list="[item.certificatePath]"
                fit="contain"
              >
              </el-image
              >
              <div v-if="item.certificatePath" style="color:#ccc;padding-top:20px">点击图片预览</div>
            </div>
            <div></div>
            <div></div>
          </div>

          <div class="nitem">
            <div>审核人</div>
            <div>{{ detail.reviewUserName }}</div>
            <div></div>
            <div></div>
          </div>

          <div class="nitem">
            <div>审核备注</div>
            <div>
              <el-input
                v-if="detail.paymentStatus && detail.paymentStatus.code == 'WAIT'"
                type="textarea"
                :rows="4"
                placeholder="请输入内容"
                v-model="remarks"
                style="display: block; padding: 0; width: 100%"
              >
              </el-input>
              <span v-else>{{detail.remarks}}</span>
            </div>
            <div></div>
            <div></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  detail,
  confirmReceipt,
} from "@/api/finance/collectionAndPaymenList/receipt";
import checkPermission from '@/utils/permission';
export default {
  name: 'receiptDetail',
  data() {
    return {
      loading: false,
      acceptedLoading: false,
      remarks: "",
      changeMethod: false,
      detail: "",
      item: "",
      methodList: [
        { value: 'BANK', label: '银行汇款' },
        { value: 'BANK_ACCEPTANCE', label: '银行承兑' },
      ]
    };
  },
  computed: {
    flowMethod() {
      return this.detail.method?.desc ? this.detail.method.desc : ''
    },
    notReceipt() {
      return this.detail.paymentStatus && this.detail.paymentStatus.code == 'WAIT' && checkPermission(['admin', 'sale-saas-finance-financeCollect:confirm', 'sale-platform-finance-financeCollect:confirm'])
    }
  },
  methods: {
    checkPermission,
    confirmReceipt() {
      if (this.remarks == "") {
        this.$message.error("请输入审核备注");
        return;
      }
      this.acceptedLoading = true
      if (!this.changeMethod) {
        this.item.method = this.detail?.method?.code || ''
      }
      confirmReceipt({
        id: this.$route.query.id,
        remarks: this.remarks,
        method: this.item.method
      }).then(res => {
        if (res.code !== 0) return;
        this.getitem();
      }).finally(() => {
        this.acceptedLoading = false
      })
    },
    backlist() {
      this.$router.go(-1);
    },
    close() {
      this.acceptedFlag = false;
      this.$refs.accepted.resetFields();
    },
    async getitem() {
      this.loading = true;
      let { data } = await detail(this.$route.query.id);
      this.loading = false;
      this.changeMethod = false
      this.detail = data
      this.item = { ...data.financePaymentFlow, method: data.method.code };
    },
  },
  mounted() {
    this.getitem();
  },
};
</script>

<style lang="less">
.detail-wrapper {
  .item {
    width: 100%;
    margin-bottom: 30px;
    border-bottom: 1px solid #eeeeee;
    .title {
      padding: 0 0 15px;
      span {
        font-size: 16px;
        padding-left: 10px;
        border-left: 4px solid rgba(64, 158, 255, 1);
      }
    }
  }
  .ntable {
    border: 1px solid #dfe6ec;
    margin-bottom: 30px;
    border-radius: 5px;
    .nitem {
      display: flex;
      &>div {
        padding: 18px 20px;
        border-bottom: 1px solid #dfe6ec;
      }
      &>div:nth-child(odd) {
        color: #323336;
        font-weight: 600;
        font-size: 14px;
        width: 150px;
        text-align: right;
        align-content: center;
        border-right: 1px solid #dfe6ec;
        background-color: #f7f7f8;
        font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
          Microsoft YaHei, Arial, sans-serif;
      }
       &>div:nth-child(even) {
        flex: 1;
        border-right: 1px solid #dfe6ec;
        font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
          Microsoft YaHei, Arial, sans-serif;
        font-size: 14px;
      }
      div:last-child {
        border-right: none;
      }
    }
    .nitem:last-child {
      div {
        border-bottom: none;
      }
    }
  }
  /deep/ .el-icon-circle-close{
    color: #fff;
  }
}
</style>
