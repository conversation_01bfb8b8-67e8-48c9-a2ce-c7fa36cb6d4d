<template>
  <el-dialog class="product-modal" title="选择商品" v-if="visible" :visible.sync="visible" width="1300px">
    <div class="product-wrap">
      <div class="product-left" style="padding-right: 16px">
        <im-search-pad
          :has-expand="false"
          :model="model"
          @reset="reset"
          @search="load"
        >
          <im-search-pad-item prop="productCode">
            <el-input v-model="model.productCode" placeholder="请输入商品编码" />
          </im-search-pad-item>
          <im-search-pad-item prop="productName">
            <el-input v-model="model.productName" placeholder="请输入商品名称" />
          </im-search-pad-item>
          <im-search-pad-item prop="manufacturer">
            <el-input v-model="model.manufacturer" placeholder="请输入生产厂家" />
          </im-search-pad-item>
        </im-search-pad>
        <div v-loading="loading">
          <el-table ref="multipleTable" :data="list" border :row-key="getRowKeys" @selection-change="handleSelectionChange">
            <el-table-column label="序号" type="index" width="54" align="center" fixed />
            <el-table-column type="selection" :reserve-selection="true" fixed align="center" />
            <el-table-column label="主图" width="80" class-name="img-cell">
              <template slot-scope="{row}">
                <img :src="row.pictIdS | imgFilter" width="50px" height="50px">
              </template>
            </el-table-column>
            <el-table-column label="产品编码" prop="productCode" width="200px" />
            <el-table-column label="产品名称" prop="productName" width="200px" />
            <el-table-column label="规格" prop="spec" width="200px" />
            <el-table-column label="单位" prop="unit" />
            <el-table-column label="批准文号" prop="approvalNumber" width="200px" />
            <el-table-column label="剂型" prop="agentiaType" />
            <el-table-column label="生产厂家" prop="manufacturer" width="200px" />
            <el-table-column label="经营类目" prop="businessRangeName" width="150px" />
          </el-table>

          <el-pagination
            style="margin-bottom: 16px; margin-top: 16px"
            background
            :total="total"
            :current-page.sync="page"
            :page-size.sync="pageSize"
            layout="->, prev, pager, next, sizes, jumper"
            @current-change="sizeChange(i)"
            @size-change="sizeChange(i)"
          />
        </div>

        <div slot="footer" class="table-footer">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="ok">确定</el-button>
        </div>
      </div>
      <div class="product-right">
        <div class="product-right__title">
          已选商品({{ this.selection.length }})<span> </span>
        </div>
        <div class="product-right__flex">
          <div v-for="(items, i) in selection" :key="i" class="product-right__flexItems" :class="{'active': activeClass === i}" @click="activeClass = i">
            <div class="product-right__img">
              <img :src="items.pictIdS | imgFilter" width="100%">
            </div>
            <div class="product-right__detail">
              <h4>{{ items.drugName }}</h4>
              <p>厂家：{{ items.manufacturer }}</p>
              <p>规格：{{ items.spec }}  单位：{{ items.unit }}</p>
              <div class="product-right__detail--sort">
                <p>排序</p>
                <el-input v-model="items.sortValue" size="mini" />
              </div>
            </div>
            <div class="delete-icon">
              <i v-if="activeClass === i" class="el-icon-error" @click="delectItems(i)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
export default {
  components: {
  },
  props: ['items'],
  data() {
    return {
      // 获取row的key值
      getRowKeys(row) {
        return row.id
      },
      model: {
        productCode: '',
        productName: '',
        manufacturer: '',
        whetherOnSale: 'Y'
      },
      activeClass: 0,
      visible: false,
      list: [],
      total: 0,
      page: 1,
      pageSize: 10,
      loading: false,
      selection: [],
      isFirst: true,
      Barr: [],
      timer: null,
      timerOther: null
    }
  },

  methods: {
    reset() {
      this.model = {}
      this.load()
    },
    delectItems(i) {
      this.Barr = this.selection
      this.$refs.multipleTable.clearSelection()
      this.Barr.splice(i, 1)
      this.timer = setTimeout(() => {
        this.Barr.forEach(row => {
          if (row) {
            this.$refs.multipleTable.toggleRowSelection(row)
          } else {
            this.$refs.multipleTable.clearSelection()
          }
        })
      })
    },

    sizeChange() {
      this.isFirst = false
      this.load()
    },

    close() {
      this.visible = false
    },

    ok() {
      this.$emit('change', this.selection)
      this.close()
    },

    open() {
     
      this.$nextTick(()=>{
          this.visible = true
          this.page = 1
          this.load()
      })
    },

    async load() {
      this.loading = true
      const { data } = await request.post('product/admin/product/page', {
        current: this.page,
        map: {},
        model: this.model,
        order: 'descending',
        size: this.pageSize,
        sort: 'id'
      })
      this.loading = false

      data.records.forEach((items) => {
        const arr = String(items.pictIdS).split(',')
        if (arr.length > 1) {
          items.pictIdS = arr[0]
        }
      })

      this.total = data.total
      this.list = data.records
      this.checkTrue()
    },
    // 去重
    unique(arr) {
      return Array.from(new Set(arr))
    },
    checkTrue() {
      if (this.items) {
        this.timerOther = setTimeout(() => {
          const arr = this.list.filter(items => this.items.map(item => item.productId).includes(items.id))
          const arr2 = this.items.filter(items => !arr.map(item => item.id).includes(items.productId))
          const arr3 = arr2.map(items => items.product)
          // 首次进入组件
          if (this.isFirst) {
            this.selection = []
            this.selection = arr.concat(arr3)
          } else {
            const a = this.Barr(this.selection)
            this.$refs.multipleTable.clearSelection()
            if (arr.length > 0) {
              a.forEach((items, i) => {
                arr.forEach((item, index) => {
                  if (items.id == item.id) {
                    if (arr.filter(v => !a.map(o => o.id).includes(v.id))) {
                      this.Barr.push(item)
                    }
                  }
                })
              })
            }

            this.selection = this.Barr
          }
          this.selection.forEach(row => {
            if (row) {
              this.$refs.multipleTable.toggleRowSelection(row, true)
            } else {
              this.$refs.multipleTable.clearSelection()
            }
          })
        })
      }
    },

    handleSelectionChange(val) {
      this.selection = val.map((item, i) => {
        return {
          id: item.id,
          drugName: item.drugName,
          pictIdS: item.pictIdS,
          manufacturer: item.manufacturer,
          unit: item.unit,
          sortValue: i
        }
      })
    },

    stringSplit(content) {
      if (content) {
        const arr = content.split(',')
        return arr[0]
      }
    },
    beforeDestroy(){
      clearTimeout(this.timer);
      clearTimeout(this.timerOther);
    }

  }
}
</script>

<style lang="scss" scoped>
  .table-footer{
      border-top: 1px solid #EBECEE;
      text-align: right;
      padding-top: 16px;
  }
  .active{
      box-shadow: 0px 2px 10px 0px rgba(5,12,36,0.08);
  }
  .product{
    &-wrap{
      margin-top: -14px;
      display: flex;
      justify-content: space-around;
    }
    &-left{
      width: 1000px;
      overflow-x: auto;
    }
    &-right{
      width: 450px;
      padding-left: 20px;
      border-left: 1px solid #ebecee;

      &__flexItems{
        width: 360px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border: 1px solid #ebecee;
        border-radius: 3px;
        padding-bottom: 10px;
        margin-top: 10px;
        position: relative;
          .delete-icon{
            position: absolute;
            right: -10px;
            top: -10px;
            z-index: 50;
            .el-icon-error{
              font-size: 22px;
              color: #999999;
            }
          }
      }

      &__flex{
        max-height: 700px;
        overflow: auto;
      }

      &__detail--sort{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .el-input{
          width: 130px;
          padding-left: 15px;
        }
      }

      &__img{
        width: 30%;
      }

      &__detail{
        h4,p {
          margin: 0;
        }
        h4, p{
          line-height: 30px;
        }
        p{
          font-size: 12px;
        }
      }

      &__title{
        font-weight: bold;
        color: #333333;
        font-size: 16px;
        span{
          color: #999999;
          font-weight: 500;
          font-size: 14px;
        }
      }

    }
  }
</style>
