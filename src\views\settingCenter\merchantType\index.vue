<template>
  <div class="merchantTypePageContent">
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '企业类型' }]">
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button
            type="primary"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-setting-manage-merchantType:add',
                'sale-platform-setting-manage-merchantType:add',
              ])
            "
            @click="editFun()"
            >+新增企业类型</el-button
          >
        </template>
      </tabs-layout>
      <div class="table">
        <el-table
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="65"
            :render-header="renderHeader"
            fixed
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :width="item.width"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <div v-if="item.name == 'licenseBases'">
                <span
                  v-for="(licenseBasesitem, index) in row[item.name]"
                  :key="index"
                  >{{ index > 0 ? "、" : ""
                  }}{{ licenseBasesitem | test }}</span
                >
              </div>
              <span v-else>
                <!-- <span> -->
                {{ row[item.name] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="160"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span
                  v-if="
                    checkPermission([
                      'admin',
                      'sale-saas-setting-manage-merchantType:edit',
                      'sale-platform-setting-manage-merchantType:edit',
                    ])
                  "
                  class="table-edit-row-item"
                >
                  <el-button @click="editFun(scope.row)" type="text"
                    >编辑</el-button
                  >
                </span>
                <span
                  v-if="
                    checkPermission([
                      'admin',
                      'sale-saas-setting-manage-merchantType:del',
                      'sale-platform-setting-manage-merchantType:del',
                    ])
                  "
                  class="table-edit-row-item"
                >
                  <el-button type="text" @click="deleteFun(scope.row)"
                    >删除</el-button
                  >
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 设置 编辑 -->
    <edit ref="editRef" :isReload.sync="submitReload"></edit>
    <!-- :visible.sync="showEdit" :row.sync="row" -->
    <!-- 设置 编辑 -->
  </div>
</template>
<script>
import checkPermission from "@/utils/permission";
import { list, deleteApi } from "@/api/setting/merchantType";
import edit from "@/views/settingCenter/merchantType/edit";
import Pagination from "@/components/Pagination";
import TabsLayout from "@/components/TabsLayout";
export default {
  data() {
    return {
      // showEdit:false,
      // row:{},
      tableTitle: [
        {
          label: "企业类型名称",
          name: "name",
          width: "220px",
        },
        {
          key: 1,
          label: "企业资质文件",
          name: "licenseBases",
        },
      ],
      submitReload: false,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        model: {},
        current: 1,
        size: 10,
      },
    };
  },
  watch: {
    submitReload: function (newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.getList();
      }
    },
  },
  components: {
    Pagination,
    edit,
    TabsLayout,
  },
  filters: {
    test: function (value) {
      if (value != null) return value.name;
    },
  },
  methods: {
    checkPermission,
    editFun(row) {
      this.$refs.editRef.showEdit(row);
      // this.row=row;
      // this.showEdit=true
    },
    deleteFun(row) {
      var _this = this;
      this.$confirm("此操作将永久删除该信息, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          _this.actionDeleteFun(row);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 刷新
    reload() {
      this.getList();
    },
    async actionDeleteFun(row) {
      const data = await deleteApi(row.id);
      if (data.code == 0) {
        this.getList();
      }
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu" />
          </div>
        </div>
      );
    },
    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },
    async getList() {
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
  },
  mounted() {
    this.getList();
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.merchantTypePageContent {
  padding: 0;
  // padding: 15px;
  .table {
    padding: 12px;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
}
</style>
