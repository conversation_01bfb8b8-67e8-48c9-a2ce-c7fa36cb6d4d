<template>
  <div class="archivesPageContent">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model.trim="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="listTime">
        <el-date-picker
          v-model="listTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @change="timeSelect"
        />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <div class="title flex_between_center">
        <div>
          <el-tabs
            v-model="listQuery.model.publishStatusEnum"
            class="typeTabs"
            @tab-click="chageTabsFun"
          >
            <el-tab-pane label="待审核" name="PENDING" />
            <el-tab-pane label="已驳回" name="REJECTED" />
            <el-tab-pane label="已撤销" name="REPEAL" />
          </el-tabs>
        </div>
        <div>
          <el-button @click="refreash">刷 新</el-button>
        </div>
      </div>

      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          v-loading="listLoading"
          :data="list"
          row-key="index"
          border
          fit
          highlight-current-row
          style="width: 100%"
          @selection-change="selectTableItemFun"
        >
          <el-table-column
            fixed="left"
            align="center"
            width="80"
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            width="55"
            fixed="left"
            align="center"
          />

          <el-table-column label="商品主图" align="center" width="80" class-name="img-cell">
            <template slot-scope="{row}">
              <img :src="row.pictIdS|imgFilter" width="50px" height="50px">
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <el-button
                v-if="item.name == 'region'"
                type="text"

                @click="showAreasFun(row)"
              >查看区域</el-button>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"

                @click="detailFun(row)"
              >查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex_between_center">
          <div />
          <pagination
            v-if="total > 0"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page.sync="listQuery.current"
            :limit.sync="listQuery.size"
            @pagination="getlist"
          />
        </div>
      </div>
    </div>

    <el-dialog
      v-if="showAreasFlag"
      :visible.sync="showAreasFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-dialogDrag
    >
      <div slot="title">
        <span>可代理销售区域({{ showAreasNum }})</span>
      </div>
      <areas
        :visible.sync="showAreasFlag"
        :row.sync="row"
        :show-areas-num.sync="showAreasNum"
      />
    </el-dialog>
  </div>
</template>

<script>
import requestAxios from '@/utils/request'
import Pagination from '@/components/Pagination'
import tableInfo from '@/views/promote/promotionCheck/tableInfo'
import { setContextData, getContextData } from '@/utils/auth'
import areas from './areas'
export default {
  name: 'promotionApplication',
  components: {
    Pagination,
    areas
  },
  data() {
    return {
      isExpand: false,
      listLoading: false,
      list: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: 'PENDING'
        }
      },
      total: 0,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      listTime: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      showAreasFlag: false,
      row: {},
      showAreasNum: 0,
      selectList: []
    }
  },
  methods: {
    refreash() {
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: 'PENDING'
        }
      }
      this.initTbaleTitle()
      this.getlist()
    },
    async listRepeal() {
      const arr = []
      this.selectList.forEach(item => {
        arr.push(item.id)
      })
      const { data } = await requestAxios(
        {
          url: '/agent/agentProduct/merchant/batchRepeal',
          method: 'post',
          data: arr
        })
      if (data) {
        this.$message.success('批量驳回成功')
        this.list = []
        this.listQuery.current = 1
        this.listQuery.model.publishStatusEnum = 'REPEAL'
        this.getlist()
      }
    },
    detailFun(row) {
      setContextData('promoteCenter_promotionCheck', this.listQuery)
      this.$router.push({
        path: '/promoteCenter/promotionCheckdetail',
        query: {
          id: row.id
        }
      })
    },
    resetForm() {
      this.listTime = []
      this.list = []
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: this.listQuery.model.publishStatusEnum
        }
      }
      this.getlist()
    },
    showAreasFun(row) {
      this.showAreasFlag = true
      this.row = row
    },
    timeSelect(e) {
      this.listQuery.model.startTime = e[0] + ' 00:00:00'
      this.listQuery.model.endTime = e[1] + ' 23:59:59'
    },
    splitString(val) {
      if (!val) {
        return ''
      }
      return val.split(',')
    },
    chageTabsFun() {
      this.list = []
      this.initTbaleTitle()
      this.listQuery.current = 1
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0]
      this.listQuery.model.cityId = e[1]
      this.listQuery.model.countyId = e[2]
    },
    selectTableItemFun: function(val) {
      this.selectList = val
    },
    onSearchSubmitFun() {
      this.list = []
      this.getlist()
    },
    async getlist() {
      this.listLoading = true
      const { data } = await requestAxios({
        url: '/agent/agentProduct/merchant/query/agencyProductsApply',
        method: 'post',
        data: this.listQuery
      })
      this.listLoading = false
      this.total = data.total
      this.list = data.records
    },
    initTbaleTitle() {
      this.tableSelectTitle = []
      this.tableTitle = tableInfo[this.listQuery.model.publishStatusEnum]
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum]
      var titlesName = ['显示字段项', '隐藏字段项']
      return (
        <div style='position:relative'>
          <div onClick={this.showHeaer}>
            <i class='el-icon-menu' />
          </div>
          <el-dialog
            title='设置显示列表'
            showClose={false}
            visible={this.showSelectTitle}
            width='640px'
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style='margin-top: 25px;text-align: center;'>
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type='primary' onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      )
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val
    },
    showHeaer: function() {
      this.showSelectTitle = true
    },
    closeHeaer: function() {
      this.showSelectTitle = false
      this.tableSelectTitle = []
    },
    setHeaer: function() {
      var titles = tableInfo[this.listQuery.model.publishStatusEnum]
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key)
      })
      this.tableTitle = listinfo
      this.showSelectTitle = !this.showSelectTitle
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == '/promoteCenter/promoteManagement/detail' || '/promoteCenter/promotionCheckdetail') {
        if (getContextData('promoteCenter_promotionCheck') != '') {
          vm.listQuery = getContextData('promoteCenter_promotionCheck')
        }
      }
      vm.initTbaleTitle()
      // vm.getSaleMerhcantCount();
      vm.getlist()
    })
  }
}
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  padding-bottom: 16px;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    padding: 0 12px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
