/**
* 操作权限处理
*/

import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    let value
    if(!vnode.data.attrs || !vnode.data.attrs.pm){
      return true
    } else {
      value = vnode.data.attrs.pm
    }
    const roles = store.getters && store.getters.roles
    const permissionRoles = value

    const hasPermission = roles.some(role => {
      return permissionRoles.includes(role)
    })
    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}
