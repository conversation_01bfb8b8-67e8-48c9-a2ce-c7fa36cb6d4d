<template>
  <div>
    <el-table :data="tableDate" style="width: 100%" border>
      <el-table-column prop="label" label="证件类型"></el-table-column>
      <el-table-column prop="licenseNumber" label="证件号">
        <template slot-scope="{ row }">
          <div class="scopeBox">
            <span
              class="requireIcon"
              v-if="row.isEdit && row.idIsRequired == 'Y'"
              >*</span
            >
            <el-input
              v-if="row.isEdit"
              placeholder="请输入证件号"
              v-model="row.licenseNumber"
              @input="judgeRejectFlag(row)"
            ></el-input>
            <span v-else>{{ row.licenseNumber }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="licenseEndTime" label="过期时间">
        <template slot-scope="{ row }">
          <div class="scopeBox">
            <span
              class="requireIcon"
              v-if="row.isEdit && row.expireIsRequired == 'Y'"
              >*</span
            >
            <el-date-picker
              v-if="row.isEdit"
              :disabled="row.isForever"
              v-model="row.licenseEndTime"
              type="datetime"
              style="width: 220px; margin-right: 10px"
              placeholder="选择日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="timeProp"
              @change="judgeRejectFlag(row)"
            ></el-date-picker>
            <span v-if="!row.isEdit && !row.isForever">{{
              row.licenseEndTime
            }}</span>
            <el-checkbox
              v-if="row.isEdit || row.isForever"
              :disabled="!row.isEdit"
              v-model="row.isForever"
              @change="handleChangForever(row)"
              >长期</el-checkbox
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="filePath"
        label="附件(点击图片可预览)"
        width="320"
        class-name="img-cell"
      >
        <template slot-scope="{ row }">
          <!-- <el-button v-if="!row.isEdit&&!row.filePath" type="text" >查看示例图片</el-button> -->
          <div class="scopeBox">
            <span
              class="requireIcon"
              v-if="row.isEdit && row.photoIsRequired == 'Y'"
              >*</span
            >
            <el-upload
              v-if="row.isEdit"
              :class="{
                hide:
                  !row.isEdit ||
                  (row.filePath.split(',').length >= row.limit &&
                    !!row.filePath),
              }"
              ref="uploadlisence"
              :limit="row.limit"
              :file-list="row.filePathList"
              :action="uploadParams.action"
              :data="uploadParams.data"
              :headers="uploadParams.headers"
              list-type="picture-card"
              :on-remove="handleRemove"
              :on-success="uploadSuccess"
              :before-upload="beforeUpload"
              accept=".jpg,.png,.bmp,.jpeg"
            >
              <i class="el-icon-plus"></i>
            </el-upload>

            <span v-else>
              <el-image
                v-for="file in row.filePathList"
                :key="file.url"
                style="width: 40px; height: 40px; margin-right: 10px"
                :src="file.url"
                :preview-src-list="[file.url]"
              >
              </el-image>
              <!-- <img
              v-for="file in row.filePathList"
              :key="file.url"
              class="el-upload-list__item-thumbnail"
              :src="file.url"
              alt=""
              style="
                contain: cover;
                width: 40px;
                height: 40px;
                margin-right: 5px;
              "
            /> -->
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button
            :disabled="$route.path.indexOf('clientDetail') != -1"
            v-hasPermission
            :pm="$props.edit"
            type="text"
            v-show="!scope.row.isEdit"
            @click="editLisenceFun(scope.row)"
          >
            编 辑
          </el-button>
          <el-button
            type="text"
            v-show="scope.row.isEdit"
            @click="cancelLisenceEdit(scope.row, scope.$index)"
            style="color: rgb(127, 127, 127)"
          >
            取 消
          </el-button>
          <el-popover
           v-if="scope.row.isEdit && scope.row.rejectFlag"
            v-model="scope.row.rejectFlag"
            placement="top"
            trigger="click"
            content="请填写必填项！"
            popper-class="mPopover"
          >
          <!-- :disabled="scope.row.rejectFlag" -->
            <el-button

              type="text"
              slot="reference"
              style="margin-left: 10px;"
            >
              确 定
            </el-button>
          </el-popover>
          <el-button
              v-if="scope.row.isEdit && !scope.row.rejectFlag"
              v-hasPermission
              :pm="$props.confirm"
              type="text"
              slot="reference"
              @click="confirmLisenceEdit(scope.row, scope.$index)"
            >
              确 定
            </el-button>
          <el-button
            type="text"
            @click="showBigPic(scope.row)"
            v-if="!!scope.row.filePath"
            v-show="!scope.row.isEdit"
            >预 览</el-button
          >
          <el-image
            :ref="`ref${scope.row.licenseBaseId}`"
            style="width: 0; height: 0"
            :src="previewImages[0]"
            :preview-src-list="previewImages"
          ></el-image>
          <DowloadButton
            v-show="
              scope.row.id && scope.row.filePath != '' && !scope.row.isEdit
            "
            :size="'small'"
            :buttonType="'text'"
            :key="scope.row.licenseBaseId"
            :imgList="getimgList(scope.row.filePath)"
          ></DowloadButton>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="资质预览"
      append-to-body
      :visible.sync="dialogStatus"
      v-if="showlisenceItem.filePath"
      :before-close="closeDialogFun"
    >
      <el-carousel arrow="always" height="50vh" :autoplay="false">
        <el-carousel-item
          v-for="(item, index) in getsrc(showlisenceItem.filePath)"
          :key="index"
        >
          <el-image
            style="width: 100%; height: 100%"
            :fit="'contain'"
            :src="item.url"
          ></el-image>
        </el-carousel-item>
      </el-carousel>
      <div slot="footer">
        <el-button @click="closeDialogFun">取 消</el-button>
        <DowloadButton
          :buttonType="'primary'"
          :key="showlisenceItem.id"
          :imgList="getimgList(showlisenceItem.filePath)"
        ></DowloadButton>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import hasPermission from "./hasPermission";
import DowloadButton from "@/components/eyaolink/DowloadButton";
import { getToken } from "@/utils/auth";
import { addLicense, editLicense } from "@/api/businessList";
import { getLocalUser } from "@/utils/local-user";
import { reject } from "lodash";
export default {
  data() {
    return {
      tableDate: [],
      previewImages: [],
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + "/file/file/upload",
        headers: {
          Authorization: "Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0",
          token: `Bearer ${getToken()}`,
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser() && getLocalUser().userId,
          folderId: 0,
        },
      },
      editLisenceItem: {},
      showlisenceItem: {},
      dialogStatus: false,
      timeProp: {
        disabledDate(r) {
          return r.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },
      popoverType: "click",
    };
  },
  directives: {
    hasPermission,
  },
  methods: {
    getimgList(row) {
      let arr = [];
      row.split(",").forEach((item) => {
        arr.push({
          fileIds: item,
        });
      });
      return arr;
    },
    judgeRejectFlag(row){
      let flag = false;
      if (
        row.expireIsRequired == "Y" &&
        !row.isForever &&
        !row.licenseEndTime
      ) {
        flag = true;
      }
      if (row.idIsRequired == "Y" && !row.licenseNumber) {
        flag = true;
      }
      if (row.photoIsRequired == "Y" && !row.filePath) {
        flag = true;
      }
      row.rejectFlag = flag;
    },
    editLisenceFun(row) {
      this.tableDate.forEach((item, index) => {
        if (item.isEdit) {
          this.$set(this.tableDate, index, this.editLisenceItem);
          item.isEdit = false;
        }
      });
      this.editLisenceItem = JSON.parse(JSON.stringify(row));

      this.judgeRejectFlag(row);

      row.isEdit = true;
    },
    cancelLisenceEdit(row, index) {
      row = this.editLisenceItem;
      this.$set(this.tableDate, index, this.editLisenceItem);
    },
    handleChangForever(row) {
      row.licenseEndTime = row.isForever
        ? ""
        : this.editLisenceItem.licenseEndTime;
      this.judgeRejectFlag(row);
    },

    async confirmLisenceEdit(row) {
      row.isEdit = false;
      // if (!row.filePathList) {
      //   this.$message.error("请上传资质");
      //   return false;
      // }
      row.filePathList?.forEach((item) => {
        item.url = item.response.data.url;
      });
      row.merchantId = this.$route.query.id;
      row.isForever = row.isForever ? "Y" : "N";
      if (row.merchantId) {
        if (row.id && row.merchantId) {
          let { data } = await editLicense(row);
          if (data) {
            this.$message.success("修改资质成功");
          }
        } else {
          let { data } = await addLicense(row);
          if (data) {
            this.$message.success("添加资质成功");
          }
        }
      }
      row.isForever = row.isForever === "Y";
      let tableDate = JSON.parse(JSON.stringify(this.tableDate));
      tableDate.forEach((item) => {
        item.isForever ? (item.isForever = "Y") : (item.isForever = "N");
      });
      this.$emit("update:lisenceTableDate", tableDate);
    },
    getsrc(str) {
      if (!str) {
        return [];
      } else {
        let arr = str.split(",");
        let list = [];
        arr.forEach((item) => {
          let obj = {
            response: {
              data: {
                url: "",
              },
            },
          };
          obj.response.data.url = item;
          obj.url = item;
          list.push(obj);
        });
        return list;
      }
    },
    uploadSuccess(res, file, fileList) {
      this.tableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
          this.judgeRejectFlag(item);
        }
      });
    },
    handleRemove(file, fileList) {
      this.tableDate.forEach((item, index) => {
        if (item.licenseBaseId == this.editLisenceItem.licenseBaseId) {
          item.filePath = this.getFilePath(fileList);
          item.filePathList = fileList;
          this.judgeRejectFlag(item);
        }
      });
    },
    getFilePath(fileList) {
      let str = "";
      fileList.forEach((item) => {
        let url = item.response.data.url;
        str += item.response.data.url + ",";
      });
      return str.substr(0, str.length - 1);
    },
    beforeUpload(file) {
      let fileTypeList = [
        "image/png",
        "image/pjpeg",
        "image/jpeg",
        "image/bmp",
      ];
      const isJPG = fileTypeList.indexOf(file.type) > -1;
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$message.error("上传图片格式错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    showBigPic(row) {
      console.log("row---->", row);
      this.showlisenceItem = row;
      // this.dialogStatus = true;
      this.previewImages = row.filePathList.map((item) => {
        return item.url;
      });
      this.$refs[`ref${row.licenseBaseId}`].showViewer = true;
    },
    closeDialogFun() {
      this.dialogStatus = false;
    },
  },
  created() {
    this.tableDate = JSON.parse(JSON.stringify(this.$props.lisenceTableDate));
    this.tableDate.forEach((item) => {
      item.isForever == "Y"
        ? (item.isForever = true)
        : (item.isForever = false);
    });
    // this.tableDate = this.$props.lisenceTableDate.map(item => Object.assign({}, item, {isForever: item.isForever.code === 'Y'} ))
  },
  components: {
    DowloadButton,
  },
  props: {
    lisenceTableDate: {
      type: Array,
      required: true,
    },
    edit: {
      type: Array,
      required: false,
    },
    confirm: {
      type: Array,
      required: false,
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-upload {
  width: 40px;
  height: 40px;
  position: relative;
}
/deep/ .el-upload > i {
  font-size: 16px;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
/deep/ .el-upload-list .el-upload-list__item {
  width: 40px;
  height: 40px;
}
/deep/ .hide .el-upload--picture-card {
  display: none;
}
/deep/ .el-icon-circle-close {
  color: #fff;
}
.scopeBox {
  display: flex;
  align-items: center;
  > .requireIcon {
    margin-right: 4px;
    color: red;
  }
}
body /deep/ .mPopover{
  padding: 0;
}
</style>
