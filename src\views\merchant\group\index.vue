<template>
  <div>
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="name">
        <el-input v-model.trim="model.name" @keyup.enter.native="searchLoad" placeholder="请输入分组名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" v-model="model.groupType" :tabs="tabList" @change="handleChangeTab">
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-pur-merchant-group:add', 'sale-platform-pur-merchant-group:add'])" type="primary" @click="addOrEdit(1, '')">+ 新增分组
          </el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="getList"
        :pageSize="pageSize" :selection="false" :operation-width="180">
        <el-table-column label="客户数" width="120" slot="customerNumber">
          <slot slot-scope="{row}">
            <el-button v-if="checkPermission(['admin', 'sale-saas-pur-merchant-group:clientRelation', 'sale-platform-pur-merchant-group:clientRelation'])" type="text" @click="handleCheck(row)">{{ row.customerNumber }}</el-button>
            <span v-else>{{ row.customerNumber }}</span>
          </slot>
        </el-table-column>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin', 'sale-saas-pur-merchant-group:edit', 'sale-platform-pur-merchant-group:edit'])" class="table-edit-row-item">
              <el-button type="text" @click="addOrEdit(2, props.row)">编辑</el-button>
            </span>
            <span v-if="checkPermission(['admin','sale-saas-pur-merchant-group:clientImport','sale-platform-pur-merchant-group:clientImport'])" class="table-edit-row-item">
              <el-button type="text" @click="handleImport(props.row.id)">导入客户</el-button>
            </span>
            <span v-if="checkPermission(['admin', 'sale-saas-pur-merchant-group:del', 'sale-platform-pur-merchant-group:del'])" class="table-edit-row-item">
              <del-el-button :targetId="props.row.id" :text="delText" @handleDel="handleDel"></del-el-button>
            </span>
          </el-row>
        </div>

      </table-pager>
      <add-group :groupType="model.groupType" :group-visible="groupVisible" :title="title" :detail="detail" @load="handleRefresh" @changeShow="changeAddGroup" />
      <!--    导入商品弹窗-->
      <importDialog ref="importDialogRef" :tipsList="tipsList" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="true" :queryParams="queryData" @uploadChangeData="uploadChangeData"></importDialog>
      <add-group :groupType="model.groupType" :group-visible="groupVisible" :title="title" :detail="detail"
        @load="handleRefresh" @changeShow="changeAddGroup" />
    </div>
    <clients-model ref="model" @close="modelVisible = false" :tabs="tabs" @ok="handleModelOk"
      :visible.sync="modelVisible"></clients-model>
  </div>
</template>

<script>
import {
  merchantGroupListNew,
  findByUserIdSale,
  deleteGroup,
  updateMerchantPurSaleRelMerchantGroup,
  fetchGroupedCustomers,
  fetchGroupedCoustomerCount,
  addCustomersToGroups,
  removeGroupedCustomers
} from '@/api/group'
import delElButton from "@/components/eyaolink/delElButton";
import AddUser from './addUser'
import ListUser from './listUser'
import AddGroup from './addGroup'
import importDialog from "@/components/eyaolink/importDialog/index";
import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'
import checkPermission from '@/utils/permission'
import TabsLayout from "../../../components/TabsLayout/index";
const TableColumns = [
  { label: '分组名称', name: 'name', prop: 'name', width: 150 },
  { label: '客户数', name: 'customerNumber', prop: 'customerNumber', width: '120', slot: true },
  { label: '分组编码', name: 'code', prop: 'code', width: '120' },
  { label: '操作人', name: 'operator', prop: 'operator' },
  { label: '操作时间', name: 'updateTime', prop: 'updateTime' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}

export default {
  name: 'groupingManagement',
  components: {
    TabsLayout,
    AddUser,
    ListUser,
    AddGroup,
    delElButton,
    ClientsModel,
    importDialog
  },
  data() {
    return {
      modelVisible: false,
      delText: '您确定删除该分组吗？删除分组后，分组所绑定的客户自动解除绑定关系，请谨慎操作',
      isExpand: false,
      showSelectTitle: false,
      showAdd: false,
      showList: false,
      groupVisible: false,
      delVisible: false,
      title: '',
      tableTitle: TableColumnList,
      tableVal: [],
      list: null,
      page: 1,
      pageSize: 10,
      listLoading: false,
      model: {
        groupType: 'CLIENT',
        name: ''
      },
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first',
      saleMerchantId: '', // 经销商id
      detail: {},
      name: '',
      rowData: '',
      titles: ['显示字段项', '隐藏字段项'],
      tableData: [],
      customerStatistics: {
        bingSum: 0,
        noBingSum: 0
      },

      actionUploadUrl: '/api/merchant/admin/merchantImport/importMerchantExcel',
      templateKey: 'IMPORT_MERCHANT_EXCEL_TEMP',
      queryData:{
        purMerchantType: 'MERCHANT_GROUP_PUR_IMPORT',
        importId:''
      },
      tipsList:[
        "指定客户导入是根据ERP客户编码导入，请留意客户是否已维护ERP客户编码。",
        "仅支持xlsx格式文件，文件大小1M以内且数据500条以内。"
      ]
    }
  },
  computed: {
    tabList() {
      return [
        {
          name: '价格分组',
          value: 'CLIENT',
          hide: !checkPermission(['admin', 'sale-saas-pur-merchant-group:priceView', 'sale-platform-pur-merchant-group:priceView'])
        },
        {
          name: '控销分组',
          value: 'CONTROL',
          hide: !checkPermission(['admin', 'sale-saas-pur-merchant-group:controlView', 'sale-platform-pur-merchant-group:controlView'])
        }
      ]
    },
    tabs() {
      let merchantGroupId = this.rowData?.id
      let groupType = this.model.groupType
      let { bingSum, noBingSum } = this.customerStatistics
      return [
        {
          text: `已关联客户(${bingSum})`,
          value: "ALREADY",
          request: fetchGroupedCustomers,
          defaultValues: {
            groupType,
            operateType: 'VIEW',
            merchantGroupId
          },
          button: {
            okText: '移除'
          }
        },
        {
          text: `未关联客户(${noBingSum})`,
          value: "NOT",
          request: fetchGroupedCustomers,
          defaultValues: {
            groupType,
            operateType: 'ADD',
            merchantGroupId: groupType === 'CLIENT' ? 1 : merchantGroupId
          },
          button: {
            okText: '添加'
          }
        },
      ];
    }
  },
  methods: {
    checkPermission,

    async handleModelOk(rows, currentTabIndex) {
      let params = {
        ids: rows.map(v => v.id),
        merchantGroupId: this.rowData?.id,
        groupType: this.model.groupType,
      }
      let isAdd = currentTabIndex === 1;
      await this.$confirm(`您确定批量${isAdd ? '添加' : '移除'}这些客户吗`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      try {
        let request = isAdd ? addCustomersToGroups : removeGroupedCustomers
        const { code, msg } = await request(params);
        if (code !== 0) throw new Error(msg);
        this.$refs.model.refresh()
        this.fetchCount()

      } catch (error) {
        console.log("🚀 ~ file: index.vue ~ line 199 ~ handleModelOk ~ error", error)

      }
    },
    async addUsers() {
      if (this.ids.length > 0) {
        const { data } = await updateMerchantPurSaleRelMerchantGroup([this.ids.join(',')], this.merchantGroupId)
        this.$message.success('批量添加成功！')
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      }
    },


    changeAddUser(data) {
      if (data === 'false') {
        this.showAdd = false
      } else {
        this.showAdd = true
      }
      if (!this.showList) {
        this.reload()
      }
    },

    changeListUser(data) {
      if (data === 'false') {
        this.showList = false
      } else {
        this.showList = true
      }
      this.reload();
    },
    changeDelGroup(data) {
      if (data === 'false') {
        this.delVisible = false
      } else {
        this.delVisible = true
      }
    },
    changeAddGroup(data) {
      if (data === 'false') {
        this.groupVisible = false
      } else {
        this.groupVisible = true
      }
    },
    // 获取经销商id
    async getSaleId() {
      const userId = localStorage.getItem('userId')
      const { data } = await findByUserIdSale(userId)
      this.saleMerchantId = data.id
    },
    async handleDel(id) {
      // console.log('id------>', id);
      let data = await deleteGroup([id], this.model.groupType);
      if (data.code == 0) {
        this.$message.success('删除成功');
        this.reload();
      }
    },
    async getList(params) {
      this.listLoading = false
      const query = {
        model: this.model
      }
      Object.assign(query, params)
      return await merchantGroupListNew(query);
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleChangeTab(tab) {
      this.model.groupType = tab.value
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    reload() {
      // this.$refs['tabs-layout'].reset();
      // this.model.groupType = 'CLIENT';
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    handleCommand(type) {
      if (type === 'add') {
        this.showAdd = !this.showAdd
        return
      }
      if (type === 'list') {
        this.showList = !this.showList
        return
      }
      if (type === 'del') {
      }
    },
    handleCheck(row) {
      // this.showList = true
      this.rowData = row
      this.modelVisible = true;
      this.fetchCount()
    },
    handleAdd(row) {
      this.showAdd = true
      this.rowData = row
    },
    // 新增分组
    addOrEdit(type, row) {
      this.groupVisible = true
      if (type === 1) {
        if (this.model.groupType == 'CLIENT') {
          this.title = '新增客户组'
        } else {
          this.title = '新增控销组'
        }
        this.detail = {}
      } else {
        if (this.model.groupType == 'CLIENT') {
          this.title = '编辑客户组'
        } else {
          this.title = '编辑控销组'
        }
        this.detail = JSON.parse(JSON.stringify(row));
      }
    },
    handleImport(id){
      this.queryData.importId = id;
      this.$refs.importDialogRef.initOpen();
    },
    //  导入商品回调
    uploadChangeData(){
      this.reload();
    },
    async fetchCount() {
      let merchantGroupId = this.rowData?.id
      let groupType = this.model.groupType
      const model = {
        merchantGroupId,
        groupType,
        publishStatus: "Y"
      }
      const { code, data } = await fetchGroupedCoustomerCount(model)
      if (code === 0) {
        this.customerStatistics = data;
      }
      
    }
  }
}
</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style scoped>
</style>
