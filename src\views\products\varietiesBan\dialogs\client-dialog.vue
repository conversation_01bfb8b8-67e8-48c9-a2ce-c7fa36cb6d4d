<template>
  <el-dialog
    title="选择客户"
    :visible.sync="visible"
    width="50%"
    @open="getList"
    :before-close="handleClose">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="reset"
      @search="getList"
    >
      <im-search-pad-item prop="purMerchantCode">
        <el-input v-model="listQuery.model.purMerchantCode" placeholder="请输入客户编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
    </im-search-pad>
    <el-table border ref="multipleTable" :data="tableData" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50"  :selectable="selectable" align="center" />
      <el-table-column label="客户编码" prop="id" min-width="180" show-overflow-tooltip />
      <el-table-column label="客户名称" prop="name" min-width="180" show-overflow-tooltip />
      <el-table-column label="企业类型" prop="merchantType" min-width="120" show-overflow-tooltip />
      <el-table-column label="所在地区" prop="region" min-width="150" show-overflow-tooltip />
    </el-table>
    <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination"></pagination>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { merchantPurSaleRelList } from '@/api/group'
  import Pagination from '@/components/Pagination/index.vue'

  export default {
    components: {
      Pagination
    },
    props: ['saleMerchantId','checkedId'],
  data () {
    return {
      listQuery: {
        current: 1,
        size: 10,
        order: "descending",
        sort: "id",
        model: {
          saleMerchantId: '',
          purMerchantCode: '',
          purMerchantName: ''
        }
      },
      total: 0,
      page: 1,
      search: '',
      visible: false,
      tableData: [
      ],
      formatAreaData: [],
      selected:[]
    }
  },
    mounted() {
      //this.getList()
    },
    methods: {
      handleConfirm() {
        this.$emit('getData', this.formatAreaData)
        this.$refs.multipleTable.clearSelection()
        this.visible = false
      },
      pagination(val) {
        this.listQuery.current = val.page
        this.listQuery.size = val.limit
        this.getList()
      },
      selectable(row, index) {
        if(row.selectable === true) {
          return false
        } else {
          return true
        }
      },
      async getList() {
        this.listQuery.model.saleMerchantId = this.saleMerchantId
        const {data} = await merchantPurSaleRelList(this.listQuery)
        this.tableData = data.records
        this.total = data.total
        this.tableData.forEach((item,index)=>{
          this.$set(item,'selectable',false)
          this.selected.forEach(itx=>{
            if (itx.id === item.purMerchantId) {
              this.tableData[index].selectable = true
            }
          })
        })
      },
      handleClose(done) {
        done()
      },
      handleSelectionChange(val) {
        this.formatAreaData = val
      },
      handlePageSize(val) {
      },
      handlePage(val) {
      },
      reset() {
        this.page = 1
        this.listQuery.model =  {
           saleMerchantId: '',
            purMerchantCode: '',
            purMerchantName: ''
        }
      }
  },
  watch: {
    checkedId: {
      immediate: true,
      handler(newVal,oldVal){
        this.selected = newVal
      }

    }
  }
}
</script>

<style lang="scss" scoped>
.search-wrapper{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .el-input{
    width: 200px;
    margin-right: 10px;
  }
}
.el-table{
  margin-bottom: 20px;
  ::v-deep{
    .el-radio{
      .el-radio__label{
        display: none;
      }
    }
  }
}
</style>
