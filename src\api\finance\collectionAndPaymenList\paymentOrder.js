import request from "@/utils/request";
import requestExport from "@/utils/requestExport";

// 获取列表数据
export function list(data) {
  return request({
    url: "/finance/admin/financePay/merchant/page",
    method: "post",
    data,
  });
}

export function detail(data) {
  return request({
    url: "/finance/admin/financePay/" + data,
    method: "get",
  });
}

// 确认支付
export function confirmPay(data) {
  return request({
    url: "/finance/admin/financePay/confirmPay",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// 重新支付
export function rePay(data) {
  return request({
    url: "/finance/admin/financePay/loadPay/" + data,
    method: "get",
  });
}

// /finance/admin/financeCollect/page

/**
 * @description  付款单管理导出Excel
 * **/
export function exportFinancePay(data) {
  return requestExport({
    url: "/finance/admin/financePay/export",
    method: "post",
    data,
    headers: { responseType: "blob" },
  });
}
