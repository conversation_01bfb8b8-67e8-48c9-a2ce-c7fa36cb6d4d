<template>
  <el-dialog :title="(isEdit ? '编辑' : '新增') + '短信任务'" :visible.sync="visible" direction="rtl" :wrapperClosable="false" width="600px">
    <div style="height: 100%;">
      <el-form ref="form" :rules="rules" class="form" size="small" :model="form" label-width="120px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="业务场景" prop="businessScenario">
          <el-select v-model="form.businessScenario" placeholder="请选择业务场景" clearable filterable>
            <el-option v-for="item in businessScenarioData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="短信内容" prop="content">
          <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model="form.content" />
        </el-form-item>
        <el-form-item label="发送内容">
          <div class="content">
            {{ '【国钥云】尊贵的' + form.content + '拒收请回复R。'}}
          </div>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button @click="hide">取消</el-button>
          <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import { createSmsTask } from "@/api/retailStore";

export default {
  name: 'addTaskDialog',
  data() {
    return {
      visible: false,
      loading: false,
      businessScenarioData: [
        { label: '直播', value: 'LIVE' },
        { label: '其他', value: 'OTHER' }
      ],
      isEdit: false, // 是否是编辑模式
      form: {
        businessScenario: '', // 业务场景
        taskName: '', // 任务名称
        physicalWxUrlLink: '', // 报名链接
        content: '{请输入自定义内容}' // 短信内容
      },
      rules: {
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        content: [{ required: true, message: '请输入短信内容', trigger: 'blur' }],
        physicalTaskId: [{ required: true, message: '请选择体检任务', trigger: 'change' }],
        businessScenario: [{ required: true, message: '请选择业务场景', trigger: 'change' }],
      }
    }
  },
  methods: {
    show(data, add = true) {
      this.isEdit = false
      Object.assign(this.$data.form, this.$options.data().form)
      if (data) {
        let form = deepClone(data)
        this.isEdit = !add
        if (add) delete form.id
        this.form = form
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        createSmsTask(this.form).then(() => {
          const text = this.isEdit ? '编辑' : '创建'
          this.$message.success(text + '任务成功')
          this.hide()
          this.$emit('createSuccess')
        }).finally(() => {
          this.loading = false
        })
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  background-color: #eee;
  color: #333;
  box-sizing: border-box;
  padding: 8px;
  border-radius: 4px;
  line-height: 1.5;
}
</style>
