<template>
  <div class="tab_bg">
    <el-tabs v-model="activeName">
      <el-tab-pane label="充值" name="first"></el-tab-pane>
    </el-tabs>
    <div class="payment">
      <p>充值到：平台入驻通用保证金</p>
      <el-form :model="form">
        <el-form-item label="充值金额：">
          <el-input v-model="form.acount" size="mini" style="width: 300px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-radio v-model="form.radio" label="1" border>微信</el-radio>
        </el-form-item>
        <el-form-item>
          <el-radio v-model="form.radio" label="2" border>支付宝</el-radio>
        </el-form-item>
      </el-form>
      <div class="payment-tips">
        <p>
          温馨提示：<br/> 不支持信用卡方式充值。<br/>余额不足请及时充值，以免影响正常服务。
        </p>
      </div>
      <el-checkbox v-model="checked">我已了解：充值的款项只可用于平台推广及服务消费，如需提现，只支持原路退回至付款账号。</el-checkbox>
      <div class="payment-btn">
        <el-button type="primary" size="middle">
          充值
        </el-button>
      </div>  
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      activeName: 'first',
      form: {
        acount: '',
        radio: 1
      },
      checked: true
    }
  }
}
</script>

<style lang="scss" scoped>
  .payment{
    padding: 0 20px;
    &-tips{
      background: #eeeeee;
      width: 400px;
      p{
        line-height: 30px;
        padding: 15px;
      }
    }

    &-btn{
      padding-top: 20px;
    }
  }
</style>