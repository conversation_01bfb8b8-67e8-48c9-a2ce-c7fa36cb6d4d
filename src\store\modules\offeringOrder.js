/*
 * @: 代客下单
 */
import { setContextData,getContextData } from '@/utils/auth'

const state = {
    purMerchantInfo: getContextData("purMerchantInfo") || null, // 代客下单已经选择的下单客户的信息
    couponInfo: null, // 代客下单已经选择的优惠券的信息
    remarkText: '', // 代客下单用户输入的备注信息
    priceInfo: null, // 代客下单的价格信息
    basicCartInfo:null // 购物车基本信息
};

const mutations = {
    SET_PURMERCHANTINFO: (state, purMerchantInfo) => {
        state.purMerchantInfo = purMerchantInfo;
        setContextData("purMerchantInfo",purMerchantInfo);
    },
    SET_COUPONINFO: (state, couponInfo) => {
        state.couponInfo = couponInfo;
    },
    SET_REMARKTEXT: (state, remarkText) => {
        state.remarkText = remarkText;
    },
    SET_PRICEINFO: (state, priceInfo) => {
        state.priceInfo = priceInfo
    },
    SET_BASICCARTINFO: (state, basicCartInfo) => {
        state.basicCartInfo = basicCartInfo;
    }
};

const actions = {
    setPurMerchantInfo({ commit }, purMerchantInfo ) {
        commit('SET_PURMERCHANTINFO', purMerchantInfo) 
    },
    setCouponInfo( { commit }, couponInfo){
        commit("SET_COUPONINFO", couponInfo)
    },
    setRemarkText( { commit }, remarkText ) {
        commit('SET_REMARKTEXT', remarkText)
    },
    setPriceInfo( { commit }, priceInfo) {
        commit('SET_PRICEINFO', priceInfo)
    },
    setBasicCartInfo( {commit}, basicCartInfo ) {
        commit('SET_BASICCARTINFO',basicCartInfo)
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
}