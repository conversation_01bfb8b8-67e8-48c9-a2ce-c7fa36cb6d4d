<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model="model.purMerchantName" placeholder="请输入企业名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="applyPerson">
        <el-input v-model="model.applyPerson" placeholder="生产申请人" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" v-model="model.auditStatus" :tabs="tabs" @change="handleChangeTab">
        <!--tabs右上角相关按钮-->
        <template slot="button">
        </template>
      </tabs-layout>
      <!--分页table-->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" :pageSize="pageSize"
        :operation-width="120" :isNeedButton="isNeedButton">
        <!--插入自定义column-->
        <el-table-column label="赠品图片" slot="pictIdS">
          <template slot-scope="scope">
            <img v-if="scope.row.pictIdS" :src="scope.row.pictIdS.split(',')[0]" width="50px">
            <img v-if="scope.row.pictIdS == null || scope.row.pictIdS == ''" src="~@/assets/product.png" width="50px">
          </template>
        </el-table-column>
        <el-table-column label="变更类型" width="140" slot="explainType">
          <slot slot-scope="{row}">
            {{ getExplainTypeText(row.explainType) }}
          </slot>
        </el-table-column>

        <el-table-column label="审核状态" width="140" slot="auditStatus">
          <slot slot-scope="{row}">
            {{ row.auditStatus== 0 ? '已驳回' : (row.auditStatus == 1 ? '已通过' : '待审核') }}
          </slot>
        </el-table-column>
        <!--操作栏-->
        <div slot-scope="{row}">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item" v-if="row.auditStatus == 2">
              <el-button type="text" @click="handleDel(row.id,1)">通过</el-button>
              <el-button type="text" @click="handleDel(row.id,0)">
                驳回</el-button>
            </span>
            <span v-else>{{ row.auditStatus== 0 ? '已驳回' : '已通过' }}</span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
  const TableColumns = [{
      label: "变更类型",
      name: "explainType",
      prop: "explainType",
      width: "150",
      slot: true
    },
    {
      label: "企业名称",
      name: "purMerchantName",
      prop: "purMerchantName",
      width: "170"
    },
    {
      label: "变更内容",
      name: "detailLocation",
      prop: 'detailLocation',
      width: "200"
    },
    {
      label: "变更说明",
      name: "addExplain",
      prop: 'addExplain',
      width: "200"
    },
    {
      label: "审核状态",
      name: "auditStatus",
      prop: 'auditStatus',
      width: "130",
      slot: true
    },
    {
      label: "申请人",
      name: "applyPerson",
      prop: 'applyPerson',
      width: "100"
    },
    {
      label: "申请时间",
      name: "updateTime",
      prop: 'updateTime',
      width: "130"
    }
  ]
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import {
    explainPage,
    explainAudit
  } from "@/api/enterprise"; // TODO 替换成对应用的列表api
  import checkPermission from "@/utils/permission";
  export default {
    name: 'ListIndex',
    components: {},
    props: {},
    data() {
      return {
        isExpand: false,
        search: '',
        tableData: [],
        pageSize: 10,
        tableColumns: TableColumnList,
        multipleSelection: [],
        isNeedButton:true,
        model: {
          applyPerson: '',
          purMerchantName: '',
          auditStatus: 3
        }
      }
    },
    computed: {
      tabs() {
        return [{
            name: '全部',
            value: 3,
          },
          {
            name: '待审核',
            value: 2,
          },
          {
            name: '已通过',
            value: 1,
          },
          {
            name: '已驳回',
            value: 0,
          }
        ]
      }
    },
    created() {},
    mounted() {},
    filters: {
      imgFilter: function (value) {
        if (value != "") {
          return value.split(",")[0]
        }
      }
    },
    methods: {
      getExplainTypeText(explainType) {
        const explainTypeString = explainType && explainType.toString() || ''
        switch (explainTypeString) {
          case '1':
            return '地图位置变更'
          case '2':
            return '企业名称变更'
          case '3':
            return '信用代码变更'
          case '4':
            return '所在区域变更'
          default:
            return '注册地址'
        }
      },
      checkPermission,
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      },
      async load(params) {
        
        let listQuery = {
          model: this.model
        }
        Object.assign(listQuery, params)
        if (this.model.auditStatus == 3) {
          listQuery.model.auditStatus = '';
        }
        // TODO 替换成对应用的列表api
        return await explainPage(listQuery)
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.model.auditStatus = '';
        this.isNeedButton = true;

        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleChangeTab(tab) {
        this.model.auditStatus = tab.value;
        this.isNeedButton = true;
        if(tab.value == 1 || tab.value == 0) {
          this.isNeedButton = false
        }
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      // 审核按钮
      handleDel(id,type) {
        this.$confirm(`您确定${type==1?'通过':'驳回'}吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
            let params = {
                id,
                auditStatus: type
            };
          explainAudit(params).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success(`${type==1?'通过':'驳回'}成功！`);
              this.reload();
            }
          })
        })
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      toInfoPageFun(id = '') {
        if (id != '') {
          this.$router.push({
            path: '/marketingCenter/donate/list/detail',
            query: {
              id: id,
            }
          })
        } else {
          this.$router.push({
            path: '/marketingCenter/donate/list/detail',
            query: {
              id: 0
            }
          })
        }
      },
      // 批量上架
      handleOnShelf() {
        console.log('this.multipleSelection', this.multipleSelection);
        let list = this.multipleSelection;
        let arr = list.map(item => {
          return item.id;
        });
        let params = {
          'ids[]': arr.toString(),
        };
        batchPutOnSale(params).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('上架成功');
            this.reload();
          }
        })
      },
      // 批量下架
      handleOffShelf() {
        console.log('this.multipleSelection', this.multipleSelection);
        let list = this.multipleSelection;
        let arr = list.map(item => {
          return item.id;
        });
        let params = {
          'ids[]': arr.toString(),
        };
        this.$confirm("您确定下架这些商品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          batchPullOffShelves(params).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('下架成功');
              this.reload();
            }
          });
        })
      },
    }
  }

</script>

<style lang="scss" scoped>
  @import "@/styles/element-variables.scss";

  .list-index {
    padding: 0;
  }

</style>
