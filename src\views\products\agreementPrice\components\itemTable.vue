<template>
  <el-table :data="list" border stripe class="items-table">
    <el-table-column type="index" align="center" width="80">
      <template slot="header">
        <span>序号</span>
      </template>
    </el-table-column>
    <el-table-column prop="imgUrl" label="商品主图" align="center">
      <template slot-scope="scope">
        <img :src="splitString(scope.row.pictIdS)[0]" width="50px"/>
      </template>
    </el-table-column>
    <el-table-column prop="productCode" label="商品编码" width="180">

    </el-table-column>
    <el-table-column prop="productName" label="商品名称" width="160"></el-table-column>
    <el-table-column prop="spec" label="规格" width="120"></el-table-column>
    <el-table-column prop="unit" label="单位"></el-table-column>
    <el-table-column prop="manufacturer" label="生产厂家" width="180"></el-table-column>
    <el-table-column label="销售价">
      <template v-slot="scope">
        <span class="sale-price">{{ scope.row.salePrice }}</span>
      </template>
    </el-table-column>
    <el-table-column label="成本价" width="100">
      <template v-slot="scope">
        <span class="cost-price">{{ scope.row.costPrice }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="price" label="协议价" width="100">
      <template v-slot="scope">
        <el-input class="price-input"

                  v-if="scope.row.editMode"
                  v-model="scope.row.inputPrice"></el-input>
        <span v-else>{{ scope.row.price }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="price" label="操作" v-if="editable" width="100">
      <template v-slot="scope">
        <el-button type="text" class="cancel" v-if="scope.row.editMode" @click="handleCancelEdit(scope.$index)">取消</el-button>
        <el-button type="text" v-else @click="handleEdit(scope.$index)">编辑</el-button>
        <el-button type="text" @click="handleConfirm(scope.$index)">确定</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleEdit (index) {
      this.$set(this.list[index], 'editMode', true)
    },
    handleCancelEdit (index) {
      this.$set(this.list[index], 'inputPrice', null)
      this.$set(this.list[index], 'editMode', false)
    },
    handleConfirm (index) {
      this.$set(this.list[index], 'editMode', false)
    },
    splitString (val) {
      if (!val) {
        return ''
      }
      return val.split(',')
    },
  }
}
</script>

<style lang="scss" scoped>
.items-table {
  .item-img {
    width: 40px;
    height: 40px;
    background-color: #eee;
    display: block;
  }
  .sale-price {
    color: #FF6600;
  }

  .cost-price {
    color: #339900;
  }

  .price-input {
    ::v-deep input {
      padding: 0 5px;
    }
  }
}
</style>
