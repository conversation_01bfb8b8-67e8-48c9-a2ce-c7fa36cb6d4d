<template>
  <div class="reward_setting" v-loading="loading">
    <el-button style="margin-bottom: 10px;float: right;" @click="setRewardSettingList">刷新</el-button>
    <RewardSettingTable :tableData="list" @reload="setRewardSettingList" />
  </div>
</template>

<script>
import { getRewardSettingList } from '@/api/retailStore';
import RewardSettingTable from "./rewardSettingTable.vue";

export default {
  name: "rewardSetting",
  components: {
    RewardSettingTable
  },
  data() {
    return {
      list: [],
      loading: false,
      saveLoading: false,
      rewardTypeCode: null,
      checkboxValue: false,
      listTotal: 0,
    }
  },
  created() {
    this.setRewardSettingList()
  },
  methods: {
    setRewardSettingList() {
      this.loading = true
      getRewardSettingList().then(res => {
        this.rewardTypeCode = null
        this.list = res
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>