<template>
  <div class="customerCard">
    <div class="totalContent">
      <div class="item">
        <div class="title">
          客户资质警告数
          <el-tooltip
            content="商品资质警告数包含即将过期数、已过期数"
            placement="bottom-start"
            effect="light"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip>
        </div>
        <div class="number">{{ merchant.qualificationMerchant }}</div>
      </div>
      <!-- <div class="item">
        <div class="title">
          客户资质警告数
        </div>
        <div class="number">{{ merchant.passMerchant }}</div>
      </div> -->
    </div>
    <div class="cardTitle">客户消费排行</div>
    <div class="togContent">
      <div class="togtap">
        <el-radio @change="getMerchantList" v-model="togTab" label="1"
          >按成交数量排行</el-radio
        >
        <el-radio @change="getMerchantList" v-model="togTab" label="2"
          >按成交金额排行</el-radio
        >
      </div>
      <selectTime
        @getDetail="getMerchantList"
        :orderTypes="false"
        :Query.sync="query"
      ></selectTime>
    </div>
    <div>
      <el-table
        ref="table"
        v-if="list"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" width="80" label="排名">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import selectTime from "@/views/dashboard/cards/components/selectTime";
import { merchantData, merchantList } from "@/api/dashboard";
const tableTitle = [
  { label: "客户名称", name: "purMerchantName", width: "170px" },
  { label: "企业类型", name: "merchantType", width: "170px" },
  { label: "所在地区", name: "detailAddress", width: "200px" },
  { label: "客户编码", name: "purMerchantCode", width: "120px" },
  { label: "成交数量", name: "transactionTotal", width: "100px" },
  { label: "成交金额", name: "transactionMoney", width: "100px" },
];
export default {
  data() {
    return {
      tableTitle,
      merchant: {},
      list: [],
      togTab: "1",
      listLoading: false,
      query: {},
    };
  },
  methods: {
    async getMerchantList(val) {
      this.listLoading = true;
      let obj = {
        current: 1,
        map: {},
        model: {
          endTime: this.query.endTime + " 23:59:59",
          startTime: this.query.startTime + " 00:00:00",
          transactionMoney: this.togTab == "2" ? "Y" : "N",
          transactionTotal: this.togTab == "1" ? "Y" : "N",
        },
        order: "descending",
        size: 10,
        sort: "id",
      }
      // 没时间就不传
      if (!this.query.startTime || !this.query.endTime) {
        obj.model.startTime = undefined
        obj.model.endTime = undefined
      }
      let { data } = await merchantList(obj);
      this.listLoading = false;
      this.list = data.records;
    },
    async getMerchante() {
      let { data } = await merchantData();
      this.merchant = data;
    },
  },
  created() {
    this.getMerchante();
  },
  components: {
    selectTime,
  },
};
</script>

<style lang="less" scoped>
.customerCard {
  .totalContent {
    background-color: #f7f7f8;
    padding: 20px 30px;
    display: flex;
    border-radius: 5px;
    .item {
      flex: 1;
      .number {
        color: #0f1831;
        font-size: 24px;
        padding-top: 10px;
      }
      .title {
        vertical-align: top;
        font-size: 14px;
        color: #051632;
        img {
          width: 16px;
          vertical-align: top;
        }
      }
    }
  }
  .cardTitle {
    color: #1e2439;
    font-size: 14px;
    padding-left: 30px;
    background-color: #f7f7f8;
    border-radius: 5px;
    margin-top: 20px;
    height: 40px;
    line-height: 40px;
    font-weight: 600;
  }
  .togContent {
    padding-left: 30px;
    display: flex;
    justify-content: space-between;
    height: 64px;
    line-height: 64px;
  }
}
.el-tooltip__popper.is-light {
  border: 1px solid #fff;
}
</style>

