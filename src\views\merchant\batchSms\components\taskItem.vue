<template>
  <div :class="['task_item', activated ? 'activated' : '']" @click="clickItem">
    <div class="top">
      <el-tooltip class="item" effect="dark" :content="item.taskName" placement="bottom-end">
        <span class="task_name">{{ item.taskName }}</span>
      </el-tooltip>
      <el-tag :type="taskStatusType" size="mini">{{ taskStatus.desc }}</el-tag>
    </div>
    <div class="time">
      <span>创建时间</span>
      <span>{{ item.createTime }}</span>
    </div>
    <el-progress :status="taskStatusType" :percentage="percentage * 100" :format="()=>''" />
    <div class="progress_text">
      <span>进度:</span>
      <span>{{ item.sendProgress || '0' }}/{{ item.smsTotal || '0'}}</span>
    </div>
    <el-button type="text" icon="el-icon-document" size="mini" @click="addPerson">添加名单</el-button>
  </div>
</template>

<script>
export default {
  name: 'taskItem',
  props: {
    item: {
      type: Object,
      default: ()=>{}
    },
    activated: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    taskStatus() {
      if (this.item.taskStatus) {
        return this.item.taskStatus
      }
      return {}
    },
    taskStatusType() {
      const statusCode = this.taskStatus.code
      switch (statusCode) {
        case 'COMPLETED':
          return 'success'
        default:
          return 'warning'
      }
    },
    percentage() {
      if (this.item.sendProgress && this.item.smsTotal) {
        return this.item.sendProgress / this.item.smsTotal
      }
      return 0
    }
  },
  methods: {
    addPerson() {
      this.$emit('addPerson', this.item.id)
    },
    clickItem() {
      this.$emit('click', this.item.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.task_item {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px 16px 12px;
  box-shadow: 0 4px 16px 0 rgba(76,126,233,.16);
  margin-bottom: 10px;
  font-size: 12px;
  cursor: pointer;
  &.activated {
    border: 2px solid #3361ff
  }
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .task_name {
      font-weight: bold;
      width: 140px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: 16px;
    }
  }
  .time {
    display: flex;
    color: #909299;
    justify-content: space-between;
    margin-bottom: 4px;
  }
  .progress_text {
    font-size: 12px;
    color: #909299;
    display: flex;
    justify-content: space-between;
    margin: 4px 0;
  }
  ::v-deep .el-progress-bar {
    padding: 0;
  }
  ::v-deep .el-progress__text {
    display: none;
  }
}
</style>