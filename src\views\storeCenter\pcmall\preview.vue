<template>
  <div class="pcmall-content">
    <page-title title="商城装修" />
    <div class="businessbg" :style="{'background-image':  'url(' + backgroundPicUrl}">
      <div class="businesswrap wrap clearfix">
        <div class="buslogo" :style="{'background-image':  'url(' + require('@/assets/imgs/pcIndex/buslogo.png')}"></div>
        <div class="bustext">
          <h2>华东医药股份有限公司</h2>
          <dl>
            <dd>上架品种<em>12720</em><b></b></dd>
            <dd>起配金额<em>¥500</em></dd>
          </dl>
        </div>
        <div class="touchmerchant">
          <h4>联系商家</h4>
          <p>有任何问题欢迎电话或在线交谈</p>
          <div class="tchmtbtn clearfix">
            <a href="javascript:;" class="fl" id="telbtn"><i class="thicon_01"></i>电话联系</a>
            <a href="javascript:;" class="fr"><i class="thicon_02"></i>QQ交谈</a>
          </div>
        </div>
      </div>
    </div>
    <div class="businessGroup">
      <div class="busHd">
        <div class="wrap clearfix">
          <ul>
            <li>首页</li>
            <li>全部商品</li>
            <li>商家资质</li>
          </ul>
          <div class="bussearch">
            <input type="text" name="" id="" value="" placeholder="搜索店铺商品">
            <a href="javascript:;" class="busseabtn"><i></i></a>
          </div>
        </div>
      </div>
      <div class="busBd wrap">
        <div class="busBox">
          <div class="ShopNotices">
            <h2><i class="spnticon"></i>店铺公告</h2>
            <div class="spnttext">
             <div v-html="content"></div>
            </div>
          </div>
          <div class="Coupondiscount">
            <h2>领券优惠</h2>
            <div class="couplist clearfix">
              <ul>
                <li>
                  <div class="coupname">
                    <b>9.5</b>
                    <em>折</em>
                    无使用门槛
                  </div>
                  <div class="couptime">2021.03.22-2021.03.30</div>
                  <a href="javascript:;" class="coupdraw">立即领取</a>
                </li>
                <li>
                  <div class="coupname">
                    <span>¥</span>
                    <b>10</b>
                    满199元可用
                  </div>
                  <div class="couptime">2021.03.22-2021.03.30</div>
                  <a href="javascript:;" class="coupdraw">立即领取</a>
                </li>
                <li>
                  <div class="coupname">
                    <b>9.5</b>
                    <em>折</em>
                    无使用门槛
                  </div>
                  <div class="couptime">2021.03.22-2021.03.30</div>
                  <a href="javascript:;" class="coupdraw">立即领取</a>
                </li>
                <li>
                  <div class="coupname">
                    <span>¥</span>
                    <b>10</b>
                    满199元可用
                  </div>
                  <div class="couptime">2021.03.22-2021.03.30</div>
                  <a href="javascript:;" class="coupdraw">立即领取</a>
                </li>
                <li>
                  <div class="coupname">
                    <b>9.5</b>
                    <em>折</em>
                    无使用门槛
                  </div>
                  <div class="couptime">2021.03.22-2021.03.30</div>
                  <a href="javascript:;" class="coupdraw">立即领取</a>
                </li>
              </ul>
            </div>
            <div class="Congratulations">
              <i class="success"></i>
              恭喜您，领取成功
            </div>
          </div>
          <div class="productGroup">
            <div class="pdHd clearfix">
              <ul>
                <li :class="{ 'on': proIndex === index }" v-for="(item, index) in pageDataProductGroupVoList" :key="index" @click="proIndex = index">{{ item.name }}</li>
              </ul>
            </div>
            <div class="pdBd">
              <div class="pdBox clearfix">
                <div class="gooscontent clearfix">
                  <ul v-if="pageDataProductGroupVoList.length > 0">
                    <li v-for="(item, index) in pageDataProductGroupVoList[proIndex].pageDataProductGroupRelVoList" :key="index">
                      <div class="recommenimg" :style="{'background-image':  'url(' +item.product.pictIdS.split(',')[0]}">
                      </div>
                      <div class="recommentext">
                        <h4 class="title"><a href="#">{{ item.product.productName }}</a></h4>
                        <div class="lable">{{ item.product.spec }}</div>
                        <div class="text">{{ item.product.manufacturer }}</div>
                        <div class="price">￥<b>0.00</b></div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPageData } from '@/api/pcmall'
export default {
  data() {
    return {
      goodsList: [{},{},{},{},{},{},{},{},{},{},],
      activeName: '',
      activeType: '',
      backgroundPicUrl: '',
      content: '',
      pageDataProductGroupVoList: [],
      proIndex: 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getPageData().then(res => {
        this.backgroundPicUrl = res.data.backgroundPicUrl
        this.content = res.data.content.replace(/\n/g,'<br/>')
        this.pageDataProductGroupVoList = res.data.pageDataProductGroupVoList
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/home.scss'
</style>
