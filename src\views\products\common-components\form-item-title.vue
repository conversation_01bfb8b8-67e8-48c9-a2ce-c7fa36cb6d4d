<template>
  <div class="form-item-title">
    <div class="title">{{ title }}</div>
    <div class="description">{{ description }}</div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item-title{
  height: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
  padding-left: 10px;
  border-left: 3px solid $--color-primary;
  .title{
    font-size: 16px;
    margin-right: 10px;
  }
  .description{
    font-size: 14px;
  }
}
</style>
