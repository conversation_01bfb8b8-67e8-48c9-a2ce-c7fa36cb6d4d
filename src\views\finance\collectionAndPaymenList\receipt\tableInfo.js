export default {
  WAIT: [
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '190px',
      disabled: true
    },
    {
      key: 8,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 4,
      label: '付款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应收金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 9,
      label: '交易单号',
      name: "flowPayOrderNo",
      width: '170px'
    },
    {
      key: 6,
      label: '支付方式',
      name: "method",
      width: '170px'
    },
    {
      key: 7,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 0,
      label: '收款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '收款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '付款方类型',
      name: "payerType",
      width: '170px'
    },
  ],
  FINISH: [
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '190px',
      disabled: true
    },
    {
      key: 8,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 4,
      label: '付款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应收金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 10,
      label: '审核时间',
      name: "reviewTime",
      width: '170px'
    },
    // {
    //   key: 9,
    //   label: '审核人',
    //   name: "reviewUserName",
    //   width: '100px'
    // },
    {
      key: 11,
      label: '审核备注',
      name: "remarks",
      width: '200px'
    },
    {
      key: 9,
      label: '交易单号',
      name: "flowPayOrderNo",
      width: '170px'
    },
    {
      key: 6,
      label: '支付方式',
      name: "method",
      width: '170px'
    },
    {
      key: 7,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 0,
      label: '收款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '收款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '付款方类型',
      name: "payerType",
      width: '170px'
    }
  ],
  COLSE: [
    {
      key: 2,
      label: '关联业务单号',
      name: "businessNo",
      width: '190px',
      disabled: true
    },
    {
      key: 8,
      label: '制单时间',
      name: "createTime",
      width: '170px'
    },
    {
      key: 4,
      label: '付款方',
      name: "payerName",
      width: '170px'
    },
    {
      key: 5,
      label: '应收金额（元）',
      name: "paymentAmount",
      width: '160px'
    },
    {
      key: 9,
      label: '交易单号',
      name: "flowPayOrderNo",
      width: '170px'
    },
    {
      key: 6,
      label: '支付方式',
      name: "method",
      width: '170px'
    },
    {
      key: 7,
      label: '制单人',
      name: "createUserName",
      width: '120px'
    },
    {
      key: 0,
      label: '收款单类型',
      name: "type",
      width: '140px',
      disabled: true
    },
    {
      key: 1,
      label: '收款单单号',
      name: "id",
      width: '200px',
      disabled: true
    },
    {
      key: 3,
      label: '付款方类型',
      name: "payerType",
      width: '170px'
    }
  ]
}