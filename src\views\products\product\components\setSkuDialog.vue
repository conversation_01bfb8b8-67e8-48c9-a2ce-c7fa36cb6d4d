<template>
  <el-dialog title="修改库存" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form">
      <div class="sku_setting" v-loading="loading">
        <div style="margin-bottom: 10px;">是否同步库存</div>
        <el-radio-group v-model="form.skuSyncType">
          <el-radio :label="item.label" v-for="(item, index) in skuData" :key="index">
            {{ item.name }}
          </el-radio>
        </el-radio-group>
        <div style="margin: 10px 0">【{{ productName }}】的库存</div>
        <el-input-number :disabled="form.skuSyncType === 'ERP_SYNC'" style="width: 100%;" v-model="form.stockQuantity" :controls="false" :min="0" placeholder="请输入内容" />
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { productSkuSyncType } from '@/api/products/product/index';

export default {
  name: 'setSkuDialog',
  data() { 
    return {
      visible: false,
      loading: false,
      productName: '',
      form: {
        ids: [],
        skuSyncType: '',
        realStockQuantity: '',
      },
      skuData: [
        { name: '是', label: 'ERP_SYNC' },
        { name: '否', label: 'LOCAL_SYNC' }
      ]
    }
  },
  methods: {
    show(row) {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.productName = row.productName
      this.form = {
        ids: [ row.id ],
        skuSyncType: row.skuSyncType.code,
        stockQuantity: row.realStockQuantity,
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.loading = true
      productSkuSyncType({...this.form, stockQuantity: this.form.stockQuantity.toString()}).then(res => {
        if (res.code !== 0) return
        this.$message.success('设置成功')
        this.hide()
        this.$emit('setSkuSuccess')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sku_setting {
  display: flex;
  flex-direction: column;
}
</style>
