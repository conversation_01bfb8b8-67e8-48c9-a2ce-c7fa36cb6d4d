<template>
  <div class="product-list">
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="queryParams"
      @reset="resetFilter"
      @search="load"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="queryParams.productCode" placeholder="请输入商品编码"/>
      </im-search-pad-item>
      <im-search-pad-item prop="erpCode">
        <el-input v-model.trim="queryParams.erpCode" placeholder="请输入ERP商品id"/>
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="queryParams.productName" placeholder="请输入商品名称"/>
      </im-search-pad-item>
      <im-search-pad-item prop="drugName">
        <el-input v-model.trim="queryParams.drugName" placeholder="请输入通用名称"/>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="categoryCode">
        <el-cascader
          v-model="queryParams.categoryCode"
          style="margin-right: 20px"
          placeholder="所在分类"
          clearable
          :options="categoryOpts"
          :props="{ checkStrictly: true, emitPath: false, label: 'name', value: 'code' }"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="busOption">
        <el-select v-model="busOption" placeholder="运营参数" clearable>
          <el-option v-for="item in busOptions" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="stockCondition">
        <el-select v-model="queryParams.stockCondition" placeholder="库存" clearable>
          <el-option value="ALL" label="全部"/>
          <el-option value="WITH" label="有库存"/>
          <el-option value="WITHOUT" label="没有库存"/>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="sourceMerchantName">
        <el-input v-model.trim="queryParams.sourceMerchantName" placeholder="请输入来源对象"/>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据来源" clearable>
          <el-option value="LOCAL" label="本地创建"/>
          <el-option value="IMPORT" label="本地导入"/>
          <el-option value="ERP" label="erp对接"/>
          <el-option value="SHARE" label="分销共享"/>
          <el-option value="SYNC" label="供方同步"/>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="product-list-container">
      <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        v-model="currentTab"
        @change="handleChangeTab"
      >
        <template slot="button">
          <div>
            <el-button v-if="currentTab === 1&&checkPermission(['admin','sale-saas-distributed-product:putOnSale','sale-platform-distributed-product:putOnSale'])"
                      :disabled="!selects.length" @click="handleBatchPutOnSale">批量上架
            </el-button>
            <el-button v-if="currentTab === 0&&checkPermission(['admin','sale-saas-distributed-product:pullOffShelves','sale-platform-distributed-product:pullOffShelves'])"
                      :disabled="!selects.length" @click="handleBatchPullOffShelves">批量下架
            </el-button>
            <el-button v-if="checkPermission(['admin','sale-saas-distributed-product:export'])" @click="outExcel">导出</el-button>
            <el-button @click="load">刷新</el-button>
          </div>
        </template>
      </tabs-layout>
      <transfer-table
        :table-options="allColumn"
        :table-data="tableData"
        :loading="loading"
        @select="onSelect" @select-all="onAllSelect"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column slot="images" label="产品主图" align="center" class-name="img-cell">
          <template slot-scope="scope">
            <img :src="scope.row.pictIdS|imgFilter" width="50px" height="50px">
          </template>
        </el-table-column>
        <el-table-column slot="whetherOnSale" label="销售状态">
          <template slot-scope="scope">
            <span>{{ scope.row.whetherOnSale.code === 'Y'?'已上架':'已下架' }}</span>
          </template>
        </el-table-column>
        <el-table-column slot="sourceType" label="数据来源">
          <template slot-scope="scope">
            <span>{{ dealSource(scope.row.sourceType) }}</span>
          </template>
        </el-table-column>
        <el-table-column slot="operator" fixed="right" label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-row class="table-edit-row">
              <span v-if="checkPermission(['admin','sale-saas-distributed-product:detail','sale-platform-distributed-product:detail'])" class="table-edit-row-item">
                <el-button type="text"
                          @click="$router.push({ path: '/productCenter/productsManagement/show', query: { id: scope.row.id ,marketing:'FRANCHISES'} })">查看详情</el-button>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </transfer-table>
      <div class="pagination">
        <div class="pagination-total">共{{ total }}条记录&nbsp;&nbsp;第{{ page }}/{{ totalPage }}页</div>
        <el-pagination
          background
          :total="total"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          layout="prev, pager, next, sizes, jumper"
          @current-change="load"
          @size-change="load"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import _ from 'lodash'
  import tableInfo from "@/views/products/product/tableInfo";
  import request from '@/utils/request'
  import transferTable from '@/components/TransferTable/index'
  import checkPermission from '@/utils/permission'
  import {downloadFile} from "@/utils/commons";
  export default {
    name: 'distributionGoods',
    components: {
      transferTable
    },
    data() {
      return {
        isExpand: false,
        allColumn: [
          {
            prop: 'images',
            label: '产品主图',
            slot: true
          },
          //新增字段
          {
            prop: 'productCode',
            label: '商品编码',
            width: 180
          },
          {
            prop: 'erpCode',
            label: 'ERP商品编码',
            width: 120
          },
          {
            prop: 'productName',
            label: '商品名称',
            width: 200
          },
          {
            prop: 'drugName',
            label: '通用名称',
            width: 120
          },
          {
            prop: 'spec',
            label: '规格',
            width: 120
          },
          {
            prop: 'agentiaType',
            label: '剂型'
          },
          {
            prop: 'manufacturer',
            label: '生产厂家',
            width: 260
          },
          {
            prop: 'area',
            label: '产地',
            width: 200
          },
          {
            prop: 'unit',
            label: '单位',
            width: 80
          },

          {
            prop: 'realStockQuantity',
            label: '实际库存'
          },
          {
            prop: 'stockQuantity',
            label: '可卖库存'
          },
          {
            prop: 'salePrice',
            label: '终端销售价',
            width: 120,
            className: 'salePrice'
          },

          {
            prop: 'costPrice',
            label: '成本价',
            width: 120,
            className: 'costPrice'
          },
          {
            prop: 'grossProfitRate',
            width: 120,
            label: '毛利率'
          },
          {
            prop: 'grossProfit',
            label: '毛利',
            className: 'grossProfit',
            width: 120
          },
          {
            prop: 'whetherOnSale',
            label: '销售状态',
            slot: true
          },
          {
            prop: 'approvalStatus.desc',
            label: '审核状态'
          },


          {
            prop: 'marketing.desc',
            label: '销售方式',

          },
          {
            prop: 'whetherShare.desc',
            label: '是否共享',
          },

          {
            prop: 'supplyPrice',
            label: '供货价',
            className: "salePrice"
          },
          {
            prop: 'wholesalePrice',
            label: '建议批发价',
            width: 120

          },
          {
            prop: 'categoryPathName',
            label: '商品分类',
            width: 250
          },
          {
            prop: 'barCode',
            label: '商品条形码',
            width: 120
          },
          {
            prop:'sourceType',
            label:'数据来源',
            width:120,
            slot: true
          },
          {
            prop:'sourceMerchantName',
            label:'来源对象',
            width:120
          },
          {
            prop: "sourceProductCode",
            label:"关联商品",
            width: 120
          },
          {
            prop: 'updateTime',
            label: '最新操作时间',
            width: 155
          },
          {
            prop: 'operator',
            label: '操作',
            slot: true,
            show: true
          }
        ], // 所有表列
        columns: {}, // 要显示的列
        loading: false,
        busOption: '',
        queryParams: {
          drugName: '',
          productCode: '',
          productName: '',
          categoryCode: '',
          stockCondition: 'ALL',
          erpCode: '',
          sourceMerchantName:'',
          sourceType:''
        },
        categoryOpts: [],
        currentTab: 0,
        tableData: [],
        page: 1,
        pageSize: 10,
        totalPage: 1,
        total: 0,
        selects: [],
        busOptions: [
          {
            label: '可现金交易',
            value: 'whetherCashTransaction:Y'
          },
          {
            label: '不可现金交易',
            value: 'whetherCashTransaction:N'
          },
          {
            label: '可退货',
            value: 'whetherReturnable:Y'
          },
          {
            label: '可共享分销',
            value: 'whetherShare:Y'
          },
          {
            label: '不可共享分销',
            value: 'whetherShare:N'
          }
        ],
        tableTitle: [],
      }
    },
    computed: {
      tabs() {
        return [
          {name: '已上架', value: 'ONSALE', hide: !checkPermission(['admin', 'sale-saas-distributed-product:putOnSaleView','sale-platform-distributed-product:putOnSaleView'])},
          {name: '已下架', value: 'NOTONSALE', hide: !checkPermission(['admin', 'sale-saas-distributed-product:pullOffShelvesView','sale-platform-distributed-product:pullOffShelvesView'])},
          {name: '待审核', value: 'PENDING', hide: !checkPermission(['admin','sale-saas-distributed-product:pendingView','sale-platform-distributed-product:pendingView'])},
          {name: '已驳回', value: 'REJECTED', hide: !checkPermission(['admin', 'sale-saas-distributed-product:rejectedView','sale-platform-distributed-product:rejectedView'])}
        ]
      }
    },
    watch: {
      currentTab() {
        this.$nextTick(() => {
          // 重置
          this.page = 1
          this.pageSize = 10
          this.totalPage = 1
          this.total = 0
          // 重新请求
          this.load()
        })
      }
    },
    mounted() {
      this.load()
      this.loadCategories()
      this.initTbaleTitle()
    },
    methods: {
      checkPermission,
      resetData() {
        this.page = 1
        this.pageSize = 10
        this.totalPage = 1
        this.total = 0
        this.selects = []
        this.tableData = []
        this.queryParams.drugName = ''
        this.queryParams.productCode = ''
        this.queryParams.productName = ''
        this.queryParams.categoryCode = ''
        this.queryParams.erpCode = ''
        this.queryParams.stockCondition = 'ALL'
        this.queryParams.sourceMerchantName = ''
        this.queryParams.sourceType = ''
        this.categoryId = ''
        this.busOption = ''
      },
      async load() {
        if (this.tabs.every(item => item.hide)) return
        this.loading = true
        const model = {
          ...this.queryParams,
          marketing:"FRANCHISES"
        }
        if (this.tabs[this.currentTab].value === 'ONSALE') {
          model.whetherOnSale = 'Y'
        } else if (this.tabs[this.currentTab].value === 'NOTONSALE') {
          model.whetherOnSale = 'N'
        } else {
          model.approvalStatus = this.tabs[this.currentTab].value
        }
        if (this.busOption) {
          const arr = this.busOption.split(':')
          model[arr[0]] = arr[1]
        }
        try {
          const {data} = await request.post('product/admin/product/page', {
            current: this.page,
            size: this.pageSize,
            map: {},
            model,
            order: 'descending',
            sort: 'createTime'
          })
          this.tableData = data.records
          this.page = data.current
          this.totalPage = data.pages
          this.total = data.total
        } catch (e) {

        }
        this.loading = false
      },
      dealSource(type){
        let text = '';
        switch (type) {
          case 'LOCAL':
            text = "本地创建"
            break;
          case 'IMPORT':
            text = "本地导入"
            break;
          case 'ERP':
            text = "erp对接"
            break;
          case 'SHARE':
            text = "分销共享"
            break;
          case 'SYNC':
            text = "供方同步"
            break;
        }
        return text
      },
      handleChangeTab(tab, index) {
        this.currentTab = index
      },

      splitString(val) {
        if (!val) {
          return ''
        }
        return val.split(',')
      },

      async loadCategories() {
        try {
          const {data} = await request.post('product/admin/category/query', {})
          const map = _.groupBy(data, 'parentId')

          this.categoryOpts = this._buildTree(0, 0, map)
        } catch (e) {

        }
      },

      _buildTree(parentId, level, map) {
        const list = map[parentId]

        if (!list) {
          return null
        }

        return list.map(item => ({
          id: item.id,
          code: item.categoryCode,
          name: item.label,
          sort: item.sortValue,
          frontShow: _.get(item, 'whetherShowFrontend.code') === 'Y',
          parentId: item.parentId,
          level,
          children: this._buildTree(item.id, level + 1, map)
        })).sort((a, b) => a.sort - b.sort)
      },

      resetFilter() {
        this.resetData()
        this.load()
      },

      handleSelectionChange(selects) {
        this.selects = selects
      },

      async handleBatchReject() {
        this.loading = true
        try {
          await request.post('product/admin/product/batchRejectProduct', {
            list: this.selects.map(item => ({
              id: item.id,
              approvalStatus: 'REJECTED',
              rejectReason: ''
            }))
          })
        } catch (e) {

        }
        this.loading = false

        this.load()
      },

      async handleBatchPending() {
        this.loading = true
        try {
          await request.post('product/admin/product/batchRejectProduct', {
            list: this.selects.map(item => ({
              id: item.id,
              approvalStatus: 'PENDING',
              rejectReason: ''
            }))
          })
        } catch (e) {

        }
        this.loading = false

        this.load()
      },

      async handleBatchPutOnSale() {
        this.$confirm("您确定批量上架这些商品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async ()=>{
          this.loading = true;
          try {
            await request.post('product/admin/product/batchPutOnSale', null, {
              params: {
                ids: this.selects.map(item => item.id)
              }
            })
          } catch (e) {

          }
          this.loading = false;
          this.load();
        })
      },
      initTbaleTitle() {
        let arr = []
        tableInfo["ACCEPTED"].forEach(item => {
          if (item.name != "id") {
            arr.push(item)
          }
        })
        this.tableTitle = arr

      },
      //  导出档案
      async outExcel() {
        if (this.selects.length > 0) {
          const tHeader = ["id"];
          const filterVal = ["id"];
          this.tableTitle.forEach(function (item) {
            tHeader.push(item.label);
            filterVal.push(item.name);
          });
          let exportData = this.formatJson(this.selects, filterVal);
          downloadFile({
            tHeader: tHeader,
            fileName: "商品列表",
            exportData: exportData,
          });
        } else {
          this.$message.error("请选择数据");
        }
      },
      formatJson(dataList, filterVal) {
        return dataList.map((v) =>
          filterVal.map((j) => {
            // if (j == "marketing") {
            //   return v[j].desc
            // }
            if (j == "whetherShare") {
              return v[j].desc
            }
            if (j == "approvalStatus") {
              return v[j].desc
            }
            if (j == "whetherOnSale") {
              return v[j].desc
            }
            return v[j]
          })
        );
      },
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect: function (val) {
        this.selects = val;
      },
      async handleBatchPullOffShelves() {
        this.$confirm("您确定批量下架这些商品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async ()=>{
          this.loading = true;
          try {
            await request.post('product/admin/product/batchPullOffShelves', null, {
              params: {
                ids: this.selects.map(item => item.id)
              }
            })
          } catch (e) {

          }
          this.loading = false;
          this.load();
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .product-list {
    &-container {
      background-color: #ffffff;
      padding: 8px 20px 20px;
      margin-top: 16px;

      .product-list-tabs-wrapper {
        position: relative;

        .product-list-tabs {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #dcdde0;
          font-size: 14px;
          color: #303133;
          margin-bottom: 16px;

          .tab {
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            margin-right: 9px;
            position: relative;
            cursor: pointer;

            &:after {
              content: '';
              display: block;
              width: 100%;
              height: 2px;
              background-color: #1890ff;
              position: absolute;
              bottom: -1px;
              left: 0;
              display: none;
            }

            &.active:after {
              display: block;
            }
          }
        }

        .operations {
          position: absolute;
          right: 0;
          bottom: 8px;
        }
      }
    }

    .el-table {
      ::v-deep {
        td.salePrice {
          color: #FF6600;
        }

        td.costPrice {
          color: #339900;
        }

        td.grossProfit {
          color: #339900;
        }
      }
    }

    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #505465;
      font-size: 14px;
      margin-top: 20px;
    }
  }
</style>

<style lang="scss">
  td.salePrice {
    color: #FF6600;
  }

  td.costPrice {
    color: #339900;
  }

  td.grossProfit {
    color: #339900;
  }
</style>

