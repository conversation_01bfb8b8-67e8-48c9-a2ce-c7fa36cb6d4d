<template>
  <div>
    <el-form ref="form" :model="form" label-width="80px" label-position="right" v-loading="loading" >
      <div class="price-information">
        <el-row>
          <el-col :span="20">
            <el-form-item label="控销类型: ">
              <!-- <el-radio-group v-model="form.visible">
                <el-radio :label="1">可见商品但不可见价格</el-radio>
                <el-radio :label="2">不可见商品</el-radio>
              </el-radio-group> -->
              <el-radio-group v-model="form.market">
                <el-radio :label="2">仅销</el-radio>
                <el-radio :label="1">禁销</el-radio>
              </el-radio-group>
              <el-tooltip placement="right" effect="light">
                <div slot="content">
                  1、选择“仅销”，则仅在控销区域、控
                  <br/>
                  销分组范围内的客户方可购买该商品,
                  <br/>
                  非范围内禁止购买
                  <br/>
                  2、选择“禁销”，则在控销区域、控销
                  <br/>
                  分组范围内的客户方禁止购买该商品,
                  <br/>
                  非范围内可购买。
                </div>
                <i style="margin-left:10px" class="el-icon-question"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <div class="addBtn">
              <el-button type="primary" @click="openAreaDialog">+ 新增控销</el-button>
            </div>
          </el-col>
        </el-row>


        <el-table :data="form.productForbidDetails" border height="274">
          <el-table-column label="序号" type="index" width="50" />

          <el-table-column label="省份" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="area" v-if="row.isAllNational == 0">
                {{ row.label && row.label.length > 0 ? row.label : '请选择省份' }}
              </span>
              <span class="area" v-if="row.isAllNational == 1">全国</span>
              <el-button type="text" @click="openAreaDialog" icon="el-icon-edit"></el-button>
            </template>
          </el-table-column>

          <el-table-column label="城市" show-overflow-tooltip>
            <template slot-scope="{row}">
              <div v-if="row.isAllNational == 0">
                <span class="area" v-if="row.isAll">全部市</span>
                <span class="area" v-else v-for="(item,index) in row.cityList"
                  :key="index">{{index>0?",":""}}{{ item.label }}</span>
              </div>
              <span class="area" v-if="row.isAllNational == 1">全国</span>
            </template>
          </el-table-column>

          <el-table-column label="区" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span v-if="row.isAllNational == 0">已选择{{ row.districtList.length || 0 }}个区</span>
              <span class="area" v-if="row.isAllNational == 1">全国</span>
            </template>
          </el-table-column>

          <el-table-column label="客户类型" width="200">
            <template slot-scope="{row}">
              <el-select v-model="row.customerType" class="multiple-select" clearable multiple collapse-tags
                placeholder="请选择" @change="onTypeChange(row, 'customerType')">
                <el-option v-for="item in tGroups" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </template>
          </el-table-column>


          <el-table-column label="操作" width="80" fixed="right">
            <template slot-scope="{row}">
              <el-button type="text" @click="handleDel(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row>

          <el-col style="margin-top:10px" :span="20">
            <!-- 控销分组 -->
            <el-form-item label="控销分组: " style="margin-bottom:10px">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            </el-form-item>

            <el-form-item prop="checkField" label="" style="margin-bottom:10px">
              <el-checkbox-group v-model="groupIds" @change="checkListFun">
                <el-checkbox v-for="item in cGroups" :key="item.id" :label="item.id"
                  style="width:16%;margin-bottom:5px">
                  <span>{{item.name}}</span>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <!-- 控销分组 end -->
            <!-- 商品展示 -->
            <el-form-item label="商品展示: ">
              <el-radio-group v-model="form.visible">
                <el-radio :label="1">可见商品但不可见价格</el-radio>
                <el-radio :label="2">不可见商品</el-radio>
              </el-radio-group>
              <el-tooltip placement="right" effect="light">
                <div slot="content">
                  1、选择“可见商品，不可见价格”，则
                  <br/>
                  不在销售范围内的客户可以看到该商
                  <br/>
                  品，但不可见销售价
                  <br/>
                  2、选择“不可见商品”，则不在销售范
                  <br/>
                  围内的客户不可以看到该商品。
                </div>
                <i style="margin-left:10px" class="el-icon-question"></i>
              </el-tooltip>
            </el-form-item>
            <!-- 用户标签 -->
            <el-form-item label="客户标签: " class="user-tags">
              <el-tag
                :key="tag.customerTagId"
                v-for="(tag,index) in dynamicTags"
                closable
                :disable-transitions="false"
                @close="handleClose(index)">
                {{tag.tagName}}
              </el-tag>
              <el-button class="button-new-tag" size="small"  @click="addUserTag">+</el-button>
            </el-form-item>

            <!-- 商品展示 end -->
          </el-col>
        </el-row>
      </div>
    </el-form>
    <area-tree-dialog ref="areaDialog" @on-confirm="getEreaData" :regionList="areaList" />
    <el-dialog title="客户标签" v-if="isVisibleUserTagsDialog" append-to-body width="50%" :visible.sync="isVisibleUserTagsDialog" :before-close="closeTagDialog">
      <clientTags :treeListInfo="customerTagList" :dynamicTags="dynamicTags" @closeTagDialog="closeTagDialog" @acceptClientTags="acceptClientTags"></clientTags>
    </el-dialog>
    <!-- 客户弹窗 -->
    <el-dialog title="选择客户" v-if="dialogStatus" append-to-body width="70%" :visible.sync="dialogStatus"
      :before-close="closeDialogFun">
      <costomer-list @closeDialogFun="closeDialogFun" @getSubmit="getSubmit" :dialogStatus="dialogStatus"
        :allType="allType" :costomerChack="currentRow.productForbidPurMerchants" :currentRow="currentRow"></costomer-list>
    </el-dialog>
  </div>
</template>


<script>
  import {
    map as _map,
    reduce as _reduce,
    cloneDeep as _cloneDeep,
  } from "lodash";
  import formItemTitle from "@/views/products/common-components/form-item-title";
  import {
    businessType
  } from "@/api/registercheck"
  import {
    merchantGroupList
  } from "@/api/group"
  import {
    detailForbid, getAllCustomerTagList
  } from "@/api/products/product/index"
  import areaTreeDialog from "@/components/multipleAreaTree";
  import costomerList from "@/views/products/product/product-components/dialogs/costomerList.vue"
  import clientTags from "./dialogs/client-tags.vue"
  export default {
    //import引入的组件
    components: {
      formItemTitle,
      areaTreeDialog,
      costomerList,
      clientTags
    },
    props: {
      currentId: {
        type: String,
        default: ''
      }
    },

    data() {
      return {
        form: {
          visible: 1, //1=可见商品不可见价格 2=不可见商品
          market: 2, //1=禁销客户 2 =仅销客户
          productForbidDetails: [],
        },
        groupIds:[],
        isAdd: true,
        allType: 1,
        dialogStatus: false,
        areaList: [],
        tGroups: [],
        cGroups: [],
        customerList: [],
        currentRow: {},
        editId:'',
        isIndeterminate: false,
        checkAll: false,
        timer: null,
        loading: false,
        dynamicTags: [],//用户选中的客户标签
        isVisibleUserTagsDialog:false,
        customerTagList:[],//所有的客户标签列表
        customerTagIds:[] //已经选中的控销分组
      }
    },
    created(){
      this.getAllCustomerTagList()
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.getCustomer(); //获取客户类型，用于控销的客户列表下拉框
      this.getCustomerGroup(); //控销分组查询
      this.getDetail(); //查询某一个id的控销详情
    },

    //方法集合
    methods: {
      getAllCustomerTagList(){
        const merchantsDetail = JSON.parse(sessionStorage.getItem('merchants-detail')) || {}
        let saleMerchantId = merchantsDetail.customerService.saleMerchantId || ''
        getAllCustomerTagList(saleMerchantId).then(res => {
          const {code,data} = res
          if(code === 0){
            this.customerTagList = data
          }
        })
      },
      // 过滤数据
      initDynamicTags(){
        this.dynamicTags = this.customerTagList.filter(item=>this.customerTagIds.includes(item.customerTagId))
      },
      // 用户标签
      handleClose(index ) {
        this.dynamicTags.splice(index, 1); // 使用splice来移除元素
      },
      // 打开客户标签弹窗
      addUserTag(){
        this.isVisibleUserTagsDialog = true
      },
      // 关闭客户标签弹窗
      closeTagDialog(){
        this.isVisibleUserTagsDialog = false
      },
      // 接受客户标签弹窗传递过来的选中的标签信息
      acceptClientTags(list){
        this.closeTagDialog()
        this.dynamicTags = list
      },
      // 全选按钮
      handleCheckAllChange(val){
        this.groupIds = [];
        this.cGroups.forEach(item=>{
          if(val) {
            this.groupIds.push(item.id);
          } else {
            this.groupIds = [];
          }
        });
        this.isIndeterminate = false;
      },
      checkListFun(e) {
          this.checkAll = e.length === this.cGroups.length;
          this.isIndeterminate = e.length > 0 && e.length < this.cGroups.length;
      },
      // 获取客户类型
      getCustomer() {
        businessType().then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.tGroups = [{
              id: "ALL",
              name: "全部类型"
            }, ...res.data]
          }
        })
      },
      // 获取客户分组 控销分组查询
      getCustomerGroup() {
        merchantGroupList({
          current: 1,
          size: 9999,
          model: {
            groupType:'CONTROL'
          },
        }).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.cGroups = [ ...res.data.records ];
          }
        })
      },
      openAreaDialog() {
        let list = [];
        if (this.form.productForbidDetails.length != 0) {
          this.form.productForbidDetails.forEach(item => {
            item.districtList.forEach(i => {
              list.push(i.id);
            })
          });
          this.areaList = list;
        }
        this.$refs.areaDialog.show();
      },
      handleDel(id) {
        this.$confirm("您确定要删除此控销数据吗？", '提示', {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.form.productForbidDetails = this.form.productForbidDetails.filter(item => {
            return item.id != id;
          })
        })
      },
      getDetail() {
        this.loading = true
        detailForbid(this.currentId).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            console.log("res.data != null && res.data !='null'", res.data != null && res.data != 'null');
            if (res.data != null && res.data != 'null') {
              this.isAdd = false;
              this.customerTagIds = res.data.customerTagIds || [] //客户标签id
              this.initDynamicTags() //初始化标签
              let {
                data
              } = res;
              let obj = {
                visible: data.visible, // 1=可见商品不可见价格 2=不可见商品
                market: data.market, // 1=禁销客户 2 =仅销客户
              };
              this.groupIds = data.groupIds || [];
              this.$nextTick(()=>{
                this.timer = setTimeout(()=>{
                  this.checkAll = this.cGroups.length === this.groupIds.length;
                  this.isIndeterminate = this.groupIds.length > 0 && this.groupIds.length < this.cGroups.length;
                },100)
              })
              this.editId = data.id;
              obj.productForbidDetails = data.productForbidDetails.map(item => {
                let dataObj = {};
                if(item.productForbidTypes != null) {
                  dataObj.customerType = item.productForbidTypes.map(i => {
                    return i.enterpriseTypeId;
                  });
                } else {
                  dataObj.customerType = [];
                }
                if(item.isAllType == 1){
                  dataObj.customerType.push("ALL")
                }
                if(item.productForbidGroups != null){
                  dataObj.customerGroup = item.productForbidGroups.map(i => {
                    return i.merchantGroupId;
                  });
                } else {
                  dataObj.customerGroup = [];
                }
                if(item.isAllGroup == 1) {
                  dataObj.customerGroup.push("ALL")
                }
                if (item.allType == 1) {
                  dataObj.allType = 1;
                } else {
                  dataObj.allType = item.allType || 0;
                }
                if (item.isAllNational == 1) {
                  dataObj.isAllNational = 1;
                  dataObj.label = '全国';
                } else {
                  // 不是全国
                  dataObj.isAllNational = item.isAllNational || 0;
                  dataObj.parentId = item.children.parentId;
                  dataObj.label = item.children.label;
                  dataObj.id = item.children.id;
                  dataObj.isAll = (item.children.allArea && item.children.allArea == 'ALL') ? true : false;
                  dataObj.children = item.children.children || [];
                  dataObj.cityList = item.children.children || [];
                  dataObj.districtList = [];

                  item.children.children.forEach(_ => {
                    dataObj.districtList = [...dataObj.districtList, ..._.children];
                  })
                };
                return dataObj;
              });
              this.form = obj;
            } else {
              // 没有设置过控销的
              this.isAdd = true;
            }
          }
        }).finally(() => {
          this.loading = false
        })
      },
      getEreaData(data, flag) {
        console.log('data---->', data);
        // flag 是true的时候就是全国，false就不是全国
        if (flag) {
          // 全国
          let allObj = {
            customerType: ['ALL'],
            customerGroup: ['ALL'],
            market: 2,
            productForbidPurMerchants: [],
            allType: 1,
            label: '全国',
            isAllNational: 1 //是全国
          };
          this.form.productForbidDetails.push(allObj);
        } else {
          let list = JSON.parse(JSON.stringify(this.form.productForbidDetails));
          console.log('list---->', list);
          let newList = [];
          let oldList = [];
          data.forEach(item => {
            let index = list.findIndex(_ => _.id == item.id);
            console.log('index--->', index);
            if (index == -1) {
              // 新增的省市区
              let newObj = {
                ...item,
                customerType: ['ALL'],
                customerGroup: ['ALL'],
                market: 2,
                productForbidPurMerchants: [],
                allType: 1,
                isAllNational: 0 //不是全国
              };
              newList.push(newObj);
            } else {
              // 修改的省市区
              let oldObj = {
                ...item,
                customerType: this.form.productForbidDetails[index].customerType || ['ALL'],
                customerGroup: this.form.productForbidDetails[index].customerGroup || ['ALL'],
                market: this.form.productForbidDetails[index].market,
                productForbidPurMerchants: this.form.productForbidDetails[index].productForbidPurMerchants || [],
                allType: this.form.productForbidDetails[index].allType,
                isAllNational: 0 //不是全国
              };
              oldList.push(oldObj);
            }
          });
          let listData = [...oldList, ...newList];

          this.form.productForbidDetails = this.dataDeal(listData);
        }
      },
      dataDeal(list) {
        let arr = JSON.parse(JSON.stringify(list));
        arr = arr.map(item => {

          let obj = {
            ...item,
            label: item.label,
            isAll: item.isAll,
            id: item.id,
            cityList: item.children,
            districtList: [],
            districtAll: false, //是否是全部区
          };
          item.children.forEach(_ => {
            obj.districtList = [...obj.districtList, ..._.children];
            if (_.isAll) {
              obj.districtAll = true;
            } else {
              obj.districtAll = false;
            }
          });
          return obj;
        });
        return arr;
      },
      // 禁销的点击事件
      handleProhibit(row) {
        console.log('------->', row);
        this.currentRow = row;
        this.allType = row.allType == 0 ? '0' : '1';
        this.dialogStatus = true;
      },
      //
      closeDialogFun() {
        this.dialogStatus = false;
      },
      onTypeChange(obj, key) {
        this.$nextTick(() => {
          if (obj[key][obj[key].length - 1] === "ALL") {
            obj[key] = ["ALL"];
          } else {
            obj[key] = obj[key].filter((item) => {
              return !(item === "ALL");
            });
          }
        });
      },
      getSubmit(list, flag) {
        console.log('list---->', list, flag, this.form.productForbidDetails);
        this.dialogStatus = false;
        let arr = JSON.parse(JSON.stringify(this.form.productForbidDetails));

        arr = arr.map(item => {
          if (item.id == this.currentRow.id) {
            item.productForbidPurMerchants = list;
            item.allType = flag;
          }
          return item
        });
        this.form.productForbidDetails = arr;
      },
      getData() {
        console.log('this.from', this.form);
        const merchantsDetail = JSON.parse(sessionStorage.getItem('merchants-detail'))
        let list = JSON.parse(JSON.stringify(this.form.productForbidDetails));
        let productForbid = {
          visible: this.form.visible,
          saleMerchantId: merchantsDetail.id,
          market: this.form.market,
          groupIds: this.groupIds || []
        };
        console.log('productForbid',productForbid);
        // return
        productForbid.productForbidDetails = list.map(item => {
          if (item.isAllNational == 1) {
            // 是全国
            item.children = null;
          } else {
            item.children = item.children.map(i => {
              i.parentId = i.parentId;
              i.label = i.label;
              i.id = i.id;
              i.children = i.children.map(o => {
                o.parentId = o.parentId;
                o.label = o.label;
                o.id = o.id;
                return o
              });
              return i;
            });
          }

          let obj = {
            allType: 1,
            isAllNational: item.isAllNational,
            // market: item.market,
            // for_num: item.allType == 0 ? item.productForbidPurMerchants.length : 0,
            children: item.isAllNational == 0 ? {
              id: item.id,
              label: item.label,
              parentId: item.parentId,
              children: item.children,
              allArea: item.isAll ? "ALL" : "notAll"
            } : null
          }
          // 客户类型分组
          obj.productForbidGroups = [];
          item.customerGroup.forEach(i => {
            if (i == 'ALL') {
              obj.isAllGroup = 1;
            } else {
              obj.isAllGroup = 0;
              obj.productForbidGroups.push(
                Object.assign({}, {
                  merchantGroupId: i
                })
              )
            }
          });
          // 控销客户
          // if(item.allType == 1) {
          //   // 全部客户
          //   obj.productForbidPurMerchants = [];
          // } else {
          //   obj.productForbidPurMerchants = item.productForbidPurMerchants.map(i => {
          //     return Object.assign({}, {
          //       purMerchantId: i
          //     })
          //   });
          // }
          // 控销客户类型
          obj.productForbidTypes = [];
          item.customerType.forEach(i => {
            if (i == "ALL") {
              obj.isAllType = 1;
            } else {
              obj.isAllType = 0;
              obj.productForbidTypes.push(
                Object.assign({}, {
                  enterpriseTypeId: i
                })
              )
            }
          })

          return obj
        });
        let result = {
          productForbid,
          isAdd: this.isAdd,
          productId: this.currentId,
          editId: this.editId,
          customerTagIds:  this.dynamicTags.map(item => item.customerTagId) || []
        };
        return result
        this.$forceUpdate()
      },
    },
    beforeDestroy() {
      clearTimeout(this.timer);
    }
  }

</script>


<style lang='scss' scoped>
  .price-information {

    .el-table {
      width: 100%;

      ::v-deep {
        .el-table__body-wrapper {
          .el-table__append-wrapper {
            text-align: center;
          }
        }

        .area {
          color: #505465;
        }

        .area.is-disabled {
          color: #505465;
          cursor: text;
        }

        .multiple-select {
          width: 100%;
        }

        .multiple-text {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  //客户标签
  ::v-deep .user-tags{
    .el-form-item__content{
      height: 36px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      padding-top: 5px;
    }
      .el-tag {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    .button-new-tag {
      height: 28px;
      line-height: 28px;
      padding: 0 9px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .addBtn {
    display: flex;
    justify-content: flex-end;
  }

</style>
