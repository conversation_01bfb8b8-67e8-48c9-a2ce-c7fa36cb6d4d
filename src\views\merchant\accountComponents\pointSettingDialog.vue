<template>
  <el-dialog title="积分设置" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="0" v-loading="showLoading">
      <div style="margin-bottom: 20px">积分消耗：</div>
      <el-form-item label="" prop="deduction">
        <el-checkbox v-model="form.pointsRange.deduction.code" true-label="Y" false-label="N">
          使用积分抵消订单金额，抵扣1.00元需积分
        </el-checkbox>
        <el-input-number v-model="form.points" :min="1" :step-strictly="true" label="请输入大于0的整数" :step="1" style="margin: 0 10px" />
      </el-form-item>
      <el-form-item label="" prop="exchange">
        <el-checkbox v-model="form.pointsRange.exchange.code" label="" true-label="Y" false-label="N">使用积分兑换礼品（积分商城）</el-checkbox>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCustomerPointSetting, setCustomerPointSetting } from '@/api/retailStore'

export default {
  name: 'pointSettingDialog',
  data() {
    return {
      visible: false,
      loading: false,
      showLoading: false,
      form: {
        id: '',
        points: '',
        pointsRange: {
          deduction: { code: '' },
          exchange: { code: '' },
        }
      },
      rules: {
        pointDeductionNum: [
          { required: true, message: '请输入要增加的积分', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    show() {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.showLoading = true
      this.visible = true
      getCustomerPointSetting().then(res => {
        if (!res.pointsRange?.deduction?.code) {
          res.pointsRange.deduction = { code: 'N' }
        }
        if (!res.pointsRange?.exchange?.code) {
          res.pointsRange.exchange = { code: 'N' }
        }
        this.form = res
      }).finally(() => {
        this.showLoading = false
      })
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        setCustomerPointSetting(this.form).then(() => {
          this.$message.success('设置成功')
          this.$emit('reload')
          this.hide()
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
