<template>
  <div class="detail-wrapper">
    <page-title :title="id ? '编辑优惠券' : '新增优惠券'">
      <span v-if="this.form.couponStatus" slot="icon"
        :class="{ 'status-box0': form.couponStatus.code == 'NOT_START', 'status-box1': form.couponStatus.code == 'PROCEED', 'status-box2': form.couponStatus.code == 'OBSOLETE', 'status-box3': form.couponStatus.code == 'FINISHED' }" />
      <template v-if="type !== 'check' || (form.couponStatus && form.couponStatus.code == 'PROCEED')">
        <el-button type="primary" @click="submit">保存</el-button>
      </template>
    </page-title>
    <el-form ref="ruleForm" label-width="120px" :model="form">
      <div v-loading="loading" class="detail-items">
        <page-module-card title="优惠券类型">
          <div class="coupon">
            <div v-for="(items, index) in couponList" :key="index" class="coupon-type"
              :class="{ 'coupon-active': active == index && !type, 'coupon-disable': active == index && type }"
              @click="chooseType(index)">
              <h4 :style="{ color: active == index && !type ? '#0056e5' : '' }">{{ items.label }}</h4>
              <p>{{ items.content }}</p>
            </div>
          </div>
        </page-module-card>
        <page-module-card title="基础信息">
          <el-form-item label="优惠券名称：" prop="couponName" :rules="[{ required: true, message: '请输入优惠券名称' }]">
            <el-input v-model="form.couponName" placeholder="请输入优惠券名称，最多20个字" :disabled="isDisabled" />
            <!-- 专享券 start -->
            <span style="padding: 5px;"></span>

            <el-checkbox v-model="form.exclusive" :disabled="isDisabled">专享券</el-checkbox>
            <el-popover placement="right" width="300" trigger="hover">
              <ul>
                <li>1、专享券发放：商家与平台达成战略合作后，按平台的要求执行发放专享券，专享券仅对会员有效。</li>
                <li>2、若平台未下达专享券发放任务，请勿擅自发放专享券。</li>
              </ul>
              <i slot="reference" style="color: #76838f" class="el-icon-question"></i>
            </el-popover>
            <!-- 专享券 end -->

          </el-form-item>
          <el-form-item label="发放数量：" prop="total" :rules="[{ required: true, message: '请输入发放数量' }]">
            <el-input v-model="form.total" placeholder="最多1000000张" :disabled="isDisabled" /> 张
          </el-form-item>
          <el-form-item label="使用门槛：" :prop="form.thresholdType == 'FULL' ? 'fullMoney' : 'thresholdType'" :rules="form.thresholdType == 'FULL' ? [{ validator: fullMoneyValidator, required: true, trigger: 'blur' }] : [{
            required: true, message: '请输入2金额'
          }]">
            <el-radio-group v-model="form.thresholdType" :disabled="isDisabled">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">无使用门槛</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="FULL">订单满
                  <el-input v-model="form.fullMoney" class="inline-input" :disabled="form.thresholdType == 'NONE'" />元
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="优惠内容：" :prop="active == 0 ? 'reduceMoney' : 'discount'" :rules="active == 0 ? [{ validator: fullMoneyValidator, required: true, trigger: 'blur' }] : [{
            validator: discountValidator, required: true, trigger: 'blur'
          }]">
            <div v-if="active == 0">
              减免
              <el-input v-model="form.reduceMoney" class="inline-input" :disabled="isDisabled" />元
            </div>
            <div v-else>

              打
              <el-input v-model="form.discount" class="inline-input" :disabled="isDisabled" />折 <span style="color: rgb(222, 126, 68);">（一折就是用原价乘以0.1）</span>
            </div>

          </el-form-item>
          <el-form-item label="用券时间：" :prop="form.useTimeType == 'ASSIGN' ? 'time' : 'useValidDay'" :rules="form.useTimeType == 'ASSIGN' ? [{
            required: true,
            message: '请选择用券时间',
            trigger: ['submit', 'change', 'input']
          }] : [{
            required: true, message: '请输入天数'
          }]">
            <el-radio-group v-model="form.useTimeType" :disabled="isDisabled">
              <div class="block-radio">
                <el-radio label="ASSIGN">
                  <el-date-picker v-model="form.time" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']" />
                </el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="DAY">
                  领券当日起
                  <el-input v-if="form.useTimeType === 'DAY'" v-model="form.useValidDay" class="inline-input" />
                  <el-input v-else class="inline-input" disabled />

                  天内可用
                  <el-popover placement="bottom" width="300" trigger="click"
                    content="有效期按自然天计算。举例：如设置领券当 日起2天内可用，用户在5月21日14:00时领 取优惠券，则该优惠券的可用时间为5月21 日的14:00:00至5月22日的23:59:59">
                    <el-button slot="reference" class="tips-btn">
                      <i class="el-icon-question" style="color: #999;" />
                    </el-button>
                  </el-popover>
                </el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="NEXT_DAY">
                  领券次日起
                  <el-input v-if="form.useTimeType === 'NEXT_DAY'" v-model="form.useValidDay" class="inline-input" />
                  <el-input v-else class="inline-input" disabled />
                  天内可用
                  <el-popover placement="bottom" width="300" trigger="click"
                    content="有效期按自然天计算。举例：如设置领券次 日起2天内可用，用户在5月21日的14:00领 取优惠券，则该优惠券的可用时间为5月22 日的00:00:00到5月23日的23:59:59">
                    <el-button slot="reference" class="tips-btn">
                      <i class="el-icon-question" style="color: #999;" />
                    </el-button>
                  </el-popover>
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="适用商品：" prop="productRangeType" :rules="[{ required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.productRangeType"
              :disabled="isDisabled && form.couponStatus && form.couponStatus.code != 'PROCEED'">
              <div class="block-radio block-radio-top"
                v-if="(isDisabled && form.couponStatus && form.couponStatus.code == 'PROCEED' && form.productRangeType == 'ALL') || (form.couponStatus && form.couponStatus.code != 'PROCEED') || type == 'add'">
                <el-radio label="ALL">全部商品可用</el-radio>
              </div>
              <div class="block-radio block-radio-none"
                v-if="(isDisabled && form.couponStatus && form.couponStatus.code == 'PROCEED' && form.productRangeType == 'PART') || (form.couponStatus && form.couponStatus.code != 'PROCEED') || type == 'add'">
                <el-radio label="PART">
                  指定商品可用
                </el-radio>
              </div>

            </el-radio-group>
            <div v-if="form.productRangeType == 'PART' || form.productRangeType == 'UN_PART'" style="margin-top:20px">
              <front-end-page ref="frontEndPage" @openProductModel="openProductModel" :data.sync="goodsList"
                :options="options" :pageSize="pageSize" :operation-width="120" :total="totalNum" :operationWidth="100" :currentPage="currentPage" :selection="true"
                @patchDelete="handleDelete" :disableHandle="disableHandle">
                  <el-table-column slot="pictIdS" label="产品主图" align="center" width="80">
                    <template v-slot="scope">
                      <el-image style="width:40px;height:40px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
                    </template>
                  </el-table-column>
                  <el-table-column slot="erpCode" width="180" label="ERP商品编码/仓库">
                    <template v-slot="{ row }">
                      <span>编码：{{ row.erpCode || '无' }}</span><br />
                      <span>仓库：{{ row.warehouseName || '无' }}</span>
                    </template>
                  </el-table-column>
                  <div slot-scope="scope" align="center">
                    <el-row class="table-edit-row">
                      <span class="table-edit-row-item">
                        <el-button type="text"
                          @click="handleDelete([scope.row])">
                          删除</el-button>

                      </span>
                    </el-row>
                  </div>
                <el-button :disabled="disableHandle" slot="moreHandleBtn" @click="handleImportProduct" v-if="checkPermission(['admin','couponDetailImportProduct'])">导入商品</el-button>
                </front-end-page>
            </div>

          </el-form-item>
          <el-form-item label="叠加规则：" prop="overlayRule">
            <el-checkbox-group v-model="overlayRule" :disabled="isDisabled" @change="checkboxChange">
              <el-checkbox v-for="(item, idx) in ruleList" :key="idx" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
            <div style="color:#de7e44">(说明：选中则代表可以叠加使用；不选则不会叠加使用)</div>
          </el-form-item>
          <el-form-item label="裂变奖励：" prop="productRangeType">
            <el-radio-group :disabled="type === 'check'" v-model="form.whetherReward.code">
              <el-radio v-for="item in whetherRewardData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <FissionRewardTip />
          </el-form-item>
          <el-form-item label="积分抵扣：" prop="productRangeType">
            <el-radio-group :disabled="isDisabled" v-model="form.whetherPointsDeduction.code">
              <el-radio v-for="item in pointDeductionData" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
            <PointDeductionTip />
          </el-form-item>
        </page-module-card>
        <page-module-card title="领取及使用规则">
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{ required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType" :disabled="isDisabled">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                  <el-checkbox-group v-if="form.limitObjectType == 'CUSTOMER_TYPE'" v-model="form.customerTypeIds"
                    style="margin-top: 10px;">
                    <el-checkbox v-for="(items, index) in merchantList" :key="index" :label="items.name" :value="items.id"/>
                  </el-checkbox-group>
                </el-radio>
              </div>
<!--              <div class="block-radio">-->
<!--                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>-->
<!--                <div v-if="form.limitObjectType == 'CUSTOMER_GROUP'" style="margin: 15px 0;">-->
<!--                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>-->
<!--                </div>-->
<!--                <el-table v-if="groupTableData.length > 0 && form.limitObjectType == 'CUSTOMER_GROUP'"-->
<!--                  :data="groupTableData" border>-->
<!--                  <el-table-column prop="name" label="客户分组" align="center" width="234" />-->
<!--                  <el-table-column prop="customerNumber" label="客户数量" align="center" width="120" />-->
<!--                  <el-table-column v-if="(!type && form.couponStatus.code == 'NOT_START') || type === 'add'" label="操作"-->
<!--                    align="center" width="52">-->
<!--                    <template slot-scope="scope">-->
<!--                      <el-button type="text" @click="deleteRow(scope.$index)">删除</el-button>-->
<!--                    </template>-->
<!--                  </el-table-column>-->
<!--                </el-table>-->
<!--              </div>-->
              <div class="block-radio">
                <el-radio label="CUSTOMER_TAG">指定客户标签可参与</el-radio>
                <div v-if="form.limitObjectType == 'CUSTOMER_TAG'" style="margin: 15px 0">
                  <el-checkbox-group v-model="form.customerLabelIds" :disabled="isDisabled">
                    <el-checkbox v-for="item in customerTag" :key="item.value" :label="item.value">{{ item.text }}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="每人限领次数：" prop="limitNumber" :rules="form.limitTimesType == 'Y' ? [{
            required: true, message: '请输入限制次数'
          }] : []">
            <el-radio-group v-model="form.limitTimesType" :disabled="isDisabled">
              <div class="block-radio block-radio-top">
                <el-radio label="N">不限次数</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="Y">
                  <el-input v-model="form.limitNumber" class="inline-input"
                    :disabled="form.limitTimesType == 'N' || isDisabled" />次
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="公开设置：" prop="limitOpenType">
            <el-checkbox v-model="form.limitOpenType" false-label="N" true-label="Y" :disabled="isDisabled">
              优惠券允许公开领取
            </el-checkbox>
            <p class="tips">开启后优惠券会在商品详情页及个性化推荐区域展示，买家可领取</p>
          </el-form-item>
          <el-form-item label="规则说明：">
            <el-input v-model="form.illustrate" type="textarea" rows="5" style="width: 300px;"
              :disabled="isDisabled" />
            <div>
              <el-popover placement="bottom" width="300" trigger="click"
                content="庆国庆优惠券 使用时间：2021-01-01 00:00:00 至 2021-01-31 23:59:59 优惠内容：部分商品，满88元8.8折优惠券 其他限制：仅原价购买商品时可用券">
                <el-button slot="reference" class="tips-btn">
                  <el-link type="primary">查看示例</el-link>
                </el-button>
              </el-popover>
            </div>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
    <div v-if="productVisible">
      <products-modal ref="products-modal" :productVisible="productVisible" @closeProductDia="closeProductDia" :check-list="goodsList" @change="handleAddProducts" />
    </div>
    <add-group :visible="showAdd" :sale-merchant-id="saleMerchantId" @changeShow="changeAddUser"
      :select-data="groupTableData" />
    <!--    导入商品弹窗-->
    <importDialog ref="importDialogRef" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="false" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import addGroup from '../components/addGroup.vue'
import detailItem from '@/views/merchant/list/detail-item'
import ProductsModal from '@/components/eyaolink/productModel/index'
import { listMerchantGroupBySaleMerchantId } from '@/api/group'
import { postGoodsList } from '@/api/promotion'
import checkPermission from '@/utils/permission'
import importDialog from "@/components/eyaolink/importDialog/index"
import _ from 'lodash';
import { page } from "@/api/merchantApi/customerlabel";
import { getPointDeductionData, getWhetherRewardData } from '@/views/products/product/components/data'
import FissionRewardTip from '@/views/promotion/components/fissionRewardTip.vue'
import PointDeductionTip from '@/views/promotion/components/pointDeductionTip.vue'
const TableColumns =[
  {
    prop: 'pictIdS',
    name: "pictIdS",
    label: '主图',
    slot: true
  },//新增字段
  {
    prop: 'erpCode',
    name: 'erpCode',
    label: 'ERP商品编码/仓库',
    width: 170,
		slot: true
  },
  {
    prop: 'productName',
    name: 'productName',
    width: 200,
    label: '商品名称',
  },
  {
    prop: 'manufacturer',
    name: 'manufacturer',
    width: 200,
    label: '生产厂家',
  },
  {
    prop: 'spec',
    name: 'spec',
    width: 100,
    label: '规格',
  },
  {
    prop: 'unit',
    name: 'unit',
    width: 100,
    label: '单位',
  },
  {
    prop: 'whetherUseCoupon.desc',
    name: 'whetherUseCoupon.desc',
    width: 100,
    label: '是否可用券'
  },
  // {
  //   prop: 'whetherReturnable.desc',
  //   name: 'whetherReturnable.desc',
  //   width: 100,
  //   label: '是否可用券'
  // },
  {
    prop: 'costPrice',
    name: 'costPrice',
    width: 100,
    label: '成本价',
  },
	{
    prop: 'salePrice',
    name: 'salePrice',
    width: 100,
    label: '销售价',
  },
  // {
  //   prop: "costPrice",
  //   name: 'costPrice',
  //   width: 100,
  //   label: '成本价'
  // },
  {
    prop: 'stockQuantity',
    name: 'stockQuantity',
    label: '库存',
    width: 100
  }
];
const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
}

export default {
  components: {
    PointDeductionTip,
    FissionRewardTip,
    detailItem,
    ProductsModal,
    addGroup,
    importDialog
  },
  data() {
    return {
      pointDeductionData: getPointDeductionData(),
      whetherRewardData: getWhetherRewardData(),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 1000 * 24
        }
      },
      id: '',
      loading: false,
      showAdd: false,
      saleMerchantId: '', // 经销商id
      active: 0,
      ruleList: [
        {
          name: '限时特价',
          id: '1',
        },
        {
          name: '会员价',
          id: '2'
        },
        {
          name: '满减活动',
          id: '3'
        }
      ],
      couponList: [{
        label: '满减券',
        content: '满1000元减20元',
        id: 0
      }, {
        label: '折扣券',
        content: '满1000元打9折',
        id: 1
      }],
      merchantList: [], // 客户类型列表
      groupTableData: [], // 已选分组数据
      form: {
        whetherReward: { code: 'N' },
        whetherPointsDeduction: { code: 'N' }, // 积分抵扣
        exclusive: false, // 是否为专享券
        couponType: '', // 优惠券类型
        couponName: '', // 优惠券名称
        total: '', // 发放数量
        thresholdType: 'NONE', // 使用门槛
        fullMoney: '', // 使用条件，满金额
        time: [], // 用券时间
        reduceMoney: '', // 使用条件，减金额
        useEndTime: '', // 用券结束时间
        useStartTime: '', // 用券开始时间
        useTimeType: 'ASSIGN', // 优惠劵使用门槛类型
        useValidDay: '', // 领券后x天可用
        productRangeType: 'ALL', // 适用商品范围类型
        limitObjectType: 'NONE', // 限制领取对象类型
        customerLabelIds: [], // 选中的客户标签id集合
        limitTimesType: 'N', // 是否限制领取次数
        limitOpenType: 'Y', // 是否公开领取
        illustrate: '', // 说明
        customerTypeIds: [], // 选中的客户类型id集合
        customerGroupIds: [],
        discount: '', // 折扣
        overlayRule: [] //叠加规则
      },
      overlayRule: [], //叠加规则
      goodsList: [],
      productVisible: false, // 选择商品弹窗是否显示
      options: TableColumnList,
      pageSize: 10,
      totalNum: 0,
      currentPage: 1,
      disableHandle: false, // 是否禁用商品操作
      actionUploadUrl: '/api/product/admin/productImport/importProductActivity',
      templateKey: 'IMPORT_PRODUCT_EXCEL_TEMP',
      queryParams:{
        productType: 'IMPORT_PRODUCT_COUPON',
        activityId:''
      },
      timer: null,
      timerOther: null,
      customerTag: [], // 商品标签列表
    }
  },
  watch: {
    overlayRule(val) {
      this.form.overlayRule = val;
    }
  },
  computed: {
    isDisabled() {
      return this.type === 'check'
    }
  },
  created() {
    this.id = this.$route.query.id;
    this.queryParams.activityId = this.$route.query.id || '';
    this.type = this.$route.query.type
    this.getMerchantType()
    this.getDetail()
    this.saleMerchantId = this.$route.query.saleMerchantId
    this.form.couponType = this.active == 0 ? 'FULL_REDUCTION' : 'DISCOUNT';
    this.$nextTick(()=>{
      this.timer = setTimeout(()=>{
        if((this.form.couponStatus && (this.form.couponStatus.code == 'NOT_START' || this.form.couponStatus.code == 'PROCEED')) || this.type == 'add') {
          this.disableHandle = false;
        } else {
          this.disableHandle = true;
        }
      },2000)
    });
    this.fetchMerchantLabels();
  },
  methods: {
    checkPermission,
    async fetchMerchantLabels() {
      let { code, data } = await page({ current: 1, page: 200, model: {} });
      if (code === 0) {
        this.customerTag = data.records.map(v => ({
          text: v.tagName,
          value: v.id
        }))
      }

    },
    fullMoneyValidator(rule, value, callback) {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        if (isNaN(value)) {
          return callback(new Error('请输入数字'))
        } else if (!isNaN(value) && Number(value) <= 0) {
          return callback(new Error('金额不能小于或等于0'))
        } else if (!isNaN(value) && Number(value) > 999999.99) {
          return callback(new Error('金额不能大于或等于999999.99'))
        } else {
          callback();
        }
      }
    },
    discountValidator(rule, value, callback) {
      if (!value) {
        return callback(new Error('折扣额度不能为空'));
      } else {
        if (isNaN(value)) {
          return callback(new Error('请输入数字'));
        } else if (!isNaN(value) && Number(value) <= 0) {
          return callback(new Error('折扣额度不能小于或等于0'));
        } else if (!isNaN(value) && Number(value) >= 10) {
          return callback(new Error('折扣额度不能大于或等于10'));
        } else if (!/^(([^0]\d*)|0)(\.\d{1})?$/.test(value)) {
          return callback(new Error('只保留一位小数点'));
        }else {
          return callback();
        }
      }
    },
    // 获取详情
    async getDetail() {
      if (this.id) {
        const { data } = await request.get(`product/merchant/coupon/${this.id}`)
        data.discount = Number(data.discount)
        this.form = data
        this.active = data.couponType.code == 'FULL_REDUCTION' ? 0 : 1
        this.form.limitObjectType = data.limitObjectType.code
        this.form.time = data.useTimeType.code == 'ASSIGN' ? [data.useStartTime, data.useEndTime] : []
        this.form.limitOpenType = data.limitOpenType.code
        this.form.limitTimesType = data.limitTimesType.code
        this.form.productRangeType = data.productRangeType.code
        this.form.thresholdType = data.thresholdType.code
        this.form.useTimeType = data.useTimeType.code;
        this.form.overlayRule = [];
        if(!Boolean(data.customerLabelIds)) {
          this.form.customerLabelIds = [];
        }
        if(this.form.customerTypeIds && this.form.customerTypeIds.length) {
          let idArr = [], nameArr = [];
          idArr = this.merchantList.filter(item => {
            return this.form.customerTypeIds.includes(item.id)
          });
          idArr.forEach(e => {
            nameArr.push(e.name)
          })
          this.form.customerTypeIds = [...nameArr]
        }
        // this.form.c
        // 是否为专享卷
        this.form.exclusive = data.exclusive.code === 'Y'

        if (data.superpositionRule && data.superpositionRule.fullReduction && data.superpositionRule.fullReduction.code == 'Y') {
          this.form.overlayRule.push('3');
        }
        if (data.superpositionRule && data.superpositionRule.timedSpecials && data.superpositionRule.timedSpecials.code == 'Y') {
          this.form.overlayRule.push('1');
        }
        if (data.superpositionRule && data.superpositionRule.vipPrice && data.superpositionRule.vipPrice.code == 'Y') {
          this.form.overlayRule.push('2');
        }
        this.overlayRule = this.form.overlayRule;
        if (this.form.customerGroupIds.length > 0) {
          this.getGroupList()
        }
        if (this.form.productIds.length > 0) {
          this.getCheckGoodsList()
        }
      }
    },
    // 打开选择商品弹窗
    openProductModel(){
      this.productVisible = true;
    },
    // 删除
    handleDelete(row){
      const arrId = [];
      if(row.length) {
        row.forEach(item => {
          arrId.push(item.id)
        })
      }
			this.goodsList = this.goodsList.filter(item => {
        return !arrId.includes(item.id)
      })
      if (JSON.parse(JSON.stringify(this.$refs.frontEndPage.tableData)).length === 1 && JSON.parse(JSON.stringify(this.goodsList)).length > 1) {
        this.$refs.frontEndPage.page = 1
      }
      this.total = this.goodsList.length;
    },
    // 获取已有客户分组列表
    async getGroupList() {
      this.loading = true
      const { data } = await listMerchantGroupBySaleMerchantId(this.saleMerchantId)
      this.loading = false
      this.groupTableData = data.filter((items) => {
        if (this.form.customerGroupIds.includes(items.id)) {
          return items
        }
      })
    },
    checkboxChange(e) {
      console.log('e', e, this.form.overlayRule);

    },
    // 获取已有的产品列表
    async getCheckGoodsList(ids) {
      const formData = new FormData()
      const idArr = ids || this.form.productIds
      formData.append('ids', idArr.toString())
      const { data } = await postGoodsList(formData)
      this.goodsList = []
      this.goodsList = data.filter((items) => {
        if (idArr.includes(items.id)) {
          return items
        }
      });
      this.totalNum = this.goodsList.length;
    },
    // 导入商品回调
    uploadChangeData(list = []) {
      let arr = _.cloneDeep(this.goodsList || []);
      let newList = arr.concat(list);
      this.goodsList = _.uniqBy(newList,'id');
      this.totalNum = this.goodsList.length;
    },
    // 清空表单
    resetForm() {
      this.$refs['ruleForm'].resetFields()
    },
    closeProductDia(){
      this.productVisible = false;
    },
    // 提交表单
    submit() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.active == 1 && Number(this.form.discount) < 0.1 || Number(this.form.discount) > 9.9) {
            this.$message.error('请输入0.1-9.9之间折扣')
            return flase
          }
          const params = {
            ...this.form
          }

          if (this.form.time) {
            params.useStartTime = this.form.time[0]
            params.useEndTime = this.form.time[1]
          }
          this.form.thresholdType == 'NONE' ? delete params.fullMoney : ''
          delete params.time
          // 选择指定客户类型
          if (this.form.customerTypeIds && this.form.customerTypeIds.length > 0) {
            const arr = this.merchantList.filter((items) => {
              if (this.form.customerTypeIds.includes(items.name)) {
                return items.id
              }
            })
            params.customerTypeIds = arr.map((item) => item.id)
          }
          // 商品ids
          if (this.goodsList.length > 0) {
            params.productIds = this.goodsList.map((items) => items.id)
          }
          // 是否有id
          if (this.id) params.id = this.id
          this.loading = true
          let superpositionRule = {}; // 叠加规则
          if (this.form.overlayRule.includes('1')) {
            superpositionRule.timedSpecials = "Y";
          } else {
            superpositionRule.timedSpecials = "N";
          }
          if (this.form.overlayRule.includes('2')) {
            superpositionRule.vipPrice = "Y";
          } else {
            superpositionRule.vipPrice = "N";
          }
          if (this.form.overlayRule.includes('3')) {
            superpositionRule.fullReduction = "Y";
          } else {
            superpositionRule.fullReduction = "N";
          }
          params.exclusive = this.form.exclusive ? 'Y' : 'N'
          params.superpositionRule = superpositionRule;
          delete params.overlayRule;
          const method = this.id ? request.put('product/merchant/coupon', params) : request.post('product/merchant/coupon', params)

          method.then((res) => {
            if (res.code == 0) {
              this.loading = false
              this.$message.success('保存成功')
              if(!this.id) {
                sessionStorage.setItem('couponList', '123')
              }
              this.timerOther = setTimeout(() => {
                this.$router.push({
                  path: '/promotion/coupon/list'
                })
              }, 500)
            } else {
              this.loading = false
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    // 删除选中客户组
    deleteRow(i) {
      this.groupTableData.splice(i, 1)
    },
    deleteRowGoods(i) {
      this.goodsList.splice(i, 1)
      this.$refs['products-modal'].isSelect = true
    },
    // 改变客户分组回调
    changeAddUser(data) {
      this.groupTableData = data
      this.form.customerGroupIds = data.map((items) => items.id)
      this.showAdd = false
    },
    //  选择优惠券类型
    chooseType(index) {
      if (this.isDisabled) {
        return false
      }
      this.active = index
      this.form.couponType = this.active == 0 ? 'FULL_REDUCTION' : 'DISCOUNT'
    },
    // 选择商品后回调
    handleAddProducts(list) {
      this.getCheckGoodsList(list)
    },
    // 获取客户类型
    async getMerchantType() {
      this.loading = true
      const { data } = await request.get('merchant/admin/merchantType', {
      })
      this.loading = false
      this.merchantList = data;
    },
    splitString(val) {
      return String(val).split(',')[0]
    },
  //  打开导入商品弹窗
    handleImportProduct() {
      this.$refs.importDialogRef.initOpen();
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
    clearTimeout(this.timerOther);
  }
}
</script>

<style lang="scss" scoped>
.el-input {
  width: 250px;
}

.detail-item {
  border: none !important;
}

.detail-tit {
  display: flex;
  align-items: center;
  margin: 0;
}

.status-box0 {
  width: 64px;
  height: 32px;
  display: inline-block;
  background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
  background-size: cover;
  margin-left: 12px;
  vertical-align: middle;
}

.status-box1 {
  width: 64px;
  height: 32px;
  display: inline-block;
  background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
  background-size: cover;
  margin-left: 12px;
  vertical-align: middle;
}

.status-box2 {
  width: 64px;
  height: 32px;
  display: inline-block;
  background: url('../../../assets/imgs/coupon/Icon_Revok.png') no-repeat;
  background-size: cover;
  margin-left: 12px;
  vertical-align: middle;
}

.status-box3 {
  width: 64px;
  height: 32px;
  display: inline-block;
  background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
  background-size: cover;
  margin-left: 12px;
  vertical-align: middle;
}

.inline-input {
  width: 80px;
  margin: 0 10px;
}

.inline-item {
  display: inline-block;
}

.block-radio {
  margin-bottom: 16px;

  &-top {
    margin-top: 11px;
  }

  &-none {
    margin: 0;
  }
}

.detail {
  &-header {
    // width: 100%;
    margin: 0 12px;
    padding: 19px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;

    p {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.tips {
  color: #999999;
  margin: 0;

  &-btn {
    border: none;
    margin: 0;
    padding: 0;
  }
}

.coupon {
  display: flex;
  padding: 15px 0;

  &-type {
    padding: 15px 10px;
    border: 1px solid #e3e3e3;
    width: 200px;
    margin-right: 20px;

    >h4 {
      font-size: 16px;
      margin: 0;
    }

    >p {
      font-size: 13px;
      color: #999999;
      padding-top: 10px;
      margin: 0;
    }
  }

  &-active {
    border: 2px solid #0056E5;
    position: relative;
  }

  &-disable {
    border: 1px solid #999999;
    position: relative;
  }

  &-disable::after {
    content: '✓';
    text-align: center;
    position: absolute;
    bottom: 0px;
    right: 0px;
    width: 20px;
    height: 20px;
    color: #fff;
    background: #999999;
  }

  &-active::after {
    content: '';
    text-align: center;
    position: absolute;
    bottom: 0px;
    right: -1px;
    width: 20px;
    height: 20px;
    color: #fff;
    background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
    background-size: cover;
    background-position: center;
  }
}
</style>
