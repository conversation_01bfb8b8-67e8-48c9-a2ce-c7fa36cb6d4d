<template>
  <div class="poolEdit">
    <div class="poolEdit_Header">
      <span class="title">编辑客户池</span>
      <div>
        <el-popover
          v-model="rejectFlag"
          placement="bottom-end"
          title="取消提醒"
          width="300"
          trigger="click"
        >
          <el-button slot="reference">取消</el-button>
          确定取消编辑?取消后编辑内容将不被保存!
          <div style="text-align: right; margin: 0; padding-top: 14px">
            <el-button size="mini" @click="rejectFlag = false">取消</el-button>
            <el-button type="primary" size="mini" @click="toBack"
              >确定</el-button
            >
          </div>
        </el-popover>
        <el-button type="primary" :loading="saveLoading" @click="toSave"
          >保存</el-button
        >
      </div>
    </div>
    <div class="poolEdit_Content">
      <el-form
        ref="poolForm"
        :rules="poolRules"
        :model="poolInfo"
        label-width="100px"
      >
        <el-form-item label="客户池名称:" prop="name">
          <el-input v-model="poolInfo.name" style="width: 300px"></el-input>
        </el-form-item>
        <el-form-item label="允许自取:" prop="whetherSelfTaken">
          <el-switch
            v-model="poolInfo.whetherSelfTaken"
            active-value="Y"
            inactive-value="N"
          ></el-switch>
        </el-form-item>
        <el-form-item label="自取限制:" prop="">
          <div>
            <p class="zqxz_item">
              <el-checkbox
                v-model="poolInfo.dayTakenType"
                true-label="Y"
                false-label="N"
              ></el-checkbox
              ><span>日自取额度</span
              ><el-input
                v-model.number="poolInfo.dayQuota"
                placeholder="请输入"
                style="width: 150px"
              ></el-input
              ><span>个客户</span>
            </p>
            <p class="zqxz_item">
              <el-checkbox
                v-model="poolInfo.monthTakenType"
                true-label="Y"
                false-label="N"
              ></el-checkbox
              ><span>月自取额度</span
              ><el-input
                v-model.number="poolInfo.monthQuota"
                placeholder="请输入"
                style="width: 150px"
              ></el-input
              ><span>个客户</span>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="自动回收:" prop="whetherAutomaticRecycling">
          <el-switch
            v-model="poolInfo.whetherAutomaticRecycling"
            active-value="Y"
            inactive-value="N"
          ></el-switch>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  getMerchantPoolDetail,
  merchantPoolEdit,
  checkPoolName,
} from "@/api/merchantApi/customerPool.js";
export default {
  name: "poolEdit",
  components: {},
  props: {},
  data() {
    let checkName = async (rule, value, callback) => {
      const chineseCharCount = (value.match(/[\u4e00-\u9fa5]/g) || []).length;
      if (chineseCharCount > 15) {
        callback(new Error("最多只能输入15个汉字"));
      }
      if (value !== this.oldName && !(await this.toCheckName(value))) {
        callback(new Error("存在同名客户池，请修改！"));
      }
      callback();
    };
    return {
      id: null,
      oldName: "",
      poolInfo: {
        name: "",
      },
      oldInfo: {},
      saveLoading: false,
      rejectFlag: false,
      poolRules: {
        name: [
          { required: true, message: "请输入客户池名称", trigger: "blur" },
          {
            validator: checkName,
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.id = this.$route.query.id ?? null;
    this.getData();
  },
  mounted() {},
  methods: {
    getData() {
      getMerchantPoolDetail(this.id)
        .then((res) => {
          this.poolInfo = Object.assign({}, res?.data ?? {}, {
            whetherAutomaticRecycling:
              res?.data?.whetherAutomaticRecycling?.code ?? "N",
            whetherSelfTaken: res?.data?.whetherSelfTaken?.code ?? "N",
            monthTakenType: res?.data?.monthTakenType?.code ?? "N",
            dayTakenType: res?.data?.dayTakenType?.code ?? "N",
          });
          this.oldName = this.poolInfo.name;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    toBack() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push("/merchant/merchantManage/customerPoolManage");
    },
    async toCheckName(name) {
      let f = await checkPoolName(name); // true 不存在同名
      return f.data ?? true;
    },
    toSave() {
      this.saveLoading = true;
      this.$refs.poolForm.validate((valid) => {
        if (!valid) {
          this.saveLoading = false;
          return;
        }
        let temp = {
          ...this.poolInfo,
          whetherAutomaticRecycling: {
            code: this.poolInfo.whetherAutomaticRecycling,
          },
          whetherSelfTaken: {
            code: this.poolInfo.whetherSelfTaken,
          },
          monthTakenType: {
            code: this.poolInfo.monthTakenType,
          },
          dayTakenType: {
            code: this.poolInfo.dayTakenType,
          },
        };
        merchantPoolEdit(temp)
          .then((res) => {
            this.saveLoading = false;
            this.$message.success("保存成功!");
            this.$store.dispatch("tagsView/delView", this.$route);
            this.$router.push("/merchant/merchantManage/customerPoolManage");
          })
          .catch((err) => {
            this.saveLoading = false;
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.poolEdit {
  background: #fff;
  height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  .poolEdit_Header {
    height: 56px;
    min-height: 56px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
  .poolEdit_Content {
    flex: auto;
    color: #505465;
    overflow: scroll;
  }
}
.poolEdit_Content {
  .zqxz_item {
    span {
      margin: 0 8px;
    }
  }
  .zqxz_item + .zqxz_item {
    margin-top: 10px;
  }
}
</style>
