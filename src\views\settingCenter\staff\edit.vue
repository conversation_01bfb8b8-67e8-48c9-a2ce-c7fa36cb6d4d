<template>
  <el-dialog
    :close-on-click-modal="false"
    v-if="visible"
    :title="(row.id > 0 ? '编辑' : '新增') + '员工'"
    :show-close="true"
    :visible.sync="visible"
    :before-close="clearFun"
    width="650px"
  >
    <div class="businessTypeEditContent">
      <el-form
        class="form"
        :model="query"
        ref="ruleForm"
        label-width="125px"
        :rules="rules"
      >
        <el-form-item class="formItem" prop="account" label="员工账号:">
          <el-input
            autocomplete="off"
            clearable
            v-model="query.account"
            placeholder="请填写员工账号"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="query.id === 0"
          class="formItem"
          prop="password"
          label="员工密码:"
        >
          <el-input
            autocomplete="off"
            :show-password="true"
            clearable
            v-model="query.password"
            placeholder="请填写员工密码"
          ></el-input>
        </el-form-item>
        <el-form-item class="formItem" prop="name" label="员工姓名:">
          <el-input
            clearable
            v-model="query.name"
            placeholder="请填写员工姓名"
          ></el-input>
        </el-form-item>
        <el-form-item class="formItem" prop="contactNumber" label="员工手机号:">
          <el-input
            clearable
            v-model="query.contactNumber"
            placeholder="请填写员工手机号"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="clearFun()">取 消</el-button>
      <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { uploadFile } from "@/api/file";
import { getToken } from "@/utils/auth";
import { query } from "@/api/setting/data/dictionaryItem";
import { editNewApi, getUserByAccount, employeeAdd } from "@/api/setting/staff";
export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      const passwordRegex =
        /^(?=.*[A-Za-z])(?=.*\d)(?=.*[`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]])[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,}$/;
      const PASSWORD_SPECIAL_CHARS = "`~!@#$%^&*()_+<>?:\",./\\;'[]";
      if (!value) {
        callback(new Error("请填写密码"));
      } else if (value.length < 6) {
        callback(new Error("长度需大于等于6个字符"));
      } else if (!passwordRegex.test(value)) {
        callback(
          new Error(
            `密码需包含字母、数字和特殊字符（如 ${PASSWORD_SPECIAL_CHARS}）`
          )
        );
      } else {
        callback();
      }
    };
    const rules = {
      account: [{ required: true, message: "请填写账号", trigger: "blur" }],
      password: [
        { required: true, trigger: "blur", validator: validatePassword },
      ],
      name: [
        { required: true, message: "请填写员工姓名", trigger: "blur" },
        { min: 2, message: "请填写不少于2位数", trigger: "blur" },
      ],
      contactNumber: [
        {
          pattern: /^1\d{10}$/,
          message: "目前只支持中国大陆的手机号码",
          trigger: "blur",
        },
      ],
    };
    return {
      insertProgram: {
        folderId: 0,
      },
      educationList: null,
      nationList: null,
      positionStatusList: null,
      query: this.initQuery(),
      rules,
    };
  },
  props: {
    row: {
      type: Object,
    },
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true,
    },
  },
  methods: {
    initQuery() {
      // return {
      //   id:0,
      //   "account": "",
      //   "avatar": "",
      //   "education": {
      //     "data": "",
      //     "key": ""
      //   },
      //   "email": "",
      //   "lastLoginTime": "",
      //   "mobile": "",
      //   "name": "",
      //   "nation": {
      //     "data": "",
      //     "key": ""
      //   },
      //   "org": null,
      //   "password": "",
      //   "positionStatus": {
      //     "data": "在职",
      //     "key": "WORKING"
      //   },
      //   "sex": "M",
      //   "status": true,
      //   "workDescribe": ""
      // }
      return {
        contactNumber: "",
        password: "",
        id: 0,
        userId: 0,
        name: "",
        positionStatus: "WORKING",
      };
    },
    // beforeUpload(file) {
    //   const isJPG = file.type === 'image/jpeg';
    //   const isLt2M = file.size / 1024 / 1024 < 2;
    //   if (!isJPG) {
    //     this.$message.error('上传头像图片只能是 JPG 格式!');
    //   }
    //   if (!isLt2M) {
    //     this.$message.error('上传头像图片大小不能超过 2MB!');
    //   }
    //   return isJPG && isLt2M;
    // },
    // uploadSuccess(res, file) {
    //   this.query.avatar=res.data.url;
    // },
    // async getDetailFun(){
    //   //	查询 员工
    //  let {data} = await detail(this.query.id)
    //  this.query=data
    // },
    // async getEducation(){
    //   //	学历
    //  let {data} = await query({
    //    dictionaryType:"EDUCATION"
    //   })
    //   var list=[]
    //   data.forEach(element => {
    //     list.push({
    //       key:element.code,
    //       data:element.name
    //     })
    //   });
    //   this.educationList= list;
    // },
    // async getNation(){
    //   //	民族
    //  let {data} = await query({
    //     dictionaryType:"NATION"
    //   })
    //   var list=[]
    //   data.forEach(element => {
    //     list.push({
    //       key:element.code,
    //       data:element.name
    //     })
    //   });
    //    this.nationList= list;
    // },
    // async getPositionStatus(){
    //   //职位状态
    //  let {data} = await query({
    //     dictionaryType:"POSITION_STATUS"
    //   })
    //    var list=[]
    //   data.forEach(element => {
    //     list.push({
    //       key:element.code,
    //       data:element.name
    //     })
    //   });
    //  this.positionStatusList=list;
    // },
    submitFun: function (ruleForm) {
      console.log("this.query", this.query);
      // return
      let _this = this;
      this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          if (_this.query.id === 0) {
            let query = JSON.parse(JSON.stringify(_this.query));
            delete query.userId;
            delete query.id;
            var data = await employeeAdd(query);
            if (data.code == 0) {
              this.$message.success("新增员工成功！");
            }
          } else {
            var data = await editNewApi(_this.query);
            if (data.code == 0) {
              this.$message.success("编辑员工成功！");
            }
          }
          if (data.code == 0) {
            _this.$emit("update:visible", false);
            _this.$emit("update:isReload", true);
          } else {
            //  _this.$message.error("提交失败！")
          }
        } else {
          return false;
        }
      });
    },
    clearFun: function () {
      this.query = this.initQuery();
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    async checkAccount(rule, value, callback) {
      var { data } = await getUserByAccount(value);
      if (data == null) {
        return callback(new Error("当前账号不存在于用户列表"));
      } else if (data != null && data.status == false) {
        return callback(new Error("当前账号处于冻结状态无法使用"));
      } else {
        this.query.userId = data.id;
        callback();
      }
    },
  },
  mounted() {
    this.query = JSON.parse(
      JSON.stringify(Object.assign(this.query, this.row))
    );
  },
};
</script>
<style lang="less" scoped>
.businessTypeEditContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 10px 20px;
  .faceItem {
    width: 100%;
  }

  .avatar-uploader {
    width: 90px;
    height: 90px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 5px;
    border: 1px dashed #d9d9d9;
  }
  .avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 90px;
    height: 90px;
    line-height: 90px;
    text-align: center;
  }
}
</style>
