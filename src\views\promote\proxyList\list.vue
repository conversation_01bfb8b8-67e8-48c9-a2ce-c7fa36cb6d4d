<template>
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="manufacturer">
        <el-input v-model.trim="listQuery.model.manufacturer" placeholder="请输入生产厂家" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <div class="title flex_between_center">
        <div>
          <el-tabs v-model="tabType" class="typeTabs">
            <el-tab-pane label="推广商品" name="list"></el-tab-pane>
          </el-tabs>
        </div>
        <div>
          <el-button
            :disabled="selectTableItems.length == 0"

            @click="backOutFun"
          >批量撤销</el-button
          >
          <el-button icon="el-icon-refresh"  @click="getlist"
          >刷新</el-button
          >
          <el-button type="primary"  @click="newFun"
          >+新增推广商品</el-button
          >
        </div>
      </div>

      <div class="table">
        <el-table
          ref="table"
          v-if="list"
          @selection-change="selectTableItemFun"
          v-loading="listLoading"
          :data="list"
          row-key="index"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="80"
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>
          <el-table-column
            type="selection"
            align="center"
            width="55"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            label="销售状态"
            width="120"
            align="left"
            prop="publishStatus"
          >
            <template slot-scope="{ row }">
              <span>{{ row['publishStatus'].code == "PUT_ON_SALE" ? "上架中" : "已下架" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="主图" width="80" class-name="img-cell">
            <template slot-scope="{row}">
              <img :src="row.pictIdS|imgFilter" width="50px" height="50px">
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in tableList"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
            <span v-if="item.name == 'publishStatus'">{{
                row[item.name].code == "PUT_ON_SALE" ? "上架中" : "已下架"
              }}</span>
              <el-button
                v-else-if="item.name == 'salesmanNum'"
                @click="showSalesManFun(row)"
                :disabled="row[item.name] == 0"
                type="text"
                size="mall"
              >{{ row[item.name] }}</el-button
              >
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            fixed="right"
            align="center"
            label="可代理销售区域"
            width="120"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-button @click="showAreasFun(row)" type="text" >查看区域</el-button>
              </span>
              </el-row>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="120"
            class="itemAction"
          >
            <template slot-scope="{ row }">
              <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-button @click="showDetail(row.id)" type="text" >查看详情</el-button>
              </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex_between_center">
          <div></div>
          <pagination
            v-if="total > 0"
            :pageSizes="[10, 20, 50, 100]"
            :total="total"
            :page.sync="listQuery.current"
            :limit.sync="listQuery.size"
            @pagination="getlist"
          />
        </div>
      </div>
    </div>
    <el-dialog
      v-if="showSalesManFlag"
      :visible.sync="showSalesManFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-dialogDrag
    >
      <div slot="title">
        <span>业务员列表({{ salesMalNum }})</span>
      </div>
      <salesManList
        :visible.sync="showSalesManFlag"
        :row.sync="row"
        :salesMalNum.sync="salesMalNum"
        :areaTree="areaTree"
      ></salesManList>
    </el-dialog>

    <el-dialog
      v-if="showAreasFlag"
      :visible.sync="showAreasFlag"
      width="80%"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-dialogDrag
    >
      <div slot="title">
        <span>可代理销售区域({{ showAreasNum }})</span>
      </div>
      <areas
        :visible.sync="showAreasFlag"
        :row.sync="row"
        :total.sync="showAreasNum"
      ></areas>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import requestAxios from '@/utils/request';
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/promote/proxyList/tableInfo";
import salesManList from "./table/salesManList";
import areas from "./table/areas";

export default {
  name: 'promotionManagement',
  data() {
    return {
      listLoading: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: "HAVE",
        },
      },
      total: 0,
      salesMalNum: 0,
      row: {},
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      selectTableItems: [],
      showSalesManFlag: false,
      showAreasFlag: false,
      showAreasNum: 0,
      areaTree: [],
      tableList: [
        {
          key: 2,
          label: '商品编码',
          name: "productCode",
          width: '170px',
          disabled: true
        },
        {
          key: 3,
          label: '商品名称',
          name: "productName",
          width: '170px'
        },
        {
          key: 4,
          label: '规格',
          name: "spec",
          width: '170px'
        },
        {
          key: 5,
          label: '生产厂家',
          name: "manufacturer",
          width: '180px'
        },
        {
          key: 6,
          label: '销售价（元）',
          name: "salePrice",
          width: '168px'
        }
        ,
        {
          key: 7,
          label: '推广费（元）',
          name: "promotionExpenses",
          width: '168px'
        },
        {
          key: 8,
          label: '业务员',
          name: "salesmanNum",
          width: '168px'
        }
      ]
    };
  },
  methods: {
    async getAreaTree() {
      let { data } = await request({
        url: "/authority/area/anno/tree",
        method: "get",
      });
      this.areaTree = data;
    },
    showAreasFun(row) {
      this.showAreasFlag = true;
      this.row = row;
    },
    showSalesManFun(row) {
      this.showSalesManFlag = true;
      this.row = row;
    },
    splitString(val) {
      if (!val) {
        return "";
      }
      return val.split(",");
    },
    showDetail(id) {
      this.$router.push({
        path: "/promoteCenter/promoteManagement/detail",
        query: {
          id: id,
        },
      });
    },
    async backOutFun() {
      let arr = []
      this.selectTableItems.forEach(item => {
        arr.push(item.id)
      })
      let { data } = await requestAxios({
        url: "/agent/agentProduct/merchant/batchRepeal",
        method: "post",
        data: {
          list: arr
        }
      })
      this.getlist()
    },
    newFun() {
      this.$router.push({
        path: "/promoteCenter/promotionCheck/editItem",
      });
    },
    resetForm() {
      this.list = [];
      this.total = 0;
      this.listQuery = {
        current: 1,
        size: 10,
        model: {
          publishStatusEnum: "HAVE",
        },
      };
      this.getlist()
    },
    selectTableItemFun: function (val) {
      this.selectTableItems = val;
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      let { data } = await requestAxios({
        url: "/agent/agentProduct/merchant/query/agencyProducts",
        method: "post",
        data: this.listQuery,
      });
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
    this.getAreaTree();
  },
  components: {
    Pagination,
    salesManList,
    areas,
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    padding: 0 12px;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
