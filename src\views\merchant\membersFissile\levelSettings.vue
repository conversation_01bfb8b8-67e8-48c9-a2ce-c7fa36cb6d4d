<template>
  <div class="fission_setting" v-loading="loading">
    <div class="item" v-for="item in levelSettingList">
      <el-form :model="item" :disabled="!getIsEdit(item.levelType.code)">
        <page-module-title :title="item.levelType.desc">
          <!-- 嵌入子form是为了让编辑按钮不受外部form开启disabled的影响 -->
          <el-form>
            <el-button v-if="!getIsEdit(item.levelType.code)" type="primary" :disabled="false" @click="setCurrentLevelCode(item.levelType.code)">编辑</el-button>
            <div v-else>
              <el-button :disabled="false" @click="cancel">取消</el-button>
              <el-button :disabled="false" type="primary" :loading="saveLoading" @click="save">保存</el-button>
            </div>
          </el-form>
        </page-module-title>
        <div class="tips">
          <div v-for="tip in levelTip[item.levelType.code]">{{ tip }}</div>
        </div>
        <div class="form_item" style="align-items: flex-start;">
          <span class="label">等级开启：</span>
          <span>
            <el-radio-group v-model="item.levelStatus.code">
              <el-radio label="ENABLE">开启</el-radio>
              <el-radio label="DISABLE">关闭</el-radio>
            </el-radio-group>
          </span>
        </div>
        <!-- 等级开启状态才展示升级条件和等级设置 -->
        <template v-if="item.levelStatus.code === 'ENABLE'">
          <div class="form_item">
            <span class="label">升级条件：</span>
            <span>
            <el-checkbox-group v-model="item.levelConditionList" style="margin-top: 5px;">
              <el-checkbox v-for="item in upgradeConditions[item.levelType.code]" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </span>
          </div>
          <div class="form_item">
            <span class="label">等级设置：</span>
            <el-button
              type="primary"
              @click="addLevelItem(item.levelSettingDetailVoList)"
              :disabled="!getIsEdit(item.levelType.code) || item.levelSettingDetailVoList.length >= 5"
            >
              +添加等级
            </el-button>
            <span style="color: #999">（最多5个等级值）</span>
          </div>
          <el-table :data="item.levelSettingDetailVoList" border style="width: 1000px">
            <el-table-column label="等级值" prop="levelNum" width="70" />
            <el-table-column label="等级名称" width="300px">
              <template v-slot="{row}">
                <span v-if="!getIsEdit(item.levelType.code)">{{ row.levelName }}</span>
                <el-input v-else type="text" v-model="row.levelName" :disabled="!getIsEdit(item.levelType.code)" />
              </template>
            </el-table-column>
            <el-table-column label="等级条件">
              <template v-slot="{row}">
                <div v-if="row.levelNum == 1">
                  1、未有等级者默认为初始等级，根据满足条件自动升降级。<br />
                  2、每月1日恢复为初始等级。
                </div>
                <div v-else>
                  <template>
                    <!-- 月度等级条件 -->
                    <template v-if="item.levelType.code === 'MONTH'">
                      <div style="margin-bottom: 5px" v-if="item.levelConditionList.includes('selfAmount')">
                        本人成交金额：月度满
                        <el-input
                          type="text"
                          v-model="row.levelRuleList.selfAmount"
                          style="width: 100px"
                          :disabled="!getIsEdit(item.levelType.code)"
                        /> 元
                      </div>
                      <div style="margin-bottom: 5px" v-if="item.levelConditionList.includes('directMember')">
                        直接成员数：月度成交金额满
                        <span style="margin-bottom: 5px">
                          <el-input
                            v-model="row.levelRuleList.directMember.directMemberAmount"
                            style="width: 100px"
                            :disabled="!getIsEdit(item.levelType.code)"
                          />元的直接成员数需达
                          <el-input
                            type="text"
                            v-model="row.levelRuleList.directMember.directMemberNum"
                            :disabled="!getIsEdit(item.levelType.code)"
                            style="width: 100px"
                          />人
                        </span>
                      </div>
                    </template>
                    <!-- 年度等级条件 -->
                    <template v-else-if="item.levelType.code === 'YEAR'">
                      <div style="margin-bottom: 5px" v-if="item.levelConditionList.includes('levelTimes')">
                        月度等级次数：年度获得
                        <span style="margin-bottom: 5px">
                          <el-select
                            v-model="row.levelRuleList.levelTimes.levelNum"
                            placeholder="请选择"
                            :disabled="!getIsEdit(item.levelType.code)"
                          >
                            <el-option v-for="item in monthLevelList" :key="item._id" :label="item.levelName" :value="item.levelNum" />
                          </el-select>
                          及以上达
                          <el-input
                            type="text"
                            v-model="row.levelRuleList.levelTimes.times"
                            :disabled="!getIsEdit(item.levelType.code)"
                            style="width: 100px"
                          />次
                        </span>
                      </div>
                      <div v-if="item.levelConditionList.includes('teamMemberAmount')">
                        团队成员成交金额：年度满
                        <el-input
                          type="text"
                          v-model="row.levelRuleList.teamMemberAmount"
                          :disabled="!getIsEdit(item.levelType.code)"
                          style="width: 100px"
                        />元
                      </div>
                    </template>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="55">
              <template v-slot="{row}">
                <el-button
                  type="text"
                  v-if="row.levelNum != 1"
                  :disabled="!getIsEdit(item.levelType.code)"
                  @click="deleteTableItem(row, item.levelSettingDetailVoList)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getLevelSetting, saveLevelSetting, deleteLevelItem } from '@/api/retailStore'
import { deepClone } from "@/utils";

export default {
  name: 'levelSettings',
  components: {
  },
  data() {
    return {
      levelSettingList: [],
      levelSettingListTemp: [],
      currentLevelCode: '',
      saveLoading: false,
      levelTip: {
        MONTH: [
          '月度等级：基于一个月内的行为数据评定月度等级。',
          '（1）每月1日0时恢复为初始等级，根据满足的等级条件自动升降等级，直至每月31日23时59分确定当月最终等级。',
          '（2）升级条件涉及成交金额时，成交金额是统计每月1日-31日已支付订单的成交金额（且剔除相应订单的已完成退款金额）。未完成的退款金额、将来发生的退款金额不再计入。',
          '（3）调整等级条件后不影响历史等级，只影响未来等级。'
        ],
        YEAR: [
          '年度等级：基于一年内的行为数据评定年度等级。',
          '（1）每年1月1日0时恢复为初始等级，根据满足的等级条件自动升降等级，直至每年12月31日23时59分确定最终等级。',
          '（2）升级条件涉及成交金额时，成交金额是统计一年内已支付订单的成交金额（且剔除相应订单的已完成退款金额）。未完成的退款金额、将来发生的退款金额不再计入。',
          '（3）调整等级条件后不影响历史等级，只影响未来等级。'
        ]
      },
      upgradeConditions: {
        MONTH: [
          { label: '本人成交金额', value: 'selfAmount' },
          { label: '直接成员数', value: 'directMember' },
        ],
        YEAR: [
          { label: '本人月度等级次数', value: 'levelTimes' },
          { label: '团队成员成交金额', value: 'teamMemberAmount' },
        ]
      },
      loading: false
    }
  },
  created() {
    this.setLevelSetting()
  },
  computed: {
    monthLevelList() {
      const monthItem = this.levelSettingList.find(item => item.levelType.code === 'MONTH')
      if (!monthItem) return []
      return monthItem.levelSettingDetailVoList.filter(item => item.levelNum != 1)
    },
  },
  methods: {
    setLevelSetting() {
      this.loading = true
      getLevelSetting().then(res => {
        res.forEach(item => {
          item.levelSettingDetailVoList = item.levelSettingDetailVoList.map(it => {
            it._id = it.id
            return it
          })
        })
        this.levelSettingList = res
        // 存一份,取消的时候直接复原
        this.levelSettingListTemp = deepClone(res)
      }).finally(() => {
        this.loading = false
      })
    },
    cancel() {
      this.levelSettingList = deepClone(this.levelSettingListTemp)
      this.currentLevelCode = ''
    },
    save() {
      return new Promise((resolve) => {
        const form = this.levelSettingList.find(item => item.levelType.code === this.currentLevelCode)
        const levelSettingDetailVoList = form.levelSettingDetailVoList
        if (form.levelConditionList.length === 0) {
          this.$message.error('请至少选择一个升级条件')
          return
        }
        if (Array.isArray(levelSettingDetailVoList) && levelSettingDetailVoList.length > 0) {
          for (let item of levelSettingDetailVoList) {
            if (!item.levelName) {
              this.$message.error('请输入等级名称')
              return
            }
            // 初始等级只校验等级名称即可
            if (item.levelNum === 1) continue
            const levelRuleList = item.levelRuleList || {}
            // 月度等级校验
            if (form.levelType.code === 'MONTH') {
              // 本人成交金额校验
              if (form.levelConditionList.includes('selfAmount')) {
                if (!levelRuleList.selfAmount) {
                  this.$message.error('请完善本人成交金额')
                  return
                }
              }
              // 直接成员数校验
              if (form.levelConditionList.includes('directMember')) {
                if (!levelRuleList?.directMember?.directMemberAmount || !levelRuleList?.directMember?.directMemberNum) {
                  this.$message.error('请完善直接成员数')
                  return
                }
              }
            }
            // 年度等级校验
            if (form.levelType.code === 'YEAR') {
              if (form.levelConditionList.includes('levelTimes')) {
                if (!levelRuleList?.levelTimes?.levelNum || !levelRuleList?.levelTimes?.times) {
                  this.$message.error('请完善月度等级次数')
                  return
                }
              }
              if (form.levelConditionList.includes('teamMemberAmount')) {
                if (!levelRuleList.teamMemberAmount) {
                  this.$message.error('请完善团队成员成交金额')
                  return
                }
              }
            }
          }
        }
        this.saveLoading = true
        saveLevelSetting(form).then(() => {
          this.$message.success('保存成功')
          this.setLevelSetting()
          this.currentLevelCode = ''
          resolve()
        }).finally(() => {
          this.saveLoading = false
        })
      })
    },
    getIsEdit(code) {
      return this.currentLevelCode === code
    },
    setCurrentLevelCode(code) {
      this.cancel()
      this.currentLevelCode = code
    },
    deleteTableItem(row, levelSettingDetailVoList) {
      // 如果删除的是未保存到数据库的,那么就直接删除
      if (!row.id) {
        const index = levelSettingDetailVoList?.findIndex(item => item._id === row._id)
        levelSettingDetailVoList.splice(index, 1)
        return
      }
      this.$confirm(`删除等级将影响月度奖励、一次性奖励, 确定要删除【${row.levelName}】这项等级吗？`, '操作', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        deleteLevelItem(row._id).then(() => {
          this.$message.success('删除成功')
          this.setLevelSetting()
        })
      }).catch(() => {})
    },
    addLevelItem(levelSettingDetailVoList) {
      const _id = new Date().getTime()
      levelSettingDetailVoList.push({
        _id,
        add: true,
        levelName: '',
        levelRuleList: this.currentLevelCode === 'MONTH' ? {
          selfAmount: '',
          directMember: {
            directMemberAmount: '',
            directMemberNum: ''
          }
        } : {
          teamMemberAmount: '',
          levelTimes: {
            levelNum: '',
            times: ''
          }
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.fission_setting {
  background-color: #fff;
  .tips {
    background-color: #eee;
    padding: 10px;
  }
  .form_item {
    display: flex;
    align-items: center;
    margin: 20px 0;
    .label {
      margin-right: 10px;
    }
  }
}
</style>
