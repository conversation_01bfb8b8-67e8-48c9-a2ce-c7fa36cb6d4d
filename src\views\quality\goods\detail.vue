<template>
  <div v-loading="listLoading">
    <div class="detailTable">
      <page-title :title="detail.productName" />
      <page-module-card>
        <p><b>规格</b>： {{detail.spec}}</p>
        <p style="margin: 10px 0;"><b>批准文号</b>：国药准{{detail.approvalNumber}}</p>
        <p><b>生产厂家</b>：{{detail.manufacturer}}</p>
      </page-module-card>
      <tabs-layout
        v-model="activeName"
        :tabs="tabs" />
      <div v-show="activeName === 'first'">
        <el-form :model="tableForm" :show-message="false" :inline-message="true">
         <el-table
            :data="tableForm.tableData"
            border
            :cell-class-name="cellClassNameFunc"
            class="table-form"

          >
            <el-table-column label="证件类型" width="120">
              <template slot-scope="{row}">
                {{row.licenseTypeName}}
              </template>
            </el-table-column>
            <el-table-column label="证件号">
              <template slot-scope="scope">
                <el-form-item v-if="scope.row.isEdit" class="table-form-item">
                  <el-input  v-model="scope.row.certificateNumber" placeholder="请输入证件号"></el-input>
                </el-form-item>
                <span v-else>
                  {{scope.row.certificateNumber}}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="过期时间">
              <template slot-scope="scope">
                <el-form-item v-if="scope.row.isEdit" class="table-form-item">
                    <el-date-picker v-model="scope.row.licenseEndDate"
                      :disabled="scope.row.whetherForever"
                      type="date"
                      style="width: 180px; margin-right: 10px"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期">
                    </el-date-picker>
                    <el-checkbox v-model="scope.row.whetherForever" @change="handleChangForever(scope.row)">长期</el-checkbox>
                </el-form-item>
                <span v-else>
                  {{scope.row.whetherForever ? '长期' : scope.row.licenseEndDate}}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="附件">
              <template slot-scope="scope">
                <el-form-item v-if="scope.row.isEdit" class="table-form-item">
                  <el-upload
                  :file-list="scope.row.fileIds"
                  list-type="picture-card"
                  :action="uploadParams.action"
                  :headers="uploadParams.headers"
                  :data="uploadParams.data"
                  :on-success="(file) => { handleQuaUploadSuccess(file, scope.row) }"
                  :on-remove="(file, filelist) => { handleQuaRemove(file, filelist, scope.row) }"
                  accept=".jpg,.png,.bmp,.jpeg">
                  <i class="el-icon-plus"></i>
                </el-upload>
                </el-form-item>

                <div class="image-wrapper" v-if="!scope.row.isEdit && scope.row.fileIds.length && scope.row.fileIds[0]!='null'">
                  <!-- <img v-for="(item, idx) of scope.row.fileIds" :key="idx" :src="item.url" width="40px"/> -->
                  <!-- <el-image
                    v-for="(item, idx) of scope.row.fileIds"
                    :key="idx"
                    :ref="'priviewImage' + idx"
                    :src="item.name"
                    alt=""
                    style="width: 50px;"
                    :preview-src-list="[item.name]">
                    >
                    </el-image> -->
                    <img v-for="(item, idx) of scope.row.fileIds" :key="idx" :src="item.name" alt="" width="50px" style="margin-left:5px">
<!-- <el-image-viewer v-if="showViewer0" :url-list="imagesSplit(scope.row.fileIds)" :on-close="closeViewer0" ></el-image-viewer>-->
                  <el-image-viewer v-if="showViewer0" :url-list="imagesSplitList" :on-close="closeViewer0" ></el-image-viewer>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="{row, $index}">
                <el-button v-if="row.isEdit" type="text" style="color: #7f7f7f;" @click="handleCancel($index)">取消</el-button>
                <el-button v-if="!row.isEdit&&checkPermission(['admin', 'sale-saas-quality-productQualification:edit','sale-platform-quality-productQualification:edit'])" type="text" @click="row.isEdit = true">编辑</el-button>
                <el-button  v-if="row.isEdit" type="text" @click="handleOk(row)">确定</el-button>
                <el-button v-if="!row.isEdit && row.fileIds.length && row.fileIds[0]!='null' && checkPermission(['admin', 'sale-saas-quality-productQualification:preview','sale-platform-quality-productQualification:preview'])" type="text"  @click="priview0(row)">预览</el-button>
                <el-button v-if="!row.isEdit && row.fileIds.length && row.fileIds[0]!='null' && checkPermission(['admin', 'sale-saas-quality-productQualification:download','sale-platform-quality-productQualification:download'])" type="text"  @click="onDownload(row.fileIds)">下载</el-button>
                <!-- <el-link v-if="!row.isEdit" type="primary" @click="row.isEdit = true"></el-link> -->
                <el-image :ref="`ref${row.licenseId}`" style="width:0;height:0" :src="imagesSplitList[0]" :preview-src-list="imagesSplitList"></el-image>
              </template>
            </el-table-column>
          </el-table>
         </el-form>
      </div>

      <div v-show="activeName === 'second'">
        <el-table
          :data="tableData1"
          border
        >
<!--          <el-table-column label="id" >-->
<!--            <template slot-scope="{row}">-->
<!--              {{row.id}}-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column label="证件类型" >
            <template slot-scope="{row}">
              {{row.licenseType.desc}}
            </template>
          </el-table-column>
          <el-table-column label="批次号" prop="certificateNumber">
            <template slot-scope="{row}">
              {{row.certificateNumber}}
            </template>
          </el-table-column>
          <el-table-column
            label="附件"
            width="230"
            prop="fileIds">
            <template slot-scope="scope">
              <img v-for="(item,index) in scope.row.fileIds.split(',')" :key="index" :src="item" alt="" width="50px" style="margin-left:5px">
              <el-image-viewer v-if="showViewer" :url-list="filePath" :on-close="closeViewer" ></el-image-viewer>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="{row}">
              <el-link type="danger" @click="deleteLince(row.id)" v-if="checkPermission(['admin', 'sale-saas-quality-productQualification:del','sale-platform-quality-productQualification:del'])">删除</el-link>
              <el-link style="margin: 0px 12px;" type="primary" @click="priview(row)" v-if="checkPermission(['admin','goodsDetail:preview-report'])">预览</el-link>
              <el-link type="primary" @click="download(row)" v-if="checkPermission(['admin','goodsDetail:download-report'])">下载</el-link>
              <el-image :ref="`ref${row.licenseId}`" style="width:0;height:0" :src="previewImages[0]" :preview-src-list="previewImages"></el-image>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-button style="margin-top: 20px;" type="primary" icon="el-icon-plus" v-if=" activeName === 'second' && checkPermission(['admin', 'sale-saas-quality-productQualification:add','sale-platform-quality-productQualification:add'])" @click="addReportMethod()">新增质检报告</el-button>
    </div>

    <el-dialog
      title="新增质检报告"
      :visible.sync="addReport"
      width="500px"
    >

      <div class="addReport">
        <el-form ref="addForm" :rules="rules" :model="addForm">
          <div style="display: flex;align-items: center;justify-content: space-between">
            <div class="imgRead">
              <el-form-item prop="fileIds">
                <el-upload
                  class="avatar-uploader"
                  :show-file-list="false"
                  :action="uploadParams.action"
                  :headers="uploadParams.headers"
                  :data="uploadParams.data"
                  :on-success="handleUploadSuccess"
                  :on-remove="handleUploadRemove"
                  accept=".jpg,.png,.bmp,.jpeg"
                >
                  <img v-if="addForm.fileIds" :src="imagesUrl" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="证书名称：">
                <el-input type="text" v-model="addForm.name" size="middle" style="width: 220px;"></el-input>
              </el-form-item>
              <el-form-item label="批次号：" prop="certificateNumber">
                <el-input type="text" v-model="addForm.certificateNumber" size="middle" style="width: 220px;"></el-input>
              </el-form-item>
            </div>
          </div>
          <el-form-item style="display: flex;align-items: center;justify-content: flex-end">
            <el-button type="primary" @click="onSubmit">保存</el-button>
            <el-button @click="onCancle">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog
            title="客户资质"
            :visible.sync="deliveryVisible"
            width="75%">
            <div style="width:100%;height: 500px;text-align: center">
              <img :src="filePath" style="height: 100%;"/>
            </div>
    </el-dialog>

     <!-- <el-dialog
        title="预览"
        :visible="!!previewImages.length"
        width="40%"
      >
        <div class="preview-images">
          <el-image v-for="image in previewImages" :key="image.url"  :src="image.url" style="width: 120px; height: 120px; margin-right: 10px;"/>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="previewImages = []">取消</el-button>
          <el-button type="primary" @click="onDownload(previewImages)">下载</el-button>
        </div>
      </el-dialog> -->
  </div>
</template>

<script>

import { getQualityDetail, getByLicenseBaseType, postProductLicenseRel, deteteProductLicense, updateProductLicenseRel, confirmProductLicense } from "@/api/quality";
import { getToken } from '@/utils/auth'
import { getLocalUser } from '@/utils/local-user'
import checkPermission from "../../../utils/permission";
// import { parse } from 'path-to-regexp';

export default {
  components: {
  },
  data () {
    return {
      tabs: [],
      deliveryVisible: false,
      filePath: [],
      showViewer0: false,
      showViewer: false,
      imagesSplitList:[],
      rules: {
        fileIds: [
          { required: true, message: '请上传资质报告', trigger: 'blur' },
        ],
        certificateNumber: [
          { required: true, message: '请填写批次号', trigger: 'blur' }
        ]
      },
      imagesUrl: [],
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + '/file/file/upload',
        headers: {
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
          token: `Bearer ${getToken()}`
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser() && getLocalUser().userId,
          folderId: 0
        }
      },
      timer: false,
      addForm: {
        fileIds: '',
        certificateNumber: '',
        name: '',
        licenseId: '',
        licenseType: 'QUALITY_REPORT',
        whetherForever: 'Y',
        productId: this.$route.query.id
      },
      addReport: false,
      listLoading: false,
      activeName: '',
      detail: {},
      previewImages:[],
      productLicenseMap: new Map(),
      productLicenseRelVoListMap: new Map(),
      tableForm: {
        tableData: []
      },
      tableData1: [],
      flagTimer: null
    }
  },
  created () {
    this.getDetail();
    this.addForm = {
        fileIds: '',
        certificateNumber: '',
        name: '',
        licenseId: '',
        licenseType: 'QUALITY_REPORT',
        whetherForever: 'Y',
        productId: this.$route.query.id
      };
    const tabs = [
      { name: '品种资质', value: 'first', hide: !checkPermission(['admin', 'sale-saas-quality-productQualification:productLicenseView','sale-platform-quality-productQualification:productLicenseView']) },
      { name: '质检报告', value: 'second', hide: !checkPermission(['admin', 'sale-saas-quality-productQualification:qualityReportView','sale-platform-quality-productQualification:qualityReportView']) }
    ].filter(item => !item.hide)
    this.tabs = tabs
    if (tabs.length > 0) {
      this.activeName = tabs[0].value
    }
  },
  methods: {
    checkPermission,
    onCancle () {
      this.addForm = {};
      this.addReport = false;
    },
    cellClassNameFunc({row, column, rowIndex, columnIndex}) {
      if(row.isEdit && [1, 2, 3].indexOf(columnIndex) !== -1) {
        return 'edit-td'
      }
    },
    imagesSplit (imgs) {
      return imgs.map(items => items.url);
    },
    handleCancel(index) {
      this.tableForm.tableData[index] = Object.assign(
        this.tableForm.tableData[index],
        this.productLicenseMap.get(this.tableForm.tableData[index].licenseId), {
          isEdit: false,
        }
        )
    },
    async handleOk(row) {
      this.listLoading = true
      const params = Object.assign({}, row, {
        whetherForever: row.whetherForever ? 'Y' : 'N',
        fileIds: row.fileIds.map(({ url }) => url).join(','),
        productId: this.$route.query.id
        })
      this.activeName == 'first' ? await confirmProductLicense(params).finally(_ => {
          this.listLoading = false
        }) : await updateProductLicenseRel(params).finally(_ => {
            this.listLoading = false
        })
      this.getDetail()
    },
    handleChangForever(row) {
      row.licenseEndDate = row.whetherForever ? '' : this.productLicenseMap.get(row.licenseId).licenseEndDate
    },
    // 下载图片
    download(row) {
      const eleLink = document.createElement('a')
      eleLink.download = row.certificateNumber
      eleLink.style.display = 'none'
      eleLink.href = row.fileIds
      // 触发点击
      document.body.appendChild(eleLink)
      eleLink.click()
      // 然后移除
      document.body.removeChild(eleLink)
    },
    priview (row) {
      console.log('row----->',row);
      this.previewImages = row.fileIds.split(',').map(item=>{
        return item
      });
      this.$refs[`ref${row.licenseId}`].showViewer = true;
    },
    priview0 (row) {
      console.log('row',row);
      this.imagesSplitList = row.fileIds.map(item=>{
        return item.url;
      })
      this.$refs[`ref${row.licenseId}`].showViewer = true;
    },
    closeViewer () {
      this.showViewer = false;
    },
    closeViewer0 () {
      this.showViewer0 = false;
    },
    handleQuaUploadSuccess (file, item) {
      item.fileIds.push({
        name: file.data.name,
        url: file.data.url,
        id: file.data.id
      })
    },
    handleQuaRemove (file, filelist, item) {
      console.log('file,fileList,item',file, filelist, item);
      item.fileIds =filelist;
    },
    onDownload(images) {
      images.forEach(item => {
        window.open(item.url, '_blank');
      })
    },
    handleUploadRemove (file, fileList) {
      this.imagesUrl = []
      this.addForm.fileIds = ''
    },
    // 上传成功的回调
    handleUploadSuccess (response) {
      this.imagesUrl = response.data.url
      this.addForm.fileIds = response.data.url
    },
    // 新增
    async onSubmit () {
      let test = false;
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          test = true;
        }
      });
      if (test && !this.timer) {
        this.listLoading = true
        let params = {
          ...this.addForm
        }
        const { data } = await postProductLicenseRel(params)
        this.$message.success('新增成功')
        this.listLoading = false
        this.addForm = {
          fileIds: '',
          certificateNumber: '',
          name: '',
          licenseId: '',
          licenseType: 'QUALITY_REPORT',
          whetherForever: 'Y',
          productId: this.$route.query.id
        };
        this.addReport = false
        this.timer = true;
        this.getDetail()
        this.flagTimer = setTimeout(() => {
          this.timer = false;
        }, 1000)
      }

    },
    // 删除资质报告
    deleteLince (id) {
      this.$confirm('是否确认删除该条记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteReport(id)
      })
    },
    async deleteReport (id) {
      this.listLoading = true;
      const { data } = await deteteProductLicense(id)
      this.$message.success('删除成功')
      this.listLoading = false
      this.getDetail()
    },
    // 添加资质报告
    addReportMethod () {
      this.getLicenseType()
    },
    // 获取资质详情
    async getDetail () {
      this.listLoading = true;
      let id = this.$route.query.id
      Promise.all([getQualityDetail(id), getByLicenseBaseType({ type: 'PRODUCT'})]).then(res => {
        const { data } = res[0]
        this.detail = data
        this.productLicenseRelVoListMap.clear()
        if (!data.productLicenseRelVoList) {
          res[1].data.forEach(item => {
              const _found = this.productLicenseRelVoListMap.get(item.id)
              this.productLicenseMap.set(item.id, Object.assign({}, {
                licenseId: item.id,
                certificateNumber: '',
                licenseEndDate: '',
                fileIds: [],
                licenseType: 'PRODUCT_LICENSE',
                licenseTypeName: item.name,
                productId: 0,
                whetherForever: false,
                reminderDateType: 'A_MONTH_AGO',
                reminderDate: '',
                saleMerchantId: getLocalUser() && getLocalUser().saleMerchantId,
                isEdit: false,
              }, _found ||{}))
          })

          this.tableForm.tableData = [...this.productLicenseMap.values()].map(item => Object.assign({}, item))
          return;
        }
        data.productLicenseRelVoList.forEach(item => {
          this.productLicenseRelVoListMap.set(item.licenseId, {
              licenseId: item.licenseId,
              certificateNumber: item.certificateNumber,
              licenseEndDate: item.licenseEndDate,
              licenseType: item.licenseType.code,
              // licenseTypeName: _item.licenseType.desc,
              productId: item.productId,
              whetherForever: item.whetherForever.code === 'Y',
              reminderDateType: item.reminderDateType.code,
              reminderDate: item.reminderDate,
              saleMerchantId: item.saleMerchantId,
              fileIds: item.fileIds.split(',').map(fileId => {
                if(fileId.trim()!=0){
                  return {
                    name: fileId,
                    url: fileId,
                    id: fileId
                  }
                } else {
                  return 'null'
                }

              }),
              isEdit: false,
            })
        })
        this.productLicenseMap.clear()
        res[1].data.forEach(item => {
          const _found = this.productLicenseRelVoListMap.get(item.id)
          this.productLicenseMap.set(item.id, Object.assign({}, {
            licenseId: item.id,
            certificateNumber: '',
            licenseEndDate: '',
            fileIds: [],
            licenseType: 'PRODUCT_LICENSE',
            licenseTypeName: item.name,
            productId: 0,
            whetherForever: false,
            reminderDateType: 'A_MONTH_AGO',
            reminderDate: '',
            saleMerchantId: getLocalUser().saleMerchantId,
            isEdit: false,
          }, _found ||{}))
      })

      this.tableForm.tableData = [...this.productLicenseMap.values()].map(item => Object.assign({}, item))

      this.tableData1 = data.qualityReportRelVoList


      }).finally(_ => {
        this.listLoading = false
      })
      // const { data } = await getQualityDetail(id);
      // this.detail = data
      // this.tableForm.tableData = data.productLicenseRelVoList.map(item => Object.assign({}, item, {
      //   isEdit: false,
      //   fileIds: item.fileIds.split(',').map(item => {
      //     return {
      //       name: item,
      //       url: item,
      //       id: item
      //     }
      //   })
      // }))
      // this.tableData1 = data.qualityReportRelVoList
      // this.listLoading = false
    },
    async getLicenseType () {
      this.listLoading = true;
      let params = {
        type: 'QUALITY'
      }
      const { data } = await getByLicenseBaseType(params)
      this.addForm.name = data[0].name
      this.addForm.licenseId = data[0].id
      this.addReport = true
      this.listLoading = false;
    }
  },
  beforeDestroy() {
    clearTimeout(this.flagTimer);
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep{
    .el-image__inner{
       width: 100px;
       height: 80px
    }

  .title{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .imgRead{
      margin: 0 auto;
      text-align: center;
      padding-bottom: 20px;
      ::v-deep .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        width: 150px;
        height: 150px;
        position: relative;
        overflow: hidden;
        margin-bottom: 20px;
      }
      // /deep/ .avatar-uploader .el-upload:hover {
      //   border-color: #409eff;
      // }
      ::v-deep .avatar-uploader-icon {
        font-size: 24px;
        color: #8c939d;
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
      }
      ::v-deep .avatar {
        width: 150px;
        height: 150px;
        display: block;
      }
    }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 150px;
    height: 150px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  .avatar {
    width: 150px;
    height: 150px;
    display: block;
  }
  }
  .detailTable{
    background: #ffffff;
    padding: 0 20px 20px;
  }

  .title{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-form-item {
    margin-bottom: 0;
  }
  .table-form {
    ::v-deep{
      .el-upload-list{
        .el-upload-list__item{
          width: 40px;
          height: 40px;
        }
      }
      .el-upload{
        width: 40px;
        height: 40px;
        position: relative;
        >i{
          font-size: 16px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translateX(-50%) translateY(-50%);
        }
      }
    }
    .image-wrapper{
      >img{
        margin-right: 10px;
      }
    }
  }
.image_box {
  width: 50px;
  height: 50px;
}
</style>
