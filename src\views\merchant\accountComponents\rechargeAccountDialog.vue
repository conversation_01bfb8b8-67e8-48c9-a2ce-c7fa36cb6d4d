<template>
  <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" >
      <el-form-item :label="title+'：'" prop="amount">
        <div style="display: flex">
          <span>{{ text }}</span>
          <span style="margin: 0 10px">
            <el-input-number
              v-if="amountType === 'POINTS'"
              v-model="form.amount"
              :min="1"
              :step="1"
              :step-strictly="true"
              label="请输入大于0的整数"
            />
            <el-input-number
              v-else
              v-model="form.amount"
              :precision="2"
              :step="0.1"
              label="请输入大于0的数，保留两位小数"
            />
          </span>
          <span>{{ amountTypeUnit }}</span>
        </div>
      </el-form-item>
      <el-form-item label="备注信息：" prop="remark">
        <el-input type="textarea" v-model="form.remark" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { rechargeCustomerPoint } from '@/api/retailStore'

export default {
  name: 'rechargeAccountDialog',
  props: {
    amountType: {
      type: String
    }
  },
  data() {
    return {
      type: 'Add', // Reduce, 充值或扣减积分
      visible: false,
      loading: false,
      form: {
        id: '',
        amount: 0,
        remark: ''
      },
    }
  },
  computed: {
    amountTypeText() {
      return this.amountType === 'POINTS' ? '积分' : '现金'
    },
    amountTypeUnit() {
      return this.amountType === 'POINTS' ? '积分' : '元'
    },
    text() {
      return this.type === 'Add' ? '增加' : '扣减'
    },
    title() {
      return (this.type === 'Add' ? '充值' : '扣减') + this.amountTypeText
    },
    rules() {
      return {
        amount: [
          { required: true, message: `请输入要${(this.type === 'Add' ? '充值' : '扣减')}的${this.amountTypeText}`, trigger: 'blur' },
        ],
        remark: [
          { required: true, message: `请输入备注`, trigger: 'blur' },
        ]
      }
    }
  },
  methods: {
    show(id, type) {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.form.remark = ''
      this.type = type
      this.form.id = id
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        rechargeCustomerPoint({
          ...this.form,
          amountType: this.amountType,
          amount: this.type === 'Reduce' ? -this.form.amount : this.form.amount
        }).then(() => {
          this.$message.success(this.title + '成功')
          this.hide()
          this.$emit('reload')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
