<!-- 预览附近-->
<template>
  <el-dialog title="协议附件"
             width="60%"
             :visible.sync="showModal">
    <el-carousel :interval="5000" arrow="always" @change="onChange">
      <el-carousel-item v-for="(image, idx) in images" :key="idx">
        <div class="image-wrapper">
          <el-image :src="image" class="image-item" :preview-src-list="images"/>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="buttons">
      <el-button @click="showModal=false">取消</el-button>
      <el-button @click="onDownload" type="primary">下载附件</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: 'image-preview',
    props: {
      images: {
        type: Array,
        required: true,
        default: []
      }
    },
    data () {
      return {
        showModal: false,
        idx: 0,
      };
    },
    methods: {
      show () {
        this.showModal = true
      },
      onChange (idx) {
        this.idx = idx;
      },
      onDownload () {
        window.open(this.images[this.idx], '_blank');
      }
    }
  }
</script>

<style lang="scss" scoped>
  .buttons {
    text-align: right;
  }
  .image-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .image-item {
    height: 220px;
    width: auto;
  }
</style>
