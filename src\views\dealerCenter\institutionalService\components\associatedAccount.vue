<template>
  <el-dialog top="20px" title="查看账号" :visible.sync="visible" :close-on-click-modal="false" width="900px">
    <div style="margin-bottom: 15px; display: flex">
      <el-input
        style="margin-right: 10px; width: 400px"
        v-model="listQuery.model.keyword"
        placeholder="请输入姓名/登录账号/注册手机号"
      />
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        ref="table"
        :data="accountList"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="status" label="账号状态" width="180">
          <template slot-scope="{ row }">
            <span>{{ row.status.desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="account" label="登录账号" />
        <el-table-column prop="phone" label="注册手机号" />
        <el-table-column prop="registerTime" label="最新修改时间" />
        <el-table-column fixed="right" align="center" label="操作" class="itemAction" width="130">
          <template slot-scope="{ row }">
            <el-row class="table-edit-row">
              <span class="table-edit-row-item">
                <el-link type="primary" :disabled="passwordLoading" style="margin-right: 10px" @click="showPassword(row.id)">查看密码</el-link>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.current"
        :limit.sync="listQuery.size"
        @pagination="getList"
      />
    </div>
    <ShowPasswordDialog ref="showPasswordDialogRef" />
  </el-dialog>
</template>

<script>
import { getAccountList, getPassword2Id } from '@/api/dealerManagement'
import ShowPasswordDialog from './showPasswordDialog.vue'
import Pagination from '@/components/Pagination'

export default {
  name: 'associatedAccount',
  components: {
    ShowPasswordDialog,
    Pagination
  },
  data() {
    return {
      visible: false,
      loading: false,
      total: 0,
      passwordLoading: false,
      accountList: [],
      listQuery: {
        current: 1,
        size: 10,
        model: {
          keyword: '',
          orgInfoId: ''
        },
      },
    }
  },
  methods: {
    search() {
      this.listQuery.current = 1
      this.getList()
    },
    reset() {
      this.listQuery.model.keyword = ''
      this.search()
    },
    handleSelectionChange(val) {
      this.selectedAccountList = val
    },
    showPassword(id) {
      this.passwordLoading = true
      getPassword2Id(id).then(res => {
        if (res.code !== 0) return
        this.$refs.showPasswordDialogRef.show(res.data)
      }).finally(() => {
        this.passwordLoading = false
      })
    },
    async getList() {
      this.accountList = []
      this.$nextTick(() => {
        this.loading = true
        getAccountList(this.listQuery).then(res => {
          if (res.code !== 0) return
          this.total = res.data.total
          this.accountList = res.data.records || []
        }).finally(() => {
          this.loading = false
        })
      })
    },
    show(orgInfoId) {
      this.loading = true
      this.visible = true
      this.listQuery.model.orgInfoId = orgInfoId
      this.getList()
    },
    hide() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .pagination-container {
    margin-top: 0;
  }
}
</style>
