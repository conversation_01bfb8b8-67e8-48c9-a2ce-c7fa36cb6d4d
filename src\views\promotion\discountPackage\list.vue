<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="reload"
      @search="onSubmit"
    >
      <im-search-pad-item prop="name">
        <el-input v-model.trim="listQuery.model.name" placeholder="请输入套餐名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="promotionPackageState">
        <el-select v-model="listQuery.model.promotionPackageState" placeholder="套餐状态" >
          <el-option label="全部" value="" />
          <el-option label="上架中" value="ON" />
          <el-option label="已下架" value="OFF" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        v-model="activeName"
        :tabs="[ { name: '优惠套餐', value: 'first' } ]">
        <template slot="button">
          <el-button @click="load">刷新</el-button>
          <el-button type="primary" @click="editCoupon()" v-if="checkPermission(['admin','sale-saas-promotion-package:add', 'sale-platform-promotion-package:add'])">+ 新增套餐</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="postCouponList" :data.sync="tableData" :operation-width="220">
        <template slot="productRelList">
          <el-table-column label="品种数量">
            <slot slot-scope="{row}">
              {{row.productRelList.length}}
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-button type="text" @click="editCoupon(props.row.id)" v-if="checkPermission(['admin','sale-saas-promotion-package:edit', 'sale-platform-promotion-package:edit'])">编辑</el-button>
          <el-divider direction="vertical" v-if="checkPermission(['admin','sale-saas-promotion-package:edit', 'sale-platform-promotion-package:edit'])"></el-divider>
          <el-button type="text" @click="handleCheck(props.row)" v-if="checkPermission(['admin','sale-saas-promotion-package:detail', 'sale-platform-promotion-package:detail'])">查看商品</el-button>
          <el-divider direction="vertical" v-if="checkPermission(['admin','sale-saas-promotion-package:detail', 'sale-platform-promotion-package:detail'])"></el-divider>
          <el-button type="text" @click="handleOnOff(props.row.id,'ON')" v-if="props.row.promotionPackageState.code == 'OFF'&&checkPermission(['admin','sale-saas-promotion-package:putOnSale', 'sale-platform-promotion-package:putOnSale'])">上架</el-button>
          <el-button type="text" @click="handleOnOff(props.row.id,'OFF')" v-if="props.row.promotionPackageState.code == 'ON'&&checkPermission(['admin','sale-saas-promotion-package:pullOffShelves', 'sale-platform-promotion-package:pullOffShelves'])">下架</el-button>
          <el-divider direction="vertical" v-if="checkPermission(['admin','sale-saas-promotion-package:detail', 'sale-platform-promotion-package:detail'])"></el-divider>
          <el-button type="text" v-if="checkPermission(['admin','sale-saas-promotion-package:del', 'sale-platform-promotion-package:del'])" @click="handleDel(props.row.id)">删除</el-button>
        </div>
      </table-pager>
      <product-dialog ref="products-dialog" :checkList="goodsList" :detail="itemDetail"></product-dialog>
    </div>
  </div>
</template>

<script>
  import { getList,updatePromotionPackageState,postGoodsList,delList } from "@/api/discount"
  import checkPermission from '../../../utils/permission';
  import productDialog from '../components/product-dialog'

  const TableColumns = [
    { label: '套餐编码', name: 'code', prop: 'code' },
    { label: "套餐名称", name: "name", prop: "name" },
    { label: '套餐状态', name: 'promotionPackageState.desc', prop:'promotionPackageState.desc' },
    { label: '品种数量', name: 'productRelList', prop: 'productRelList',slot: true },
      ];

  const TableColumnList = [];

  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  export { TableColumnList };

  export default {
    name: 'discountPackage',
    components: {
      productDialog
    },
    data () {
      return {
        // table配置
        showSelectTitle: false,
        tableTitle: TableColumnList,
        tableVal: [],
        loading: false,
        showCoupon: false,
        showAdd: false,
        activeName: 'first',
        listQuery: {
          model: {
            'name': '',
            'promotionPackageState': '',
          }
        },
        tableData: [],
        goodsList: [],
        itemDetail: ''
      }
    },
    created () {
    },
    activated() {
      if(sessionStorage.getItem('discount')) {
        this.reload()
        sessionStorage.removeItem('discount')
      }
    },
    methods: {
      checkPermission,
      async postCouponList (params) {
        Object.assign(this.listQuery, params)
        this.loading = true
        return await getList(this.listQuery)
      },
      async handleOnOff(id,promotionPackageState) {
        const param = {
          promotionPackageState: promotionPackageState
        }
        const data = await updatePromotionPackageState(id,param)
        if(data.code === 0) {
          this.$message.success('操作成功！')
          this.onSubmit()
        }
      },
      // 获取已有的产品列表
      async getCheckGoodsList(productRelList) {
        const formData = new FormData()
        let idArr = productRelList.map(item=>item.productId)
        formData.append('ids', idArr.toString())
        let {data} = await postGoodsList(formData)
        this.goodsList = []
        this.goodsList = data.filter((items,index) => {
          if (idArr.includes(items.id)) {
            this.$set(items, 'main', productRelList[index].main)
            this.$set(items, 'num', productRelList[index].num )
            this.$set(items, 'price', productRelList[index].price)
            return items;
          }
        })
      },
      //查看商品
      handleCheck(rows) {
        this.itemDetail = rows
        this.getCheckGoodsList(rows.productRelList)
        this.$refs['products-dialog'].open()

      },
      // 新增或者编辑
      editCoupon (id, type) {
        this.$router.push({
          path: '/promotion/discount/edit',
          query: {
            saleMerchantId: this.saleMerchantId,
            id: id,
            type: type
          }
        })
      },
      handleDel(id){
        this.$confirm("您确定删除此优惠券套餐吗？", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delList(id).then(res=>{
            if(res.code == 0 && res.msg=='ok'){
              this.$message.success('删除成功');
              this.reload();
            }
          })
        })
        .catch((req) => {});
      },
      onSubmit () {
        // this.postCouponList()
        this.$refs.todoTable.doRefresh({
          page: 1,
          pageSize: 10
        })
      },
      async load (params) {
        Object.assign(this.listQuery, params)
        this.$refs.todoTable.doRefresh()
      },
      reload () {
        this.listQuery.model = {}
        this.$refs.todoTable.doRefresh()
      },
      pagination(val) {
        this.listQuery.current = val.page
        this.listQuery.size = val.limit
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
