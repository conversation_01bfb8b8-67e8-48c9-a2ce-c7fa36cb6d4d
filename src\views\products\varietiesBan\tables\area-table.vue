<template>
  <el-table border :data="tableData">
    <el-table-column type="index" label="序号" width="50"/>
    <el-table-column label="省份" prop="provinceName"/>
    <el-table-column label="城市" prop="cityNames"/>
    <el-table-column label="区" prop="districtNames"/>
    <el-table-column v-if="controlType === 'RESTRICTION'" label="控销数量">
      <template slot-scope="scope">
        <el-input v-model="scope.row.amount"/>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="60">
      <template slot-scope="scope">
        <el-button type="text" @click="handleDelete(scope.row,scope.$index)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { map as _map, reduce as _reduce } from 'lodash'
import { trees } from '@/api/group.js'
import { deleteControlItems } from '@/api/product'
import { analysisByDb } from '@/api/settingCenter'

export default {
  props: {
    controlType: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      tableData: [],
      areaData: [],
      hasArea: false
    }
  },
  mounted() {
    this.$emit('done')
    },
  methods: {
    //根据id查找地区
    async getTree(detailData) {
      const {data} = await trees()
      this.areaData = data
      this.tableData = _map(detailData, (item, idx) => {
        const province = {
          text: '',
          value: [],
          provinceId: '',
        }
        const city = {
          text: '',
          value: [],
          cityIdS: ''
        }
        const district = {
          text: '',
          value: [],
          districtIdS: ''
        }

        //省
        province.provinceId = item.provinceId
        city.cityIdS = item.cityIdS
        district.districtIdS = item.districtIdS
        let i = _map(this.areaData,(info,j) => info.id).indexOf(item.provinceId);
        province.value = this.areaData[i].province
        province.text = this.areaData[i].label
        if(item.cityIdS === 'ALL') {
          city.text = '全部'
          city.cityIdS = 'ALL'
          district.text = '全部'
        } else {
          let o, w,t
          _map(item.cityIdS.split(','),itm => {
            o = _map(this.areaData[i].children, (info, j) => info.id).indexOf(itm)
            city.text += ('，' + this.areaData[i].children[o].label)
          })

          city.value = this.areaData[i].children[o]
          _map(item.districtIdS.split(','),(its,index) => {
            let itsLast = its.substr(5, its.length)
            let itsFist = its.substr(0, 4)
            if (itsLast === 'ALL') {
              district.text += '，全部'
            } else {
              t = _map(this.areaData[i].children, (info, j) => info.id).indexOf(itsFist)
              w = _map(this.areaData[i].children[t].children, (info, j) => info.id).indexOf(itsLast)
              district.text += ('，' + this.areaData[i].children[t].label)
            }
          })
        }
        return {
          province: province,
          city: city,
          district: district,
          amount: item.amount,
          innerId: item.id
        }
      })
    },

    setData(type, data) {
      let curData = data[0]
      if(type === 1) {
        this.tableData.push({
          provinceName: curData.provinceName,
          provinceId: curData.provinceId,
          cityNames: curData.cityNames,
          cityIdS: curData.cityIdS,
          districtNames: curData.districtNames,
          districtIdS: curData.districtIdS,
          amount:''

        })
       /*_map(data, (item, idx) => {
          const province = {
            text: '',
            value: [],
            provinceId: '',
          }
          const city = {
            text: '',
            value: [],
            cityIdS: ''
          }
          const district = {
            text: '',
            value: [],
            districtIdS: '',
          }
          //省
          province.text = item.province.label
          province.value = item.province
          province.provinceId = item.province.id

          const disNew = _reduce(_map(item.districts, items => items.id.substring(0,4)),(prev,next,currentIndex, arr) => {
            prev[next] = (prev[next] + 1) || 1
            return prev
          },[])
          //市
          if (item.province.children.length !== item.citys.length) {
            city.text = _map(item.citys, item => item.label).join('，')
            city.cityIdS = _map(item.citys, item => item.id).join(',')
            _map(item.citys, (items,index) => {
              if (items.children.length === disNew[items.id]) {
                item.districts.splice(index,disNew[items.id])
                district.districtIdS += (',' + items.id + ':ALL')
                district.text += ('，全部')
                district.text.substr(1)
                district.districtIdS.substr(1)
              } else {
                item.districts.forEach(function (itms, idx) {
                  if(itms.id) {
                    let ids = itms.id.substring(0, 4) + ':' + itms.id
                    item.districts.splice(idx, 1)
                    district.districtIdS += (',' + ids)
                    district.text += ('，' + itms.label)
                    district.text.substr(1)
                    district.districtIdS.substr(1)
                  }
                })
              }
            })
          } else {
            if (item.citys[0].label === '市辖区') {
              city.text = '直辖区'
              city.cityIdS = item.citys[0].id
              if(item.citys[0].children.length === item.districts.length) {
                district.districtIdS = city.cityIdS + ':ALL'
                district.text = '全部'
              } else {
                _map(item.districts, (itms,idx) => {
                  const ids = itms.id.substring(0, 4) + ':' + itms.id
                  district.districtIdS += (','+ids)
                  district.text += ('，' + itms.label)
                  district.text.substr(1)
                  district.districtIdS.substr(1)
                })
              }
            } else {
              city.text = '全部'
              city.cityIdS = 'ALL'
              district.text = '全部'
              district.districtIdS = 'ALL:ALL'
            }
          }
         this.tableData.push({
            province: province,
            city: city,
            district: district,
            clientNumber: '',
            varietiesBanNumber: ''
          })
        })*/
      } else {
        _map(data, (item, index) => {
          analysisByDb({
            provinceId: item.provinceId,
            cityIdS: item.cityIdS,
            districtIdS: item.districtIdS
          }).then(res => {
            const curData = res.data
            this.tableData.push({
              checkData: curData.checkedKeys,
              provinceName: curData.provinceName,
              provinceId: item.provinceId,
              cityNames: curData.cityNames,
              cityIdS: item.cityIdS,
              districtNames: curData.districtNames,
              districtIdS: item.districtIdS,
              amount: item.amount,
              innerId: item.id,
            })
            this.loading = false
          })
        })

      }
    },
    handleDelete(row,idx) {
      this.tableData.splice(idx, 1)
      /*if(row.innerId) {
        this.$confirm('是否确定删除？').then(_ => {
          deleteControlItems(row.id,'AREA').then(res=> {
            this.$message.success('删除成功！')
          }).catch()
        }).catch(_ => {});
      } else {
        this.tableData.splice(idx, 1)
      }*/
    }
  }

}
</script>
