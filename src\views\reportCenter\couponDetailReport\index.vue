<template>
    <div>
        <!-- 搜索表单 start -->
        <im-search-pad :is-expand.sync="isExpand" :model="queryParams.model" @reset="reload" @search="onSubmit">
            <im-search-pad-item prop="erpCode">
                <el-input v-model.trim="queryParams.model.erpCode" placeholder="ERP客户编码" />
            </im-search-pad-item>
            <im-search-pad-item prop="purMerchantName">
                <el-input v-model.trim="queryParams.model.purMerchantName" placeholder="客户名称" />
            </im-search-pad-item>
            <im-search-pad-item prop="couponActivityCode">
                <el-input v-model.trim="queryParams.model.couponActivityCode" placeholder="活动编码" />
            </im-search-pad-item>
            <im-search-pad-item v-show="isExpand" prop="couponName">
                <el-input v-model.trim="queryParams.model.couponName" placeholder="优惠券名称" />
            </im-search-pad-item>
            <im-search-pad-item v-show="isExpand" prop="whetherVipCoupon">
                <el-select v-model="queryParams.model.whetherVipCoupon" placeholder="是否为专享券">
                    <el-option label="是" value="Y" />
                    <el-option label="否" value="N" />
                </el-select>
            </im-search-pad-item>
            <im-search-pad-item v-show="isExpand" prop="couponType">
                <el-select v-model="queryParams.model.couponType" placeholder="优惠券类型">
                    <el-option label="折扣券" value="DISCOUNT" />
                    <el-option label="满减券" value="FULL_REDUCTION" />
                </el-select>
            </im-search-pad-item>
            <im-search-pad-item v-show="isExpand" prop="receiveType">
                <el-select v-model="queryParams.model.receiveType" placeholder="领劵方式">
                    <el-option label="自主领取" value="AUTONOMY" />
                    <el-option label="手动赠送" value="GIVE" />
                </el-select>
            </im-search-pad-item>
            <im-search-pad-item v-show="isExpand" prop="orderStatus">
                <!-- multiple -->
                <el-select multiple collapse-tags v-model="queryParams.model.orderStatus" placeholder="订单状态">
                    <el-option label="待审核" value="WAIT_PROCESS" />
                    <el-option label="待发货" value="WAIT_DELIVERY" />
                    <el-option label="发货中" value="PART_DELIVERY" />
                    <el-option label="已发货" value="HAD_DELIVERY" />
                    <el-option label="已完成" value="SUCCESS" />
                    <el-option label="已取消" value="CANCEL" />
                </el-select>
            </im-search-pad-item>

            <im-search-pad-item v-show="isExpand" prop="receiveTime">
                <el-date-picker v-model="receiveTime" type="daterange" range-separator="至" value-format="yyyy-MM-dd"
                    start-placeholder="领劵开始日期" end-placeholder="领劵结束日期" />
            </im-search-pad-item>

            <im-search-pad-item v-show="isExpand" prop="deactivationTime">
                <el-date-picker v-model="deactivationTime" type="daterange" range-separator="至"
                    value-format="yyyy-MM-dd" start-placeholder="失效开始日期" end-placeholder="失效结束日期" />
            </im-search-pad-item>
        </im-search-pad>
        <!-- 搜索表单 end -->

        <div class="tab_bg">
            <!--Tabs布局 start-->
            <tabs-layout ref="tabs-layout" :tabs="[{ name: '优惠券明细表' }]">
                <!--tabs右上角相关按钮-->
                <template slot="button">
                    <paged-export v-if="checkPermission(['admin', 'sale-saas-report-couponItem:export', 'sale-platform-report-couponItem:export'])" :max-usable="maxUsableExport" controllable :before-close="handleExportBeforeClose">
                    </paged-export>
                </template>
            </tabs-layout>
            <!--Tabs布局 end-->

            <!-- 统计 start -->
            <div class="statistic">
                <div v-for="item in statistic" :key="item.prop" class="item">
                    <h5>{{ item.text }}</h5>
                    <p>{{ item.value }}</p>
                </div>
                <!-- <el-row :gutter="20">
                    <el-col :span="4" v-for="item in statistic" :key="item.prop">
                        <h5>{{ item.text }}</h5>
                        <p>{{ item.value }}</p>
                    </el-col>
                </el-row> -->
            </div>
            <!-- 统计 end -->

            <!-- 表格 start -->
            <table-pager ref="pager-table" :is-need-button="false" show-summary :summary-method="getSummaries"
                :options="tableColumns" :remote-method="load" :data.sync="tableData" :selection="false"
                :pageSize="queryParams.size" :operation-width="150">

                <el-table-column slot="whetherVipCoupon" label="是否专享券" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.whetherVipCoupon ? scope.row.whetherVipCoupon.desc : '' }}</span>
                    </template>
                </el-table-column>


                <el-table-column slot="couponType" label="优惠券类型" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.couponType ? scope.row.couponType.desc : '' }}</span>
                    </template>
                </el-table-column>

                <el-table-column slot="useType" label="使用状态" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.useType ? scope.row.useType.desc : '' }}</span>
                    </template>
                </el-table-column>

                <el-table-column slot="receiveType" label="领券方式" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.receiveType ? scope.row.receiveType.desc : '' }}</span>
                    </template>
                </el-table-column>


                <el-table-column slot="orderStatus" label="订单状态" width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.orderStatus ? scope.row.orderStatus.desc : null }}</span>
                    </template>
                </el-table-column>


            </table-pager>
            <!-- 表格 end -->
        </div>
    </div>

</template>

<script>
const TableColumns = [
    {
        label: "ERP客户编码",
        name: "erpCode",
        prop: "erpCode",
        width: "150"
    },
    {
        label: "客户名称",
        name: "purMerchantName",
        prop: "purMerchantName",
        width: "120"
    },
    {
        label: "活动编码",
        name: "couponActivityCode",
        prop: 'couponActivityCode',
        width: "150"
    },
    {
        label: "优惠券名称",
        name: "couponName",
        prop: 'couponName',
        width: "150"
    },
    {
        label: "使用规则",
        name: "useRule",
        prop: 'useRule',
        width: "100"
    },
    {
        label: "是否专享券",
        name: "whetherVipCoupon",
        prop: 'whetherVipCoupon',
        width: "140",
        slot: true
    },
    {
        label: "优惠券类型",
        name: "couponType",
        prop: 'couponType',
        width: "160",
        slot: true
    },
    {
        label: "优惠券面值(元)",
        name: "couponValue",
        prop: 'couponValue',
        width: "180",
    },
    {
        label: "优惠金额(元)",
        name: "preferentialMoney",
        prop: 'preferentialMoney',
        width: "140"
    },
    {
        label: "领券方式",
        name: "receiveType",
        prop: 'receiveType',
        width: "200",
        slot: true
    },
    {
        label: "领券时间",
        name: "receiveTime",
        prop: 'receiveTime',
        width: "200"
    },
    {
        label: "失效时间",
        name: "deactivationTime",
        prop: 'deactivationTime',
        width: "200"
    },
    {
        label: "使用状态",
        name: "useType",
        prop: 'useType',
        width: "200",
        slot: true
    },
    {
        label: "用券时间",
        name: "useTime",
        prop: 'useTime',
        width: "200"
    },
    {
        label: "订单编号",
        name: "orderNo",
        prop: 'orderNo',
        width: "200"
    },
    {
        label: "订单金额(元)",
        name: "orderMoney",
        prop: 'orderMoney',
        width: "200"
    },
    {
        label: "实付金额(元)",
        name: "orderRealMoney",
        prop: 'orderRealMoney',
        width: "200"
    },
    {
        label: "订单状态",
        name: "orderStatus",
        prop: 'orderStatus',
        width: "200",
        slot: true
    }
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
        key: i,
        ...TableColumns[i]
    });
}
import { fetchCouponDetailReportList, fetchCouponDetailReportStatistics, exportCouponDetailsReport } from '@/api/reportCenter/couponDetailReport'

import PagedExport from "@/components/PagedExport/index";

import { MessageConfirmExport } from '@/utils/index';
import checkPermission from '@/utils/permission';

export default {
    name: 'couponsDetailList',
    data() {
        return {
            isExpand: false,
            receiveTime: [], // 领劵时间段
            deactivationTime: [], // 失效时间段
            maxUsableExport: 0,
            queryParams: {
                current: 1,
                size: 10,
                model: {
                    erpCode: '', // ERP客户编码
                    purMerchantName: '', // 客户名称
                    couponActivityCode: '', // 活动编码
                    couponName: '', // 优惠劵名称
                    whetherVipCoupon: '', // 是否专享劵
                    couponType: '', // 优惠价类型
                    receiveType: '', // 领劵方式
                    orderStatus: [], // 订单状态

                    receiveStartTime: '', // 领劵开始时间
                    receiveEndTime: '', // 领劵结束时间

                    deactivationStartTime: '', // 失效开始时间
                    deactivationEndTime: '',// 失效结束时间
                },
            },
            tableData: [],
            tableColumns: TableColumnList,
            statistic: [
                {
                    prop: 'orderRealTotalMoney',
                    text: '实付总额(元)',
                    value: 0
                },
                {
                    prop: 'orderTotalMoney',
                    text: '订单总额(元)',
                    value: 0
                },
                {
                    prop: 'couponTotalNumber',
                    text: '优惠券总数(张)',
                    value: 0
                },
                {
                    prop: 'couponTotalMoney',
                    text: '优惠券总面值(元)',
                    value: 0
                }
            ]
        }
    },

    methods: {
        checkPermission,
        /**
         * 格式化请求参数  -> 主要处理领券日期 和 失效日期两个字断
         */
        formatQueryParams() {
            const { receiveTime, deactivationTime } = this;
            const [receiveStartTime, receiveEndTime] = receiveTime;
            const [deactivationStartTime, deactivationEndTime] = deactivationTime;
            this.queryParams.model.receiveStartTime = receiveStartTime || ''
            this.queryParams.model.receiveEndTime = receiveEndTime || ''
            this.queryParams.model.deactivationStartTime = deactivationStartTime || ''
            this.queryParams.model.deactivationEndTime = deactivationEndTime || ''
        },


        /**
         * 搜索
         */
        onSubmit() {
            this.formatQueryParams();
            this.$refs['pager-table'].doRefresh(this.queryParams)
        },

        /**
         * 重置搜索表单
         */
        reload() {
            this.receiveTime = []
            this.deactivationTime = []
            this.$refs['pager-table'].doRefresh();
            this.queryParams.model.purMerchantId = "";
            this.queryParams.model.purMerchantName = "";
        },


        /**
         * 导出弹窗关闭之前的回调函数
         * start 开始页码  end 结束页码
         * params: {startPage: number, endPage: number}
         */
        async handleExportBeforeClose(params) {
            const loading = this.$loading({
                lock: true,
                text: '正在导出中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.4)'
            });

            const { data } = await exportCouponDetailsReport({
                ...this.queryParams,
                model: {
                    ...this.queryParams.model,
                    ...params
                }
            })
            loading.close();
            // exoprtToExcel(data, `优惠券明细报表${formatDataTime('yyyyMMDDHHmmss')}.xlsx`)
            return await MessageConfirmExport()
        },

        /**
         * 加载数据
         * params: {current: number, size: number}
         */
        async load(params) {
            this.formatQueryParams();
            this.queryParams = { ...this.queryParams, ...params }
            // this.queryParams.model.purMerchantId = this.$route.query.purMerchantId;
            // console.log('----decodeURIComponent(this.$route.query.purMerchant)------>',decodeURIComponent(this.$route.query.purMerchant));
            if (this.$route.query && this.$route.query.purMerchantName && this.$route.query.purMerchantId) {
                let routeQuery = this.$route.query || {};
                this.queryParams.model.purMerchantId = routeQuery.purMerchantId || '';
                this.queryParams.model.purMerchantName = routeQuery.purMerchantName || '';
                this.queryParams.model.whetherVipCoupon = routeQuery.whetherVipCoupon || '';
                this.queryParams.model.couponType = routeQuery.couponType || '';
                this.$router.push({ query: {} });
            }
            this.fetchStatistics(this.queryParams);
            const result = await fetchCouponDetailReportList(this.queryParams);
            this.maxUsableExport = result.data.total
            return result;
        },

        /**
         * 获取统计数据
         */
        async fetchStatistics(queryParams) {
            const { data } = await fetchCouponDetailReportStatistics(queryParams)
            this.statistic = this.statistic.map(item => {
                item.value = data[item.prop] || 0;
                return item
            })
        },

        /**
         * 合计
         */
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            const sumKeys = ['couponValue', 'preferentialMoney', 'orderMoney', 'orderRealMoney']
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                if (sumKeys.some(v => v === column.property)) {
                    sums[index] = data.map(v => Number(v[column.property])).reduce((prev, curr) => {
                        // const value = Number(curr);
                        return (Number(prev) + curr).toFixed(2);
                        // if (!isNaN(value)) {
                        //     return prev + curr;
                        // } else {
                        //     return prev;
                        // }
                    }, 0);
                }
            });
            return sums;
        },

    },
    components: {
        PagedExport
    }
}
</script>

<style lang="scss" scoped>
.statistic {
    padding: 30px 30px 0;
    margin-bottom: 16px;
    color: #333333;
    background-color: rgba(248, 248, 248, 1);

    .item {
        display: inline-block;
        margin-right: 50px;
        margin-bottom: 32px;

        h5 {
            font-size: 16px;
            margin-bottom: 16px;
        }

        p {
            font-size: 26px;
        }
    }



}
</style>
