<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="listvisible"
      width="65%"
      @close="handleClose"
    >
      <im-search-pad
        :has-expand="false"
        :model="listQuery"
        @reset="reset"
        @search="search"
      >
        <im-search-pad-item prop="customerCode">
          <el-input
            @keyup.enter.native="search"
            v-model="listQuery.model.customerCode"
            placeholder="请输入ERP客户编码"
          />
        </im-search-pad-item>
        <im-search-pad-item prop="name">
          <el-input
            @keyup.enter.native="search"
            v-model="listQuery.model.name"
            placeholder="请输入名称"
          />
        </im-search-pad-item>
      </im-search-pad>
      <div class="fr" style="margin-top: -68px">
        <el-button
          type="primary"
          @click="addUser"
          v-if="checkPermission(['admin', 'group:addClient'])"
          >+添加客户</el-button
        >
      </div>
      <!--<el-table
        ref="dragTable"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          min-width="100px"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button v-on:click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>-->
      <table-pager
        ref="todoTable"
        :options="tableTitle"
        :data.sync="tableData"
        :remote-method="load"
      >
        <div slot-scope="props">
          <!--<el-link @click="$router.push({ name: 'clientDetail', params: { id: props.row.id } })">查看</el-link>-->
          <del-el-button
            :targetId="props.row.purMerchantId"
            :text="delText"
            @handleDel="handleDel"
          ></del-el-button>
        </div>
      </table-pager>

      <div slot="footer" class="dialog-footer">
        <el-button @click="listvisible = false">取 消</el-button>
        <el-button type="primary" @click="listvisible = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryPageSaleMerchantGroupCustomerListDTO,
  delGroup,
} from "@/api/group";
import delElButton from "@/components/eyaolink/delElButton";
import Sortable from "sortablejs";
import checkPermission from "@/utils/permission";
const TableColumns = [
  { label: "ERP客户编码", prop: "customerCode", width: "150" },
  { label: "客户名称", prop: "name", width: "140" },
  { label: "企业类型", prop: "merchantType" },
  { label: "负责人", prop: "ceoName" },
  { label: "联系电话", prop: "ceoMobile", width: "120" },
  { label: "所在区域", prop: "region", width: "150" },
  { label: "创建时间", prop: "createTime", width: "155" },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export default {
  name: "DragTable",
  props: ["visible", "row", "saleMerchantId", "groupType"],
  components: {
    delElButton,
  },
  data() {
    return {
      delText: "您确定要移除此客户的绑定关系吗？",
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      tableData: [],
      listQuery: {
        model: {
          customerCode: "",
          merchantGroupId: "",
          name: "",
          saleMerchantId: "",
        },
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
      select: "1",
      input: "",
      title: "",
      listvisible: false,
      detail: "",
    };
  },
  created() {
    //this.getList();
  },
  methods: {
    checkPermission,
    handleClose() {
      // 子组件调用父组件方法，并传递参数
      this.$emit("changeShow", "false");
    },
    addUser() {
      this.$emit("addUser");
    },
    async handleDel(id) {
      let parmas = {
        purMerchantId: id,
        merchantGroupId: this.listQuery.model.merchantGroupId,
      };
      if (this.groupType == "CONTROL") {
        parmas.groupType = "CONTROL";
      }
      let { code } = await delGroup(parmas);
      if (code == 0) {
        this.$message.success("删除成功！");
      }
      this.reset();
    },
    search() {
      /*if (this.select === '1') {
        this.listQuery.model.purMerchantCode = this.input
        this.listQuery.model.purMerchantName = ''
      } else {
        this.listQuery.model.purMerchantName = this.input
        this.listQuery.model.purMerchantCode = ''

      }*/
      this.handleRefresh({
        page: 1,
        pageSize: 10,
      });
      this.load();
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10,
      });
      this.listQuery.model.customerCode = "";
      this.listQuery.model.name = "";
      // this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams);
    },
    async load(params) {
      this.listQuery.model.merchantGroupId = this.detail.id;
      this.listQuery.model.operateType = "VIEW";
      console.log("this.groupType", this.groupType);
      // if(this.groupType == 'CONTROL') {
      this.listQuery.model.groupType =
        this.groupType === "CONTROL" ? "CONTROL" : "";
      // }
      /*this.listQuery.model.saleMerchantId = this.saleMerchantId*/
      this.listLoading = true;
      Object.assign(this.listQuery, params);
      const { data } = await queryPageSaleMerchantGroupCustomerListDTO(
        this.listQuery
      );
      this.title = this.row.name + "（" + data.total + "）";
      return { data };
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost",
        setData: function (dataTransfer) {
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    },
  },
  watch: {
    visible() {
      this.listvisible = this.visible;
      if (this.listvisible === true) {
        this.$nextTick(() => {
          this.handleRefresh({
            page: 1,
            pageSize: 10,
          });
        });
      }
    },
    row() {
      this.detail = this.row;
    },
  },
};
</script>
