<template>
    <el-dialog  width="600px" v-bind="$attrs" v-on="$listeners" v-dialogDrag append-to-body title="批量转移" @open="handleOpen" @close="handleClose">
        <el-form ref="form" :model="model" :rules="rules" label-width="120px">
            <el-form-item prop="id" label="接收业务员：">
                <!-- <el-select :class="$style['w-full']" v-model="model.newSalesmanId" filterable remote placeholder="接收业务员"
                    :remote-method="fetch" :loading="loading">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select> -->
								<el-select
									filterable
									v-model="model.newSalesmanId"
									placeholder="请选择"
									style="width: 92%;"
								>
									<el-option
										v-for="(item, index) in options"
										:key="index"
										:label="`${item.label}`"
										:value="item.value"
									>
										<!-- <span style="float: left">{{ item.salesmanName }}</span>
										<span style="float: right; color: #8492a6; font-size: 13px">{{item.mobile}}</span> -->
									</el-option>
								</el-select>
            </el-form-item>
        </el-form>
        <div :class="$style['dialog-footer']">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="onOk">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { salemanListPort, batchTransferPort } from "@/api/salemanCenter/index";
export default {
    props: {
        // 旧销售员id
        salesmanId: {
          type: String,
          default: ''
        },
        // 销售商id
        salesMerchantId: {
          type: Array,
          default: []
        },
				salemanName: {
					type: String,
					default: () => {
						return ''
					}
				}
    },
    data() {
			return {
				model: {
					newSalesmanId: '',
					oldSalesmanId: '',
					purMerchantIds: []
				},
				options: [],
				loading: false,
				rules: {
					newSalesmanId: {
						required: true,
						message: '请选择业务员'
					}
				}
			}
    },
		// mounted(){
		// 	this.fetch()
		// },
    methods: {
        handleOpen() {
					this.fetch()
					const arr = []
					this.salesMerchantId.forEach(item => {
            arr.push(item.id)
					})
					this.model = {...this.model, oldSalesmanId: this.salesmanId, purMerchantIds: [...arr]}
					this.$nextTick(() => {
							this.$refs.form.clearValidate()
					})
        },
        handleClose() {
          this.$emit('update:visible', false)
        },
        async onOk() {
          let valid = await this.$refs.form.validate()
          if (!valid) return;
          const loading = this.$loading({
            lock: true,
            text: '转移中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
					try {
						const query = {...this.model}
						const { code, msg } = await batchTransferPort(query)
						if (code !== 0) {
								throw new Error({ msaage: msg })
						}
						loading.close();
						this.$message.success('转移成功');
						this.$emit('update:visible', false)
						this.$emit('onsuccess')
						this.model = { id: '' }
					} catch (error) {
						console.log('error', error)
						loading.close();
					}
        },

        /**
         * 获取平台业务员列表
         */
        async fetch() {
            try {
                this.loading = true;
                const { data, code } = await salemanListPort({});
                if (code === 0) {
                    this.options = data?.map(item => ({ value: item.id, label: item.name }))
                }
            } catch (error) {
                console.error('fetch saleman', error);
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>
<style lang="scss" module>
.w-full {
    width: 100%;
}

.dialog-footer {
    text-align: right;
}
</style>