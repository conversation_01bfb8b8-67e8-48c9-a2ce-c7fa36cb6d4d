<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="tagName">
        <el-input v-model.trim="model.tagName" @keyup.enter.native="searchLoad" placeholder="请输入标签名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '客户标签' }]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-pur-merchant-label:add','sale-platform-pur-merchant-label:add'])" type="primary" @click="showDialogFun">+ 新增客户标签</el-button>
        </template>
      </tabs-layout>
      <!-- table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :pageSize="pageSize" :operation-width="180">
        <template slot="tagType">
          <el-table-column label="标签类型" width="150" >
            <slot slot-scope="{ row }">
              <!-- {EXTERNAL:100,外部标签;INTERNAL:110,内部标签; SYSTEM:系统标签} -->
              <template v-if="row.tagType && row.tagType.code &&row.tagType.desc">
                {{ row.tagType.desc}}
              </template>
            </slot>
          </el-table-column>
        </template>
        <template slot="exhibit">
          <el-table-column label="标签展示" width="150">
            <slot slot-scope="{ row }">
              <div class="boxStyle disp" :style="{ backgroundColor: row.tagColor }">
                {{ row.tagName }}
              </div>
            </slot>
          </el-table-column>
        </template>
        <template slot="showStatus">
          <el-table-column label="前端是否显示" width="200">
            <slot slot-scope="{ row }">
              <el-radio-group v-model="row.showStatus.code" @change="showStatusChange(row)">
                <el-radio label="Y">显示</el-radio>
                <el-radio label="N">隐藏</el-radio>
              </el-radio-group>
            </slot>
          </el-table-column>
        </template>
        <template slot="bindingNum">
          <el-table-column label="查看客户" width="140">
            <slot slot-scope="{ row }">
              <el-button type="text" @click="handlEassociation(row.id)">
                {{ row.bindingNum }}
              </el-button>
            </slot>
          </el-table-column>
        </template>
        <!--操作栏-->
        <div slot-scope="{ row }">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button :disabled ='row.tagType.code==="SYSTEM"' type="text" v-if="checkPermission(['admin','sale-saas-pur-merchant-label:clientRelation','sale-platform-pur-merchant-label:clientRelation'])" @click="showImportFun(row.id, row.saleMerchantId)">关联客户</el-button>
              <el-button :disabled ='row.tagType.code==="SYSTEM"' type="text" v-if="checkPermission(['admin','sale-saas-pur-merchant-label:edit','sale-platform-pur-merchant-label:edit'])" @click="handleEdit(row.id)"
              >编辑</el-button
              >
              <del-el-button
                v-if="checkPermission(['admin', 'sale-saas-pur-merchant-label:del', 'sale-platform-pur-merchant-label:del'])"
                style="margin-left: 5px"
                :targetId="row.id"
                :text="delText"
                :tagTypeCode="row.tagType.code"
                @handleDel="handleDel"
              >
              </del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog v-dialogDrag :title="diaText" :visible.sync="dialogVisible" width="450px">
      <el-form ref="productFrom" :model="productFrom" label-width="130px">
        <el-form-item label="标签名称：" prop="tagName" :rules="[
          { required: true, trigger: 'blur', message: '请选择标签名称' },
        ]">
          <el-input v-model="productFrom.tagName" style="width: 200px" :maxlength="5" placeholder="请输入标签名称,限五个字">
          </el-input>
        </el-form-item>
        <el-form-item label="标签类型：" prop="tagType" :rules="[
          { required: true, trigger: 'blur', message: '请选择标签类型' },
        ]">
          <el-select v-model="productFrom.tagType" style="width: 200px" placeholder="请选标签类型">
            <el-option label="请选择" value=""></el-option>
            <el-option label="外部标签" value="EXTERNAL"></el-option>
            <el-option label="内部标签" value="INTERNAL"></el-option>
            <el-option label="系统标签" value="SYSTEM"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签颜色：" prop="tagColor" :rules="[
          { required: true, trigger: 'change', message: '请选择标签颜色' },
        ]">
          <div style="display: flex; align-items: center">
            <el-color-picker v-model="productFrom.tagColor"></el-color-picker>
            <div class="boxStyle" v-if="
              productFrom.tagName != undefined &&
              productFrom.tagName.length > 0 &&
              productFrom.tagColor != '#FFFFFF'
            " :style="{ backgroundColor: productFrom.tagColor }">
              {{ productFrom.tagName }}
            </div>
          </div>
        </el-form-item>
        <el-form-item class="formItem" prop="sort" label="分类排序:">
          <el-input-number clearable :min="1" v-model="productFrom.sort" placeholder="请填写分类排序"></el-input-number>
        </el-form-item>
        <el-form-item class="formItem" prop="showStatus" label="是否前端显示:" :rules="[
          {
            required: true,
            message: '请填写是否前端显示',
            trigger: 'change',
          },
        ]">
          <el-radio v-model="productFrom.showStatus" label="Y">显示</el-radio>
          <el-radio v-model="productFrom.showStatus" label="N">隐藏</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 查看用户 -->
    <CustomerList title="查看关联用户" v-if="productStatus" :visible.sync="productStatus" :request="request" :customerTagId="currentId" @closeProduct="closeProduct">
    </CustomerList>
    <!-- 关联客户 -->
    <AssociatedCustomerDialog ref="AssociatedCustomerDialog" :visible.sync="isAssociatedCustomer" @reload="reload" :tagId="tagId" :saleMerchantId="saleMerchantId" />
  </div>
</template>
<script>
const TableColumns = [
  {
    label: '标签名称',
    name: 'tagName',
    prop: 'tagName',
    width: '150'
  },
  {
    label: '标签类型',
    name: 'tagType',
    prop: 'tagType',
    width: '150',
    slot: true
  },
  {
    label: '标签颜色',
    name: 'tagColor',
    prop: 'tagColor',
    width: '120'
  },
  {
    label: '标签展示',
    name: 'exhibit',
    prop: 'exhibit',
    width: '150',
    slot: true
  },
  {
    label: '排序',
    name: 'sort',
    prop: 'sort',
    width: '100'
  },
  {
    label: '前端是否展示',
    name: 'showStatus',
    prop: 'showStatus',
    width: '250',
    slot: true
  },
  {
    label: '关联客户',
    name: 'bindingNum',
    prop: 'bindingNum',
    width: '140',
    slot: true
  }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}

import {
  page,
  getDetail,
  addLabel,
  updateLabel,
  delLabel
} from '@/api/merchantApi/customerlabel'
import { merchantPurSaleRelList,seeAssociation } from '@/api/group'
import delElButton from '@/components/eyaolink/delElButton'
import CustomerList from '@/views/merchant/customerLabelGroup/components/customerList'
// import importDialog from '@/components/eyaolink/importDialog/index'
import AssociatedCustomerDialog from './components/AssociatedCustomerDialog.vue'
import checkPermission from '@/utils/permission'
export default {
  name:'customerTag',
  components: {
    delElButton,
    // importDialog
    AssociatedCustomerDialog,
    CustomerList
  },

  data() {
    return {
      isAssociatedCustomer: false,
      isExpand: false,
      model: {
        tagName: ''
      },
      diaText: '新增客户标签',
      productFrom: {
        tagType: "",
        tagName: "",
        tagColor: "#00CB25",
        sort: 1,
        showStatus: 1
      },
      currentId: '',
      saleMerchantId: '',//销售商id
      delText: '您确定删除该客户标签吗？',
      dialogVisible: false,
      productStatus: false,
      tableData: [],
      pageSize: 10,
      tableColumns: TableColumnList,
      actionUploadUrl: '/api/merchant/admin/merchantImport/importMerchantExcel',
      templateKey: 'IMPORT_MERCHANT_EXCEL_TEMP',
      queryData: {
        purMerchantType: 'MERCHANT_TAG_PUR_IMPORT',
        importId: ''
      },
      tagId: '',
      request: seeAssociation
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.productFrom = {
          tagType: "",
          tagName: "",
          tagColor: "#00CB25",
          sort: 1,
          showStatus: 1
        }
      }
    },
  },
  //方法集合
  methods: {
    checkPermission,
    async load(params) {
      let listQuery = {
        model: this.model
      }
      Object.assign(listQuery, params)
      return await page(listQuery)
    },
    // 表格中前端是否展示的radio的改变事件
    showStatusChange(val) {
      console.info(val)
      let params = {
        tagType: val.tagType.code,
        tagName: val.tagName,
        tagColor: val.tagColor,
        sort: val.sort,
        showStatus: val.showStatus.code,
        id: val.id
      }
      updateLabel(params).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          this.$message.success('修改显示状态成功');
          this.reload();
        }
      })
    },
    //    编辑
    handleEdit(id) {
      getDetail(id).then((res) => {
        if (res.code == 0 && res.msg == 'ok') {
          this.dialogVisible = true
          this.diaText = '编辑客户标签'
          this.productFrom = {
            tagType: res.data.tagType.code,
            tagName: res.data.tagName,
            id: res.data.id,
            showStatus: res.data.showStatus.code,
            tagColor: res.data.tagColor,
            sort: res.data.sort
          }
        }
      })
    },
    // 删除
    handleDel(id) {
      delLabel(id).then((res) => {
        if (res.code == 0 && res.msg == 'ok') {
          this.$message.success('删除成功')
          this.reload()
        }
      })
    },
    // 新增客户标签
    showDialogFun() {
      this.diaText = '新增客户标签'
      this.dialogVisible = true
    },
    reload() {
      this.$refs['tabs-layout'].reset()
      this.isAssociatedCustomer = false
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    searchLoad() {
      this.handleRefresh({
        page: 1,
        pageSize: this.pageSize
      })
    },
    handleRefresh(pageParams) {
      this.$refs['pager-table'].doRefresh(pageParams)
    },
    //   关联客户弹窗
    handlEassociation(id) {
      this.currentId = id;
      this.productStatus = true
    },
    closeProduct() {
      this.productStatus = false
      this.reload()
    },
    submit() {
      this.$refs['productFrom'].validate((valid) => {
        if (valid) {
          // 验证成功
          let params = {
            ...this.productFrom
          }
          if (params.id && this.diaText == '编辑客户标签') {
            // 编辑
            updateLabel(params).then((res) => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('修改客户标签成功')
                this.dialogVisible = false
                this.reload()
              }
            })
          } else {
            // 新增
            addLabel(params).then((res) => {
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('新增客户标签成功')
                this.dialogVisible = false
                this.reload()
              }
            })
          }
        } else {
          // 验证失败
        }
      })
    },
    showImportFun(id,saleMerchantId) {
      this.tagId = id;
      this.saleMerchantId = saleMerchantId;
      this.isAssociatedCustomer = true;
    },
    //  导入商品回调·
    uploadChangeData() {
      this.reload()
    }
  }
}
</script>

<style lang="scss" scoped>
.boxStyle {
  margin-left: 10px;
  padding: 0 10px;
  border-radius: 5px;
  color: #fff;
}

.disp {
  display: inline;
  padding: 5px 10px;
}

.componentsList {
  ::v-deep .el-dialog__headerbtn {
    z-index: 99999;
  }

  ::v-deep .el-dialog__header {
    padding: 0 !important;
    border-bottom: none !important;
  }

  ::v-deep .el-dialog__body {
    padding-top: 10px !important;
  }
}
</style>
