<template>
  <div class="detail-wrapper">
    <page-title :title="detail.name" />
    <el-form>
      <div class="detail-items">
        <page-module-card title="基础信息">
          <el-form-item label="首营状态：">
            {{detail.firstCampStatus.desc}}
          </el-form-item>
        </page-module-card>
        <page-module-card title="基础信息">
          <div class="detail-flex">
            <p class="detail-flexItems">客户编码：{{detail.code}}</p>
            <p class="detail-flexItems">客户名称：{{detail.name}}</p>
            <p class="detail-flexItems">统一社会信用代码：{{detail.socialCreditCode}}</p>
            <p class="detail-flexItems">法人代表：{{detail.legalPerson}}</p>
          </div>
          <div class="detail-flex">
            <p class="detail-flexItems">负责人：{{detail.ceoName}}</p>
            <p class="detail-flexItems">负责人手机：{{detail.ceoMobile}}</p>
            <p class="detail-flexItems">所在区域：{{detail.region}}</p>
            <p class="detail-flexItems">注册地址 ：{{detail.registerAddress}}</p>
          </div>
          <div class="detail-flex">
            <p class="detail-flexItems">注册资金：{{detail.registerCapital}}</p>
            <p class="detail-flexItems">客户标识码：{{detail.identifyCode}}</p>
            <p class="detail-flexItems">客户分组：{{detail.merchantGroup}}</p>
          </div>
        </page-module-card>
        <page-module-card title="经营范围">
          <div class="detail-flex">
            <p class="detail-flexItems">药品：抗生素、化学药制剂、中成药、化学原料药、中药饮片、中药材</p>
            <p class="detail-flexItems">器械：一类医疗器械、二类医疗器械、三类医疗器械</p>
            <p class="detail-flexItems">化妆品：无</p>
            <p class="detail-flexItems">其他商品：保健用品、计生用品</p>
          </div>
        </page-module-card>
        <page-module-card title="客商资质">
          <el-table
            border
            :data="list"
          >
            <el-table-column label="证件类型" prop="name">
            </el-table-column>
            <el-table-column label="证件号" prop="id"></el-table-column>
            <el-table-column label="过期时间">
              <template slot-scope="{row}">
                {{row.licenseEndTime ? row.licenseEndTime : row.multiple.code === 'N' ? '长期' : '无'}}
              </template>
            </el-table-column>
            <el-table-column label="证件图片" prop="filePath">
              <template slot-scope="{row}">
                <img :src="row.filePath" width="50px" />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-quality-clientQualification:detail', 'sale-platform-quality-clientQualification:detail'])" @click="goDetail(row)">查看证件</el-button>
              </template>
            </el-table-column>
           </el-table>
         </page-module-card>
          <el-dialog
            title="客户资质"
            :visible.sync="deliveryVisible"
            width="75%">
            <div style="width:100%;height: 500px;text-align: center">
              <img :src="filePath" style="height: 100%;"/>
            </div>
          </el-dialog>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getMerchantLicenseDetail, getMerchantTypeDetails } from "@/api/quality"
import DetailItem from '@/views/merchant/list/detail-item'
import PageTitle from "../../../components/PageTitle/index";
import PageModuleCard from "../../../components/PageModuleCard/index";
import checkPermission from '@/utils/permission';
export default {
  components: {
    PageModuleCard,
    PageTitle,
    DetailItem
  },
  data () {
    return {
      detail: {
        status: '0'
      },
      filePath: '',
      deliveryVisible: false,
      merchantLicenses: [],
      list: [],
      listLoading: false
    }
  },
  created () {
    this.getMerchantLicenseDetail()
  },
  methods: {
    checkPermission,
    goDetail(row) {
        this.deliveryVisible = true
        this.filePath = row.filePath
    },
    async getMerchantLicenseDetail () {
      this.listLoading = true
      let id = this.$route.query.id

      const  { data } = await getMerchantLicenseDetail(id)
      this.listLoading = false
      this.detail = data
      this.merchantLicenses = data.merchantLicenses
      this.getMerchantTypeDetails()
    },
    async getMerchantTypeDetails () {
      const { data } = await getMerchantTypeDetails(this.detail.merchantTypeId)
      data.licenseBases.map((item,i) => {
          this.merchantLicenses.map((info,j) => {
            if(item.id === info.licenseBaseId) {
              this.list.push({
                name: item.name,
                id: item.id,
                licenseNumber: info.licenseNumber,
                licenseEndTime: info.licenseEndTime ? info.licenseEndTime : '长期',
                filePath: info.filePath
              })
            }
          })
      })
    }
  }
}
</script>
<style lang="scss" scope>
  .detail{
    &-flex{
      display: flex;
      justify-content: left;
      align-items: center;
    }

    &-flexItems{
      width: 25%;
    }
  }
</style>
