<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="handleReset"
      @search="onSubmit"
    >
      <im-search-pad-item prop="productCode">
        <el-input v-model.trim="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[{ name: '商品资质', value: '' }]" />
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <template slot="pictIdS">
            <el-table-column
              label="商品主图"
              align="center"
              >
              <template slot-scope="{ row }" align="center">
                <div>
                  <img :src="row.pictIdS|imgFilter" alt="" width="50px">  
                </div>
              </template>
            </el-table-column>
        </template>
        <template slot="productLicenseRelVoList">
            <el-table-column
              label="品种资质"
              align="center"
              >
              <template slot-scope="{ row }" align="center">
                <div>
                  <el-button type="text" v-if="row.productLicenseRelVoList">已上传{{row.productLicenseRelVoList.length}}张</el-button>
                  <el-button type="text" v-else>未上传</el-button>
                </div>
              </template>
            </el-table-column>
        </template>
        <template slot="qualityReportRelVoList">
            <el-table-column
              label="质检报告"
              align="center"
              >
              <template slot-scope="{ row }" align="center">
                <div>
                  <el-button type="text" v-if="row.qualityReportRelVoList">已上传{{row.qualityReportRelVoList.length}}张</el-button>
                  <el-button type="text" v-else>未上传</el-button>
                </div>
              </template>
            </el-table-column>
        </template>


        <div slot-scope="props">
             <el-button @click="goDetail(props.row.id)" v-if="checkPermission(['admin','sale-saas-quality-productQualification:detail', 'sale-platform-quality-productQualification:detail'])" type="text">查看详情</el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { postQualityList } from "@/api/quality";
import Sortable from "sortablejs";
import Pagination from '@/components/Pagination/index.vue'
import checkPermission from "../../../utils/permission";

const TableColumns = [
  { label: "商品主图", name: "pictIdS", prop: "pictIdS", slot: true},
  { label: "商品编码", name: "productCode", prop: "productCode", width: 180},
  { label: "商品名称", name: "productName", prop: "productName", width: 200},
  { label: "规格", name: "spec", prop: "spec", width: 150},
  { label: "批准文号", name: "approvalNumber", prop: "approvalNumber", width: 200},
  { label: "生产厂家", name: "manufacturer", prop: "manufacturer", width: 250},
  // { label: '所属商家', name: "saleMerchantName", prop: "saleMerchantName", width: 250},
  { label: '品种资质', name: "productLicenseRelVoList", prop: "productLicenseRelVoList", slot: true},
  { label: '质检报告', name: "qualityReportRelVoList", prop: "qualityReportRelVoList", slot: true }
];

const TableColumnList = [];

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}
export { TableColumnList };

export default {
  components: {
    Pagination
  },
  data () {
    return {
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      tableData: [],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        current: 1,
        size: 10,
        model: {
          productCode: '',
          productName: ''
        }
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
    }
  },
  methods: {
    checkPermission,
    async load (params) {
      this.listLoading = true
      Object.assign(this.listQuery, params)
      return await postQualityList(this.listQuery)
    },
    goDetail (id) {
      this.$router.push({
        path: '/quality/goods/detail',
        query: {
          id: id
        }
      })
    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (
        <div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },

    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },
    handleReset() {
      this.listQuery.model = {
          productCode: '',
          productName: ''
        }
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    },

    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    stringSplit (content) {
      if (content) {
        let pic = String(content).split(',')[0];
        return pic;
      } else {
        return 'https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/01/0267aba4-1dd1-438c-af1a-7ce206eff4a0.jpg'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .mode{
    background: #ffffff;
  }
</style>
