<template>
<el-form class="product-form" ref="form" :model="form" label-width="80px" label-position="right" :disabled="action === 'SHOW'">
  <template v-if="action === 'CREATE'">
    <basic-file ref="basicFile"/>
    <el-divider></el-divider>
  </template>
  <basic-information ref="basicInformation" :action="action" :isInt7="isInt7" :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <other-attributes ref="otherAttrs" :action="action" :isInt7="isInt7" :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <manage-parameters ref="manageParameters" :action="action" :isInt7="isInt7"
                     @changeShare="changeShare"
                     :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <price-information ref="priceInformation" :areaData="areaData" :action="action" :isInt7="isInt7" :is-share="isShare" :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <!-- <control-pin ref="controlPin" :areaData="areaData" :action="action" :isInt7="isInt7" :is-share="isShare" :isNumber2="isNumber2"></control-pin>
  <el-divider></el-divider> -->
  <content-information ref="contentInformation" :action="action" :isInt7="isInt7" :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <product-qualification ref="productQualification" :action="action" :isInt7="isInt7" :isNumber2="isNumber2"/>
  <el-divider></el-divider>
  <gps-quality ref="gpsQuality" :action="action"/>
</el-form>
</template>

<script>
import basicFile from './product-components/basic-file'
import basicInformation from './product-components/basic-information'
import otherAttributes from './product-components/other-attributes'
import manageParameters from './product-components/manage-parameters'
import priceInformation from './product-components/price-information'
import contentInformation from './product-components/content-information'
import productQualification from './product-components/product-qualification'
import gpsQuality from './product-components/gps-quality'
// import controlPin from '@/views/products/product/product-components/controlPin.vue'
import { translateDataEnumerations } from '@/utils/index'
export default {
  components: { basicFile, basicInformation, otherAttributes, manageParameters, priceInformation, contentInformation, productQualification, gpsQuality },
  props: {
    action: { required: true, type: String },
    areaData: {required: true, type: Array},
    // isType:{ required: true, type: String }
  },
  data () {
    return {
      form: {},
      isShare: 'N',
      isPriceAdjustable: 'N'
    }
  },

  methods: {
    /**
     * 7位正整数
     * @param value
     * @returns {boolean}
     */
    changeShare(value) {
      this.isShare = value
    },
    isInt7 (value) {
      let v = parseInt(value, 10);
      if (isNaN(v)) {
        return false;
      }
      if ((value + '').indexOf('.') > -1) {
        return false;
      }
      value = v;
      if (value <= 0) {
        return false;
      }
      // 最多7位
      return (v < 10000000);

    },
    /**
     * 最多两位小数正整数
     * @param value
     * @returns {boolean}
     */
    isNumber2 (value) {
      let v = parseFloat(value);
      if (isNaN(v)) {
        return false;
      }
      value = v;
      if (value <= 0) {
        return false;
      }
      let arr = (value + '').split('.');
      if (arr[1] && arr[1].length > 2) {
        return false;
      }
      return true;
    },
    restore (data) {
      // 处理枚举数据
      translateDataEnumerations(data, ['approvalStatus', 'curingType', 'keepWay', 'otcType', 'publishStatus',
        'specialMngMedicinal', 'whetherCashTransaction', 'whetherColdChain', 'whetherContainEphedrine', 'whetherEnableMultiSpec',
        'whetherJicai', 'whetherMedicareVariety', 'whetherOnSale', 'whetherPresell', 'whetherReturnable', 'whetherUnbundled',
      'whetherUseCoupon'])
      if (data.productLicenseRelVoList) {
        data.productLicenseRelVoList.forEach(item => {
          translateDataEnumerations(item, ['licenseType', 'reminderDateType', 'whetherForever'])
        })
      }
      if (data.skuVoList) {
        data.skuVoList.forEach(item => {
          translateDataEnumerations(item, ['whetherEnableMultiSpec', 'whetherGiveIntegral', 'whetherLimit'])
        })
      }
      if(this.$route.query.marketing == "DIRECT"){
        data.platformCategoryId = data.platformCategoryId;
      } else {
        data.platformCategoryId = data.categoryId;
      }
      let refs = ['basicInformation', 'otherAttrs', 'manageParameters', 'priceInformation', 'contentInformation', 'productQualification', 'gpsQuality'];
      refs.forEach(ref => {
        this.$refs[ref].setForm(data)
      })
    },
    getData () {
      // 获取子组件表单数据
      let refs = ['basicInformation', 'otherAttrs', 'manageParameters', 'priceInformation','contentInformation', 'productQualification', 'gpsQuality'];
      if(this.action === 'CREATE') {
        refs = ['basicInformation', 'otherAttrs', 'manageParameters', 'priceInformation','contentInformation', 'productQualification', 'gpsQuality','basicFile'];
      }
      let data = {};
      // 记录限购数量
      let limitQuantity = 0
      let stockQuantity = 0 // 库存数量
      for (let i = 0; i < refs.length; i++) {
        let formData = this.$refs[refs[i]].getData();
        if (!formData) {
          return false;
        }
        if (refs[i] === 'manageParameters') {
          limitQuantity = formData.maxBuy
          stockQuantity = formData.stockQuantity
        }
        data = {...data, ...formData};
      }
      data.skuList[0].limitQuantity = limitQuantity
      data.skuList[0].stockQuantity = stockQuantity
      return data;
    }
  }
}
</script>
