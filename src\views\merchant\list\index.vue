<template>
  <div>
    <div class="cards">
      <div class="item">
        <p>
          <span>资质即将过期</span>
          <el-tooltip
            effect="dark"
            content="部分客户的资质在30天后将过期，请及时更新资质"
            placement="top-start"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">
            {{ cards.preOverLicense }}
          </div>
          <el-button
            @click="onFilterCard('preOverLicense')"
            size="mini"
            type="danger"
            >{{
              listQuery.model.preOverLicense === "Y" ? "取消" : "筛选"
            }}</el-button
          >
        </div>
      </div>
      <div class="item">
        <p>
          <span>资质已过期</span>
          <el-tooltip
            effect="dark"
            content="部分客户的资质已过期，请尽快更新资质"
            placement="top-start"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">{{ cards.overLicense }}</div>
          <el-button
            @click="onFilterCard('overLicense')"
            size="mini"
            type="danger"
            >{{
              listQuery.model.overLicense === "Y" ? "取消" : "筛选"
            }}</el-button
          >
        </div>
      </div>
      <div class="item">
        <p>
          <span>近三个月未下单</span>
          <el-tooltip
            effect="dark"
            content="指截至当前，三个月内未下单的客户。已取消订单也视为未下单。"
            placement="top-start"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">
            {{ cards.threeMonthNotOrder }}
          </div>
          <el-button
            @click="onFilterCard('threeMonthNotOrder')"
            size="mini"
            type="danger"
            >{{
              listQuery.model.threeMonthNotOrder === "Y" ? "取消" : "筛选"
            }}</el-button
          >
        </div>
      </div>
      <div class="item">
        <p>
          <span>近六个月未下单</span>
          <el-tooltip
            effect="dark"
            content="指截至当前，六个月内未下单的客户。已取消订单也视为未下单。"
            placement="top-start"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </p>
        <div class="flex items-center">
          <div class="flex-1 weight-600 text-16">
            {{ cards.sixMonthNotOrder }}
          </div>
          <el-button
            @click="onFilterCard('sixMonthNotOrder')"
            size="mini"
            type="danger"
            >{{
              listQuery.model.sixMonthNotOrder === "Y" ? "取消" : "筛选"
            }}</el-button
          >
        </div>
      </div>
    </div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="handleReset"
      @search="onSubmit"
    >
      <im-search-pad-item prop="code">
        <el-input
          v-model.trim="listQuery.model.customerCode"
          @input="
            listQuery.model.customerCode = minxinLogin(
              listQuery.model.customerCode
            )
          "
          placeholder="请输入ERP编码"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input
          v-model.trim="listQuery.model.purMerchantName"
          placeholder="请输入客户名称"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="warehouseIds">
        <el-select
          v-model="listQuery.model.warehouseIds"
          class="width160"
          collapse-tags
          multiple
          placeholder="所在仓库"
          clearable
        >
          <el-option
            v-for="item in storeArr"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="firstCampStatus">
        <el-select
          v-model="listQuery.model.firstCampStatus"
          placeholder="请选择首营关系"
        >
          <el-option label="已建立" value="ESTABLISH"></el-option>
          <el-option label="未建立" value="NOT_ESTABLISH"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="publishStatus">
        <el-select
          v-model="listQuery.model.publishStatus"
          placeholder="请选择客户状态"
        >
          <el-option label="启用" value="Y"></el-option>
          <el-option label="冻结" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="customerSource">
        <el-select
          v-model="listQuery.model.customerSource"
          placeholder="请选择客户来源"
        >
          <el-option label="客户注册(B2B)" value="B2B"></el-option>
          <el-option label="业务员拓客" value="SALE"></el-option>
          <el-option label="手动创建" value="MANUALAAD"></el-option>
          <el-option label="erp对接" value="ERP_ADD"></el-option>
          <el-option label="客户注册(平台)" value="PLATFORM"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="salesmanName">
        <el-input
          v-model.trim="listQuery.model.salesmanName"
          placeholder="请输入业务员名字"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="address">
        <el-cascader
          v-model="listQuery.model.address"
          placeholder="请选择所在区域"
          :options="options"
          :props="{
            checkStrictly: true,
            expandTrigger: 'hover',
            value: 'id',
            label: 'label',
            children: 'children',
          }"
          clearable
          style="width: 240px"
          @change="parentChangeAction"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="merchantGroupId">
        <el-select
          v-model="listQuery.model.merchantGroupId"
          placeholder="请选择客户分组"
        >
          <el-option
            v-for="(item, index) in costometList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="ceoName">
        <el-input
          v-model.trim="listQuery.model.ceoName"
          placeholder="请输入企业负责人"
        />
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="customerPurchasingMethodList">
        <el-select
          v-model="listQuery.model.customerPurchasingMethodList"
          multiple
          collapse-tags
          placeholder="请选择采购方式"
        >
          <el-option label="先款后货" :value="0"></el-option>
          <el-option label="先货后款" :value="1"></el-option>
          <el-option label="全部" :value="3"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        :tabs="tabList"
        v-model="listQuery.model.approvalStatus"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button icon="el-icon-refresh" @click="handleReset"
            >刷新</el-button
          >
          <paged-export
            :max="10000"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-pur-merchant:export',
                'sale-platform-pur-merchant:export',
              ])
            "
            :text="'导出列表'"
            :max-usable="total"
            controllable
            :before-close="handleExportBeforeClose"
          >
          </paged-export>
          <el-button
            type="primary"
            v-if="!listQuery.model.approvalStatus"
            @click="handleChangeAdjustment()"
            >批量企业类型/业务员</el-button
          >
          <el-button
            type="primary"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-pur-merchant:method-edit',
                'sale-platform-pur-merchant:method-edit',
              ])
            "
            :disabled="this.ids.length == 0"
            @click="handleSetting"
            >设置采购方式</el-button
          >
          <el-button
            type="primary"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-pur-merchant:status-edit',
                'sale-platform-pur-merchant:status-edit',
              ])
            "
            :disabled="this.ids.length == 0"
            @click="batchSetting"
            >批量客户状态</el-button
          >
          <template v-if="listQuery.model.approvalStatus === 'PENDING'">
            <el-button
              type="primary"
              :disabled="this.ids.length == 0"
              @click="batchReject"
              >批量驳回</el-button
            >
            <el-button
              type="primary"
              :disabled="this.ids.length == 0"
              @click="batchPass"
              >批量通过</el-button
            >
          </template>
          <template v-else-if="listQuery.model.approvalStatus === 'ACCEPTED'">
            <el-button
              type="primary"
              :disabled="this.ids.length == 0"
              @click="batchReject"
              >批量驳回</el-button
            >
          </template>
          <el-button
            type="primary"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-pur-merchant:qualification-download',
                'sale-platform-pur-merchant:qualification-download',
              ])
            "
            v-throttle
            @click="handleOutExcel()"
            >资质打包下载</el-button
          >
          <el-button
            type="primary"
            v-if="
              checkPermission([
                'admin',
                'sale-saas-pur-merchant-client:view',
                'sale-platform-pur-merchant-client:view',
              ])
            "
            @click="addCustomer"
            >新增客户</el-button
          >
        </template>
      </tabs-layout>
      <table-pager
        ref="todoTable"
        :options="tableTitle"
        :data.sync="tableData"
        :operation-width="240"
        :remote-method="load"
        :selection="true"
        @selection-all="onAllSelect"
        @selection-change="handleSelectionChange"
      >
        <template slot="publishStatus">
          <el-table-column label="账号状态">
            <slot slot-scope="{ row }">
              <span v-text="row.publishStatus.code === 'Y' ? '启用' : '冻结'" />
            </slot>
          </el-table-column>
        </template>
        <template slot="approvalStatus">
          <el-table-column label="客户状态">
            <slot slot-scope="{ row }">
              <span v-text="row.approvalStatus && row.approvalStatus.desc" />
            </slot>
          </el-table-column>
        </template>
        <template slot="merchantLicenseFileList">
          <el-table-column label="资质图片" width="130">
            <slot slot-scope="{ row }">
              <div class="img-cell" v-if="row.merchantLicenseFileList">
                <div v-if="row.merchantLicenseFileList.length < 2">
                  <img
                    style="width: 50px; height: 50px; margin-right: 5px"
                    v-for="(i, indexs) in row.merchantLicenseFileList"
                    :key="indexs"
                    :src="i"
                    alt=""
                  />
                </div>
                <div class="img_box" v-else>
                  <img
                    style="width: 50px; height: 50px; margin-right: 5px"
                    :src="row.merchantLicenseFileList[0]"
                    alt=""
                  />
                  <div class="img-posi">
                    <img
                      style="width: 50px; height: 50px; margin-right: 5px"
                      :src="row.merchantLicenseFileList[1]"
                      alt=""
                    />
                    <div class="img_mask">
                      +{{ row.merchantLicenseFileList.length - 1 }}
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>无上传</div>
            </slot>
          </el-table-column>
        </template>

        <template slot="customerPurchasingMethod">
          <el-table-column label="采购方式" width="80">
            <slot slot-scope="{ row }">
              <span>{{
                row.customerPurchasingMethod &&
                row.customerPurchasingMethod.desc
              }}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span
              class="table-edit-row-item"
              v-if="
                checkPermission([
                  'admin',
                  'sale-saas-pur-merchant:qualification-preview',
                  'sale-platform-pur-merchant:qualification-preview',
                ])
              "
            >
              <el-button type="text" @click="handleBigImage(props.row)"
                >预览资质</el-button
              >
              <el-image
                :ref="`ref${props.row.id}`"
                style="width: 0; height: 0"
                :src="previewDetail[0]"
                :preview-src-list="previewDetail"
              ></el-image>
            </span>
            <span
              v-if="
                checkPermission([
                  'admin',
                  'sale-saas-pur-merchant:first-camp-edit',
                  'sale-platform-pur-merchant:first-camp-edit',
                ])
              "
              class="table-edit-row-item"
            >
              <el-button type="text" @click="handleChange(props.row)"
                >首营变更</el-button
              >
            </span>
            <span
              v-if="
                checkPermission([
                  'admin',
                  'sale-saas-pur-merchant:detail',
                  'sale-platform-pur-merchant:detail',
                ])
              "
              class="table-edit-row-item"
            >
              <el-button
                type="text"
                @click="
                  $router.push({
                    path: '/merchant/clientDetail',
                    query: { id: props.row.purMerchantId, tabType: 'PENDING' },
                  })
                "
              >
                查看</el-button
              >
            </span>
            <span
              v-if="
                checkPermission([
                  'admin',
                  'sale-saas-pur-merchant:edit',
                  'sale-platform-pur-merchant:edit',
                ])
              "
              class="table-edit-row-item"
            >
              <el-button
                type="text"
                @click="
                  $router.push({
                    path: '/merchant/list/editItem',
                    query: { id: props.row.purMerchantId },
                  })
                "
                >编辑</el-button
              >
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <el-dialog
      v-dialogDrag
      title="设置采购方式"
      width="30%"
      class="purchaseFormDialog"
      style="min-width: 550px"
      :visible.sync="visiPurchase"
    >
      <el-form
        ref="purchaseForm"
        :model="purchaseForm"
        label-width="90px"
        :rules="rules"
      >
        <el-form-item
          label="采购类型"
          prop="customerPurchasingMethod"
          :rules="[
            { required: true, trigger: 'blur', message: '请选择采购类型' },
          ]"
        >
          <el-radio-group v-model="purchaseForm.customerPurchasingMethod">
            <el-radio label="CASH_BEFORE_DELIVERY">仅先款后货</el-radio>
            <el-radio label="CASE_ON_DELIVERY">仅先货后款</el-radio>
            <el-radio label="SELECT_ALL">先款后货，先货后款</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visiPurchase = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitPurchase"
          :disabled="!purchaseForm.customerPurchasingMethod"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog v-dialogDrag title="首营变更" :visible.sync="changeVisible">
      <el-form
        ref="changeForm"
        :model="changeForm"
        label-width="130px"
        :rules="rules"
      >
        <el-form-item label="首营状态：" prop="firstCampStatusEnum">
          <el-select
            v-model="changeForm.firstCampStatusEnum"
            placeholder="请选择"
          >
            <el-option label="未建立" value="NOT_ESTABLISH" />
            <el-option label="已建立" value="ESTABLISH" />
          </el-select>
        </el-form-item>
        <el-form-item label="变更备注：" prop="remark">
          <el-input
            v-model="changeForm.remark"
            placeholder="请填写变更备注"
            autocomplete="off"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="ERP客户编码：">
          <el-input
            v-model="changeForm.customerCode"
            autocomplete="off"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm('changeForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('changeForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 新增客户 -->
    <el-dialog
      v-dialogDrag
      title="新增客户"
      :visible.sync="addCustomerVisible"
      width="30%"
    >
      <!-- 第一个弹窗的内容 -->
      <el-form
        :model="costomerForm"
        status-icon
        label-width="100px"
        v-if="costomerOne"
      >
        <el-form-item
          prop="purMerchantName"
          label="企业名称"
          :rules="[
            { required: true, message: '请输入企业名称', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="costomerForm.purMerchantName"
            filterable
            reserve-keyword
            remote
            :remote-method="remoteMethodFn"
            style="width: 100%"
            placeholder="请输入关键词搜索"
            :loading="remoteLoading"
          >
            <el-option style="" :value="''">
              <span
                style="
                  color: #0056e5;
                  display: inline-block;
                  width: 100%;
                  height: 100%;
                "
                @click="showNewAccount = true"
                >手动输入</span
              >
            </el-option>
            <el-option
              v-for="(item, index) in enterpriseList"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 第二个弹窗的内容 -->
      <div v-if="costomerTwo">
        <div>
          <i class="el-icon-warning-outline"></i>
          <span>该企业已创建档案，并已注册采购账号。</span>
          <template v-if="infoData.isSaas">
            <span>是否将其关联为您的客户？</span>
            <div>
              <div class="item_customer">
                <div>ERP客户编码：</div>
                <div>{{ infoData.customerCode }}</div>
              </div>
              <div class="item_customer">
                <div>登录账号：</div>
                <div>{{ infoData.loginAccount }}</div>
              </div>
              <div class="item_customer">
                <div>注册手机号：</div>
                <div>{{ infoData.userMobile }}</div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div v-if="costomerThree">
        <template v-if="infoData.isSass">
          <div style="margin-bottom: 15px">添加为我的客户？</div>
          <div>将该企业添加为我的客户，并在客户列表中管理。</div>
        </template>
        <template v-else>
          <div>
            <i class="el-icon-warning-outline"></i>
            <span>该企业已关联为您的客户！您可查看该客户档案。</span>
          </div>
          <div class="item_customer">
            <div>ERP客户编码：</div>
            <div>{{ infoData.customerCode }}</div>
          </div>
          <div class="item_customer">
            <div>登录账号：</div>
            <div>{{ infoData.loginAccount }}</div>
          </div>
          <div class="item_customer">
            <div>注册手机号：</div>
            <div>{{ infoData.userMobile }}</div>
          </div>
        </template>
      </div>
      <span slot="footer" class="dialog-footer" v-if="costomerOne">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleOneSure">确 定</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else-if="costomerTwo">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          v-if="
            infoData.isSass &&
            checkPermission(['admin', 'sale-saas-pur-merchant:detail'])
          "
          type="primary"
          @click="handleDetail"
          >查看详情</el-button
        >
        <el-button v-else type="primary" @click="associatedCustomer"
          >关联客户</el-button
        >
      </span>
      <span slot="footer" class="dialog-footer" v-else-if="costomerThree">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button
          v-if="infoData.isSass"
          type="primary"
          @click="handleThreeSure"
          >确定</el-button
        >
        <el-button v-else type="primary" @click="handleDetail"
          >查看详情</el-button
        >
      </span>
    </el-dialog>
    <!-- 企业名称手动输入 -->
    <el-dialog
      v-dialogDrag
      title="企业名称手动输入"
      append-to-body
      :visible.sync="showNewAccount"
      style="min-width: 500px"
      width="500px"
    >
      <el-form ref="newAccount" :model="newAccountForm" label-width="100px">
        <el-form-item prop="company" label="企业名称:">
          <el-input
            clearable
            style="width: 300px"
            v-model.trim="newAccountForm.company"
            placeholder="请填写企业名称"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showNewAccount = false">取 消</el-button>
        <el-button type="primary" @click="newCompany">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="导出资质"
      append-to-body
      v-if="exportStatus"
      width="50%"
      :visible.sync="exportStatus"
      :before-close="closeExportDia"
      v-dialogDrag
    >
      <export-page
        ref="exportPage"
        :total="total"
        :totalPage="totalPage"
      ></export-page>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportStatus = false">取消</el-button>
        <el-button type="primary" @click="submitExport">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-dialogDrag
      title="设置客户状态"
      width="20%"
      class="purchaseFormDialog"
      style="min-width: 350px"
      :visible.sync="displayBatchSetting"
    >
      <el-form
        ref="purchaseForm"
        :model="batchSettingForm"
        label-width="90px"
        :rules="rules"
      >
        <el-form-item
          label="客户状态"
          prop="publishStatus"
          :rules="[
            { required: true, trigger: 'blur', message: '请选择客户状态' },
          ]"
        >
          <el-radio-group v-model="batchSettingForm.publishStatus">
            <el-radio label="Y">启用</el-radio>
            <el-radio label="N">冻结</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="displayBatchSetting = false">取 消</el-button>
        <el-button type="primary" @click="submitBatchSetting">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 导入弹窗 -->
    <importDialog
      ref="importDialogRef"
      :tipsList="tipsList"
      :actionUploadUrl="actionUploadUrl"
      :templateKey="templateKey"
      :isShowTipsDia="false"
      :queryParams="{}"
      @uploadChangeData="uploadChangeData"
    ></importDialog>
  </div>
</template>

<script>
import {
  merchantPurSaleRelList,
  merchantGroupListNew,
  findByUserIdSale,
  listMerchantGroupBySaleMerchantId,
  trees,
  updateSetFirstCampStatusAndRemark,
  savePurSaleRel,
  batchPurChasingByIds,
  getByCreditName,
  getPurMerchantName,
  updatePublishStatusByIds,
  exportMerchantPurSaleRel,
  getPurLicenseCount,
  getNotOrderCustomer,
  updatePurMerchantRejected,
  updatePurMerchantAcceptedByIds,
} from "@/api/group";

import { packagingMerchantLicenseDownload } from "@/api/registercheck";
import { exoprtZipByURL } from "@/utils/commons";
import { formatDataTime, MessageConfirmExport, MessageConfirmImport } from "@/utils/index";
import checkPermission from "@/utils/permission";
import ExportPage from "@/views/merchant/registerCheck/components/exportPage";
import TabsLayout from "../../../components/TabsLayout/index";
import { getAllStore } from "@/api/products/store";
import PagedExport from "@/components/PagedExport/index";
import importDialog from "@/components/eyaolink/importDialog/index.vue";
const TableColumns = [
  {
    lable: "采购方式",
    prop: "customerPurchasingMethod",
    name: "customerPurchasingMethod",
    width: "100",
    slot: true,
  },
  {
    label: "账户状态",
    prop: "publishStatus",
    name: "publishStatus",
    width: "80",
    slot: true,
  },
  {
    label: "客户状态",
    prop: "approvalStatus",
    name: "approvalStatus",
    width: "80",
    slot: true,
  },
  {
    label: "ERP编码",
    prop: "customerCode",
    width: "80",
  },
  {
    label: "客户名称",
    prop: "name",
    width: "150",
  },
  {
    label: "企业类型",
    prop: "merchantType",
    width: "120",
  },
  {
    label: "信用代码",
    prop: "socialCreditCode",
    width: "80",
  },
  {
    label: "法人代表",
    prop: "legalPerson",
  },
  {
    label: "负责人",
    prop: "ceoName",
  },
  {
    label: "联系电话",
    prop: "ceoMobile",
    width: "115",
  },
  {
    label: "所在区域",
    prop: "region",
    width: "160",
  },
  {
    label: "注册地址",
    prop: "registerAddress",
    width: "170",
  },
  {
    label: "所在仓库",
    prop: "warehouseNames",
    width: "170",
  },
  {
    label: "首营状态",
    prop: "firstCampStatus.desc",
  },
  {
    label: "客户来源",
    prop: "customerSource.desc",
    width: "150",
    // slot: true
  },
  {
    label: "推荐人",
    name: "referee",
    width: "130",
  },
  {
    label: "资质",
    name: "merchantLicenseFileList",
    prop: "merchantLicenseFileList",
    width: "150",
    slot: true,
  },
  {
    label: "所属业务员",
    prop: "salesmanName",
    width: "130",
  },
  {
    label: "最近下单时间",
    prop: "latestOrderTime",
    width: "130",
  },
  {
    label: "修改时间",
    prop: "updateTime",
    width: "130",
  },
  {
    label: "创建时间",
    prop: "createTime",
    width: "160",
  },
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i],
  });
}
export { TableColumnList };
const getInitCards = () => {
  return {
    overLicense: 0,
    preOverLicense: 0,
    threeMonthNotOrder: 0,
    sixMonthNotOrder: 0,
  };
};
export default {
  name: "purchaserFilesList",
  components: {
    ExportPage,
    TabsLayout,
    PagedExport,
    importDialog,
  },
  data() {
    return {
      exportStatus: false,
      isExpand: false,
      totalPage: 0,
      previewDetail: [],
      storeArr: [],
      displayBatchSetting: false,
      batchSettingForm: {
        publishStatus: "Y",
      },
      showNewAccount: false,
      remoteLoading: false,
      isExpand: false,
      showSelectTitle: false,
      tableTitle: TableColumnList,
      addCustomerVisible: false,
      costomerOne: true,
      costomerTwo: false,
      costomerThree: false,
      costometList: [],
      ids: [],
      enterpriseList: [],
      visiPurchase: false, // 设置采购方式
      purchaseForm: {
        customerPurchasingMethod: "",
      },
      tabList: [
        {
          name: "全部",
          value: "",
          hide: !checkPermission([
            "admin",
            "sale-saas-pur-merchant:view",
            "sale-platform-pur-merchant:view",
          ]),
        },
        {
          name: "待审核",
          value: "PENDING",
        },
        {
          name: "已通过",
          value: "ACCEPTED",
        },
        {
          name: "已驳回",
          value: "REJECTED",
        },
      ],
      costomerForm: {
        purMerchantName: "",
      },
      newAccountForm: {
        company: "",
      },
      infoData: {
        loginAccount: "",
        userMobile: "",
        customerCode: "",
        isSaas: false,
        id: "",
      },
      tableData: [],
      tableVal: [],
      list: [],
      total: 0,
      page: 0,
      listLoading: false,
      changeForm: {
        firstCampStatusEnum: "NOT_ESTABLISH",
        remark: "",
        customerCode: "",
        id: "",
      },
      rules: {
        firstCampStatusEnum: [
          {
            required: true,
            trigger: "blur",
            message: "请选择变更状态",
          },
        ],
        remark: [
          {
            required: true,
            trigger: "blur",
            message: "请选择变更备注",
          },
        ],
      },
      changeVisible: false,
      listQuery: {
        model: {
          publishStatus: "",
          warehouseIds: [],
          overLicense: "N",
          preOverLicense: "N",
          purMerchantName: "",
          saleMerchantId: "",
          ceoName: "",
          customerPurchasingMethodList: [],
          provinceId: "",
          cityId: "",
          countyId: "",
          merchantGroupId: "",
          merchantTypeId: "",
          customerCode: "",
          customerSource: "",
          salesmanName: "",
          firstCampStatus: "",
          threeMonthNotOrder: "N",
          sixMonthNotOrder: "N",
        },
      },
      form: {
        ceoName: "",
        name: "",
        code: "",
        address: "",
        merchantGroup: "",
        customerCode: "",
      },
      cards: getInitCards(),
      sortable: null,
      oldList: [],
      newList: [],
      activeName: "first",
      options: [],
      timer: null,
      // 导入提示
      tipsList: ["仅支持xlsx格式文件。"],
      // 上传地址
      actionUploadUrl: "/api/merchant/admin/purMerchant/importAdjustment",
      // 下载模板key值
      templateKey: "MERCHANT_IMPORT_ADJUSTMENT_EXCEL_TEMP",
    };
  },
  created() {
    this.getCount();
    /*    this.getSaleId()*/
    this.getArea();
    this.getCustomerGroup();
    this.getStoreList();
  },
  watch: {
    addCustomerVisible(val) {
      if (!val) {
        this.costomerForm = {
          name: "",
          socialCreditCode: "",
        };
        this.enterpriseList = [];
        this.costomerOne = true;
        this.costomerTwo = false;
        this.costomerThree = false;
      }
    },
    visiPurchase(newVal, oldVal) {
      if (!newVal && oldVal) {
        this.timer = setTimeout(() => {
          this.purchaseForm = {
            customerPurchasingMethod: "",
          };
        }, 10);
      }
    },
  },
  methods: {
    // 批量修改企业类型/所属业务员
    handleChangeAdjustment() {
      this.$refs.importDialogRef.initOpen();
    },
    // 企业类型/所属业务员上传成功回调
    uploadChangeData() {
      console.log('上传成功回调');
      this.handleReset();
      MessageConfirmImport();
    },
    // 关联客户
    associatedCustomer() {
      savePurSaleRel({
        purMerchantId: this.infoData.id,
      }).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          this.$message.success("添加成功");
          this.costomerThree = true;
          this.costomerTwo = false;
        }
      });
    },
    checkPermission,
    // 预览图片
    async handleBigImage(row) {
      this.previewDetail = row.merchantLicenseFileList || [];
      if (
        row.merchantLicenseFileList == undefined ||
        row.merchantLicenseFileList.length == 0
      ) {
        this.$message.warning("无图片可查看");
        return;
      }

      this.$refs[`ref${row.id}`].showViewer = true;
    },
    getCount() {
      if (this.tabList.every((item) => item.hide)) return;
      let { model } = this.listQuery;
      Promise.all(
        model.sixMonthNotOrder === "Y"
          ? [
              getPurLicenseCount({
                ...model,
                ...this.getQueryTimeoutNoOrderPurScreen(),
              }),
              getNotOrderCustomer({ ...model, timeoutNoOrderPurScreen: 6 }),
            ]
          : [
              getPurLicenseCount({
                ...model,
                ...this.getQueryTimeoutNoOrderPurScreen(),
              }),
              getNotOrderCustomer({ ...model, timeoutNoOrderPurScreen: 6 }),
              getNotOrderCustomer({ ...model, timeoutNoOrderPurScreen: 3 }),
            ]
      ).then((res) => {
        let cards = getInitCards();
        const purLicenseRes = res[0];
        if (purLicenseRes.code === 0) {
          Object.keys(cards).forEach((key) => {
            cards[key] = purLicenseRes.data ? purLicenseRes.data[key] : 0;
          });
        }
        if (res[1].code === 0) cards.sixMonthNotOrder = res[1].data || 0;
        if (res.length === 2) {
          cards.threeMonthNotOrder = cards.sixMonthNotOrder;
        } else {
          if (res[2].code === 0) cards.threeMonthNotOrder = res[2].data || 0;
        }
        this.cards = cards;
      });
    },
    /**
     * @description 获取全部仓库的下拉列表
     * <AUTHOR>
     */
    getStoreList() {
      getAllStore().then((res) => {
        const data = res.data;
        if (data && data.length) {
          data.forEach((item) => {
            const obj = {};
            obj.label = item.name;
            obj.value = item.id;
            this.storeArr.push(obj);
          });
        }
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          updateSetFirstCampStatusAndRemark(this.changeForm).then((res) => {
            this.$message.success("首营变更操作成功！");
            this.changeVisible = false;
            this.onSubmit();
          });
        } else {
          return false;
        }
      });
    },
    onFilterCard(key) {
      let { model } = this.listQuery;
      model[key] = model[key] === "Y" ? "N" : "Y";
      this.handleRefresh({
        page: 1,
        pageSize: 10,
      });
      this.getCount();
    },
    // 获取客户分组
    getCustomerGroup() {
      let params = {
        current: 1,
        model: {},
        size: 10000,
      };
      merchantGroupListNew(params).then((res) => {
        // console.log("客户分组---------->", res);
        if (res.code == 0 && res.msg == "ok") {
          this.costometList = res.data.records || [];
        }
      });
    },
    remoteMethodFn(query) {
      if (query != "") {
        this.remoteLoading = true;
        getByCreditName(query).then((res) => {
          this.remoteLoading = false;
          if (res.code == 0 && res.msg == "ok") {
            this.enterpriseList = res.data || [];
          }
        });
      } else {
        this.enterpriseList = [];
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.changeVisible = false;
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10,
      });
      this.getCount();
    },
    handleChange(row) {
      // console.log("row--->", row);
      this.changeForm.id = row.id;
      this.changeForm.firstCampStatusEnum = row.firstCampStatus.code;
      this.changeForm.remark = row.remarks;
      this.changeForm.customerCode = row.customerCode;
      this.changeVisible = true;
    },
    handleChangeTab(tab, index) {
      this.listQuery.model.approvalStatus = tab.value;
      this.onSubmit();
    },
    // 新增客户
    addCustomer() {
      this.addCustomerVisible = true;
    },
    // 新增客户取消按钮
    handleCancel() {
      this.addCustomerVisible = false;
    },
    // 第一个弹窗确定
    handleOneSure() {
      getPurMerchantName(this.costomerForm.purMerchantName).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          let purName = "";
          if (res.data == "null" || res.data == null) {
            this.enterpriseList.forEach((item) => {
              if (item.id == this.costomerForm.purMerchantName) {
                purName = item.name;
              }
            });
            this.addCustomerVisible = false;
            // 没有查到去新增
            this.$router.push({
              path: "/merchant/list/editItem",
              query: {
                purMerchantName: encodeURIComponent(purName),
              },
            });
          } else {
            this.infoData.customerCode = res.data.customerCode;
            this.infoData.loginAccount = res.data.loginAccount;
            this.infoData.userMobile = res.data.userMobile;
            this.infoData.id = res.data.id;
            // 要获取平台端和商家端
            const isSass = res.data.commerceModel.code === "SAAS";
            this.costomerOne = false;
            if (res.data.isAssociated.code === "Y" && !isSass) {
              this.costomerThree = true;
            } else {
              this.costomerTwo = true;
            }
          }
        }
      });
    },
    handleDetail() {
      this.$router.push({
        path: "/merchant/clientDetail",
        query: {
          id: this.infoData.id,
          tabType: "PENDING",
        },
      });
    },
    newCompany() {
      this.showNewAccount = false;
      this.addCustomerVisible = false;
      this.$router.push({
        path: "/merchant/list/editItem",
        query: {
          purMerchantName: encodeURIComponent(this.newAccountForm.company),
        },
      });
    },
    // 第二个弹窗确定
    handleTwoAdd() {
      this.costomerTwo = false;
      this.costomerThree = true;
    },
    // 第三个弹窗确定
    handleThreeSure() {
      savePurSaleRel({
        purMerchantId: this.infoData.purMerchantId,
      }).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          this.$message.success("添加成功");
          this.costomerThree = false;
          this.addCustomerVisible = false;
          this.handleReset();
        }
      });
    },
    batchPass() {
      this.$confirm("此操作将审核通过采购商，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { data } = await updatePurMerchantAcceptedByIds(
          this.ids.toString()
        );
        if (data) this.setSucss("ACCEPTED", "批量审核成功！");
      });
    },
    // 客户分组
    batchReject() {
      this.$confirm("此操作将审核驳回采购商，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let { data } = await updatePurMerchantRejected(this.ids.toString());
        if (data) this.setSucss("REJECTED", "批量驳回成功！");
      });
    },
    setSucss(type, msg) {
      this.$message.success(msg);
      this.handleReset();
    },
    async getList() {
      const { data } = await listMerchantGroupBySaleMerchantId(
        this.listQuery.model.saleMerchantId
      );
      this.list = data;
    },
    // 地区
    async getArea() {
      const { data } = await trees();
      this.options = data;
    },
    parentChangeAction(val) {
      this.listQuery.model = {
        provinceId: val[0],
        cityId: val[1],
        countyId: val[2],
      };
    },
    // 获取经销商id
    async getSaleId() {
      const userId = localStorage.getItem("userId");
      const { data } = await findByUserIdSale(userId);
      this.saleMerchantId = data.id;
      this.listQuery.model.saleMerchantId = data.id;
      this.getList();
    },
    getQueryTimeoutNoOrderPurScreen() {
      // 近三个月未下单和近六个月未下单同时选中只传6
      let timeoutNoOrderPurScreen = undefined;
      if (this.listQuery.model.threeMonthNotOrder === "Y") {
        timeoutNoOrderPurScreen = 3;
      }
      if (this.listQuery.model.sixMonthNotOrder === "Y") {
        timeoutNoOrderPurScreen = 6;
      }
      return { timeoutNoOrderPurScreen };
    },
    async load(params) {
      if (this.tabList.every((item) => item.hide)) return;
      this.listLoading = true;
      Object.assign(this.listQuery, params);
      this.ids = [];
      let result = await merchantPurSaleRelList({
        ...this.listQuery,
        model: {
          ...this.listQuery.model,
          ...this.getQueryTimeoutNoOrderPurScreen(),
        },
      });
      this.totalPage = result.data.pages;
      this.total = result.data.total;
      return result;
    },
    handleReset() {
      this.listQuery.model = {
        ceoName: "",
        customerPurchasingMethodList: [],
        name: "",
        code: "",
        address: "",
        merchantGroup: "",
        customerCode: "",
        customerSource: "",
        salesmanName: "",
        purMerchantName: "",
        merchantGroupId: "",
        firstCampStatus: "",
        approvalStatus: this.listQuery.model.approvalStatus,
        overLicense: "N",
        preOverLicense: "N",
      };
      this.handleRefresh({
        page: 1,
        pageSize: 10,
      });
      this.getCount();
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams);
    },
    handleSelectionChange(val) {
      this.ids = val.map(function (item, index) {
        return item.id;
      });
    },
    // 设置采购方式
    handleSetting() {
      // console.log("this.ids", this.ids);
      this.visiPurchase = true;
    },
    // 全部勾选
    onAllSelect(val) {
      this.ids = val.map(function (item, index) {
        return item.id;
      });
    },
    submitPurchase() {
      let params = {
        ids: this.ids.toString(),
        customerPurchasingMethod: this.purchaseForm.customerPurchasingMethod,
      };
      batchPurChasingByIds(params).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          if (res.data) {
            this.$message.success("设置采购方式成功");
            this.visiPurchase = false;
            this.ids = [];
            this.handleReset();
          }
        }
      });
    },

    // 导出数据选择弹窗关闭
    closeExportDia() {
      this.exportStatus = false;
    },
    handleOutExcel() {
      if (this.ids.length > 0) {
        const loading = this.$loading({
          lock: true,
          text: "正在导出中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.4)",
        });

        let model = {
          purMerchantIdList: this.ids,
        };
        packagingMerchantLicenseDownload(model).then((res) => {
          loading.close();
          if (res.code !== 0) throw new Error(res.msg);
          exoprtZipByURL(
            res.data,
            `客户资质${formatDataTime("yyyyMMDDHHmmss")}.zip`
          );
        });
        // 选中导出
      } else {
        this.exportStatus = true;
      }
    },
    // 确定导出
    submitExport() {
      const loading = this.$loading({
        lock: true,
        text: "正在导出中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.4)",
      });
      let params = this.$refs["exportPage"].getData();

      let model = {
        ...this.listQuery.model,
        ...params,
        purMerchantIdList: [],
      };
      // delete model.purMerchantIdList;
      packagingMerchantLicenseDownload(model).then((res) => {
        loading.close();
        if (res.code !== 0) throw new Error(res.msg);
        exoprtZipByURL(
          res.data,
          `客户资质${formatDataTime("yyyyMMDDHHmmss")}.zip`
        );
      });
    },
    // 批量设置客户状态
    batchSetting() {
      this.displayBatchSetting = true;
    },
    submitBatchSetting() {
      let params = {
        "ids[]": this.ids.toString(),
        publishStatus: this.batchSettingForm.publishStatus,
      };
      updatePublishStatusByIds(params).then((res) => {
        if (res.code == 0 && res.msg == "ok") {
          if (res.data) {
            this.$message.success("设置客户状态成功");
            this.displayBatchSetting = false;
            this.ids = [];
            this.handleReset();
          }
        }
      });
    },
    async handleExportBeforeClose(params) {
      const loading = this.$loading({
        lock: true,
        text: "正在导出中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.4)",
      });

      const { data } = await exportMerchantPurSaleRel({
        current: 1,
        map: {},
        order: "descending",
        size: "10",
        sort: "id",
        model: {
          ...this.listQuery.model,
          ...params,
        },
      });
      loading.close();
      // exoprtToExcel(data, `优惠券明细报表${formatDataTime('yyyyMMDDHHmmss')}.xlsx`)
      return await MessageConfirmExport();
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  },
};
</script>

<style>
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}
</style>

<style lang="less" scoped>
.item_customer {
  display: flex;
  align-content: center;
  margin-top: 15px;
}
/deep/ .purchaseFormDialog .el-dialog {
  min-width: 570px;
}

.img-cell {
  display: flex;
  align-items: center;
  .img_box {
    display: flex;
    align-items: center;
    position: relative;
    .img-posi {
      width: 50px;
      height: 50px;
    }
    .img_mask {
      position: absolute;
      width: 50px;
      height: 50px;
      top: 0;
      left: 55px;
      line-height: 50px;
      text-align: center;
      background-color: rgba(0, 0, 0, 0.6);
      color: #fff;
    }
  }
}
.cards {
  margin-bottom: 8px;

  .item {
    display: inline-block;
    width: 186px;
    padding: 16px 20px;
    margin-bottom: 8px;
    background-color: #fff;

    &:not(:last-child) {
      margin-right: 8px;
    }

    p {
      margin-bottom: 16px;
      font-size: 14px;
      color: #4e5766;
    }
  }
}
.tags {
  .item {
    padding: 2px 8px;
    display: inline-block;
    margin-bottom: 4px;
    border-radius: 2px;
    background: #eceffb;
    border: 1px solid #4062d8;
    font-size: 12px;
    line-height: 16px;
    color: #4062d8;

    &:not(:last-child) {
      margin-right: 4px;
    }
  }
}
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.mb-16 {
  margin-bottom: 16px;
}

.flex-none {
  flex: 0 0 auto;
}

.weight-600 {
  font-weight: 600;
}

.text-16 {
  font-size: 16px;
}

.text-left {
  text-align: left;
}

.text-red {
  color: #ff4245;
}

.text-orange {
  color: #ff5c00;
}
.text-green {
  color: #00ba81;
}
.text-blue {
  color: #4062d8;
}
</style>
