<template>
  <im-dialog :title="title" :visible.sync="visibleDialog" :width="width" :append-to-body="true" @confirm="confirm">
    <el-table
      ref="tableData"
      :data="tableData"
      border
    >
      <el-table-column label="序号" type="index" width="54" align="center" />
      <el-table-column label="导航名称" prop="picUrl" width="200">
        <template slot-scope="scope">
          <el-input v-model="scope.row.picUrl" placeholder="请输入导航名称" class="el-input-none-border" />
        </template>
      </el-table-column>
      <el-table-column label="链接类型" prop="linkType" width="180">
        <template slot-scope="scope">
          <el-select v-model="scope.row.linkType" placeholder="请选择链接类型" @change="changeLinkFun($event, scope.$index, scope.row)">
            <el-option
              v-for="itemInfo in selectList"
              :key="itemInfo.code"
              :label="itemInfo.name"
              :value="itemInfo.code"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="链接目标" prop="linkUrl" min-width="300">
        <template slot-scope="scope">
          <el-input v-model="scope.row.linkUrl" placeholder="链接目标">
            <ProductItemTable v-if="scope.row.linkType==='PRODUCT_DETAIL'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <StoreListTable v-if="scope.row.linkType==='STORE_INDEX'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <ProductTypeItemTable v-if="scope.row.linkType==='CATEGORYTYPE_LIST'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
            <HelpCenter v-if="scope.row.linkType==='HELP_CENTER'" slot="append" :width="'100px'" :select-items.sync="scope.row.linkParam" @confirm="confirmLink(scope.row)" />
          </el-input>
          <span style="display: none;">{{ scope.row.sortValue = scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleUp(scope.$index)">上移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDown(scope.$index)">下移</el-button>
            </span>
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
            </span>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-button" @click="add"><span>添加导航</span></div>
  </im-dialog>
</template>

<script>
import { advUpdate } from './index'

import ProductItemTable from '@/components/eyaolink/Product/productItemCodeTable'
import ProductTypeItemTable from '@/components/eyaolink/Product/ProductTypeItemTable'
import StoreListTable from '@/components/eyaolink/Store/listTable'
import HelpCenter from '@/components/eyaolink/help'

export default {
  name: 'MallNavigation',
  components: {
    ProductItemTable,
    ProductTypeItemTable,
    StoreListTable,
    HelpCenter
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '1000px'
    },
    selectList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      visibleDialog: false,
      tableData: []
    }
  },
  methods: {
    init(data) {
      if (data) {
        this.tableData = data
      }
      this.visibleDialog = true
    },
    confirmLink(row) {
      let index = 0
      if (row.linkUrl.lastIndexOf('=') === -1) {
        index = row.linkUrl.lastIndexOf('/')
      } else {
        index = row.linkUrl.lastIndexOf('=')
      }
      const str = row.linkUrl.substring(index + 1, row.linkUrl.length)
      row.linkUrl = row.linkUrl.replace(str, row.linkParam)
    },
    changeLinkFun(val, index, row) {
      row.linkParam = ''
      this.selectList.map(item => {
        if (item.code === val) {
          row.linkUrl = item.describe
        }
      })
    },
    confirm() {
      let valid = true
      const data = []
      this.tableData.map(val => {
        if (!val.picUrl) {
          valid = false
        } else {
          data.push({
            linkUrl: val.linkUrl,
            linkType: val.linkType,
            linkParam: val.linkParam,
            sortValue: val.sortValue,
            picUrl: val.picUrl,
            type: 'PC_MALL_NAVIGATION'
          })
        }
      })
      if (valid) {
        advUpdate(data).then(res => {
          this.$message.success('编辑导航成功')
          this.$emit('confirm')
          this.visibleDialog = false
        })
      } else {
        this.$message.error('请先输入导航名称')
      }
    },
    handleUp(index) {
      if (index !== 0) {
        this.tableData[index] = this.tableData.splice(index - 1, 1, this.tableData[index])[0]
      }
    },
    handleDown(index) {
      if (index !== this.tableData.length - 1) {
        this.tableData[index] = this.tableData.splice(index + 1, 1, this.tableData[index])[0]
      }
    },
    handleDel(index) {
      this.tableData.splice(index, 1)
    },
    add() {
      this.tableData.push({
        picUrl: '',
        linkUrl: '',
        linkType: '',
        linkParam: ''
      })
    }
  }
}
</script>

<style lang="less">
  .add-button{
    border: 1px solid #EBECEE;
    border-top: 0;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 14px;
      color: #0056e5;
      position: relative;
      &:after{
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        background-color: #0056E5;
        width: 7px;
        height: 1px;
        margin-top: 0;
      }
      &:before{
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -3px;
        background-color: #0056E5;
        width: 1px;
        height: 7px;
      }
    }
  }

</style>
