<template>
  <!-- 修改成功！ -->
  <div class="archivesPageContent">
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="resetForm"
      @search="getlist"
    >
      <im-search-pad-item prop="purMerchantName">
        <el-input v-model.trim="listQuery.model.purMerchantName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="merchantTypeId">
        <el-select v-model="listQuery.model.merchantTypeId" placeholder="请选择企业类型">
          <el-option v-for="item in listmerchantType" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="cityValue">
        <el-cascader placeholder="请选择所在区域"  v-model="cityValue" :props="props" @change="cityChange " clearable />
      </im-search-pad-item>
      <im-search-pad-item prop="customerSource">
        <el-select v-model="listQuery.model.customerSource" placeholder="请选择客户来源">
          <el-option label="客户注册" :value="1"></el-option>
          <el-option label="业务员拓客" :value="2"></el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="salesmanName">
        <el-input v-model.trim="listQuery.model.salesmanName" placeholder="请输入业务员名字" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.approvalStatus.code"
        :tabs="approvalStatusList"
        @change="chageTabsFun"
      >
        <template slot="button">
          <div>
            <el-button @click="refresh">刷新</el-button>

            <el-button v-if="checkPermission(['admin', 'sale-saas-pur-merchant-apply:audit', 'sale-platform-pur-merchant-apply:audit'])" v-throttle @click="auditSetting">审核设置</el-button>

            <el-dropdown style="margin:0 10px;">
              <el-button type="primary">
                批量操作<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>

              <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item style="margin-bottom: 10px;"  v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'sale-saas-pur-merchant-apply:reject', 'sale-platform-pur-merchant-apply:reject'])" >
                    <el-button  type="text" :disabled="multipleSelectionIds.length== 0"    @click="updatePurMerchantApplyRejected" >批量驳回</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item style="margin-bottom: 10px;"  v-if="listQuery.model.approvalStatus.code== 'PENDING' && checkPermission(['admin', 'sale-saas-pur-merchant-apply:accept', 'sale-platform-pur-merchant-apply:accept'])">
                    <el-button  type="text" :disabled="multipleSelectionIds.length== 0"    @click="updatePurMerchantApplyAccepted" >批量通过</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item style="margin-bottom: 10px;" v-if="listQuery.model.approvalStatus.code== 'REJECTED' && checkPermission(['admin', 'sale-saas-pur-merchant-apply:batchReAudit', 'sale-platform-pur-merchant-apply:batchReAudit'])" >
                    <el-button  type="text" :disabled="multipleSelectionIds.length== 0"    @click="updatePurMerchantApplyPending" >批量再审</el-button>
                  </el-dropdown-item>   
                  <el-dropdown-item style="margin-bottom: 10px;" v-if="checkPermission(['admin', 'sale-saas-pur-merchant-apply:info-export','sale-platform-pur-merchant-apply:info-export'])" >
                    <el-button  type="text" :disabled="multipleSelectionIds.length== 0"   @click="outExcel" >导出档案</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item style="margin-bottom: 10px;" v-if="checkPermission(['admin', 'sale-saas-pur-merchant-apply:qualification-download', 'sale-platform-pur-merchant-apply:qualification-download'])">
                    <el-button type="text" v-throttle @click="handleOutExcel()">资质打包下载</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
          </div>
        </template>
      </tabs-layout>
      <div class="table">
        <el-table v-if="list" @selection-change="selectTableItemFun" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
          <el-table-column align="center" width="65" :render-header="renderHeader" fixed>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="55" align="center" fixed></el-table-column>
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :min-width="(item.width?item.width:'350px')" :label="item.label" show-overflow-tooltip align="left">
            <template slot-scope="{row}">
              <template v-if="item.name === 'merchantLicenseFileList'">
                <div class="img-cell" v-if="row.merchantLicenseFileList">
                  <div v-if="row.merchantLicenseFileList.length < 2">
                    <img style="width:50px;height:50px;margin-right:5px" v-for="(i,indexs) in row.merchantLicenseFileList"
                      :key="indexs" :src="i" alt="">
                  </div>
                  <div class="img_box" v-else>
                    <img style="width:50px;height:50px;margin-right:5px" :src="row.merchantLicenseFileList[0]" alt="">
                    <div class="img-posi">
                      <img style="width:50px;height:50px;margin-right:5px" :src="row.merchantLicenseFileList[1]" alt="">
                      <div class="img_mask"> +{{ row.merchantLicenseFileList.length - 1 }}</div>
                    </div>
                  </div>
                </div>
                <div v-else>无上传</div>
              </template>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='Y'" type="text" style="color:#409EFF">已启用</el-button>
              <el-button v-else-if="item.name=='publishStatus'&&row[item.name].code=='N'" type="text" style="color:#FF3C54">已冻结</el-button>
              <span v-else-if="item.name == 'customerSource'">{{ row[item.name] && row[item.name].desc }}</span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" align="center" label="操作" width="180" class="itemAction">
            <template slot-scope="scope">
              <el-row class="table-edit-row">
                <span v-if="checkPermission(['admin', 'sale-saas-pur-merchant-apply:detail', 'sale-platform-pur-merchant-apply:detail'])" class="table-edit-row-item">
                  <el-button @click="detailFun(scope.row)" type="text" >查看详情</el-button>
                </span>
                <span  class="table-edit-row-item" v-if="checkPermission(['admin', 'sale-saas-pur-merchant-apply:qualification-preview', 'sale-platform-pur-merchant-apply:qualification-preview'])">
                  <el-button type="text" @click="handleBigImage(scope.row)">预览资质</el-button>
                  <el-image :ref="`ref${scope.row.id}`" style="width:0;height:0" :src="previewDetail[0]"
                :preview-src-list="previewDetail"></el-image>
                </span>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="total>0" :pageSizes="[2, 10, 20, 50]" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size" @pagination="getlist" />
      </div>
    </div>
    <el-dialog title="审核设置" :visible.sync="dialogVisible" width="20%" :before-close="handleClose">
      <el-form :model="auditForm" label-width="85px">
        <el-form-item label="申请审核：">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="0">人工审核</el-radio>
            <el-radio :label="1">自动审核</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="导出资质"
      append-to-body
      v-if="exportStatus"
      width="50%"
      :visible.sync="exportStatus"
      :before-close="closeExportDia"
    >
      <exportpage
        ref="exportPage"
        :total="total"
        :totalPage="totalPage"
      ></exportpage>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportStatus = false">取消</el-button>
        <el-button type="primary" @click="submitExport">确定</el-button>
      </div>
    </el-dialog>
    <BatchRejectModal ref="batchRejectModalRef" @submitSuccess="getlist" />
  </div>
</template>

<script>
import { setContextData, getContextData } from "@/utils/auth";
import checkPermission from '@/utils/permission'
import Pagination from "@/components/Pagination";
import {
  list,
  updatePurMerchantApplyPending,
  updatePurMerchantApplyAccepted,
  getPurApplyMerhcantCount,
  getsaleMerchantAudit,
  saleMerchantAudit,
  packagingMerchantApplyLicenseDownload
} from "@/api/registercheck";
import { merchantType } from "@/api/archivesList";
import tableInfo from "./tableInfo";
import { downloadFile, exoprtZipByURL } from "@/utils/commons";
import exportpage from "@/views/merchant/registerCheck/components/exportPage";
import { formatDataTime } from "@/utils/index";
import { areas } from "@/api/enterprise";
import TabsLayout from '@/components/TabsLayout'
import BatchRejectModal from "./components/batchRejectModal.vue";

export default {
  name:'buyerApplicationList',
  components: {
    Pagination,
    TabsLayout,
    exportpage,
    BatchRejectModal
  },
  data() {
    return {

      exportStatus: false,
      isExpand: false,
      totalPage: 0,

      previewDetail: [],
      arealist: [],
      props: {
        lazy: true,
        checkStrictly: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
      cityValue: [],
      list: [],
      total: 0,
      dialogVisible:false,
      listLoading: true,
      multipleSelectionIds: [],
      multipleSelection: [],
      listmerchantType: [],
      tableTitle: [],
      submitReload: false,
      pageCount: {
        pendingCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        expireCount: 0,
      },
      listQuery: {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { code: "PENDING" },
          customerSource:'',
          salesmanName:''
        },
      },
      showEditPage: false,
      row: {},
      tableSelectTitle: [0, 1, 2, 3],
      showSelectTitle: false,
      auditForm:{
        id:'',
        auditStatus: 0
      }
    };
  },
  computed: {
    approvalStatusList() {
      return [
        {
          name: '待审核' + ((+this.pageCount.pendingCount) > 0 ? '(' + this.pageCount.pendingCount + ')' : ''),
          value: 'PENDING',
          hide: !checkPermission(['admin','sale-saas-pur-merchant-apply-pending:view','sale-platform-pur-merchant-apply-pending:view'])
        },
        {
          name: '已审核' + ((+this.pageCount.acceptedCount) > 0 ? '(' + this.pageCount.acceptedCount + ')' : ''),
          value: 'ACCEPTED',
          hide: !checkPermission(['admin','sale-saas-pur-merchant-apply-accepted:view','sale-platform-pur-merchant-apply-accepted:view'])
        },
        {
          name: '已驳回' + ((+this.pageCount.rejectedCount) > 0 ? '(' + this.pageCount.rejectedCount + ')' : ''),
          value: 'REJECTED',
          hide: !checkPermission(['admin','sale-saas-pur-merchant-apply-rejected:view','sale-platform-pur-merchant-apply-rejected:view'])
        }
      ]
    }
  },
  methods: {
    checkPermission,

    // 预览图片
      async handleBigImage(row) {
        this.previewDetail =row.merchantLicenseFileList||[];
        console.info(row)
        if (row.merchantLicenseFileList ==undefined ||row.merchantLicenseFileList.length == 0) {
          this.$message.warning('无图片可查看');
          return
        }
        
        this.$refs[`ref${row.id}`].showViewer = true;
      },



    refresh() {
      this.listQuery ={
        ...this.listQuery,
        current: 1,
        size: 10
      }
      this.cityValue = []
      this.initTbaleTitle()
      this.getlist()
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    checkSelect() {
      if (this.multipleSelectionIds.length < 1) {
        this.$alert("请至少选择一个采购商");
        return true;
      }
    },
    selectTableItemFun(val) {
      let arr = [];
      val.forEach((item) => {
        arr.push(item.purMerchantApplyId);
      });
      this.multipleSelectionIds = arr;
      this.multipleSelection = val;
    },
    async getPurApplyMerhcantCount() {
      getPurApplyMerhcantCount().then(res => {
        if (res.code !== 0) return
        this.pageCount = res.data;
      });
    },
    setSucss(data, type, msg) {
      if (!data) {
        return false;
      }
      // this.listQuery.model.approvalStatus.code = type;
      this.$message.success(msg);
      this.getlist();
    },
    // 批量待审
    async updatePurMerchantApplyPending() {
      if (this.checkSelect()) return 0;
      this.$confirm("此操作将审核通过采购商申请，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async ()=>{
        let { data } = await updatePurMerchantApplyPending({
          'ids[]': this.multipleSelectionIds.toString(),
        });
        this.setSucss(data, "PENDING", "批量待审成功！");
      });
    },
    // 批量审核
    async updatePurMerchantApplyAccepted() {
      if (this.checkSelect()) return 0;
      this.$confirm("此操作将审核通过采购商申请，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async ()=>{
        let { data } = await updatePurMerchantApplyAccepted({
          'ids':this.multipleSelectionIds.toString()
        });
        this.setSucss(data, "ACCEPTED", "批量审核成功！");
      })
    },
    // 批量驳回
    async updatePurMerchantApplyRejected() {
      if (this.checkSelect()) return 0;
      this.$refs.batchRejectModalRef.show(this.multipleSelectionIds.toString())
    },
    // 获取商家类型
    async getmerchantType() {
      let { data } = await merchantType();
      this.listmerchantType = data;
    },
    async getlist() {
      if (this.approvalStatusList.every(item => item.hide)) return
      this.list = []
      this.getPurApplyMerhcantCount();
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      this.list = data.records;
      this.total = data.total;
      this.listLoading = false;
    },

    chageTabsFun: function () {
      this.list = [];
      this.listQuery.current =1
      this.initTbaleTitle();
      this.getlist();
    },
    resetForm() {
      this.cityValue = []
      this.listQuery = {
        page: 1,
        size: 10,
        model: {
          approvalStatus: { code: this.listQuery.model.approvalStatus.code },
          customerSource:'',
          salesmanName:''
        },
      };
      this.getlist();
    },
    async detailFun(item) {
      setContextData('registerCheck_list', this.listQuery)
      this.$router.push({
        path: "/merchant/registerCheck/detail",
        query: {
          id: item.purMerchantApplyId,
          tabType: this.listQuery.model.approvalStatus.code,
        },
      });
    },
    //  导出档案
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["purMerchantApplyId"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "采购商注册审批列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在注册审批列表中勾选需要导出的采购商");
      }
    },
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "publishStatus") {
             return v[j].code =="Y" ? '已启用' : '已冻结';
          } else {
            return v[j];
          }
        })
      );
    },
    initTbaleTitle() {
      this.tableTitle = tableInfo[this.listQuery.model.approvalStatus.code];
      this.tableSelectTitle = [];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.listQuery.model.approvalStatus.code];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
    // 审核设置
    auditSetting(){
      console.log('审核设置');
      getsaleMerchantAudit().then(res=>{
        if(res.code == 0 && res.msg == 'ok') {
          this.auditForm.auditStatus = res.data.auditStatus;
          this.auditForm.id = res.data.id;
          this.dialogVisible = true;
        }
      })
    },
    handleClose(){
      this.dialogVisible = false;
    },
    submitAudit(){
      saleMerchantAudit(this.auditForm).then(res=>{
        if(res.code == 0 & res.msg == 'ok') {
          this.$message.success('设置成功');
          this.dialogVisible = false;
        }
      })
    },



     // 导出数据选择弹窗关闭
    closeExportDia() {
      this.exportStatus = false;
    },
    handleOutExcel() {
      if (this.multipleSelection.length > 0) {
        const loading = this.$loading({
          lock: true,
          text: "正在导出中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.4)",
        });
        var purMerchantApplyIds = Array.from(this.multipleSelection,({purMerchantApplyId})=>purMerchantApplyId);
        let listParams = {
          "purMerchantIdList":purMerchantApplyIds,
        };
        
        packagingMerchantApplyLicenseDownload(listParams).then((res) => {
          loading.close();
          if(res.code !== 0) throw new Error(res.msg)
          exoprtZipByURL(
            res.data,
            `客户资质${formatDataTime("yyyyMMDDHHmmss")}.zip`
          );
        });
        // 选中导出
      } else {
        this.exportStatus = true;
      }
    },
    // 确定导出
    submitExport() {
      const loading = this.$loading({
        lock: true,
        text: "正在导出中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.4)",
      });
      let params = this.$refs["exportPage"].getData();
      let model = {
          ...this.listQuery.model,
          ...params,
         "purMerchantIdList": [],
      };
      // delete model.purMerchantIdList;
      packagingMerchantApplyLicenseDownload(model).then((res) => {
        loading.close();
        if(res.code !== 0) throw new Error(res.msg)
         exoprtZipByURL(
            res.data,
            `客户资质${formatDataTime("yyyyMMDDHHmmss")}.zip`
          );
      });
    },
    // async outExcelLicense() {
      
    //   if (this.multipleSelection.length > 0) {
    //     const loading = this.$loading({
    //       lock: true,
    //       text: "正在导出中...",
    //       spinner: "el-icon-loading",
    //       background: "rgba(0, 0, 0, 0.4)",
    //     });
    //     let params = this.$refs["exportPage"].getData();
    //     let model = {
    //       ...this.model,
    //       ...params,
    //       "purMerchantIds": this.multipleSelection.join(","),
    //     };
    //     delete model.id;

    //     let listParams = {
    //       current: this.page,
    //       size: this.pageSize,
    //       map: {},
    //       model,
    //       order: "descending",
    //       sort: "createTime",
    //     };
    //     console.info(listParams)
    //     packagingMerchantApplyLicenseDownload(listParams).then((res) => {
    //       loading.close();
    //       exoprtToExcel(
    //         res.data,
    //         `客户资质${formatDataTime("yyyyMMDDHHmmss")}.xlsx`
    //       );
    //     });
    //     // 选中导出
    //   } else {
    //     this.submitExport();
    //   }
    // },
  },
  created() {
    this.getmerchantType();
  },
  mounted() {
    this.initTbaleTitle();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == "/purchasingAgent/registerCheck/detail") {
        if (getContextData("registerCheck_list") != "") {
          vm.listQuery = getContextData("registerCheck_list");
        }
      }
      vm.initTbaleTitle();
      vm.getlist();
    });
  },
  watch: {
    submitReload(newVal, oldVal) {
      if (newVal) {
        this.submitReload = false;
        this.listQuery.model.approvalStatus.code = this.listQuery.model.approvalStatus.code;
        this.getlist();
      }
    },
    dialogVisible(newVal) {
      if(!newVal){
        this.auditForm = {
          auditStatus: 0,
          id: ''
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
    margin-left: 30px;
  }
}


.img-cell {
    display: flex;
    align-items: center;
    .img_box {
      display: flex;
      align-items: center;
      position: relative;
      .img-posi {
        width: 50px;
        height: 50px;
      }
      .img_mask {
        position: absolute;
        width: 50px;
        height: 50px;
        top: 0;
        left: 55px;
        line-height: 50px;
        text-align: center;
        background-color: rgba($color: #000000, $alpha: 0.6);
        color: #fff;
      }
    }
  }




</style>
