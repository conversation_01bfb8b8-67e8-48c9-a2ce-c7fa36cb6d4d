<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listParams"
      @reset="load"
      @search="onSubmit"
    >
      <im-search-pad-item prop="code">
        <el-input v-model="listParams.code" placeholder="请输入商品信息/订单编号/代理业务员" />
      </im-search-pad-item>
      <im-search-pad-item prop="status">
        <el-select v-model="listParams.status" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="time">
        <el-date-picker
          v-model="listParams.time"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <el-tabs v-model="active">
        <el-tab-pane label="推广费用明细" name="0"></el-tab-pane>
      </el-tabs>
      <el-table
        ref="dragTable"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" :render-header="renderHeader" width="60">
          <template slot-scope="{ row }">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :width="item.width"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination" class="fr"></pagination>
    </div>
  </div>
</template>

<script>
import { fetchList } from "@/api/article";
import Sortable from "sortablejs";
import Pagination from '@/components/Pagination/index.vue'

const TableColumns = [
  { label: "商品信息", name: "productInfo" },
  { label: "数量", name: "productNum" },
  { label: "关联订单编号", name: "productCode" },
  { label: "订单金额（元）", name: "orderFee" },
  { label: "时间", name: "orderTime" },
  { label: "订单状态", name: "orderStatus" },
  { label: "预估佣金（元）", name: "commission" },
  { label: "代理业务员", name: "salesman" },
  { label: "结算状态", name: "settlementStatus" }
];

const TableColumnList = [];

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

export { TableColumnList };

export default {
  components: {
    Pagination
  },
  data () {
    return {
      active: '0',
      options: [
        {
          label: '现金交易',
          value: 0
        },
        {
          label: '禁止现金交易',
          value: 1
        },
        {
          label: '不可退货',
          value: 2
        },
        {
          label: '预售/集采',
          value: 3
        }
      ],
      listParams: {
        code: '',
        status: '',
        time: ''
      },
       // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      list: [{
        id:1,
        productInfo: '枸橼酸莫沙必利分散片',
        productNum: '20',
        productCode: '12432334234',
        orderFee: '30213.00',
        orderTime: '2020-10-12 11:20:21.',
        orderStatus: '待付款',
        commission: '400.00',
        salesman: '深水蛋蛋',
        settlementStatus: '待结算'
      },
      {
        id:2,
        productInfo: '氯雷他定片',
        productNum: '120',
        productCode: '12432334234',
        orderFee: '30213.00',
        orderTime: '2020-10-12 11:20:21.',
        orderStatus: '待付款',
        commission: '2400.00',
        salesman: '深水蛋蛋',
        settlementStatus: '待结算'
      }],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
    }
  },
  methods: {
    onSubmit () {},
    load () {},
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (
        <div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },

    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    }
  }
}
</script>
