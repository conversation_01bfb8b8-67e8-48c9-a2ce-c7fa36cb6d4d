<template>
    <div :class="$style.list">
        <div @click="onItemClick(item.value)" v-for="item in options" :key="item.value"
            :class="createTypeClass(item.value)">
            <h4>{{ item.text }}</h4>
            <p>{{ item.tip }}</p>
        </div>
    </div>
</template>
<script>
const MODEL = "UPDATE_MODEL"
export default {
    props: {
        value: [String, Number],
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: () => []
        }
    },
    model: {
        prop: 'value',
        event: MODEL,
    },
    data() {
        return {}
    },
    computed: {
        current() {
            return this.value;
        }
    },
    methods: {
        /**
         * 生成类型卡片Class样式类
         */
        createTypeClass(type) {
            let _class = [this.$style.item];
            let isActive = this.current === type;
            isActive && _class.push(this.$style.active);
            this.disabled && _class.push(this.$style.disabled);
            return _class;
        },

        onItemClick(value) {
            if (this.disabled || this.current === value) return;
            this.$emit(MODEL, value)
        }
    }
}
</script>
<style lang="scss" module>
.list {
    display: flex;
    padding: 15px 0;
    line-height: 1.46;

    .item {
        position: relative;
        padding: 15px 10px;
        border: 1px solid #e3e3e3;
        width: 200px;
        margin-right: 20px;
        cursor: pointer;

        &::after {
            display: none;
            content: '';
            text-align: center;
            position: absolute;
            bottom: 0px;
            right: -1px;
            width: 20px;
            height: 20px;
            color: #fff;
            background: url('../../../../assets/imgs/coupon/<EMAIL>') no-repeat;
            background-size: cover;
            background-position: center;
        }

        &.active {
            border: 1px solid #0056E5;
            position: relative;

            &:not(.disabled) {
                h4 {
                    color: #0056e5;
                }
            }

            &::after {
                display: block;
            }
        }

        &.disabled {
            border: 1px solid #999999;
            position: relative;
            cursor: not-allowed;

            &::after {
                filter: grayscale(100%);
            }
        }

        h4 {
            font-size: 16px;
            margin: 0;
        }

        p {
            font-size: 13px;
            color: #999999;
            padding-top: 10px;
            margin: 0;
        }

    }
}
</style>