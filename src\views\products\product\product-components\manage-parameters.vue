<template>
  <el-form :model="form" label-width="80px" label-position="right" :disabled="action==='SHOW'">
    <div class="manage-parameters">
      <form-item-title title="经营参数"/>
      <!-- <el-form-item label-width="100px" label="售后服务" required>
        <el-radio-group v-model="form.whetherReturnable">
          <el-radio label="Y">支持申请退货</el-radio>
          <el-radio label="N">不可退货</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label-width="100px" label="用券" required>
        <el-radio-group v-model="form.whetherUseCoupon">
          <el-radio label="Y">允许使用券优惠</el-radio>
          <el-radio label="N">不可用券</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label-width="100px" label="现款交易商品">
        <el-checkbox true-label="Y" false-label="N" v-model="form.whetherCashTransaction">仅支持现款交易，不可赊销</el-checkbox>
      </el-form-item>
      <el-form-item label-width="100px" label="含麻黄碱商品">
        <el-checkbox true-label="Y" false-label="N" v-model="form.whetherContainEphedrine">含麻黄碱商品，不可现金交易</el-checkbox>
      </el-form-item>
      <el-form-item label-width="100px" label="销售方式" required>
        <el-radio-group v-model="form.marketing.code" @change="changeMarketing"
        >
          <el-radio label="DIRECT">自营</el-radio>
          <el-radio label="FRANCHISES" :disabled="!isDirectProductInfo">代营
            <span v-if="isDirectProductInfo">
              <span>(供货商:{{(directProductInfo||{}).supplierName||""}},</span>
              <span>供货价:{{(directProductInfo||{}).supplyPrice||""}},</span>
              <span>是否共享:{{((directProductInfo||{}).whetherShare||{}).desc||""}},</span>
              <span>库存:{{(directProductInfo||{}).stockQuantity||""}})</span>
            </span>
            <span v-else>(无供货商)</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!--      代营默认值是是-->
      <el-form-item label-width="100px" label="是否共享" required>
        <el-radio-group v-model="form.whetherShare.code"
                        :disabled="form.marketing.code=='FRANCHISES'"
                        @change="changeWhetherShare">
          <el-radio label="Y">是</el-radio>
          <el-radio label="N">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label-width="100px" label="是否可调价" required>
        <el-radio-group v-model="form.whetherAdjustPrice.code"
                        :disabled="form.marketing.code=='FRANCHISES'"
                        @change="handleWhetherAdjustPrice">
          <el-radio label="Y">是</el-radio>
          <el-radio label="N">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label-width="100px" label="拆销" required>
        <el-radio-group v-model="form.whetherUnbundled">
          <el-radio label="N">不可拆零</el-radio>
          <el-radio label="Y">可拆零</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label-width="100px" label="库存" required :rules="!syncErp ? {validator: stockQuantityRules, trigger: 'change'} : {}"
                    prop="stockQuantity">
        <el-input :disabled="syncErp" v-model="form.stockQuantity" placeholder="请输入库存" type="number" style="width: 200px;"/>
        <el-checkbox style="margin-left: 15px;" v-model="syncErp">同步ERP库存（系统对接后生效）</el-checkbox>
      </el-form-item>
      <el-form-item class="purchase-quantity" label-width="100px" label="购买数量" required>
        <div class="purchase-quantity-item">
          <div class="purchase-quantity-item-label">每单限购数量</div>
          <el-input @change="handleBuyNumberChange" v-model="form.maxBuy"></el-input>
        </div>
        <div class="purchase-quantity-item">
          <div class="purchase-quantity-item-label">每单起订数量</div>
          <el-input @change="handleBuyNumberChange" v-model="form.minBuyQuantity"></el-input>
        </div>
        <div v-if="buyNumberTip" class="buyNumberTip" style="color: #FF3C54;">{{ buyTip }}</div>
      </el-form-item>
      <el-form-item label-width="100px" label="裂变奖励" required prop="whetherReward" :rules="{ required: true, message: '请选择分销奖励', trigger: 'change' }">
        <el-radio-group v-model="form.whetherReward.code">
          <div style="margin-top: 12px;" v-for="(item, index) in whetherRewardData" :key="index">
            <el-radio :label="item.value">
              {{ item.label }}
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label-width="100px" label="限购">
        <el-checkbox v-model="form.purchaseLimitation">限制没人可购买数量</el-checkbox>
        <el-radio-group v-model="radio">
          <el-radio :label="3">备选项</el-radio>
          <el-radio :label="6">备选项</el-radio>
          <el-radio :label="9">备选项</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </div>
  </el-form>
</template>

<script>
  import formItemTitle from '@/views/products/common-components/form-item-title'
  import { getWhetherRewardData } from "@/views/products/product/components/data";
  export default {
    props: {
      action: {
        type: String,
      },
      isNumber2: {
        type: Function
      },
      isInt7: {
        type: Function
      }
    },
    components: {formItemTitle},
    data() {
      return {
        whetherRewardData: getWhetherRewardData(),
        directProductInfo: {},
        isDirectProductInfo: false,
        isEditMarkting: false,
        form: {
          // whetherReturnable: 'Y',
          whetherUseCoupon: 'Y',
          whetherCashTransaction: 'N',
          whetherContainEphedrine: 'N',
          whetherUnbundled: 'Y',
          ephedrine: false,
          separate: 'Y',
          maxBuy: 999999,
          minBuyQuantity: 1,
          stockQuantity: '',
          whetherShare: {code: "N"},
          whetherReward: {code: "N"},
          whetherAdjustPrice: {code: 'N'},
          marketing: {
            code: "DIRECT"
          },
          // // ------ 限购
          // purchaseLimitation: false, // 是否限购数量
          // purchaseDay: false, // 是否每天限购
          // dayNum: 0, // 每天限购数量
          // whetherUnit: false, // 是否每单限购
          // whetherDate: false, // 是否限制有效期
          // confineStart:'', // 限制有效期开始时间
          // confineEnd:'', // 限制有效期结束时间
          // // ------
        },
        syncErp: false,
        buyNumberTip: false
      }
    },
    computed: {
      buyTip() {
        if (this.form.maxBuy < this.form.minBuyQuantity) {
          return '起订数量不能大于限购数量';
        }
        if (this.form.maxBuy <= 0 || (this.form.maxBuy + '').length > 7) {
          return '每单限购数量只能是正整数，且最多7位';
        }
        if (this.form.minBuyQuantity <= 0 || (this.form.minBuyQuantity + '').length > 7) {
          return '每单起订数量只能是正整数，且最多7位';
        }
      },

    },
    mounted() {
      // this.form.marketing = this.form.marketing.code ?'2222222222' : this.form.marketing;
      // this.form.whetherShare = this.form.whetherShare.code ? this.form.whetherShare.code : this.form.whetherShare;
    },
    methods: {
      changeMarketing(value) {
        if (value == 'FRANCHISES') {
          this.changeWhetherShare("N")
          this.form.whetherShare.code = "N"
        }
      },
      changeWhetherShare(value) {
        this.form.whetherShare.code = value
        this.$emit('changeShare', value)
      },
      handleWhetherAdjustPrice(value) {
        this.form.whetherAdjustPrice.code = value
        this.$emit('changePrice', value)
      },
      int7(rule, value, cb) {
        if (!this.isInt7(value)) {
          return cb(new Error('只能是正整数，且最多7位'));
        }
        cb();
      },
      stockQuantityRules(rule, value, cb) {
        let val = Number(value);
        if(val < 0) {
          return cb(new Error('请填写大于0的数'));
        }
        cb();
      },
      handleBuyNumberChange() {
        if (parseInt(this.form.minBuyQuantity) > parseInt(this.form.maxBuy)) {
          this.buyNumberTip = true
        } else {
          this.buyNumberTip = false
        }
      },

      getData() {
        // if (!this.form.whetherReturnable) {
        //   this.$message.error('请选择售后服务')
        //   return false
        // }
        if (!this.form.whetherUseCoupon) {
          this.$message.error('请选择用券')
          return false
        }
        if (!this.form.whetherCashTransaction) {
          this.$message.error('请选择现款交易商品')
          return false
        }
        if (!this.form.whetherContainEphedrine) {
          this.$message.error('请选择含麻黄碱商品')
          return false
        }
        if (!this.form.whetherUnbundled) {
          this.$message.error('请选择拆销')
          return false
        }
        if (!this.form.maxBuy) {
          this.$message.error('请输入每单限购数量')
          return false
        }
        if (!this.form.minBuyQuantity) {
          this.$message.error('请输入每单起订数量')
          return false
        }
        if (!this.form.stockQuantity) {
          this.$message.error('请输入库存')
          return false
        }
        if ((this.form.stockQuantity + '').length && Number(this.form.stockQuantity) < 0) {
          this.$message.error('库存输入不正确')
          return false
        }
        if ((this.form.maxBuy + '').length && Number(this.form.maxBuy) < 0) {
          this.$message.error('每单限购数量输入不正确')
          return false
        }
        if ((this.form.minBuyQuantity + '').length && Number(this.form.minBuyQuantity) < 0) {
          this.$message.error('每单起订数量输入不正确')
          return false
        }
        const skuSyncType = this.syncErp ? 'ERP_SYNC' : 'LOCAL_SYNC'
        return {...this.form, whetherAdjustPrice: this.form.whetherAdjustPrice.code, skuSyncType}
      },
      setForm(data) {
        this.directProductInfo = data.directProductInfo
        this.syncErp = data.skuSyncType.code === 'ERP_SYNC'
        //无供货商  --不可选择代营
        this.isDirectProductInfo = data.directProductInfo ? true : false
        if (!data.directProductInfo) {
          //  只能选择自营并且不可编辑
          this.form.marketing.code = "DIRECT"
          this.isEditMarkting = true
        }
        for (let k in this.form) {
          this.form[k] = data[k]
        }
        // 限购数量 WTF
        if (data.skuList && data.skuList.length) {
          this.form.maxBuy = data.skuList[0].limitQuantity || ''
          this.form.stockQuantity = data.skuList[0].realStockQuantity || ''
        }
        // 默认值，tmd
        let defaultObj = {
          // whetherReturnable: 'Y',
          whetherUseCoupon: 'Y',
          whetherCashTransaction: 'N',
          whetherContainEphedrine: 'N',
          whetherUnbundled: 'Y',
          ephedrine: false,
          separate: 'Y',
          minBuyQuantity: 1,
          whetherShare: {code: "N"},
          whetherAdjustPrice: {code: "N"},
          marketing: {code: "DIRECT"}
        };
        for (let k in defaultObj) {
          if (!this.form[k]) {
            this.form[k] = defaultObj[k]
          }
        }
        this.form.minBuyQuantity = this.form.minBuyQuantity || '';
        this.form.maxBuy = this.form.maxBuy || 999999;
        this.form.stockQuantity = this.form.stockQuantity || '';
      }
    }
  }
</script>

<style lang="scss" scoped>
  .manage-parameters {
    .purchase-quantity {
      ::v-deep {
        .el-form-item__content {
          display: flex;
          align-items: center;

          .purchase-quantity-item {
            display: flex;
            align-items: center;
            margin-right: 30px;

            &-label {
              margin-right: 10px;
            }

            .el-input {
              width: 100px;
            }
          }
        }
      }

    }
  }
</style>
