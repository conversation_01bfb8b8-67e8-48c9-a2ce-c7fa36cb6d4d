<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: 苏木
 * @Date: 2021-11-04 18:46:38
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-26 16:48:04
-->
<template>
  <div class="tempPaySettingForm">
    <!-- <el-form label-width="120px">
      <el-form-item v-for="(item,index) in FormList" :key="index" :label="item.name+':'">
        <el-input v-model="item.val" :placeholder="'请输入'+item.name"></el-input>
      </el-form-item>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </el-form> -->
    <div style="height:400px;overflow: scroll;">
      <el-row v-for="(item,index) in FormList" :key="index" style="margin-bottom:20px">
        <el-col :span="24" style="margin-bottom:10px">
          <!-- <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
            
          </el-tooltip> -->
          <div class="grid-content bg-purple item_name">{{item.name}} :</div>
        </el-col>
        <el-col :span="24">
          <div class="grid-content bg-purple-light">
            <el-input :disabled="payWayType=='OFFLINE'" v-model="item.val" :placeholder="'请输入'+item.name"></el-input>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="bottom_btn">
      <div></div>
      <el-button type="primary" @click="handleSubmit">{{payWayType=='OFFLINE'?'确定':'保存'}}</el-button>
    </div>
  </div>
</template>
<script>
import { paymentSettingOpen } from '@/api/settingCenter';
export default {
  data() {
    return {
      // FormList:[],
    };
  },
  props: {
    cardId: {
      required: true,
      type: String,
      default: ''
    },
    payWayType:{
      required: true,
      type: String,
      default: ''
    },
    FormList:{
      type:Array,
      default:[]
    }
  },
  methods: {
    handleSubmit(){
      console.log('this.FormList---->',this.FormList);
      let isNull = false;
      this.FormList.forEach(item=>{
        if(item.val.trim().length==0){
          isNull = true;
        }
      });
      if(isNull){
        this.$message.warning('请把表单填写完整');
        return
      }
      let params = {
        id:this.cardId,
        paramList:this.FormList
      };
      paymentSettingOpen(params).then(res=>{
        if(res.code == 0 && res.msg=='ok'){
          this.$emit('handleSure',false);
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";
.tempPaySettingForm {
  // .title {
  //   color: #333333;
  //   font-family: "Arial-BoldMT", "Arial Bold", "Arial";
  //   font-weight: 700;
  //   font-style: normal;
  //   font-size: 15px;
  //   text-indent: 4px;
  //   margin-bottom: 20px;
  //   border-left: 3px solid $--color-primary;
  // }
  .formItem{margin-bottom: 16px;}
}
.item_name {
// width: 100px;
// white-space: nowrap;
// overflow: hidden;
// text-overflow: ellipsis;
}
.bottom_btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
