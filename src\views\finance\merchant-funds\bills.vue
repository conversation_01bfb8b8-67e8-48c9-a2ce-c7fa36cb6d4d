<template>
  <div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="model"
      @reset="reload"
      @search="onSearch"
    >
      <im-search-pad-item prop="id">
        <el-input v-model.trim="model.id" placeholder="请输入账单编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="customerName">
        <el-input v-model.trim="model.customerName" placeholder="请输入客户名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="type">
        <el-select v-model="model.type" placeholder="账单类型">
          <el-option label="销售账单" value="SALE" />
          <el-option label="退款账单" value="REFUND" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="during">
        <el-date-picker
          v-model="model.during"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <div class="varietiesBan-list-container">
        <div class="varietiesBan-list-tabs-wrapper">
          <div class="varietiesBan-list-tabs">
            <div class="tab" :class="{'active': currentTab == index}" v-for="(tab, index) in tabs"
                :key="tab.value"
                @click="handleChangeTab(index,tab.value)">
              <span v-if="checkPermission(['admin',tab.permission])">{{ tab.name }}（{{tab.count}}）</span>
            </div>
          </div>
          <div class="operations">
            <el-button  v-if="checkPermission(['admin', 'bills:export'])" :disabled="multipleSelection.length ==0" @click="outExcel">导出</el-button>
            <el-button  @click="reload">刷新</el-button>
          </div>
        </div>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :selection="true" :remote-method="load" @selection-change="handleSelectionChange" :data.sync="tableData" :isNeedButton="false">
        <template slot="settlementOrderId" v-if="model.merchantsStatus==='FINISH'||model.merchantsStatus==='PROCESS'">
          <el-table-column label="结算单号" width="110">
            <slot slot-scope="{row}">
              <span class="text-primary">{{ row.settlementOrderId }}</span>
            </slot>
          </el-table-column>
        </template>
        <!-- <template v-if="model.merchantsStatus==='FINISH'" slot="settlementSerialNumber">
          <el-table-column label="银行流水编号" width="110">
            <slot slot-scope="{row}">
              <span class="text-primary">{{ row.settlementSerialNumber }}</span>
            </slot>
          </el-table-column>
        </template> -->
        <template v-if="model.merchantsStatus==='FINISH'" slot="orderPaymentTime">
          <el-table-column label="支付时间" width="170">
            <slot slot-scope="{row}">
              <span class="text-primary">{{ row.orderPaymentTime }}</span>
            </slot>
          </el-table-column>
        </template>
        <template slot="orderAmount">
          <el-table-column label="订单/退款单金额（元）" width="170">
            <slot slot-scope="{row}">
              {{ row.orderAmount|getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="realAmout">
          <el-table-column label="实收/退金额（元）" width="140">
            <slot slot-scope="{row}">
              {{ row.realAmout|getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementAmount">
          <el-table-column label="应结货款（元）" width="135">
            <slot slot-scope="{row}">
              {{ row.settlementAmount|getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementServiceAmount">
          <el-table-column label="交易服务费（元）" width="140">
            <slot slot-scope="{row}">
              {{ row.settlementServiceAmount|getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementCommissionAmount">
          <el-table-column label="业务员品种推广佣金（元）" width="190">
            <slot slot-scope="{row}">
              {{ row.settlementCommissionAmount|getDecimals }}
            </slot>
          </el-table-column>
        </template>
        <template slot="settlementChargedAmount">
          <el-table-column label="交易手续费（元）" width="170">
            <slot slot-scope="{row}">
              {{ row.settlementChargedAmount|getDecimals }}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { downloadFile } from "@/utils/commons";
const TableColumns = [
  { label: '账单类型', name: 'type.desc', prop: 'type.desc' },
  { label: '账单编码', name: 'id', prop: 'id' },
  { label: '订单/退款单编号', name: 'orderNumber', prop: 'orderNumber', width: '160' },
  { label: '结算单号', name: 'settlementOrderId', prop: 'settlementOrderId', slot: true },
  { label: '银行流水编号', name: 'settlementSerialNumber', prop: 'settlementSerialNumber', slot: true },
  { label: '支付时间', name: 'orderPaymentTime', prop: 'orderPaymentTime', slot: true },
  { label: '商家名称', name: 'merchantName', prop: 'merchantName', width: '150' },
  { label: '客户名称', name: 'customerName', prop: 'customerName', width: '150' },
  { label: '订单支付时间', name: 'orderPaymentTime', prop: 'orderPaymentTime', width: '170' },
  { label: '订单/退款单金额（元）', name: 'orderAmount', prop: 'orderAmount', slot: true, width: '170' },
  { label: '实收/退金额（元）', name: 'realAmount', prop: 'realAmount', width: '140', slot: true },
  { label: '应结货款（元）', name: 'settlementAmount', prop: 'settlementAmount', width: '135', slot: true },
  { label: '交易服务费（元）', name: 'settlementServiceAmount', prop: 'settlementServiceAmount', width: '140', slot: true },
  { label: '业务员品种推广佣金（元）', name: 'settlementCommissionAmount', prop: 'settlementCommissionAmount', width: '190', slot: true },
  { label: '交易手续费（元）', name: 'settlementChargedAmount', prop: 'settlementChargedAmount', width: '180', slot: true },
  { label: '制单人', name: 'createUserName', prop: 'createUserName', width: '100' },
  { label: '制单时间', name: 'createTime', prop: 'createTime', width: '170' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
import checkPermission from '@/utils/permission'
import { merchantsBillOrderDetail, merchantsBillOrderCount } from '@/api/finance'
export default {
  data() {
    return {
      isExpand: false,
      loading: '',
      search: '',
      controlType: '',
      currentTab: 0,
      tabs: [
        { name: '可结算', value: 'WAIT', count: 0, permission: 'bills-wait:view' },
        { name: '结算中', value: 'PROCESS', count: 0, permission: 'bills-process:view' },
        { name: '已结算', value: 'FINISH', count: 0, permission: 'bills-finish:view' },
        { name: '未结算', value: 'UNSETTLED', count: 0, permission: 'bills-unsettled:view' }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      totalPage: 0,
      total: 0,
      tableTitle: TableColumnList,
      model: {
        type: '', // 0 -> 销售账单 1 -> 退款账单
        customerName: '',
        id: '',
        during: '',
        merchantsStatus: 'WAIT'// 0->可结算、1->结算中、2->已结算、3->未结算
      },
      products: [],
      ids: [],
      multipleSelection: []
    }
  },
  mounted() {
    this.getCount()
  },
  methods: {
    async outExcel() {
      if (this.multipleSelection.length > 0) {
        const tHeader = ["id"];
        const filterVal = ["id"];
        this.tableTitle.forEach(function (item) {
          tHeader.push(item.label);
          filterVal.push(item.name);
        });
        let exportData = this.formatJson(this.multipleSelection, filterVal);
        downloadFile({
          tHeader: tHeader,
          fileName: "商家账单列表",
          exportData: exportData,
        });
      } else {
        this.$message.error("请在商家列表中勾选需要商家");
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    formatJson(dataList, filterVal) {
      return dataList.map((v) =>
        filterVal.map((j) => {
          if (j === "type.desc") {
            return v.type.desc;
          } else {
            return v[j];
          }
        })
      );
    },
    checkPermission,
    async getCount() {
      const { data } = await merchantsBillOrderCount({})
      this.tabs[0].count = data.wait
      this.tabs[1].count = data.process
      this.tabs[2].count = data.finish
      this.tabs[3].count = data.unsettled
    },
    async load(params) {
      const listQuery = {
        model: {
          ...this.model,
          startTime: this.model.during[0],
          endTime: this.model.during[1]
        }
      }
      delete listQuery.model.during
      Object.assign(listQuery, params)
      this.loading = true
      return await merchantsBillOrderDetail(listQuery)
    },
    handleChangeTab(index, value) {
      this.currentTab = index
      this.model.merchantsStatus = value
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    onSearch() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    reload() {
      this.currentTab = 0
      this.model = {
        // type: '', // 0 -> 销售账单 1 -> 退款账单
        // purMerchantName: '',
        // orderNumber: '',
        // code: '',
        // during: '',
        // merchantsStatus: 0
        type: '', // 0 -> 销售账单 1 -> 退款账单
        customerName: '',
        purMerchantName:'',
        orderNumber:'',
        code:'',
        id: '',
        during: '',
        merchantsStatus: 'WAIT'// 0->可结算、1->结算中、2->已结算、3->未结算
      }
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
