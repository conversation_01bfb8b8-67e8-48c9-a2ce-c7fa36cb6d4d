import request from '@/utils/request'

/**
 * @description 获取商城首页页面数据
 */
export function getPageData() {
  return request({
    url: `/merchant/admin/page/data/getPageData`,
    method: 'get'
  })
}

/**
 * @description 批量保存热词
 */
export function hotUpdate(data) {
  return request({
    url: `/merchant/admin/page/data/hot`,
    method: 'POST',
    data
  })
}

/**
 * @description 批量保存广告
 */
export function advUpdate(data) {
  return request({
    url: `/merchant/admin/page/data/ad`,
    method: 'POST',
    data
  })
}

/**
 * @description 批量保存商品分组
 */
export function groupUpdate(data) {
  return request({
    url: `/merchant/admin/page/data/group`,
    method: 'POST',
    data
  })
}

/**
 * @description 批量保存
 */
export function batchSaveUpdate(data, id) {
  return request({
    url: `/merchant/admin/pageDataFoot/batchSave?parentId=` + id,
    method: 'POST',
    data
  })
}

/**
 * @description 参数配置 新增
 */
export function parameterAdd(data) {
  return request({
    url: `/authority/parameter`,
    method: 'POST',
    data
  })
}

/**
 * @description 参数配置 修改
 */
export function parameterUpdate(data) {
  return request({
    url: `/authority/parameter`,
    method: 'PUT',
    data
  })
}

/**
 * @description 参数配置 单体查询
 */
export function parameterGet(id) {
  return request({
    url: `/authority/parameter/`+id,
    method: 'get'
  })
}

/**
 * @description 获取 版权信息
 */
export function copyrightPage() {
  return request({
    url: `/merchant/admin/pageDataCopyright/get`,
    method: 'GET'
  })
}

/**
 * @description 修改 版权信息
 */
export function copyrightUpdate(data) {
  return request({
    url: `/merchant/admin/pageDataCopyright`,
    method: 'PUT',
    data
  })
}

/**
 * @description 新增 版权信息
 */
export function copyrightAdd(data) {
  return request({
    url: `/merchant/admin/pageDataCopyright`,
    method: 'POST',
    data
  })
}

/**
 * @description 获取页脚数据集合
 */
export function listByParentId(data) {
  return request({
    url: `/merchant/admin/pageDataFoot/listByParentId`,
    method: 'POST',
    data
  })
}


/**
 * @description 批量保存优惠广告
 */
export function batchSaveAd(data, id) {
  return request({
    url: `/merchant/admin/pageDataFoot/batchSaveAd?parentId=` + id,
    method: 'POST',
    data
  })
}

/**
 * 获取产品列表
 * @param data
 */
export function getProductList(data) {
  return request({
    method: 'post',
    url: '/product/admin/product/page',
    data
  })
}

export function getAllAd(data) {
  return request({
    url: '/merchant/admin/pageDataFoot/getAllAd',
    method: 'POST',
    data
  })
}

/**
 * 获取商家客服工作时间
 */
export function getWorkTime() {
  return request({
    url: `/merchant/admin/customer`,
    method: 'GET'
  })
}

/**
 * 增添客服工作时间
 * @param data
 */
export function addWorkTime(data) {
  return request({
    url: '/merchant/admin/customer',
    method: 'POST',
    data
  })
}

/**
 * 更新客服工作时间
 * @param data
 */
export function updateWorkTime(data) {
  return request({
    url: '/merchant/admin/customer',
    method: 'PUT',
    data
  })
}
