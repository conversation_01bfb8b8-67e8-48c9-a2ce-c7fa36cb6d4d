<template>
  <div class="list-index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="merchantName">
        <el-input v-model.trim="model.merchantName" placeholder="请输入企业名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="whetherEnabled">
        <el-select v-model="model.whetherEnabled" placeholder="请选择关联状态">
          <el-option label="开启" value="Y"></el-option>
          <el-option label="关闭" value="N"></el-option>
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" v-model="model.tabCode" :tabs="tabs"
        @change="handleChangeTab">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button @click="reloadFn">刷新</el-button>
          <el-button @click="handleAdd" v-if="checkPermission(['admin','sale-saas-sync-product:releationView','sale-platform-sync-product:releationView'])" type="primary">+ 关联企业</el-button>
        </template>
      </tabs-layout>
      <!-- 分页table -->
      <el-table border :data="tableData" style="width: 100%" v-loading="isLoading">
        <el-table-column type="index" width="50" align="center">
        </el-table-column>
        <el-table-column prop="synchronizeStu" label="同步开启状态" width="180">
          <template slot-scope="scope">
            <el-switch v-if="checkPermission(['admin','sale-saas-sync-product:switch','sale-platform-sync-product:switch'])" v-throttle v-model="scope.row.synchronizeStu" active-text="开启" inactive-text="关闭"
              @change="switchChange(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="企业编码" width="180">
        </el-table-column>
        <el-table-column prop="merchantName" label="我同步至(企业名称)">
        </el-table-column>
        <el-table-column prop="syncProductCount" label="已同步商品数">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <!-- <el-button @click="handleClick(scope.row)" type="text" size="small">重新同步</el-button> -->
            <del-el-button v-if="checkPermission(['admin','sale-saas-sync-product:del','sale-platform-sync-product:del'])" style="margin-left:5px" :targetId="scope.row.id" :text="delText" @handleDel="delSalesman">
            </del-el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page-row">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="current" :page-sizes="[10, 20, 50, 100]" :page-size.sync="limit"
          layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
        </el-pagination>
      </div>
    </div>
    <enterprise-Dialog ref="enterpriseDialog" @closeDialog="closeDialog"></enterprise-Dialog>
  </div>
</template>


<script>
  import checkPermission from '@/utils/permission'
  import delElButton from "@/components/eyaolink/delElButton";
  import enterpriseDialog from "@/views/products/dataSynchronize/enterpriseDialog"
  import {
    targetPage,
    sourcePage,
    batchUpdateSyncY,
    batchUpdateSyncN,
    delMerchantRel
  } from '@/api/product';
  export default {
    name: 'dataSync',
    //import引入的组件
    components: {
      delElButton,
      enterpriseDialog
    },
    data() {
      return {
        isExpand: false,
        delText: "您确定删除该商品同步数据吗？",
        model: {
          merchantName: '',
          whetherEnabled: '',
          tabCode: '1'
        },
        isLoading: false,
        current: 1,
        limit: 10,
        totalCount: 0,
        tableData: [],
        pageSize: 10,
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.initData();
    },

    computed: {
      tabs() {
        return [{
            name: '我同步至',
            value: '1',
            hide: !checkPermission(['admin','sale-saas-sync-product:pushView','sale-platform-sync-product:pushView'])
          },
          {
            name: '同步给我',
            value: '2',
            hide: !checkPermission(['admin','sale-saas-sync-product:pullView','sale-platform-sync-product:pullView'])
          }
        ]
      }
    },
    //方法集合
    methods: {
      checkPermission,
      initData() {
        if (this.tabs.every(item => item.hide)) return
        this.isLoading = true;
        let params = {
          model: {
            merchantName: this.model.merchantName,
            whetherEnabled: this.model.whetherEnabled,
          },
          current: this.current,
          size: this.limit,
          sort: 'id',
          order: 'descending',
        }
        if (this.model.tabCode == '1') {
          targetPage(params).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.isLoading = false;
              this.tableData = res.data.records.map(item => {
                return Object.assign({},
                  item, {
                    synchronizeStu: item.whetherEnabled.code == 'Y' ? true : false
                  }
                )
              });
              this.totalCount = res.data.total || 0;
            } else {
              this.isLoading = false;
            }
          })
        } else {
          sourcePage(params).then(res => {
            if (res.code == 0 && res.msg == 'ok') {
              this.isLoading = false;
              this.tableData = res.data.records.map(item => {
                return Object.assign({},
                  item, {
                    synchronizeStu: item.whetherEnabled.code == 'Y' ? true : false
                  }
                )
              });
              this.totalCount = res.data.total || 0;
            } else {
              this.isLoading = false;
            }
          })
        }
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        });
      },
      // 开启和关闭的方法
      switchChange(row) {
        console.log('row', row);
        if (row.synchronizeStu) {
          // 开启
          if (row.initEnabled.code === "N") {
            this.$confirm(`首次开启同步，将把当前已上架的商品数据全部同步给${row.merchantName}，是否确认开启同步？`, "确定开启同步？", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(async () => {
              batchUpdateSyncY({
                ids: row.id,
                initEnabled: "Y"
              }).then(res => {
                this.initData();
                if (res.code === 0 && res.msg === 'ok') {
                  this.$message.success('开启成功');
                }
              })
            }).catch(action => {
              if (action == 'cancel') {
                this.initData();
              }
            })
          } else {
            batchUpdateSyncY({
              ids: row.id,
            }).then(res => {
              this.initData();
              if (res.code == 0 && res.msg == 'ok') {
                this.$message.success('开启成功');
              }
            })
          }
        } else {
          // 关闭
          batchUpdateSyncN({
            ids: row.id
          }).then(res => {
            this.initData();
            if (res.code == 0 && res.msg == 'ok') {
              this.$message.success('关闭成功');
            }
          })
        }
      },
      // 删除
      delSalesman(id) {
        delMerchantRel(id).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('删除成功');
            this.initData();
          }
        })
      },
      handleSizeChange(val) {
        this.limit = val;
      },
      handleCurrentChange(val) {
        this.current = val;
      },
      reload() {
        this.$refs['tabs-layout'].reset();
        this.model = {
          merchantName: '',
          whetherEnabled: '',
          tabCode: '1'
        }
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reloadFn() {
        this.model = {
          merchantName: '',
          whetherEnabled: '',
          tabCode: this.model.tabCode
        }
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.initData();
      },
      // 弹窗关闭事件
      closeDialog(){
        this.initData();
      },
      // tab切换
      handleChangeTab(tab) {
        this.model.tabCode = tab.value;
        this.initData();
      },
      //   关联企业
      handleAdd() {
        this.$refs['enterpriseDialog'].openDia();
      },
    },

  }

</script>


<style lang='scss' scoped>
  .page-row {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

</style>
