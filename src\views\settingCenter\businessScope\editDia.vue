<template>
  <el-dialog :close-on-click-modal="false" :before-close="clearFun" :show-close="true" :title="title" :visible.sync="visible" width="35%">
    <div class="newRootContent">
      <el-form ref="ruleForm" class="form" :model="query" label-width="146px">
        <el-form-item class="formItem" label="父经营类目编码:" v-if="row.type === 'addChild'">
          <el-input v-model="row.categoryCode" disabled style="width:250px" placeholder="分类编码由系统生成"/>
        </el-form-item>
        <el-form-item class="formItem" label="经营类目编码:">
          <el-input v-model="query.categoryCode" :disabled="true" style="width:250px" placeholder="分类编码由系统生成"/>
        </el-form-item>

        <el-form-item class="formItem" prop="label" label="分类名称:" :rules="[{ required: true, message: '请填写分类名称',trigger: ['blur','change'] },
                   { max:10, message: '分类编码最多10位',trigger: ['blur','change'] }]"
        >
          <el-input v-model="query.label" clearable style="width:250px" placeholder="请填写分类名称"/>
        </el-form-item>
<!--        <el-form-item class="formItem" prop="sortValue" label="分类排序:" :rules="[{  validator: validateNumber,required: true, trigger: 'blur' }]">-->
<!--          <el-input-number v-model="query.sortValue" clearable style="width:300px" placeholder="请填写分类排序"/>-->
<!--        </el-form-item>-->
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="clearFun()">取 消</el-button>
      <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import checkPermission from "@/utils/permission";

import { addBusinessCategory, editBusinessCategory } from "@/api/settingCenter"
export default {
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  computed: {
    title() {
      if (!this.row) return '标题'
      if (!this.row.id) return '新增经营类目'
      if (this.row.type === 'addChild') return '新增子分类'
      return '编辑分类'
    },
  },
  data() {
    return {
      query: {
        categoryCode: '',
        label: '',
        sortValue: 1,
      }
    };
  },
  watch:{
    visible: {
      handler(newVal,oldVal){
        console.log('newVal',newVal)
        if (newVal) {
          this.initQuery();
        }
      },
      immediate: true
    }
  },
  mounted() {

  },
  beforeDestroy() {},
  methods: {
    checkPermission,
    validateNumber(rule, value, cb) {
      // 输入正数，保留小数点后两位
      if (!value) {
        return cb();
      }
      if (!this.isNumber2(value)) {
        return cb(new Error("只能输入正数"));
      }
      cb();
    },
    isNumber2(value) {
      let v = parseFloat(value);
      if (isNaN(v)) {
        return false;
      }
      value = v;
      if (value <= 0) {
        return false;
      }
      let arr = (value + "").split(".");
      if (arr[ 1 ] && arr[ 1 ].length > 2) {
        return false;
      }
      return true;
    },
    initQuery () {
      if (JSON.stringify(this.row) === '{}') {
        console.log('initQuery--1')
        // 新增
        this.query = {
          categoryCode: '',
          label: '',
          sortValue: 1,
        };
      } else if (this.row && this.row.type === 'addChild') {
        //  新增子分类
        this.query = {
          categoryCode: '',
          label: '',
          sortValue: 1,
          parentCategoryCode: this.row.categoryCode
        };
      } else if(this.row && this.row.type === 'edit') {
        console.log('initQuery--3')
        this.query = {
          categoryCode: this.row.categoryCode,
          label: this.row.categoryName,
          id: this.row.id,
          parentCategoryCode: this.row.parentCategoryCode,
          sortValue: this.row.sortValue,
        };
        delete this.query.type;
      }
    },
    clearFun() {
      this.$emit('update:visible', false);
      this.$emit('resetForm');
      this.query = {
        categoryCode: '',
          label: '',
          sortValue: 1,
      };
    },
    async submitFun(ruleForm) {
      this.$refs[ruleForm].validate(async (valid) => {
        if (valid) {
          let params = {
            ...this.query,
            whetherShowFrontend: 'Y'
          };
          if (this.row && this.row.type === 'edit') {
            //  编辑
            editBusinessCategory(params).then(res=>{
              if (res.code === 0 && res.msg === 'ok') {
                this.$message.success('编辑分类成功');
                this.clearFun();
              }
            })
          } else {
            addBusinessCategory(params).then(res=>{
              if (res.code === 0 && res.msg === 'ok') {
                this.$message.success('新增分类成功');
                this.clearFun();
              }
            })
          }
        }
      })
    },

  }
};
</script>
<style lang="less" scoped>
.newRootContent {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;

  .avatar-uploader {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }

  .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
}
</style>
