<template>
    <el-row v-if="options.length">
        <el-col :span="24">
            <el-radio-group :disabled="disabled" @change="handleRadioChange('TOP')" v-model="topValue">
                <el-radio v-for="item in options" :label="item.value" :key="item.value">{{ item.text }}</el-radio>
            </el-radio-group>
        </el-col>

        <el-col style="margin-top: 22px;" :span="24" v-if="hasChildren">
            <el-radio-group :disabled="disabled" @change="handleRadioChange('CHILDREN')" v-model="childrenValue">
                <el-radio v-for="item in children" :label="item.value" :key="item.value">{{ item.text }}</el-radio>
            </el-radio-group>
        </el-col>
    </el-row>
</template>

<script>
const MODEL = 'UPDATE_MODEL'
export default {
    props: {
        // 设置首个项目为默认值
        firstDefault: {
            type: <PERSON>olean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: [String, Number]
        },
        validateEvent: {
        type: Boolean,
        default: true
      },
        options: {
            type: Array,
            default: () => []
        }
    },
    inject: {
        elForm: {
            default: ''
        },
        elFormItem: {
            default: ''
        }
    },
    watch: {
        value: {
            handler(value) {
                let index = this.options.findIndex(v => v.value === value);
                if (~index && (!this.options[index].children || !this.options[index].children.length)) {
                    this.topValue = value;
                } else {
                    this.options.forEach(opt => {
                        if (opt.children) {
                            opt.children.forEach(v => {
                                if (v.value === value) {
                                    this.topValue = opt.value;
                                    this.childrenValue = value;
                                }
                            })
                        }
                    });
                }

            },
            immediate: true,
            deep: true
        },
    },
    data() {
        return {
            topValue: '',
            childrenValue: ''
        }
    },
    model: {
        prop: 'value',
        event: MODEL,
    },
    computed: {
        /**
         * 当前选中节点是否有子节点
         */
        hasChildren() {
            let index = this.options.findIndex(v => v.value === this.topValue);
            return !!(index >= 0 && this.options[index].children && this.options[index].children.length)
        },
        /**
         * 当前选中子节点集合
         */
        children() {
            if (!this.hasChildren) return []
            let index = this.options.findIndex(v => v.value === this.topValue);
            return this.options[index].children
        }
    },
    methods: {
        handleRadioChange(type) {
            // this.dispatch('ElFormItem', 'el.form.blur', [this.childrenValue])
            
            switch (type) {
                case 'TOP':
                    this.childrenValue = ''
                    let value = ''
                    console.log('this.hasChildren', this.hasChildren)
                    if (!this.hasChildren) {
                        value = this.topValue;
                    } else if (this.firstDefault && this.hasChildren) {
                        value = this.children[0].value
                        this.childrenValue = value
                        
                    }
                    this.$emit(MODEL, value)
                    this.elFormItem && this.elFormItem.$emit('el.form.blur', value)
                    break;
                case 'CHILDREN':
                    this.$emit(MODEL, this.childrenValue)
                    this.elFormItem && this.elFormItem.$emit('el.form.blur', this.childrenValue)
                    // this.elFormItem && this.elFormItem.dispatch('el.form.blur', [this.childrenValue])        
                    break;

                default:
                    break;
            }
        }
    }
}
</script>