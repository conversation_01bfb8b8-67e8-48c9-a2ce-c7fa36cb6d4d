<template>
    <div :class="$style.container">

        <im-search-pad :has-expand="false" :model="model" @reset="reload" @search="handleSearch">
            <im-search-pad-item prop="activityTime">
                <el-date-picker v-model="model.activityTime" type="datetimerange" range-separator="至"
                    value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']" />
            </im-search-pad-item>
            <im-search-pad-item prop="activityName">
                <el-input v-model.trim="model.activityName" placeholder="请输入活动名称" />
            </im-search-pad-item>
        </im-search-pad>

        <div :class="$style.main">
            <tabs-layout @change="handleTabChange" v-model="model.activityStatus" :tabs="tabs">
                <template slot="button">
                    <el-button @click="onRefersh">刷新</el-button>
                    <el-button @click="onAdd" v-if="checkPermission(['admin', 'sale-saas-promotion-fullReduce:add','sale-platform-promotion-fullReduce:add'])" type="primary" icon="el-icon-plus">新增满减送</el-button>
                </template>
            </tabs-layout>

            <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load"
                :operationWidth="150">
                <template slot="activityContent">
                    <el-table-column label="活动内容" width="200">
                        <template slot-scope="{row}">

                            <el-tooltip placement="top-start" :visible-arrow="false">
                                <div :class="$style.tooltip" slot="content" v-html="row.activityContentHtml"></div>
                                <span :class="$style.txt">{{ row.activityContent }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </template>
                <template slot="activityStartTime">
                    <el-table-column label="活动时间" width="300">
                        <template slot-scope="{row}">
                            {{ row.activityStartTime }}至{{ row.activityEndTime }}
                        </template>
                    </el-table-column>

                </template>
                <div slot-scope="{row}">
                    <el-button type="text" @click="onEdit(row.id, 'EDIT')" v-if="isEditable(row)">编辑</el-button>
                    <el-button type="text" @click="onEdit(row.id, 'VIEW')" v-if="isViewable(row)">查看</el-button>

                    <template v-if="isDisposable(row)">
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" @click="onObsolete(row.id)">作废</el-button>
                    </template>

                    <template v-if="isDeletable(row)">
                        <el-divider direction="vertical"></el-divider>
                        <el-button type="text" @click="onDelete(row.id)">删除</el-button>
                    </template>
                </div>
            </table-pager>
        </div>

    </div>
</template>
<script>
import PagedExport from "@/components/PagedExport/index.vue"
import { fetchActivityList, deleteActivity, updateActivityStatusToObsolete } from "@/api/fullReduction"
import checkPermission from '../../../utils/permission';
const tableColumns = [
    { label: '活动编码', name: 'activityCode', prop: 'activityCode' },
    { label: '活动名称', name: 'activityName', prop: 'activityName' },
    { label: '活动内容', name: 'activityContent', prop: 'activityContent', slot: true },
    { label: '活动时间', name: 'activityStartTime', prop: 'activityStartTime', slot: true },
    { label: '活动状态', name: 'activityStatus.desc', prop: 'activityStatus.desc', width: 80 },
    { label: '操作时间', name: 'updateTime', prop: 'updateTime', width: 80 },
].map((v, i) => ({ key: i, ...v }))

export default {
    name: 'fullDiscountActivities',
    components: {
        PagedExport
    },
    data() {
        return {
            tableData: [],
            tableTitle: tableColumns,
            tabs: [
                { name: "全部", value: 0, hide: !checkPermission(['admin', 'sale-saas-promotion-fullReduce:allView','sale-platform-promotion-fullReduce:allView']) },
                { name: "未开始", value: 'NOT_START', hide: !checkPermission(['admin', 'sale-saas-promotion-fullReduce:peddingView','sale-platform-promotion-fullReduce:peddingView']) },
                { name: "进行中", value: 'PROCEED', hide: !checkPermission(['admin', 'sale-saas-promotion-fullReduce:processingView','sale-platform-promotion-fullReduce:processingView']) },
                { name: "已结束", value: 'FINISHED', hide: !checkPermission(['admin', 'sale-saas-promotion-fullReduce:finishedView','sale-platform-promotion-fullReduce:finishedView']) },
                { name: "已作废", value: 'OBSOLETE', hide: !checkPermission(['admin', 'sale-saas-promotion-fullReduce:abolishedView','sale-platform-promotion-fullReduce:abolishedView']) }
            ],
            model: {
                activityTime: [],
                activityName: '',
                activityStatus: ''
            }
        }
    },
    methods: {
        checkPermission,
        /**
         * 是否可编辑 - 按钮权限判断
         */
        isEditable(data) {
            let isNotStart = !!(data.activityStatus && data.activityStatus.code === 'NOT_START');
            let isProceed = !!(data.activityStatus && data.activityStatus.code === 'PROCEED');
            let hasPermission = checkPermission(['admin', 'sale-saas-promotion-fullReduce:edit','sale-platform-promotion-fullReduce:edit'])
            return isNotStart && hasPermission && !isProceed
        },
        /**
         * 是否可查看 - 按钮权限判断
         */
        isViewable(data) {
            let isFinished = !!(data.activityStatus && data.activityStatus.code === 'FINISHED');
            let isObsolete = !!(data.activityStatus && data.activityStatus.code === 'OBSOLETE');
            let isProceed = !!(data.activityStatus && data.activityStatus.code === 'PROCEED');
            let hasPermission = checkPermission(['admin', 'sale-saas-promotion-fullReduce:detail','sale-platform-promotion-fullReduce:detail'])
            return (isFinished || isObsolete || isProceed) && hasPermission
        },
        /**
         * 是否可作废 - 按钮权限判断
         */
        isDisposable(data) {
            let isProceed = !!(data.activityStatus && data.activityStatus.code === 'PROCEED');
            let hasPermission = checkPermission(['admin', 'sale-saas-promotion-fullReduce:abolish','sale-platform-promotion-fullReduce:abolish'])
            return isProceed && hasPermission
        },
        /**
         * 是否可删除 - 按钮权限判断
         */
        isDeletable(data) {
            let hasPermission = checkPermission(['admin', 'sale-saas-promotion-fullReduce:del','sale-platform-promotion-fullReduce:del'])
            return hasPermission
        },
        onAdd() {
            this.$router.push({
                path: '/promotion/fullDiscount/edit'
            })
        },
        onRefersh() {
            this.$refs.todoTable.doRefresh({
                page: 1,
                pageSize: 10
            })
        },

        onEdit(id, type) {
            this.$router.push({
                path: '/promotion/fullDiscount/edit',
                query: {
                    saleMerchantId: this.saleMerchantId,
                    id: id,
                    type: type
                }
            })
        },



        async onDelete(id) {
            const isConfirm = await this.$confirm('确定删除该活动?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            if (!isConfirm) return;
            const data = await deleteActivity([id])
            if (data.code === 0) {
                this.$message.success('删除成功！')
                this.$refs.todoTable.doRefresh({
                    page: 1,
                    pageSize: 10
                })
            }
        },

        async onObsolete(id) {
            const isConfirm = await this.$confirm('确定作废该活动?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            if (!isConfirm) return;
            const data = await updateActivityStatusToObsolete(id)
            if (data.code === 0) {
                this.$message.success('该满减活动已作废！')
                this.$refs.todoTable.doRefresh({
                    page: 1,
                    pageSize: 10
                })
            }
        },




        handleSearch() {
            this.$refs.todoTable.doRefresh({
                page: 1,
                pageSize: 10
            })
        },
        reload() {
            this.model = {
                activityTime: [],
                activityName: '',
                activityStatus: ''
            }
            this.$refs.todoTable.doRefresh()
        },
        handleTabChange() {
            this.$refs.todoTable.doRefresh({
                page: 1,
                pageSize: 10
            })
        },
        async load(params) {

            let { activityTime, ...model } = this.model;
            let [activityStartTime, activityEndTime] = activityTime;
            let listQuery = {
                model: {
                    ...model,
                    activityStartTime,
                    activityEndTime
                }

            }
            Object.assign(listQuery, params)
            let result = await fetchActivityList(listQuery);
            result.data.records.forEach(item => {
                let unit = item.fullReduceType && (item.fullReduceType.code === 'NUMBER' ? '个' : '元') || '';
                let prefix = item.discountSetUp && (item.discountSetUp.code === 'LADDER' ? '满' : '每满') || '';

                let list = item.fullReduceActivityRuleRelDTOList?.map(v => {
                    let gifts = v.whetherGiveaway?.code === 'Y' && v.giveawayList?.map(g => `【${g.productName}】x${g.giveawayQuantity}`) || [];
                    let text = `${prefix}${Number(v.couponThreshold)}${unit}${v.discount ? `减${v.discount}` : ''}`
                    if (gifts.length) {
                        text += `，送${v.giveawaySpeciesNumber}种赠品（${gifts.join()}）${gifts.length !== v.giveawaySpeciesNumber ? `${gifts.length}选${v.giveawaySpeciesNumber}` : ``}`
                    }
                    return text;
                }) || []
                let desc =  item.discountSetUp?.desc || ''
                const whetherRewardText = item?.whetherReward?.code === 'Y' ? '裂变奖励，' : ''
                const whetherPointsDeductionText = item?.whetherPointsDeduction?.code === 'Y' ? '积分抵扣，' : ''
                desc = whetherRewardText + desc
                desc = whetherPointsDeductionText + desc
                item.activityContent = `${[desc, list.join(';')].join('，')}`
                item.activityContentHtml = `<h1>${desc}</h1>${list.map((v, i) => `<p class='${this.$style.tip}'>${list.length > 1 ? `${i + 1}.` : ''}${v}</p>`).join('')}`


            })
            return result;
        }
    }
}
</script>

<style lang="scss" module>
.container {
    width: 100%;

    .main {
        background-color: #ffffff;
        padding: 8px 20px 20px;
        margin-top: 16px;
    }

    .txt {
        width: 300px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }




}

.tooltip {
    max-width: 240px;



    .tip {
        padding-top: 10px;
    }
}
</style>
