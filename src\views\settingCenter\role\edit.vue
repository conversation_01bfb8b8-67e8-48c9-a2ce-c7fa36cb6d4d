<template>
  <div class="roleEditContent">
    <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;" >
        <div>
          <el-button @click="clearFun()">取 消</el-button>
          <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
        </div>
    </div>
    <el-form class="form" :model="query" ref="ruleForm" label-width="90px">
      <el-form-item class="formItem" prop="role.code" label="角色编码:" :rules="[{ required: true, message: '请填写角色编码',trigger: 'blur' }]">
        <el-input :disabled="query.role.id>0" clearable style="width:100%" v-model="query.role.code" placeholder="请填写角色编码"></el-input>
      </el-form-item>
      <el-form-item class="formItem" prop="role.name" label="角色名称:" :rules="[{ required: true, message: '请填写角色名称',trigger: 'blur' }]">
        <el-input clearable style="width:100%;" v-model="query.role.name" placeholder="请填写角色名称"></el-input>
      </el-form-item>
      <el-form-item class="formItem" prop="role.status" label="状态:" :rules="[{ required: true, message: '请选中状态',trigger: 'blur' }]">
        <el-radio-group v-model="query.role.status">
          <el-radio-button :label="true">启用</el-radio-button>
          <el-radio-button :label="false">禁用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="formItem" prop="role.describe" label="描述:"
        :rules="[
          { required: true, message: '请填写角色描述',trigger: 'blur' },
          { max: 50, message: '角色描述，最多50个字',trigger: ['blur','change'] }
        ]"
      >
        <el-input
          maxlength="50"
          show-word-limit
          type="textarea"
          clearable
          style="width:100%;"
          rows="2"
          v-model="query.role.describe"
          placeholder="请输入角色描述，最多50个字"
        ></el-input>
      </el-form-item>
     <el-form-item label="权限设置：" v-if="query.roleAuthority">
         <roleAuthority :roleAuthorityInfo.sync="query.roleAuthority"></roleAuthority>
    </el-form-item>

    </el-form>
  </div>
</template>
<script>
import roleAuthority from '@/views/settingCenter/role/roleAuthority'
import {editRoleAndAuthority,findAuthorityIdByRoleId } from '@/api/setting/permission/userRole'
export default {
  data() {
    return {
      query: {
        role:{
          	code: "",
            describe: "",
            dsType: "ALL",
            name: "",
            orgList: [],
            status: true
        },
        roleAuthority:{},
      },
      check: [],
      data: [],
      role: {},
      id: '',
      appList: []
    };
  },
  components:{
    roleAuthority
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    },
    isReload: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    submitFun: function(businessScopeForm) {
      let _this=this;
      this.$refs[businessScopeForm].validate(async valid => {
        if (valid) {
          await editRoleAndAuthority(_this.query)
          _this.$emit("update:visible", false);
          _this.$emit("update:isReload", true);
        } else {
          return false;
        }
      });
    }
  },
  async mounted() {
    if(this.row.id!=undefined&&this.row.id>0){
      this.query.role =Object.assign(this.query.role,this.row);
      let {data} = await findAuthorityIdByRoleId(this.row.id)
      this.query.roleAuthority=data
    }

  },
};
</script>
<style lang="less" scoped>
.roleEditContent {
  background-color: #fff;
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
