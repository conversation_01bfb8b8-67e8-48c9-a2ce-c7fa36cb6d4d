<template>
    <popup-selection @open="handleOpen" :limit="5" :disabled="disabled" label="赠品" :data.sync="list" :request="request"
        :searchFields="searchFields" :selectTableColumns="selectTableColumns" :tableColumns="tableColumns"
        @ok="handleOk" @change="handleChange">
    </popup-selection>
</template>

<script>
import PopupSelection from "../LogicalComponents/PopupSelection.vue"
import { fetchGiftsList } from "@/api/gifts"
import { getAllStore } from "@/api/products/store";
const firstColRender = row => {
    
    return `<span>编码：${row.erpCode ? row.erpCode : '无'}</span> <br /><span>仓库：${row.warehouseName ? row.warehouseName : '无' }</span>`;
}

const selectTableColumns = [
    { label: 'ERP商品编码/仓库', name: 'erpCode', prop: 'erpCode', width: 160, render: firstColRender },
    { label: '商品名称', name: 'productName', prop: 'productName', width: 200 },
    { label: '生产厂家', name: 'manufacturer', prop: 'manufacturer' },
    { label: '规格', name: 'spec', prop: 'spec' },
    { label: '换购价（元）', name: 'salePrice', prop: 'salePrice', width: 110 },
    { label: '库存', name: 'stockQuantity', prop: 'stockQuantity' },
].map((v, i) => ({ key: i, ...v }))


const tableColumns = [
    { label: 'ERP商品编码/仓库', name: 'erpCode', prop: 'erpCode',slot: true, width: 160, render: firstColRender },
    { label: '商品名称', name: 'productName', prop: 'productName', width: 200 },
    { label: '生产厂家', name: 'manufacturer', prop: 'manufacturer' },
    { label: '规格', name: 'spec', prop: 'spec' },
    { label: '单位', name: 'unit', prop: 'unit' },
    { label: '销售价', name: 'salePrice', prop: 'salePrice', width: 110 },
    { label: '可赠库存', name: 'stockQuantity', prop: 'stockQuantity' },
    { label: '赠送数量', name: 'giveawayQuantity', prop: 'giveawayQuantity', slot: true, edit: 'number', maxprop: 'stockQuantity' }
].map((v, i) => ({ key: i, ...v }))
const MODEL = 'UPDATE_MODEL'
export default {
    props: {
        data: {
            type: Array,
            default: () => []
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        },
        update: {
            type: Boolean,
            default: true
        }
    },
    inject: {
        elForm: {
            default: ''
        },
        elFormItem: {
            default: ''
        }
    },
    watch: {
        data: {
            handler(data) {
                this.list = data || [];
            },
            immediate: true,
            deep: true
        },
        update: {
            handler(isUpdate) {
                
                this.tableColumns = this.tableColumns.map(item => {
                    if(item.edit === 'number') {
                        item.enableMaxCheck = !isUpdate
                    }
                    return item
                })
            },
            immediate: true,
        }
    },
    model: {
        prop: 'data',
        event: MODEL
    },
    data() {
        return {
            list: [],
            request: fetchGiftsList,
            selectTableColumns,
            tableColumns,
            searchFields: [
                {
                    type: 'Text',
                    prop: 'keyword',
                    value: '',
                    placeholder: '商品名称/商品编码'
                },
                {
                    type: 'Text',
                    prop: 'manufacturer',
                    value: '',
                    placeholder: '生产厂家	'
                },
                {
                    prop: 'warehouseIds',
                    text: '所在仓库',
                    type: 'MultipleSelect',
                    options: []
                },
            ]
        }
    },
    methods: {
        handleOpen() {
            !(this.searchFields[2]?.options?.length) && this.fetchWarehouseList();
        },
        handleChange(data) {
            this.$emit(MODEL, data)
            this.elFormItem && this.elFormItem.$emit('el.form.blur', data)
        },
        handleOk(data) {
            this.$emit(MODEL, data)
        },
        async fetchWarehouseList() {
            const { data } = await getAllStore();
            this.searchFields.splice(2, 1, {
                ...this.searchFields[2],
                options: data?.map(({ name: text, id: value }) => ({ text, value })) || []
            })
            console.log(data)
        }
    },
    components: {
        PopupSelection
    }
}
</script>

<style lang="scss" module>
</style>