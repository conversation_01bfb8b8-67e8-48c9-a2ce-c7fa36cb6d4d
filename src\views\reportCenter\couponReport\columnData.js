import { list } from "@/enterprise"

export default {
    order:[
        {
            label: '订单编号',
            name: 'orderNo',
            prop: "orderNo",
            width:'120',
        },
        {
            label: '订单金额(元)',
            name: 'orderMoney',
            prop: 'orderMoney',
            width: '120'
        },
        {
            label: '实付金额(元)',
            name: 'orderReal<PERSON><PERSON>',
            prop: 'orderRealMoney',
            width: '120'
        },
        {
            label: '订单状态',
            name: 'orderStatus.desc',
            prop: 'orderStatus.desc',
            width: '120',
        },
        {
            label: 'ERP客户编码',
            name: 'customerCode',
            prop: 'customerCode',
            width: '120',
        },
        {
            label: '客户名称',
            name: 'customerName',
            prop: 'customerName',
            width: '180',
        },
        {
            label: '活动编码',
            name: 'activityCode',
            prop: 'activityCode',
            width: '130',
        },
        {
            label: '优惠券名称',
            name: 'couponName',
            prop: 'couponName',
            width: '120',
        },
        {
            label: '是否专享券',
            name: 'whetherVipCoupon.desc',
            prop: 'whetherVipCoupon.desc',
            width: '100',
        },
        {
            label: '优惠金额',
            name: 'couponMoney',
            prop: 'couponMoney',
            width: '100',
        },
        {
            label: '领券方式',
            name: 'couponGetType.desc',
            prop: 'couponGetType.desc',
            width: '120',
        },
        {
            label: '用券时间',
            name: 'userCouponTime',
            prop: 'userCouponTime',
            width: '150',
        }
    ],
    client:[
        {
            label: 'ERP客户编码',
            name: 'erpCode',
            prop: 'erpCode',
            width: '120',
        },
        {
            label: '客户名称',
            name: 'purMerchantName',
            prop: 'purMerchantName',
            width: '180',
        },
        {
            label: '是否会员',
            name: 'whetherMember.desc',
            prop: 'whetherMember.desc',
            width: '120',
        },
        {
            label: '领券数',
            name: 'couponReceivedNum',
            prop: 'couponReceivedNum',
            width: '120',
        },
        {
            label: '领券面值总额(元)',
            name: 'couponReceivedMoney',
            prop: 'couponReceivedMoney',
            width: '150',
        },
        {
            label: '用券数',
            name: 'couponUseNum',
            prop: 'couponUseNum',
            width: '120',
        },
        {
            label: '用券面值总额(元)',
            name: 'couponUseMoney',
            prop: 'couponUseMoney',
            width: '150',
        },
        {
            label: '剩余券数',
            name: 'couponHaveNum',
            prop: 'couponHaveNum',
            width: '120',
        },
        {
            label: '剩余面值总额(元)',
            name: 'couponHaveMoney',
            prop: 'couponHaveMoney',
            width: '150',
        },
    ]
}