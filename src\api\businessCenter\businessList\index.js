import requestAxios from '@/utils/requestAxios'
import request from '@/utils/request'

// 经营范围
export function findSaleScope() {
  return requestAxios({
    url: '/merchant/admin/businessCategory/getTreeByShow',
    method: 'get'
  })
}

export function areas () {
  return requestAxios({
    url: '/authority/area/anno/tree',
    method: 'get'
  })
}

// 添加资质
export function addLicense(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantLicense',
    method: 'post',
    data
  })
}

// 修改资质
export function editLicense(data) {
  return requestAxios({
    url: '/api/merchant/admin/merchantLicense',
    method: 'put',
    data
  })
}