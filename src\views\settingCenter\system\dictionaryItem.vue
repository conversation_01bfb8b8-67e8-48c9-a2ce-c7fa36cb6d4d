<template>
  <div class="dictionaryItem">
    <el-table
      v-loading="isLoading"
      border
      fit
      :data="list"
      style="width: 100%"
      max-height="500px"
      @filter-change="fliterChange"
    >
      <el-table-column prop="id"  align="center" label="序号" show-overflow-tooltip width="60">
        <template slot-scope="scope">
          {{scope.$index+1}}
        </template>
      </el-table-column>
      <el-table-column prop="name" show-overflow-tooltip label="名称" width="200"></el-table-column>
      <el-table-column prop="code" label="编码" show-overflow-tooltip width="100"  column-key="code"></el-table-column>
      <el-table-column width="100"  align="center" show-overflow-tooltip label="状态" :filters="tableStutasList" :filter-multiple="false" column-key="status">
        <template slot-scope="{ row }" >
            <span v-if="row['status']==true" class="el-tag el-tag--success" >启用</span>
            <span v-if="row['status']==false" class="el-tag el-tag--danger" >禁用</span>
        </template>
      </el-table-column>
      <el-table-column prop="describe" show-overflow-tooltip label="描述"  min-width="200"></el-table-column>
      <el-table-column
        v-if="
          checkPermission([
            'admin',
            'dictionaryItem:update',
            'dictionaryItem:delete'
          ])
        "
        fixed="right"
        align="center"
        label="操作"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            v-if="checkPermission(['admin', 'dictionaryItem:update'])"
            @click="editActionFun(scope.row)"
            type="text"

            >编辑</el-button
          >
          <el-button
            v-if="checkPermission(['admin', 'dictionaryItem:delete'])"
            @click="deleteActionFun(scope.row)"
            type="text"

            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="checkPermission(['admin', 'dictionaryItem:add'])"
      class="tableCenter"
    >
      <el-button
        type="text"
        icon="el-icon-plus"
        @click="editActionFun({
        id: 0,
        dictionaryId:  row.id,
        dictionaryType: row.name,
        name: '',
        code: '',
        status: true,
        sortValue: 1,
        describe: ''
      }
     )"
        >新增字典项</el-button
      >
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :title="(query.id == 0 ? '编辑' : '新增') + '字典项'"
      :visible.sync="showEditAction"
      :show-close="false"
      width="500px"
      :before-close="editActionClose"
    >
      <div style="position:absolute;top:10px; right:15px; background:#fff; height:38px;" >
        <div>
            <el-button @click="showEditAction = false" >取 消</el-button>
            <el-button type="primary" @click="submitFun('ruleForm')" >确 定</el-button>
        </div>
      </div>
      <div class="editDictionaryItem">
        <el-form class="form" :model="query" ref="ruleForm" label-width="60px">
          <el-form-item
            label="编码"
            prop="code"
            :rules="[
              { required: true, message: '请填写类型', trigger: 'blur' }
            ]"
          >
            <el-input :disabled="query.id > 0" v-model="query.code" />
          </el-form-item>
          <el-form-item
            label="名称"
            prop="name"
            :rules="[
              { required: true, message: '请填写名称', trigger: 'blur' }
            ]"
          >
            <el-input v-model="query.name" />
          </el-form-item>
          <el-form-item
            label="排序"
            prop="sortValue"
            :rules="[
              { required: true, message: '请填写排序', trigger: 'blur' }
            ]"
          >
            <el-input v-model="query.sortValue" />
          </el-form-item>
          <el-form-item
            label="状态"
            prop="status"
            :rules="[
              { required: true, message: '请填写状态', trigger: 'blur' }
            ]"
          >
            <el-radio-group v-model="query.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述" prop="describe">
            <el-input v-model="query.describe" />
          </el-form-item>
          <!-- <el-form-item>
            <el-button @click="showEditAction = false">取 消</el-button>
            <el-button type="primary" @click="submitFun('ruleForm')">确 定</el-button>
          </el-form-item> -->
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import checkPermission from "@/utils/permission";
import {
  query,
  editApi,
  deleteApi
} from "@/api/setting/dictionary/dictionaryItem";
export default {
  data() {
    return {
      tableStutasList:[
        {
          text:"启用",
          value:true
        },
        {
          text:"禁用",
          value:false
        }
      ],
      isLoading: true,
      showEditAction: false,
      list: [],
      query: {},
      listQuery: {
        status:null,
        dictionaryId:0
      }
    };
  },
  props: {
    row: {
      type: Object
    },
    visible: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  methods: {
    checkPermission,

    clearFun: function() {
      this.$emit("update:visible", false);
      this.$emit("update:row", {});
    },
    editActionFun(row) {
      this.query = row;
      this.showEditAction = true;
    },
    fliterChange(filters){
      for (var key in filters)
      {
          this.listQuery[key]=filters[key][0]
      }
      this.getList();
    },
    async deleteActionFun(row) {
      var _this = this;
      var data = await deleteApi(row.id);
      if (data.code == 0) {
        _this.$message({
          message: "提交成功！",
          type: "success",
          onClose: function() {
            _this.list = data.records;
            _this.getList();
          }
        });
      } else {
        this.$message({
          showClose: true,
          message: data.msg,
          type: "error"
        });
      }
    },
    async submitFun(ruleForm) {
      var _this = this;
      _this.$refs[ruleForm].validate(async valid => {
        if (valid) {
          var data = await editApi(this.query);
          if (data.code == 0) {
            _this.$message({
              message: "提交成功！",
              type: "success",
              onClose: function() {
                _this.getList();
                _this.showEditAction = false;
              }
            });
          } else {
            this.$message({
              showClose: true,
              message: data.msg,
              type: "error"
            });
          }
        } else {
          return false;
        }
      });
    },
    editActionClose() {
      this.showEditAction = false;
    },
    async getList() {
      this.list = [];
      this.isLoading = true;
      let { data } = await query(this.listQuery);
      this.list = data;
      this.isLoading = false;
    }
  },
  async mounted() {

    this.listQuery.dictionaryId = this.row.id;
    this.getList();
  },
};
</script>
<style lang="less" scoped>
.dictionaryItem {
  margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;

  .tableCenter {
    border: 1px solid #efefef;
    text-align: center;
  }
  p.note {
    font-size: 12px;
    margin: 0;
    padding: 0;
  }
}

.editDictionaryItem{
   margin: -30px -20px;
  border-top: 1px solid #ebecee;
  padding: 30px 20px;
}
</style>
