<template>
  <!-- <el-dialog title="选择客户"
          width="60%"
          :visible.sync="showModal">
    <div class="search">
      <el-input v-model="customerCode"  style="width: 240px;margin-right: 16px;" placeholder="请输入ERP客户编码"/>
      <el-input v-model="purMerchantName"  style="width: 240px;margin-right: 16px;" placeholder="请输入客户名称"/>
      <el-button type="primary"  @click="handleSearch">搜索</el-button>
      <el-button  @click="handleReset">重置</el-button>
    </div>
    <el-table border
              stripe
              :data="list">
      <el-table-column type="index" align="center">
        <template slot="header">
          <i class="el-icon-menu"></i>
        </template>
      </el-table-column>
      <el-table-column label="选择" align="center" width="60">
        <template v-slot="scope">
          <el-radio v-model="selectedIndex" :label="scope.$index"></el-radio>
        </template>
      </el-table-column>
      <el-table-column label="ERP客户编码" prop="customerCode" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="客户名称" prop="name" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column label="客户类型" prop="merchantType" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="负责人" prop="ceoName" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="联系电话" prop="ceoMobile" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="所在地区" prop="region" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160" show-overflow-tooltip></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background
                    :current-page="pageNum"
                    :total="total"
                    :page-size="pageSize"
                    @current-change="handlePageChange"
                    layout="prev, pager, next, sizes, jumper">
      </el-pagination>
    </div>
    <div class="buttons">
      <el-button @click="showModal=false">取消</el-button>
      <el-button @click="onSelect" type="primary">确定</el-button>
    </div>
  </el-dialog> -->
  <clients-model enable-radio ref="model" @close="showModal = false" @ok="onSelect" :visible.sync="showModal">
  </clients-model>
</template>

<script>
import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'
import { merchantPurSaleRelList } from '@/api/group';

export default {
  components: { ClientsModel },
  data() {
    return {
      showModal: false,
      customerCode: '',
      purMerchantName: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectedIndex: null,
      list: [],
    }
  },
  created() {
    this.getList();
  },
  methods: {
    show() {
      this.showModal = true
    },
    onSelect(item) {
      // let item = this.list[this.selectedIndex];
      // if (!item) {
      //   return this.$message.error('请选择客户');
      // }
      this.$emit('on-select', item);
      this.showModal = false;
    },
    getList() {
      let query = {
        current: this.pageNum,
        size: this.pageSize,
        order: "descending",
        model: {
          customerCode: this.customerCode,
          purMerchantName: this.purMerchantName,
          publishStatus: "Y",
          firstCampStatus: "ESTABLISH"
        },
      };
      merchantPurSaleRelList(query).then(res => {
        if (res.code === 0) {
          this.list = res.data.records || [];
          this.total = res.data.total;
        }
      });
    },
    handleSearch() {
      this.pageNum = 1
      this.getList()
    },
    handleReset() {
      this.customerCode = '';
      this.purMerchantName = '';
      this.pageNum = 1
      this.getList()
    },
    handlePageChange(val) {
      this.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 16px;
}

.el-radio {
  ::v-deep .el-radio__label {
    display: none;
  }
}

.pagination {
  margin-top: 16px;
  text-align: right;
}

.buttons {
  // border-top: 1px solid #CCC;
  text-align: right;
  margin-top: 10px;
  // padding: 10px 0;
}
</style>
