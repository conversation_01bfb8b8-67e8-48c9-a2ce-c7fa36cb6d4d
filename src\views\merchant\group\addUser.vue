<template>
  <div>
    <el-dialog
      title="添加客户"
      :visible.sync="addUservisible"
      width="65%"
      @close="handleClose"
      v-dialogDrag
    >
      <im-search-pad
        :has-expand="false"
        :model="listQuery"
        @reset="reset"
        @search="search"
      >
        <im-search-pad-item prop="customerCode">
          <el-input @keyup.enter.native="search" v-model="listQuery.model.customerCode" placeholder="请输入ERP客户编码" />
        </im-search-pad-item>
        <im-search-pad-item prop="name">
          <el-input @keyup.enter.native="search" v-model="listQuery.model.name" placeholder="请输入客户名称" />
        </im-search-pad-item>
      </im-search-pad>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load"  :operation-width="60" @selection-change="handleSelectionChange">
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-pur-merchant-group:clientRelation'])"  @click="add(props.row)">添加</el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
      <div slot="footer" class="dialog-footer">
<!--        <el-button @click="addUservisible = false">取 消</el-button>-->
        <!-- <el-button type="primary" @click="adds">确定</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import { queryPageSaleMerchantGroupCustomerListDTO, updateMerchantPurSaleRelMerchantGroup } from '@/api/group'

const TableColumns = [
  { label: 'ERP客户编码', prop: 'customerCode', width: '150' },
  { label: '客户名称', prop: 'name', width: '140' },
  { label: '企业类型', prop: 'merchantType' },
  { label: '负责人', prop: 'ceoName' },
  { label: '联系电话', prop: 'ceoMobile', width: '120' },
  { label: '所在区域', prop: 'region', width: '150' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export default {
  name: 'DragTable',
  props: ['visible', 'saleMerchantId', 'row','groupType'],
  data() {
    return {
      tableTitle: TableColumnList,
      list: null,
      total: null,
      listLoading: false,
      sortable: null,
      tableData: [],
      ids: [],
      listQuery: {
        model: {
          'customerCode': '',
          'name': '',
          'saleMerchantId': '',
          merchantGroupId: 1
        }
      },
      addUservisible: false,
      merchantGroupId: ''
    }
  },
  watch: {
    visible() {
      this.addUservisible = this.visible
      if (this.addUservisible === true) {
        this.$nextTick(() => {
          this.handleRefresh({
            page: 1,
            pageSize: 10
          })
        })
      }
    },
    row() {
      this.merchantGroupId = this.row.id
    }
  },
  created() {
    // this.getList();
  },
  methods: {
    checkPermission,
    handleClose() {
      // 子组件调用父组件方法，并传递参数
      this.listLoading = false
      this.$emit('changeShow', 'false')
    },
    search() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.load()
    },
    reset() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
      this.listQuery.model.customerCode = ''
      this.listQuery.model.name = ''
      this.load()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    async load(params) {
      this.listQuery.model.saleMerchantId = this.saleMerchantId;
      this.listQuery.model.operateType = "ADD";
      console.log('this.merchantGroupId',this.merchantGroupId);
      if(this.groupType == 'CONTROL') {
        this.listQuery.model.groupType = "CONTROL";
        this.listQuery.model.merchantGroupId = this.merchantGroupId;
      } else {
        this.listQuery.model.merchantGroupId = 1;
      }
      this.listLoading = true
      Object.assign(this.listQuery, params);
      return await queryPageSaleMerchantGroupCustomerListDTO(this.listQuery)
    },
    async add(row) {
      const params = {
        'ids[]': row.id,
        merchantGroupId: this.merchantGroupId
      }
      if(this.groupType == 'CONTROL'){
        params.groupType = 'CONTROL'
      }
      await updateMerchantPurSaleRelMerchantGroup(params)
      this.$message.success('添加成功！')
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    async adds() {
      if (this.ids.length > 0) {
        const { data } = await updateMerchantPurSaleRelMerchantGroup([this.ids.join(',')], this.merchantGroupId)
        this.$message.success('批量添加成功！')
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      }
      this.$emit('changeShow', 'false')
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item, index) {
        return item.id
      })
    }
  }
}
</script>
