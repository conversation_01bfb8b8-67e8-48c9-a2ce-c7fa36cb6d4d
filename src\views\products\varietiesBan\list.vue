<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="reload"
      @search="handleSearch"
    >
      <im-search-pad-item prop="productName">
        <el-input v-model="listQuery.model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="productCode">
        <el-input v-model="listQuery.model.productCode" placeholder="请输入商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="controlType">
        <el-select v-model="listQuery.model.controlType" placeholder="控销类型" clearable @change="getType">
          <el-option value="RESTRICTION" label="商品控销" />
          <el-option value="PROHIBITION" label="商品禁销" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="listQuery.model.processStatus"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button v-if="checkPermission(['admin','varieties:batchDel'])"  @click="dels">批量删除</el-button>
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin','varieties:add'])" type="primary"  @click="$router.push({path: '/productCenter/varietiesBan/create'})">+ 新增控销商品</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="load" :data.sync="tableData" :selection="true" @selection-change="handleSelectionChange">
        <template slot="controlSaleProduct.pictIdS">
          <el-table-column label="商品主图" width="80" class-name="img-cell">
            <slot slot-scope="{row}">
              <!-- <img v-if="row.controlSaleProduct.pictIdS" :src="row.controlSaleProduct.pictIdS.split(',')[0]" class="productImg">
              <img v-if="row.controlSaleProduct.pictIdS == null || row.controlSaleProduct.pictIdS == ''" :src="pictImg" width="50px"> -->
              <img :src="row.controlSaleProduct.pictIdS|imgFilter" width="50px" height="50px">
            </slot>
          </el-table-column>
        </template><template slot="controlBeginTime">
          <el-table-column label="控销时间" width="300px">
            <slot slot-scope="{row}">
              <span>{{ row.controlBeginTime + '至'+ row.controlEndTime }}</span>
            </slot>
          </el-table-column>
        </template>
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin','varieties:edit'])" class="table-edit-row-item">
              <el-button type="text" @click="$router.push({path: '/productCenter/varietiesBan/edit',query:{id: props.row.id}})">编辑</el-button>
            </span>
            <span v-if="checkPermission(['admin','varieties:del'])" class="table-edit-row-item">
              <!-- <el-button type="text" @click="del(props.row)">删除</el-button> -->
              <del-el-button
                :targetId="props.row.id"
                :text="delText"
                @handleDel="del"
              ></del-el-button>
            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
const TableColumns = [
  { label: '商品主图', name: 'controlSaleProduct.pictIdS', prop: 'controlSaleProduct.pictIdS', slot: true },
  { label: '商品编码', name: 'controlSaleProduct.productCode', prop: 'controlSaleProduct.productCode', width: '180' },
  { label: '商品名称', name: 'controlSaleProduct.productName', prop: 'controlSaleProduct.productName', width: '200' },
  { label: '规格', name: 'controlSaleProduct.spec', prop: 'controlSaleProduct.spec', width: '60' },
  { label: '单位', name: 'controlSaleProduct.unit', prop: 'controlSaleProduct.unit', width: '60' },
  { label: '生产厂家', name: 'controlSaleProduct.manufacturer', prop: 'controlSaleProduct.manufacturer' },
  { label: '商品分类', name: 'controlSaleProduct.categoryPath', prop: 'controlSaleProduct.categoryPath', width: '300' },
  { label: '控销/禁销', name: 'controlType.desc', prop: 'controlType.desc', width: '90' },
  { label: '控销时间', name: 'controlBeginTime', prop: 'controlBeginTime', slot: true }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
import checkPermission from '../../../utils/permission'
import delElButton from "@/components/eyaolink/delElButton";
import { productControlSaleList, productList, deleteProductControlSale } from '@/api/product.js'
import productImg from '../../../assets/product.png'
import TabsLayout from "../../../components/TabsLayout/index";
export default {
  components: {TabsLayout,delElButton},
  // inject: ['reload'],
  data() {
    return {
      delText:"您是否删除此渠道控销商品吗？",
      pictImg: productImg,
      loading: '',
      search: '',
      controlType: '',
      currentTab: 0,
      tableData: [],
      page: 1,
      pageSize: 10,
      totalPage: 0,
      total: 0,
      tableTitle: TableColumnList,
      listQuery: {
        order: 'descending',
        map: {},
        model: {
          controlType: '',
          processStatus: 'UNFINISHED',
          productName: '',
          productCode: ''
        },
        sort: 'id'
      },
      keyword: '',
      keywordSelect: 1,
      products: [],
      ids: []
    }
  },
  computed: {
    tabs() {
      return [
        { name: '待执行', value: 'UNFINISHED', hide: !checkPermission(['admin', 'varieties-unfinish:view']) },
        { name: '执行中', value: 'IN_PROCESS', hide: !checkPermission(['admin', 'varieties-process:view']) },
        { name: '已执行', value: 'FINISHED',  hide: !checkPermission(['admin', 'varieties-finish:view']) }
      ]
    }
  },
  mounted() {
    // 注意一定要保证DOM渲染完成后在进行合并操作，否则会找不到元素
    this.$nextTick(function() {
      // this.setColSpan();
    })
  },
  methods: {
    checkPermission,
    async querySearch(queryString, cb) {
      const params = {
        model: {
          keyword: this.keyword
        },
        current: 1,
        size: 1000,
        order: 'descending',
        sort: 'id'
      }
      const { data } = await productList(params)
      this.products = data.records
      for (let i = 0; i < this.products.length; i++) {
        this.products[i].value = this.products[i].approvalUserName
      }
      // 调用 callback 返回建议列表的数据
      cb(this.products)
    },
    handleSelect(item) {
      this.listQuery.model.productId = item.id
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      this.loading = true
      return await productControlSaleList(this.listQuery)
      return data
    },
    handleSearch() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    reload() {
      this.listQuery = {
        order: 'descending',
        map: {},
        model: {
            controlType: '',
            processStatus: 'UNFINISHED',
            productName: '',
            productCode: ''
        },
        sort: 'id'
      };
      this.$refs['tabs-layout'].reset();
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    handleChangeTab(tab, index) {
      this.currentTab = index
      // this.listQuery.model.processStatus = tab.value
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    getType(val) {
      this.listQuery.model.controlType = val
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    splitString(val) {
      return val.split(',')
    },
    async del(id) {
      deleteProductControlSale([id]).then(res=>{
        if(res.code==0){
          this.$message.success('删除成功！');
          this.reload();
        }
      })
    },
    dels() {
      if (this.ids.length === 0) {
        return false
      }
      this.$confirm("您确定批量删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(_ => {
        const { data } = deleteProductControlSale([this.ids.join(',')])
        this.$message.success('删除成功！')
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
      }).catch(_ => {})
    },
    handleSelectionChange(val) {
      this.ids = val.map(function(item, index) {
        return item.id
      })
    }
    /* objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 10) {
        return {
          rowspan: 1,
          colspan: 2
        }
      }
    },*/
  }
}
</script>

<style lang="scss" scoped>
</style>
