<template>
  <div class="detail-wrapper">
    <page-title :title="id ? (form.activityStatus&&form.activityStatus.code == 'NOT_START' ? '编辑满减':'查看满减') : '新增满减'">
      <template slot="icon">
        <span :class="{'status-box0': form.activityStatus.code == 'NOT_START', 'status-box1': form.activityStatus.code == 'PROCEED', 'status-box2': form.activityStatus.code == 'OBSOLETE', 'status-box3': form.activityStatus.code == 'FINISHED'}" v-if="this.form.activityStatus"></span>
      </template>
      <template>
        <template v-if="!id || (form.activityStatus&&form.activityStatus.code == 'NOT_START')">
          <el-button type="primary" @click="submit">保存</el-button>
        </template>
        <template v-if="form.activityStatus&&form.activityStatus.code == 'PROCEED'">
          <el-button @click="handleState">作废</el-button>
        </template>
      </template>
    </page-title>
    <el-form label-width="120px" ref="ruleForm" :model="form" :rules="rules">
      <div class="detail-items" v-loading="loading">
        <page-module-card title="基础信息">
          <el-form-item
            label="活动名称："
            prop="activityName"
            :rules="[{ required: true, message: '请输入活动名称' }]"
            style="width:370px;"
          >
            <el-input v-model="form.activityName" placeholder="请输入活动名称，最多20个字" :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'"></el-input>
          </el-form-item>
          <el-form-item
            label="活动时间："
            prop="during"
            :rules="[{ required: true, message: '请选择活动时间', trigger: 'blur' }]">
            <el-date-picker
              type="datetimerange"
              range-separator="至"
              v-model="form.during"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']">
              :disabled="form.activityStatus&&form.activityStatus.code !== 'NOT_START'">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动商品：">
            <el-radio-group v-model="form.productRangeType" :disabled="form.activityStatus && form.activityStatus.code !== 'NOT_START'">
              <div class="block-radio block-radio-top">
                <el-radio label="ALL">全部商品可用</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="PART">
                  指定商品可用
                </el-radio>
              </div>
            </el-radio-group>
            <div v-if="form.productRangeType==='PART'&&(!id || (form.activityStatus&&form.activityStatus.code == 'NOT_START'))">
              <el-button type="primary" @click="openProducts()">选择商品</el-button>
            </div>
            <div v-if="form.productRangeType==='PART'&&indexResultsTable.length > 0" v-loading="loading">
              <el-table
                style="margin-top:15px;"
                :data="indexResultsTable"
                border
                class="product-table"
              >
                <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
                <el-table-column label="主图" align="center" width="80" class-name="img-cell">
                  <template slot-scope="{row}">
                    <img :src="splitString(row.pictIdS)" width="50px" v-if="row.pictIdS">
                    <img :src="pictImg" width="50px" v-if="row.pictIdS == null || row.pictIdS == ''">
                  </template>
                </el-table-column>
                <el-table-column label="商品编码" prop="productCode" width="200px"></el-table-column>
                <el-table-column label="商品名称" prop="productName" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="规格" prop="spec" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="生产厂家" prop="manufacturer" show-overflow-tooltip></el-table-column>
                <el-table-column label="销售价" prop="salePrice" width="100"/>
                <el-table-column label="成本价" prop="costPrice" width="100" />
                <el-table-column label="库存" prop="stockQuantity" width="100" />
                <el-table-column
                  label="操作"
                  prop="stockQuantity"
                  width="100"
                  align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="deleteRowGoods(scope)" :disabled="(form.activityStatus && form.activityStatus.code !== 'NOT_START')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container" >
                <div class="page-row-left">
                  <div class="page-row-leftBlock">
                        <span class="selectBlock">
                          <img src="@/assets/imgs/coupon/<EMAIL>" />
                        </span>
                    已选商品 {{form.fullReduceProductRelSaveDTOList.length}}
                  </div>
                </div>
                <div class="page-row-right">
                  <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-size="pagesize"
                    background
                    layout="prev, pager, next, jumper"
                    :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </el-form-item>
        </page-module-card>
        <page-module-card title="活动规则">
          <el-form-item label="使用门槛：" prop="couponThreshold">
            订单满<el-input v-model="form.couponThreshold" style="width: 100px;margin: 0 10px;" :disabled="form.activityStatus && form.activityStatus.code !== 'NOT_START'"/>元

          </el-form-item>
          <el-form-item label="优惠内容：" prop="discount">
            <el-checkbox :checked="true" :disabled="true">订单金额优惠</el-checkbox><br>
            减 <el-input v-model="form.discount" style="width: 100px;margin: 0 10px;" :disabled="form.activityStatus && form.activityStatus.code !== 'NOT_START'"/>元
          </el-form-item>
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{  required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType" :disabled="form.activityStatus && form.activityStatus.code !== 'NOT_START'">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                </el-radio>
                <el-checkbox-group style="margin-top: 10px;" v-model="form.merchantTypeIds" v-if="merchantList.length > 0 && form.limitObjectType === 'CUSTOMER_TYPE' ">
                  <el-checkbox
                    v-for="(item) in merchantList"
                    :key="item.id"
                    :label="item.id">
                    {{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>
                <div style="margin: 15px 0;" v-if="form.limitObjectType == 'CUSTOMER_GROUP'">
                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>
                </div>
                <el-table
                  v-if="groupTableData && form.limitObjectType == 'CUSTOMER_GROUP' "
                  :data="groupTableData"
                  border
                >
                  <el-table-column
                    prop="name"
                    label="客户分组"
                    width="234" />
                  <el-table-column
                    prop="customerNumber"
                    label="客户数量"
                    width="120" />
                  <el-table-column
                    label="操作"
                    width="52">
                    <template slot-scope="scope">
                      <el-button @click="deleteRow(scope.$index)" type="text">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
    <products-modal ref="products-modal" @change="handleAddProducts" :checkList="form.fullReduceProductRelSaveDTOList"/>
    <add-group :visible="showAdd" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser" :select-data="groupTableData" />
  </div>
</template>

<script>
  import request from '@/utils/request'
  import addGroup from '../components/addGroup.vue'
  import ProductsModal from '../components/productList'
  import { merchantGroupListNew } from "@/api/group";
  import { postGoodsList,detail,updatePromotionPackageState } from "@/api/fullReduction"
  import productImg from "@/assets/product.png";

  export default {
    components: {
      ProductsModal,
      addGroup,
    },
    data() {
      var validateOld = (rule, value, callback) => {
        if (Number(value)<0) {
          return callback(new Error('优惠门槛必须大于0'));
        } else {
          callback()
        }
      }
      var validateDiscount = (rule, value, callback) => {
        if (Number(value)<0) {
          return callback(new Error('优惠内容必须大于0'));
        } else {
          callback()
        }
      }
      return {
        pictImg: productImg,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        },
        id: '',
        loading: false,
        showAdd: false,
        saleMerchantId: '', //经销商id
        active: 0,
        merchantList: [],   // 客户类型列表
        groupTableData: [], // 已选分组数据
        form: {
          productRangeType:'ALL',
          during: '',
          activityName: '',
          couponThreshold: '',//优惠门槛
          discount: '',
          limitObjectType: 'NONE',//指定人类型 NONE,CUSTOMER_TYPE,CUSTOMER_GROUP,CUSTOMER,UN_CUSTOMER
          merchantTypeIds: [], //客户类型
          fullReduceProductRelSaveDTOList: [],
          limitLoop: 'N',//是否允许循环满减\n#BaseCommonEnum{N:0,否;Y:1,是;},可用值:N,Y
          merchantGroupIds: [],//分组集合
        },
        rules: {
          couponThreshold: [{required: true,message: '优惠门槛不能为空',trigger: 'blur'},{ validator: validateOld, trigger: 'blur'}],
          discount: [{required: true,message: '优惠内容不能为空',trigger: 'blur'},{ validator: validateDiscount,trigger: 'blur'}]
        },
        fullReduceProductRelSaveDTOList: [],
        total: 0,
        goodsList: [],
        currentPage:1, //初始页
        pagesize: 10,
        indexResultsTable:[],
        selectionArr: [],
        discounts: '',
        subtrahend: '',
        notSearchId: '',
        timer: null
      }
    },
    created() {
      this.id = this.$route.query.id;
      this.getMerchantType()
      if (this.id) {
        this.getDetail()
      }
    },
    methods: {
      //分页
      handleSizeChange:function(size){
        this.pagesize = size;
        this.getResultsTable();
      },
      handleCurrentChange:function(page){
        this.currentPage = page;
        this.getResultsTable();
      },
      //前端自己分页
      getResultsTable:function() {
        // es6过滤得到满足搜索条件的展示数据list
        let self=this
        let list = this.form.fullReduceProductRelSaveDTOList
        //表格渲染的数据  indexResultsTable:[],
        this.indexResultsTable = list.filter((item, index) =>
          index < this.currentPage * this.pagesize && index >= this.pagesize * (this.currentPage - 1)
        )//根据页数显示相应的内容
        this.total = list.length;

      },
      //作废
      async handleState() {
        const data = await updatePromotionPackageState(this.id)
        if(data.code === 0) {
          this.$message.success('该满减活动已作废！')
          this.getDetail()
        }
      },
      //打开商品
      openProducts() {
        this.$refs['products-modal'].open()
       // this.form.fullReduceProductRelSaveDTOList = this.goodsList

      },
      // 获取详情
      async getDetail() {
        this.loading = true
        if (this.id) {
          let {data} = await detail(this.id)
          this.form = data
          this.$nextTick(() => {
            this.$set(this.form, 'during', [data.activityStartTime, data.activityEndTime]);
          });
          this.form.limitObjectType = data.limitObjectType.code;
          this.form.productRangeType = data.productRangeType.code
          if (data.limitLoop) {
            this.form.limitLoop = data.limitLoop.code
          }
          this.form.fullReduceProductRelSaveDTOList = data.fullReduceProductRelSaveDTOList
          this.goodsList = data.fullReduceProductRelSaveDTOList
          if (data.merchantTypeIds===null) {
            this.form.merchantTypeIds = []
          }
          if (data.merchantGroupIds===null) {
            this.form.merchantGroupIds = []
          }
          if (data.merchantGroupIds) {
            this.getGroupList();
          }
          if (this.goodsList.length > 0) {
            this.getCheckGoodsList()
          }
          if (data.merchantTypeIds) {

          }
        }
      },
      // 获取已有客户分组列表
      async getGroupList() {
        this.loading = true;
        const query = {
          model: {}
        }
        const {data} = await merchantGroupListNew(query);
        this.loading = false;
        this.groupTableData = data.records.filter((items) => {
          if (this.form.merchantGroupIds.includes(items.id)) {
            return {
              items
            };
          }
        })
      },
      // 获取已有的产品列表
      async getCheckGoodsList(ids) {
        const formData = new FormData()
        let idArr = ids ? ids : this.form.fullReduceProductRelSaveDTOList.map((items) => items.productId)
        formData.append('ids', idArr.toString())
        let {data} = await postGoodsList(formData)
        this.form.fullReduceProductRelSaveDTOList = []
        this.form.fullReduceProductRelSaveDTOList = data.filter((items,index) => {
          if (idArr.includes(items.id)) {
            return items;
          }
        })
        this.loading = false
        this.getResultsTable()
      },
      // 清空表单
      resetForm() {
        this.$refs['ruleForm'].resetFields();
      },
      // 提交表单
      submit() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            if (this.form.productRangeType==='PART'&&this.form.fullReduceProductRelSaveDTOList.length === 0) {
              this.$message.warning('请选择活动商品！')
              return false
            }
            if (this.form.limitObjectType === 'CUSTOMER_TYPE'&&this.form.merchantTypeIds.length===0) {
              this.$message.warning('请点击选择相关的客户类型')
              return false
            } else if(this.form.limitObjectType === 'CUSTOMER_GROUP'&&this.form.merchantGroupIds.length===0) {
              this.$message.warning('请点击选择相关的客户分组')
              return false
            }

            if (this.form.productRangeType==='PART'&&this.form.fullReduceProductRelSaveDTOList.length > 0) {
              this.form.fullReduceProductRelSaveDTOList = this.form.fullReduceProductRelSaveDTOList.map((item) => {
                return {
                  fullReduceId: this.id,
                  productId: item.id,
                  manufacturer: item.manufacturer,
                  salesVolume: item.salesVolume,
                  price: item.salePrice,
                  productName: item.productName
                }
              })
            } else {
              this.form.fullReduceProductRelSaveDTOList = []
            }
            // 选择指定客户类型
            if (this.form.limitObjectType === 'CUSTOMER_TYPE') {
              this.form.merchantGroupIds = []
            }
            if(this.form.limitObjectType === 'CUSTOMER_GROUP'||this.form.limitObjectType === 'NONE') {
              this.form.merchantTypeIds = []
            }
            // 是否有id
            if (this.id) {
              this.form.id = this.id
            } else {
              delete this.form.id
            }
            this.loading = true;
            let param = {
              ...this.form,
              activityStartTime: this.form.during[0],
              activityEndTime: this.form.during[1],
            }
            delete param.during
            let method = this.id ? request.put('product/admin/fullReduce', param) : request.post('product/admin/fullReduce', param)
            method.then((res) => {
              if (res.code == 0) {
                this.loading = false;
                this.$message.success('保存成功')
                this.timer = setTimeout(() => {
                  this.$router.push({
                    path: '/promotion/fullReduce/list'
                  })
                }, 500)
              } else {
                this.loading = false;
                this.$message.error(res.msg)
              }
            }).catch(() => {
              this.loading = false;
            })

          }
        });
      },
      // 删除选中客户组
      deleteRow(i) {
        this.groupTableData.splice(i, 1)
      },
      async deleteRowGoods(scope) {
        let index = this.goodsList.findIndex((item)=>item.id===scope.row.id)
        this.form.fullReduceProductRelSaveDTOList.splice(index, 1)
        if (JSON.parse(JSON.stringify(this.indexResultsTable)).length === 1&&JSON.parse(JSON.stringify(this.form.fullReduceProductRelSaveDTOList)).length > 1) {
          this.currentPage = 1
        }
        this.getResultsTable()
        this.$refs['products-modal'].isSelect = true;
      },
      // 改变客户分组回调
      changeAddUser(data) {
        this.groupTableData = data;
        this.form.merchantGroupIds = data.map((items) => items.id)
        this.showAdd = false
      },
      // 选择商品后回调
      async handleAddProducts(list) {
        this.getCheckGoodsList(list)
      },
      async postCouponList (params) {
        let listQuery = {
          model: {
            limitTimeDiscountId: this.id
          }

        }
        Object.assign(listQuery, params)
        const data =  await getCheckList(listQuery)
        this.goodsList = data.records

        return data
      },
      // 获取客户类型
      async getMerchantType() {
        const {data} = await request.get('merchant/admin/merchantType', {})
        this.merchantList = data
      },
      splitString(val) {
        return String(val).split(',')[0]
      }
    },
    beforeDestroy() {
      clearTimeout(this.timer)
    }
  }
</script>

<style lang="scss">
  .product-table {
    .el-input-number.is-controls-right .el-input__inner {
      padding-right: 15px;
    }
  }
  .limit-form .el-form-item {
    margin-bottom: 0;
  }
  .detail-wrapper {
    .el-pager li {
      width: 0;
    }
    .page-row-left {
      float: left;
    }

    .page-row-leftBlock{
      display: flex;
      .selectBlock{
        display: block;
        width: 14px;
        height: 14px;
        margin-right: 8px;
        img{
          width: 14px;
        }
      }
    }

    .page-row-right {
      float: right;
    }
    .status-box0 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box1 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box2 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/Icon_Revok.png') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box3 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    background: #f7f7f8;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 20px;
    color: #8c939d;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .detail-item{
    border: none !important;
  }
  .detail-tit{
    display: flex;
    align-items: center;
    margin: 0;
  }
  .inline-input{
    width: 80px;
    margin: 0 10px;
  }
  .inline-item{
    display: inline-block;
  }
  .block-radio{
    margin-bottom: 16px;
    &-top{
      margin-top: 11px;
    }
    &-none{
      margin: 0;
    }
  }
  .detail{
    &-header{
      // width: 100%;
      margin: 0 12px;
      padding: 19px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ddd;
      p{
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  .tips{
    color: #999999;
    margin: 0;
    &-btn{
      border: none;
      margin: 0;
      padding: 0;
    }
  }
  .no-button {
    .el-input-number__decrease,.el-input-number__increase {
      display: none;
    }
  }
</style>
