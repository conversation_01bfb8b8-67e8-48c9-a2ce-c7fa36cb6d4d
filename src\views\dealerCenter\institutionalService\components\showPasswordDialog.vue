<template>
  <el-dialog title="查看密码" :visible.sync="visible" append-to-body :close-on-click-modal="false" width="600px">
    <el-form ref="form" :model="form" label-width="98px">
      <el-form-item label="姓名：">
        {{form.name}}
      </el-form-item>
      <el-form-item label="登录账户：">
        {{form.account}}
      </el-form-item>
      <el-form-item label="注册手机：">
        {{form.phone}}
      </el-form-item>
      <el-form-item label="登录密码：">
        {{form.password}}
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <span>
        <el-button @click="hide">取消</el-button>
        <el-button type="primary" @click="hide">确定</el-button>
      </span>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'seePassword',
  data() {
    return {
      visible: false,
      form: {}
    }
  },
  methods: {
    show(row) {
      this.form = row
      this.visible = true
    },
    hide() {
      this.visible = false
    }
  }
}
</script>

<style></style>
