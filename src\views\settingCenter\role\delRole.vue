<template>
  <el-dialog
    title="删除分组"
    :visible.sync="delGroupVisible"
    width="25%"
    @close="handleClose"
  >
    <el-dialog
      width="30%"
      title="确定删除？"
      :visible.sync="innerVisible"
      append-to-body>
      <p>确定删除"{{data.name}}"分组！</p>
      <span slot="footer" class="dialog-footer">
      <el-button @click="innerVisible = false">取 消</el-button>
      <el-button type="primary" @click="delHandle">确 定</el-button>
    </span>
    </el-dialog>
    <p>请输入删除分组牌名称，删除分组后，分组所绑定的客户自动解除绑定关系，请谨慎操作</p>
    <el-input v-model="name" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="delGroupVisible = false">取 消</el-button>
      <el-button type="primary" @click="delSubmit">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
  import { deleteGroup } from "@/api/group";
  export default {
    name: "delGroup",
    props: ['delVisible','data'],
    data() {
      return {
        name: '',
        innerVisible: false,
        ids: [],
        delGroupVisible: false
      }
    },
    methods: {
      handleClose(){
        // 子组件调用父组件方法，并传递参数
        this.$emit('changeShow','false')
      },
      delSubmit() {
        if(this.name === this.data.name) {
          this.innerVisible = true
        } else {
          this.name = ''
          this.$message.warning('请输入正确的分组名称！')
        }
      },
      async delHandle() {
        await deleteGroup([this.data.id])
        this.$message.success('已删除')
        this.innerVisible = false
        this.delVisible = false
      }
    },
    watch: {
      delVisible() {
        this.delGroupVisible = this.delVisible
      }
    }
  }
</script>

<style scoped>

</style>
