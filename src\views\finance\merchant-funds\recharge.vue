<template>
  <div class="tab_bg">
    <tabs-layout
      v-model="activeName"
      :tabs="[{ name: '充值', value: '1' }, { name: '充值记录', value: '2' }]"
      @change="handleClick"
    />
    <template v-if="activeName === '1'">
      <el-form v-if="checkPermission(['admin','deposit:view'])" ref="rechargeForm" rules="rechargeRule" model="rechargeForm" label-width="120px">
        <el-form-item label="账户可用余额：">
          <span>{{balance|getDecimals}}</span>
        </el-form-item>
        <el-form-item label="收款账户：">
          <span>商家账户余额</span>
        </el-form-item>
        <el-form-item label="充值金额：" prop="amount">
          <el-input v-model="rechargeForm.amount" placeholder="请输入充值金额" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary">确认充值</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template>
      <table-pager v-if="checkPermission(['admin','deposit-record:view'])" ref="todoTable" :options="tableTitle" :remote-method="loadPay" :isNeedButton="false" :data.sync="tablePay">
        <template slot="amount">
          <el-table-column label="金额（元）" width="100">
            <slot slot-scope="{row}">
              {{row.amount|getDecimals}}
            </slot>
          </el-table-column>
        </template>
      </table-pager>
    </template>
    <cashier-dialog :cashierFlag="cashierVisible" @changeShow="changeShow" :data="cashier"></cashier-dialog>
  </div>
</template>

<script>
  import {merchantsEarnestMoneyPage,merchantsEarnestMoneyList,refundDeposit,financeCapitalAccount } from '@/api/finance'
  const TableColumns = [
    { label: "业务类型", name: "type.desc",prop: "type.desc",width: "120"},
    { label: "业务单号", name: "id",prop: "id",width: "170"},
    { label: "银行流水号", name: "", prop:"",width: "190" },
    { label: "付款凭证", name: "paymentStatus.desc", prop:"paymentStatus.desc" },
    { label: "支付时间", name: "remarks", prop:"remarks" },
    { label: "保证金名称", name: "name", prop:"name",width: '150' },
    { label: "业务单状态", name: "businessStatus.desc", prop:"businessStatus.desc",width: '120' },
    { label: "申请人", name: "applicantUserName", prop:"applicantUserName" },
    { label: "金额（元）", name: "amount", prop:"amount",slot: 'true' },
    { label: "制单人", name: "createUser", prop:"createUser",width: '150' },
    { label: "制单时间", name: "createTime", prop:"createTime",width: '170' },
  ]
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }
  import checkPermission from "@/utils/permission";
  import CashierDialog from './cashier-dialog'
  export default {
    name: "deposit",
    components: {CashierDialog},
    data() {
      return {
        tableTitle: TableColumnList,
        activeName: '1',
        tablePay: [],
        loading: true,
        cashierVisible: false,
        cashier: '',
        balance:0,
        rechargeForm: {
          amount: ''
        },
        rechargeRule: {
          amount: [{required: true,message: '请输入充值金额',trigger: 'blur'},{min:0,message: '充值不能小于0',trigger: 'blur'}]
        }

      }
    },
    mounted() {
      this.getBalance()
    },
    methods: {
      checkPermission,
      async getBalance() {
        const {data} = await financeCapitalAccount()
        this.balance = data.amount
      },
      openCaisher(row) {
        this.cashierVisible=true
        this.cashier = row
      },
      //申请退还
      refund(row) {
        refundDeposit({id:row.id}).then(res=>{
          if(res.code===0) {
            this.$message.success('申请成功！')
            this.load()
          }
        })
      },
      //取消
      cancel(row) {

      },
      changeShow(data) {
        if (data === 'false') {
          this.cashierVisible = false
        } else {
          this.cashierVisible = true
        }
      },
      async loadPay(params) {
        const listQuery = {
          model: {}}
        Object.assign(listQuery, params)
        this.loading = false
        return await merchantsEarnestMoneyPage(listQuery)
      },
      handleClick(tab, index) {
        if(tab.value ==='2') {
          this.handleRefresh({
            page: 1,
            pageSize: 10
          })
        }

      },
      handleRefresh(pageParams) {
        this.$nextTick(() => {
          this.$refs.todoTable.doRefresh(pageParams)
        })
      },

    }
  }
</script>

<style lang="scss">
</style>
