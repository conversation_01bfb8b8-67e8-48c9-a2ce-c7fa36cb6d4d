<template>
    <el-dialog append-to-body @close="onClose" :visible.sync="show_" :title="title" width="625px">
        <el-transfer v-model='values' :props='props' :titles='titles' :data='data'></el-transfer>
        <template slot="footer">
            <el-button @click="show_ = false">取消</el-button>
            <el-button type="primary" @click="onOk">确定</el-button>
        </template>
    </el-dialog>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: '设置显示列表'
        },
        titles: {
            type: Array,
            default: () => ["显示字段项", "隐藏字段项"]
        },
        data: {
            type: Array,
            default: () => []
        },
        props: {
            type: Object,
            default: function () {
                return
            }
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            values: [],
            show_: false
        }
    },
    watch: {
        show: {
            handler(val) {
                console.log('handler', this.show, val)
                this.show_ = val;
            },
            immediate: true
        }
    },
    methods: {
        onClose() {
            this.$emit('update:visible', this.visible_);
        },
        onOk() {
            const fields = this.data.filter((v, i) => {
                return !this.values.includes(i)
            })
            this.$emit('update:show', false)
            this.$emit('change', fields)
        }
    }
}
</script>