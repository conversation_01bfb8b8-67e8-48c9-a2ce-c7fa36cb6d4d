import request from '@/utils/request'
import requestAxios from '@/utils/requestAxios'

export  function list(data) {
  return requestAxios({
    url: '/merchant/admin/enterprise/page',
    method: 'post',
    data
  })
}

export function areas(data) {
  return requestAxios({
    url: '/authority/area/linkage',
    method: 'get',
    params: data
  })
}

// 企业变更审核分页查询
export function explainPage(data) {
  return requestAxios({
    url:'/merchant/admin/purMerchant/explain/page',
    method: 'post',
    data
  })
}

// 企业变更审核
export function explainAudit(data) {
  return requestAxios({
    url:'/merchant/admin/purMerchant/explain/audit',
    method: 'post',
    data
  })
}
