<template>
  <el-dialog title="批量充值扣减" :visible.sync="visible" width="500px">
    <div class="top">
      <div style="display: flex;justify-content: center;">
        <el-upload drag action="#" :file-list="fileList" ref="upload" :multiple="false" :http-request="requestUpload" :before-upload="beforeUpload">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖拽到此区域或选择文件上传</div>
        </el-upload>
      </div>
    </div>
    <div class="center" style="line-height: 2;">
      <div>1、根据姓名导入充值/扣减金额，若存在重复姓名，则该姓名不做处理。</div>
      <div>2、金额为正数视为充值，金额为负数视为扣减。</div>
      <div>
        <span>3、仅支持xlsx格式文件，文件大小1M以内。</span>
        <el-button type="text" class="download" @click="downloadFile">下载模板</el-button>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
    </span>
    <ImportTipDialog ref="importTipDialog" />
  </el-dialog>
</template>

<script>
import { batchImportCash } from "@/api/retailStore";
import ImportTipDialog from "@/components/importTipDialog.vue";

export default {
  name: 'batchRechargeAccountDialog',
  components: {
    ImportTipDialog
  },
  props: {
    importType: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      fileList: []
    }
  },
  methods: {
    show() {
      this.visible = true
      this.fileList = []
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    hide() {
      this.visible = false
    },
    requestUpload(file) {},
    beforeUpload(file) {
      this.fileList = [file]
    },
    submit() {
      if (this.fileList.length === 0) {
        this.$message.error("请上传文件")
        return
      }
      const formData = new FormData()
      formData.append('file', this.fileList[0])
      formData.append('importType', this.importType)
      this.loading = true
      batchImportCash(formData).then(_ => {
        this.$message.success("添加成功")
        this.$refs.importTipDialog.show()
        setTimeout(() => {
          this.hide()
        }, 1000)
      }).finally(() => {
        this.loading = false
      })
    },
    downloadFile() {
      let a = document.createElement("a")
      const fileName = '现金充值扣减导入模板（商家版）.xlsx'
      a.download = fileName
      a.href = `${process.env.BASE_URL}${fileName}?response-content-type=application/octet-stream`
      a.click()
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  position: relative;
  .download {
    position: absolute;
    right: 4px;
    top: 0px;
  }
}
.center {
  border-top: 1px solid #ddd;
  padding-top: 10px;
  margin-top: 10px;
}
</style>