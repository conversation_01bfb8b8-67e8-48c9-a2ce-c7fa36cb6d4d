<template>
  <div class="list—index">
    <!--搜索Form-->
    <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
      <im-search-pad-item prop="keyword">
        <el-input v-model.trim="model.keyword" placeholder="请输入姓名/手机" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <!--Tabs布局-->
      <tabs-layout ref="tabs-layout" v-model="model.approvalStatus" :tabs="tabs" @change="handleChangeTab">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button v-if="checkPermission(['admin', 'sale-saas-salesman:audit', 'sale-platform-salesman:audit']) && model.approvalStatus === '' || model.approvalStatus === 'PENDING' || model.approvalStatus === 'REJECTED'" :disabled="multipleSelection.length === 0" @click="handlePass">批量审核</el-button>
          <el-button v-if="model.approvalStatus === 'PENDING' && checkPermission(['admin', 'sale-saas-salesman:overruled', 'sale-platform-salesman:overruled'])" :disabled="multipleSelection.length === 0" @click="handleReject">批量驳回</el-button>
          <el-button @click="reload">刷新</el-button>
          <el-button v-if="checkPermission(['admin', 'sale-saas-salesman:add', 'sale-platform-salesman:add'])" @click="handleAdd" type="primary">+ 新增业务员</el-button>
        </template>
      </tabs-layout>
      <!-- 分页table -->
      <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
        :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" :pageSize="pageSize"
        :operation-width="150" :isNeedButton="true">
        <el-table-column label="所在区域" min-width="180" slot="area">
          <template slot-scope="{ row }">
            <div>{{ row.provinceName }} - {{ row.cityName }} - {{ row.districtName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="服务类型" width="80" slot="serviceType">
          <slot slot-scope="{row}">
            <div>{{ row.serviceType && row.serviceType.desc }}</div>
          </slot>
        </el-table-column>
        <el-table-column label="审核状态" width="140" slot="approvalStatus">
          <slot slot-scope="{row}">
            <div>{{ row.approvalStatus.desc }}</div>
          </slot>
        </el-table-column>
        <el-table-column label="客户数量" width="140" slot="purCount">
          <slot slot-scope="{row}">
            <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-salesman:clientRelation', 'sale-platform-salesman:clientRelation'])" @click="openCustomer(row.id, row.name)">{{ row.purCount }}</el-button>
            <span v-else>{{ row.purCount }}</span>
          </slot>
        </el-table-column>
        <el-table-column label="任职公司" slot="inOfficeCompanyName">
          <slot slot-scope="{row}">
            <span>{{ row.inOfficeCompanyName }}</span>
          </slot>
        </el-table-column>
        <div slot-scope="scope" align="center">
          <el-row class="table-edit-row">
            <span class="table-edit-row-item">
              <el-button type="text" @click="handleEdit(scope.row.id,'edit')" v-if="checkPermission(['admin', 'sale-saas-salesman:edit', 'sale-platform-salesman:edit'])">编辑</el-button>
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-salesman:detail', 'sale-platform-salesman:detail'])"
                @click="handleEdit(scope.row.id,'see')">
                查看
                </el-button>
              <el-button type="text" v-if="checkPermission(['admin', 'sale-saas-salesman:del', 'sale-platform-salesman:del'])"
                @click="handleDelete(scope.row)">
                删除</el-button>

            </span>
          </el-row>
        </div>
      </table-pager>
    </div>
    <!-- 客户列表弹窗 -->
    <CustomersDialog :visible.sync="isCustomersDialog" :currentId="currentId" :salemanName="salemanName"/>
    <!-- 新增业务员 -->
    <SalesmanVerificationDialog  :visible.sync="isSalesmanVerification" @onVerification="handleGoToUrl" />
  </div>
</template>

<script>
import SalesmanVerificationDialog from './components/SalesmanVerificationDialog.vue'
  const TableColumns = [{
      label: "业务员编码",
      name: "code",
      prop: "code",
      width: "150"
    },
    {
      label: "真实姓名",
      name: "name",
      prop: "name",
      width: "170"
    },
    {
      label: "联系方式",
      name: "contactWay",
      prop: "contactWay",
      width: "130"
    },
    {
      label: "所在区域",
      name: "area",
      prop: 'area',
      width: "150",
      slot: true
    },
    {
      label: "注册手机号",
      name: "mobile",
      prop: "mobile",
      width: "130"
    },
    {
      label: "服务类型",
      name: "serviceType",
      prop: "serviceType",
      width: "70",
      slot: true
    },
    {
      label: "角色",
      name: "orgRoleName",
      prop: "orgRoleName",
      width: "170"
    },
    {
      label: "服务客户数",
      name: "purCount",
      prop: 'purCount',
      width: "140",
      slot: true
    },
    {
      label: "任职公司",
      name: "inOfficeCompanyName",
      prop: 'inOfficeCompanyName',
      slot: true
    },
    {
      label: "审核状态",
      name: "approvalStatus",
      prop: 'approvalStatus',
      width: "140",
      slot: true
    },
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  import {
    salesmanPage,
    batchAudit,
    batchReject,
    countByApprovalStatus,
    deleteSalesmanPort
  } from '@/api/salemanCenter/index' // TODO 替换成对应用的列表api
  import {
    areas
  } from "@/api/enterprise";
  import checkPermission from "@/utils/permission";
  import delElButton from "@/components/eyaolink/delElButton";
  import CustomersDialog from './components/CustomersDialog.vue'
  export default {
    name: 'businessAdministrator',
    components: {
      delElButton,
      CustomersDialog,
      SalesmanVerificationDialog
    },
    props: {},
    data() {
      return {
        props: {
          lazy: true,
          checkStrictly: true,
          async lazyLoad(node, resolve) {
            const {
              level
            } = node;
            let id = node.data ? node.data.id : "";
            let res = await areas({
              parentId: id
            });
            let list = res.data;
            list.forEach((item) => {
              item.value = item.id;
              item.leaf = level >= 2;
            });
            resolve(list);
          },
        },
        cityValue: [],
        isExpand: false,
        model: {
          keyword: '',
          approvalStatus:''
        },
        currentId:'',
        delText: '您确定删除此业务员数据吗？',
        salesmanForm: {
          phone: '',
        },
        salesmanOne: true,
        salesmanTwo: false,
        salesmanThree: false,
        infoData: { // 第二个弹窗的内容
          name: '',
          mobile: '',
          approvalStatus: 'ACCEPTED'
        },
        dialogStatus: false, // 客户列表弹窗
        multipleSelection: [],
        search: '',
        tableData: [],
        pageSize: 10,
        tableColumns: TableColumnList,
        tabNum: {
          accepted: 0,
          pending: 0,
          rejected: 0,
        },
        isCustomersDialog: false,
        salemanName: '',
        isSalesmanVerification: false
      }
    },
    computed: {
      tabs() {
        return [{
            name: '全部' + ((this.tabNum.all) > 0 ? '(' + this.tabNum.all + ')' : ''),
            value: '',
            hide: !checkPermission(['admin', 'sale-saas-salesman:allView','sale-platform-salesman:allView'])
          },{
            name: '已通过' + ((this.tabNum.accepted) > 0 ? '(' + this.tabNum.accepted + ')' : ''),
            value: 'ACCEPTED',
            hide: !checkPermission(['admin', 'sale-saas-salesman:acceptedView','sale-platform-salesman:acceptedView'])
          },
          {
            name: '待审核' + ((this.tabNum.pending) > 0 ? '(' + this.tabNum.pending + ')' : ''),
            value: 'PENDING',
            hide: !checkPermission(['admin', 'sale-saas-salesman:pendingAuditView','sale-platform-salesman:pendingAuditView'])
          },
          {
            name: '已驳回' + ((this.tabNum.rejected) > 0 ? '(' + this.tabNum.rejected + ')' : ''),
            value: 'REJECTED',
            hide: !checkPermission(['admin', 'sale-saas-salesman:rejectedView','sale-platform-salesman:rejectedView'])
          }
        ]
      }
    },
    mounted() {
      this.getTabNum();
    },
    methods: {
      /**
       * @description 删除业务员
       */
      handleDelete(row){
        this.$confirm('确定要删除该业务员吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteSalesmanPort({ salesmanId: row.id }).then(res => {
            if(res.code === 0) {
              this.$message.success('删除成功!');
              this.searchLoad()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      },
      checkPermission,
      async load(params) {
        let listQuery = {
          model: {
            ...this.model,
          }
        };
        // listQuery.model.approvalStatus = this.model.approvalStatus === 'All' ? '' : this.model.approvalStatus
        Object.assign(listQuery, params);
        return await salesmanPage(listQuery)
      },
      getTabNum(){
        countByApprovalStatus({ keyword:this.model.keyword }).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.tabNum = res.data;
          }
        })
      },
      cityChange(e) {
        this.model.provinceId = e[0];
        this.model.cityId = e[1];
        this.model.countyId = e[2];
      },
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      },
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        });

      },
      // tab切换
      handleChangeTab(tab) {
        this.model.approvalStatus = tab.value;
        this.multipleSelection = [];
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      reload() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams);
        this.getTabNum();
      },
      // 批量驳回
      handleReject() {
        let list = [];
        this.multipleSelection.forEach(item=>{
          list.push(item.id);
        })
        this.$confirm('您确定批量驳回这些业务员吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          batchReject({ salesmanIds: [...list], rejectionReason: '' }).then(res => {
            if(res.code === 0) {
              this.$message.success('驳回成功！');
              this.searchLoad()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消驳回'
          });
        });
      },
      // 批量通过
      handlePass() {
        let list = [];
        this.multipleSelection.forEach(item=>{
          list.push(item.id);
        })
        this.$confirm('您确定批量审核通过这些业务员吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          batchAudit({ salesmanIds: [...list], rejectionReason: '' }).then(res => {
            if(res.code === 0) {
              this.$message.success('审批成功！');
              this.searchLoad()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消审核'
          });
        });
      },
      closeDialogFun() {
        this.dialogStatus = false;
      },
      // 新增业务员
      handleAdd() {
        this.isSalesmanVerification = true;
      },
      // 打开客户列表弹窗
      openCustomer(id, name) {
        this.currentId = id;
        this.salemanName = name;
        this.isCustomersDialog = true;
      },
      dealRole(type) {
        let text = "";
        switch (type) {
          case "BOSS":
            text = "老板"
            break;
          case "LEADER":
            text = "领导"
            break;
          case "EMPLOYEE":
            text = "员工"
            break;
        }
        return text
      },
      // 编辑或者查看
      handleEdit(id, type) {
        this.$router.push({
          path: '/salesmanCenter/salesmanManage/detail',
          query: {
            id,
            type,
            isADD: "editPage"
          }
        })
      },
      /**
       * @description 新增跳转
       * <AUTHOR>
       */
      handleGoToUrl(id,mobile) {
        if(id) {
          this.$router.push({
            path: '/salesmanCenter/salesmanManage/detail',
            query: {
              id,
              type: 'edit',
              isADD: "addPage"
            }
          })
        }else {
          this.$router.push({
            path: '/salesmanCenter/salesmanManage/detail',
            query: {
              type: 'add',
              mobile,
              isADD: "addPage"
            }
          })
        }
      }
    }
  }

</script>

<style lang="scss" scoped>
  .item_customer {
    display: flex;
    align-content: center;
    margin-top: 15px;
  }

</style>
