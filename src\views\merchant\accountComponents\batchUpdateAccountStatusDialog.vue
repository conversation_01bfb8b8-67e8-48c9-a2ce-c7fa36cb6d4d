<template>
  <el-dialog title="批量启用或冻结" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="90px" >
      <el-form-item label="账户状态:" prop="publishStatus">
        <el-radio-group v-model="form.publishStatus">
          <el-radio label="Y">启用</el-radio>
          <el-radio label="N">冻结</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { batchUpdateCustomerPoint } from '@/api/retailStore'

export default {
  name: 'batchUpdateCustomerPointDialog',
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        idSet: [],
        publishStatus: '',
      },
      rules: {
        publishStatus: [
          { required: true, message: '请选择账户状态', trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    show(idSet) {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.form.idSet = idSet
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        batchUpdateCustomerPoint(this.form).then(() => {
          this.$message.success('批量设置成功')
          this.hide()
          this.$emit('reload')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
