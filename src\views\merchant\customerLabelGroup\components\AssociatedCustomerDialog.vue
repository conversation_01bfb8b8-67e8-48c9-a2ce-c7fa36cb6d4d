<template>
  <div>
    <el-dialog width="70%" title="关联客户" v-bind="$attrs" v-on="$listeners" @opened="handleOpen" destroy-on-close :close-on-click-modal="false" @close="clearData">
      <div class="explain">
        说明：
      </div>
      <p>1、设置”指定区域/客户类型+指定客户+排除客户“时，客户范围=指定区域/客户类型的客户+指定客户-排除客户</p>
      <p>2、仅设置“排除客户”时，客户范围=所有客户-排除客户</p>
      <div class="operation" v-loading="loading">
        <div class="items-content">
          <el-checkbox v-model="model.clientType">指定区域/客户类型
            <el-popover placement="right" width="350" trigger="hover">
              <ul>
                <li>（1）设置“区域+客户类型+不限新客户+不限合伙人+不限月度等级”时，客户范围=符合区域且符合类型的客户，如广东省的单体药店。</li>
                <li>（2）设置“区域+客户类型+限新客户+限合伙人+限月度等级”时，客户范围=符合区域且客户类型且是新客户且是合伙人且是某月度等级的客户，如广东省的单体药店中，即是合伙人又是某等级的新客户。</li>
                <li>（3）设置“区域+客户类型+排除新客户+排除合伙人+排除月度等级”时，客户范围=符合区域且客户类型的客户-新客户-合伙人-满足某月度等级的客户，如广东省的单体药店，不含新客户、合伙人、某等级的客户。</li>
              </ul>
              <i slot="reference" style="color: #76838f" class="el-icon-question"></i>
            </el-popover>
          </el-checkbox>
          <div v-if="model.clientType">
            <el-button class="district" type="primary" size="medium" @click="handleOpenAreaDialog([],'add', 100000)">+ 新增指定区域</el-button>
            <div class="client_item">
              <span>新客户：</span>
              <el-radio-group v-model="firstOrderSetting.firstOrderType.code">
                <el-radio v-for="item in firstOrderTypeList" :label="item.value" :key="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </div>
            <div class="client_item">
              <span>合伙人：</span>
              <el-radio-group v-model="firstOrderSetting.partnerType.code">
                <el-radio v-for="item in partnerTypeList" :label="item.value" :key="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </div>
            <div class="client_item">
              <span>月度等级：</span>
              <el-radio-group v-model="firstOrderSetting.monthlyLevel.code">
                <el-radio v-for="item in monthlyLevelList" :label="item.value" :key="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
              <!-- 指定月度等级 -->
              <div :class="['client_item_checkbox', firstOrderSetting.monthlyLevel.code]" v-if="['SPECIFY', 'EXCLUDE'].includes(firstOrderSetting.monthlyLevel.code)">
                <el-checkbox v-for="(item, index) in firstOrderSetting.levelRanges" :key="index" v-model="item.checked.code" true-label="Y" false-label="N">
                  {{ item.levelName }}
                </el-checkbox>
              </div>
            </div>
            <div>
              <el-form ref="form" :model="form" label-width="80px" label-position="right">
                <el-table :data="form.productForbidDetails" border height="274">
                  <el-table-column label="序号" type="index" width="50"/>
                  <el-table-column label="客户类型" width="200">
                    <template slot-scope="{row}">
                      <el-select v-model="row.customerType" class="multiple-select" clearable collapse-tags
                                 placeholder="请选择"
                      >
                        <el-option v-for="item in tGroups" :key="item.id" :label="item.name" :value="item.id"/>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="区域" show-overflow-tooltip>
                    <template slot-scope="{row, $index}">
                      <el-button type="text" @click="handleOpenAreaDialog(row.districtList,'edit', $index, row.isAllNational)" icon="el-icon-edit"></el-button>
                      <span class="area" v-if="row.isAllNational === 0">
                        {{ row.label && row.label.length > 0 ? row.label : "请选择省份" }}
                      </span>
                      <span class="area" v-if="row.isAllNational === 1">全国</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" fixed="right">
                    <template slot-scope="scope">
                      <el-button type="text" @click="handleDel(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </div>
          </div>
        </div>
        <div class="items-content">
          <el-checkbox v-model="model.specific">指定具体客户
            <el-popover placement="right" width="350" trigger="hover">
              <ul>
                <li>请Excel表导入客户名单。若需调整已导入的客户，请重新导入，最新导入结果将覆盖上次结果。</li>
              </ul>
              <i slot="reference" style="color: #76838f" class="el-icon-question"></i>
            </el-popover>
          </el-checkbox>
          <div class="total-top" v-if="model.specific">
            <div>
              <el-button type="primary" size="medium" @click="handleImport('assign')">+ 导入客户</el-button>
              <span class="check-btn" @click="handleAssignCheck" v-if="assignLen > 0">查看已导入的客户（{{ assignLen }}）</span>
              <el-button type="text" v-throttle style="margin-left:10px" v-if="assignUploadStatus == 'PROCESSING'" :loading="statusLoading" @click="uploadChangeData('SPECIFY')">文件处理中，点击刷新</el-button>
            </div>
            <div style="margin-top: 10px">
              <el-button type="primary" size="medium" @click="importBySalesmanFun('SPECIFY')">+ 按业务员导入</el-button>
              <span class="check-btn" @click="handleSalesmanAssignCheck('SPECIFY')" v-if="specifySaleClientNum > 0">查看已导入的客户（{{specifySaleClientNum}}）</span>

            </div>
          </div>
        </div>
        <div>
          <el-checkbox v-model="model.exclude">排除具体客户
            <el-popover placement="right" width="350" trigger="hover">
              <ul>
                <li>请Excel表导入客户名单。若需调整已导入的客户，请重新导入，最新导入结果将覆盖上次结果。</li>
              </ul>
              <i slot="reference" style="color: #76838f" class="el-icon-question"></i>
            </el-popover>
          </el-checkbox>
          <div class="total-top" v-if="model.exclude">
            <div>
              <el-button type="primary" size="medium" @click="handleImport('exclude')">+ 导入客户</el-button>
              <span class="check-btn" @click="handleExcludeCheck" v-if="excludeLen > 0">查看已导入的客户（{{ excludeLen }}）</span>
              <el-button type="text" v-throttle style="margin-left:10px" v-if="excludeUploadStatus == 'PROCESSING'" :loading="statusLoading" @click="uploadChangeData('ELIMINATE')">文件处理中，点击刷新</el-button>
            </div>
            <div style="margin-top: 10px">
              <el-button type="primary" size="medium" @click="importBySalesmanFun('ELIMINATE')">+ 按业务员导入</el-button>
              <span class="check-btn" @click="handleSalesmanAssignCheck('ELIMINATE')" v-if="eliminateSaleClientNum > 0">查看已导入的客户（{{eliminateSaleClientNum}}）</span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clearData">关 闭</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handlePrimary">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 选择区域的弹窗 -->
    <area-tree-dialog ref="areaDialog" @on-confirm="getEreaData" :regionList="areaList"/>
    <!-- 导入弹窗 -->
    <importDialog ref="importDialogRef" :tipsList="tipsList" :actionUploadUrl="actionUploadUrl" :templateKey="templateKey" :isShowTipsDia="false" :queryParams="queryParams" @uploadChangeData="uploadChangeData"></importDialog>
    <!-- 查看用户 -->
    <CustomerList :title="title" v-if="productStatus" :visible.sync="productStatus" @ok="uploadChangeData" :customerTagId="tagId" :checkType="checkType" :request="request" @closeProduct="closeProduct" :isBySalesman="isBySalesman">
    </CustomerList>
  <!--  按照业务员维度导入  -->
    <importBySalesman ref="importBySalesmanRef" @sendTransferValue="sendTransferValue" :customerTagId="tagId" :saleMerchantId="saleMerchantId"></importBySalesman>
  </div>
</template>

<script>
import areaTreeDialog from "@/components/multipleAreaTree";
import importDialog from "@/components/eyaolink/importDialog/index";
import importBySalesman from "./importBySalesman.vue";

import { businessType } from "@/api/registercheck";
import { customerTagNum,specifySumBySaleMan, pageImportCustomerList, getAssociationDetails, AssociateCustomerSetting,specifySetting } from "@/api/merchantApi/customerlabel";
import CustomerList from "./customerList.vue";
import _ from "lodash";

export default {
  name: "AssociatedCustomerDialog",
  components: {
    areaTreeDialog,
    importDialog,
    CustomerList,
    importBySalesman
  },
  props: {
    tagId: {
      type: String,
      default: () => {
        return "";
      }
    },
    saleMerchantId: {
      type: String,
      default: () => {
        return "";
      }
    }
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      model: {
        clientType: false,
        specific: false,
        exclude: false,
      },
      form: {
        productForbidDetails: []
      },
      // 指定区域客户类型额外数据(原本数据结构有问题,抽离出来为了不影响原本数据)
      firstOrderTypeList: [
        { label: '不限新客户', value: 'NONE' },
        { label: '仅限新客户', value: 'INCLUDE' },
        { label: '排除新客户', value: 'EXCLUDE' }
      ],
      partnerTypeList: [
        { label: '不限合伙人', value: 'NONE' },
        { label: '仅限合伙人', value: 'SPECIFY' },
        { label: '排除合伙人', value: 'EXCLUDE' }
      ],
      monthlyLevelList: [
        { label: '不限月度等级', value: 'NONE' },
        { label: '指定月度等级', value: 'SPECIFY' },
        { label: '排除月度等级', value: 'EXCLUDE' }
      ],
      firstOrderSetting: {
        id: undefined,
        firstOrderType: { code: '' }, // 新客户
        partnerType: { code: '' }, // 合伙人
        monthlyLevel: { code: '' }, // 月度等级
        levelRanges: [] // 等级范围
      },
      areaList: [],
      isFlag: "add",
      areaIndex: 100000,
      tGroups: [],
      actionUploadUrl: "/api/merchant/admin/merchantImport/importMerchantExcel",
      templateKey: "IMPORT_MERCHANT_EXCEL_TEMP",
      queryParams: {
        purMerchantType: "MERCHANT_TAG_SPECIFY_PUR_IMPORT",
        importId: this.tagId
      },
      tipsList: [
        "指定客户导入是根据ERP客户编码导入，请留意客户是否已维护ERP客户编码。",
        // "仅支持xlsx格式文件，文件大小1M以内且数据500条以内。"
      ],
      importType: "assign",
      assignLen: 0,
      excludeLen: 0,
      title: "",
      productStatus: false,
      request: pageImportCustomerList,
      checkType: "",
      districtObj: {},
      assignUploadStatus: "",
      excludeUploadStatus: "",
      statusLoading: false,
      specifySaleClientNum:0,
      eliminateSaleClientNum:0,
      isBySalesman:false
    };
  },
  watch: {
    productStatus(value){
      console.log(value,'watch ... productStatus...')
      if(!value){
        this.isBySalesman = false
      }
    }
  },
  mounted() {
    this.getCustomer();
    window.addEventListener('beforeunload', e => this.clearData())
  },
  methods: {
    sendTransferValue(transferValues){
      const { infos,type } = transferValues
      if(type === "SPECIFY"){
        this.specifySaleClientNum = infos
      }else{
        this.eliminateSaleClientNum = infos
      }
    },
    // 恢复已删除指定-排除导入客户
    cancelFn(){
      let params = {
        customerTagId: this.tagId,
      };

      if(!this.$attrs.visible) {
        return;
      }
      specifySetting(params);
    },
    /**
     * @description 查看指定导入客户
     * <AUTHOR>
     */
    handleAssignCheck() {
      this.title = "已指定客户";
      this.importType = "assign";
      this.checkType = "SPECIFY";
      this.productStatus = true;
    },
    // 查看以业务员的导入客户
    handleSalesmanAssignCheck(type){
      this.isBySalesman = true
      if(type === 'SPECIFY'){
        this.handleAssignCheck()
      }else{
        this.handleExcludeCheck()
      }
    },
    /**
     * @description 查看排除导入客户
     * <AUTHOR>
     */
    handleExcludeCheck() {
      this.title = "已排除客户";
      this.importType = "exclude";
      this.checkType = "ELIMINATE";
      this.productStatus = true;
    },
    // 获取客户类型
    getCustomer() {
      businessType().then(res => {
        if (res.code === 0 && res.msg === "ok") {
          this.tGroups = [{
            id: "NONE",
            name: "不限类型"
          }, ...res.data];
        }
      });
    },
    handleOpen() {
      this.queryParams.importId = this.tagId;
      //信息回显
      this.loading = true
      getAssociationDetails(this.tagId).then(res => {
        if (res.code === 0 && res.msg === "ok") {
          const data = res.data;
          this.model = {
            clientType: data.tagRange?.region?.code === "Y" ? true : false,
            specific: data.tagRange?.specify?.code === "Y" ? true : false,
            exclude: data.tagRange?.eliminate?.code === "Y" ? true : false,
          }
          this.firstOrderSetting = data.firstOrderSetting
          if (!this.firstOrderSetting.firstOrderType) {
            this.firstOrderSetting.firstOrderType = { code: '' }
          }
          if (!this.firstOrderSetting.partnerType) {
            this.firstOrderSetting.partnerType = { code: '' }
          }
          if (!this.firstOrderSetting.monthlyLevel) {
            this.firstOrderSetting.monthlyLevel = { code: '' }
          }
          if (this.model.specific) {
            this.getNum("SPECIFY");
            this.getSalesmanClienNumber('SPECIFY')
          }else{
            this.assignLen = 0 ;
          }
          if (this.model.exclude) {
            this.getNum("ELIMINATE");
            this.getSalesmanClienNumber('ELIMINATE')
          }else{
            this.excludeLen = 0;
          }
          //组装区域的信息
          this.areaTableInfo(data.regionSettingList);
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 查看详情的区域信息
    areaTableInfo(list = []){
      let regionList = _.cloneDeep(list);
      regionList = regionList.map(item => {

        let obj = {};
        obj.customerType = item.enterpriseType && item.enterpriseType.ids; // 255191043874488341
        obj.allType = '';
        obj.cityLabel = '';
        obj.cityList = []; // 市
        obj.districtAll = true;
        obj.districtList = []; // 区
        obj.isAll = true;
        obj.isAllNational = 0;
        obj.label = '';
        obj.provinceId = ''; // 省id

        let provinceIdList = [];
        if (item.areaList && item.areaList.length > 0) {
          item.areaList.forEach(provinceItem=>{
            // 循环省
            obj.label = `${obj.label}${obj.label.length > 0 ? ',' : ''}${provinceItem.label}`; // 省名字拼接
            provinceIdList.push(provinceItem.id);
            if (provinceItem.id === "ALL") {
              obj.isAllNational = 1;
            }

            if (provinceItem.children && provinceItem.children.length > 0) { // 判断是否有市
              // 判断合并所有市
              obj.cityList.push(...provinceItem.children)
              // 循环市
              provinceItem.children.forEach(cityItem=>{
                if (cityItem.children && cityItem.children.length > 0) { // 判断是否有区
                  //合并所有的市
                  obj.districtList.push(...cityItem.children);
                }
              })
            }
          })
        }

        obj.provinceId = provinceIdList.join(',');
        return obj;
      });
      this.form.productForbidDetails = [...regionList];
    },
    clearData() {
      this.cancelFn();
      this.$emit("reload");
    },
    // 获取指定客户类型额外数据
    getFirstOrderSetting() {
      const { firstOrderType, partnerType, monthlyLevel } = this.firstOrderSetting
      const firstOrderSetting = {
        ...this.firstOrderSetting,
        firstOrderType: !firstOrderType.code ? null : firstOrderType,
        partnerType: !partnerType.code ? null : partnerType,
        monthlyLevel: !monthlyLevel.code ? null : monthlyLevel,
        customerTagId: this.tagId,
        saleMerchantId: this.saleMerchantId
      }
      return { firstOrderSetting }
    },
    /**
     * @description 点击确定的方法
     * <AUTHOR>
     */
    handlePrimary() {
      let query = {
        id: this.tagId,
        regionList: [],
        ...this.getFirstOrderSetting(),
        tagRange: {
          region: this.model.clientType ? "Y" : "N",
          specify: this.model.specific ? "Y" : "N",
          eliminate: this.model.exclude ? "Y" : "N",
        }
      };
      const list = _.cloneDeep(this.form.productForbidDetails);
      list.forEach(item => {
        let obj = {};
        obj.customerTagId = this.tagId; // 客户标签id
        obj.saleMerchantId = this.saleMerchantId; // 销售商id
        obj.enterpriseTypeIdS = item.customerType; // 企业类型id
        if (item.isAllNational === 1) {
          // 是全国
          obj.cityIdS = "ALL";
          obj.districtIdS = "ALL";
          obj.provinceIdS = "ALL"
        } else {
          obj.provinceIdS = item.provinceId; // 省份id
          let cityIds = "";
          item.cityList.forEach((ele, i) => {
            cityIds += i === item.cityList.length - 1 ? `${ ele.id }` : `${ ele.id },`;
          });
          obj.cityIdS = cityIds;
          let districts = "";
          item.districtList.forEach((e, j) => {
            districts += j === item.districtList.length - 1 ? `${ e.id }` : `${ e.id },`;
          });
          obj.districtIdS = districts;
        }
        query.regionList.push(obj);
      });
      this.submitLoading = true
      AssociateCustomerSetting({ ...query }).then(res => {
        if (res.code === 0 && res.msg === "ok") {
          this.$message.success("设置成功!");
          this.clearData()
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    /**
     * @description 新增指定区域的弹窗
     * <AUTHOR>
     */
    handleOpenAreaDialog(districtList, type, index, isAllNational) {
      this.areaIndex = index;
      let list = [];
      this.isFlag = type;
      if (districtList.length) {
        districtList.forEach(item => {
          list.push(item.id);
        });
        this.areaList = [...list];
      } else {
        this.areaList = [];
      }
      if (type === 'edit') {
        if (isAllNational == 1) {
          // 是全国
          this.areaList = ['0']
        }
      }
      this.$refs.areaDialog.show();
    },
    /**
     * @description 删除区域
     * <AUTHOR>
     * @param {*} id
     */
    handleDel(index) {
      this.form.productForbidDetails.splice(index, 1);
    },
    /**
     * @description 选择完区域的回调
     * <AUTHOR>
     */
    getEreaData(data, flag) {
      if (flag) {
        //全国的情况下
        let allObj = {
          customerType: this.form.productForbidDetails[ this.areaIndex ]?.customerType || "NONE",
          // customerGroup: ["ALL"],
          // market: 2,
          productForbidPurMerchants: [],
          allType: 1,
          label: "全国",
          isAllNational: 1 //是全国
        };
        if (this.isFlag === "add") {
          //新增的时候直接加进去
          this.form.productForbidDetails.push(allObj);
          const len = this.form.productForbidDetails.length;
          this.districtObj[ len - 1 ] = data;
        } else {
          this.$set(this.form.productForbidDetails, this.areaIndex, allObj);
          this.districtObj[ this.areaIndex ] = data;
          this.$forceUpdate();
        }
      } else {
        // 选择的是其他省市区的情况下
        let newCityList = [];
        let newDistrictList = [];
        let labelname = "";
        let cityName = "";
        let isAllVal = true;
        let isDistrictAllVal = true;
        let provinceId = "";
        data.forEach((item, index) => {
          labelname += index === data.length - 1 ? `${ item.label }` : `${ item.label },`;
          provinceId += index === data.length - 1 ? `${ item.id }` : `${ item.id },`;
          newCityList = newCityList.concat(item.children);
          if (item.children.length) {
            item.children.forEach((ele, i) => {
              cityName += `${ ele.label },`;
              newDistrictList = newDistrictList.concat(ele.children);
            });
          }
        });
        for (let i = 0; i < data.length; i++) {
          if (data[ i ].isAll === false) {
            isAllVal = false;
            break;
          }
        }

        // 判断市是不是全部区
        for (let j = 0; j < newCityList.length; j++) {
          if (newCityList[ j ].isAll === false) {
            isDistrictAllVal = false;
            break;
          }
        }
        if (this.isFlag === "add") {
          // 添加区域
          let newObj = {
            label: labelname,
            isAll: isAllVal,
            customerType: "NONE",
            market: 2,
            productForbidPurMerchants: [],
            allType: 1,
            isAllNational: 0, //不是全国
            cityList: JSON.parse(JSON.stringify(newCityList)),
            cityLabel: cityName.substring(0, cityName.length - 1),
            districtList: JSON.parse(JSON.stringify(newDistrictList)),
            districtAll: isDistrictAllVal,
            provinceId: provinceId
          };
          this.form.productForbidDetails.push(newObj);
          const len = this.form.productForbidDetails.length;
          this.districtObj[ len - 1 ] = data;
        } else {
          // 修改区域
          let oldObj = {
            label: labelname,
            isAll: isAllVal,
            customerType: this.form.productForbidDetails[ this.areaIndex ].customerType || "NONE",
            market: this.form.productForbidDetails[ this.areaIndex ].market,
            productForbidPurMerchants: this.form.productForbidDetails[ this.areaIndex ].productForbidPurMerchants || [],
            allType: this.form.productForbidDetails[ this.areaIndex ].allType,
            isAllNational: 0, //不是全国
            cityList: JSON.parse(JSON.stringify(newCityList)),
            cityLabel: cityName.substring(0, cityName.length - 1),
            districtList: JSON.parse(JSON.stringify(newDistrictList)),
            districtAll: isDistrictAllVal,
            provinceId: provinceId
          };
          this.$set(this.form.productForbidDetails, this.areaIndex, oldObj);
          this.districtObj[ this.areaIndex ] = data;
          this.$forceUpdate();
        }
      }
    },
    /**
     * @description 导入客户
     * <AUTHOR>
     */
    handleImport(type) {
      //导入客户指定
      this.importType = type;
      if (type === "assign") {
        this.queryParams.purMerchantType = "MERCHANT_TAG_SPECIFY_PUR_IMPORT";
      } else {
        this.queryParams.purMerchantType = "MERCHANT_TAG_ELIMINATE_PUR_IMPORT";
      }
      this.$refs.importDialogRef.initOpen();
    },
    // 按照业务员维度导入客户
    importBySalesmanFun(type){
      this.$refs.importBySalesmanRef.openDialog(type)
    },
    /**
     * @description 导入的回调函数
     * <AUTHOR>
     */
    uploadChangeData(type) {
      if(this.isBySalesman){
        let specifyType = this.importType === "assign" ? 'SPECIFY':'ELIMINATE'
        this.getSalesmanClienNumber(specifyType)
        return
      }
      let query = { customerTagId: this.tagId };
      this.statusLoading = true;
      if (this.importType === "assign") {
        query.specifyType = "SPECIFY";
      } else {
        query.specifyType = "ELIMINATE";
      }
      if (type == 'SPECIFY' || type == 'ELIMINATE') {
        query.specifyType = type;
      }
      customerTagNum({ ...query }).then(res => {
        this.statusLoading = false;
        if (res.code === 0) {
          if (this.importType === "assign") {
            this.assignLen = res.data.bingSum;
            this.assignUploadStatus = res.data.uploadStatus == null ? "PROCESSING" : res.data.uploadStatus.code;
          } else {
            this.excludeLen = res.data.bingSum;
            this.excludeUploadStatus = res.data.uploadStatus == null ? "PROCESSING" : res.data.uploadStatus.code;
          }
          if (res.data.uploadStatus && res.data.uploadStatus.code == 'FAILURE') {
            this.$message.error('导入文件处理失败，请重新导入');
          } else if(res.data.uploadStatus && res.data.uploadStatus.code == 'SUCCESS') {
            this.$message.success("导入文件处理完成")
          }
        }
      });
    },
    closeProduct() {},
    /**
     * @description 获取导入的数量
     */
    getNum(typeVal) {
      let query = { customerTagId: this.tagId, specifyType: typeVal };
      customerTagNum({ ...query }).then(res => {
        if (res.code === 0) {
          if (typeVal === "SPECIFY") {
            this.assignLen = res.data.bingSum;
          } else {
            this.excludeLen = res.data.bingSum;
          }
        }
      });
    },
    getSalesmanClienNumber(typeVal){
      let params = { customerTagId: this.tagId, specifyType: typeVal };
      specifySumBySaleMan(params).then(res=>{
        const {data,code} = res
        if (code === 0) {
          if(typeVal === 'SPECIFY'){
            this.specifySaleClientNum =  data.bingSum
          }else {
            this.eliminateSaleClientNum = data.bingSum
          }
        }
      })
    },
  },
  destroyed () {
    window.removeEventListener('beforeunload', e => this.clearData());
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 20px !important;
}

.explain {
  margin-bottom: 10px;
}

.operation {
  margin-top: 20px;
}

.items-content {
  margin-bottom: 8px;
}

.district {
  margin-top: 6px;
  margin-bottom: 10px;
}

.total-top {
  margin-top: 10px;

  span {
    color: #0056E5;
    display: inline-block;
    margin-left: 30px;
  }
}

.check-btn {
  cursor: pointer;
}
.client_item {
  margin-bottom: 15px;
  .client_item_checkbox {
    margin: 10px 0 0 240px;
    &.EXCLUDE {
      margin-left: 380px;
    }
  }
}
</style>
