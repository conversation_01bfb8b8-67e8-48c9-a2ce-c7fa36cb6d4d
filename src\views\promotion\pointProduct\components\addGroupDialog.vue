<template>
  <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" >
      <el-form-item label="分组名称：" prop="groupingName">
        <el-input v-model="form.groupingName" placeholder="请输入分组名称" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :loading="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addProductGroup, editProductGroup } from '@/api/retailStore'

export default {
  name: 'addGroupDialog',
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        id: undefined,
        groupingName: '',
      },
    }
  },
  computed: {
    title() {
      return (!this.form.id ? '新增' : '编辑') + '分组'
    },
    rules() {
      return {
        groupingName: [
          { required: true, message: `请输入分组名称`, trigger: 'blur' },
        ]
      }
    }
  },
  methods: {
    show(data) {
      this.form = { groupingName: '', id: undefined }
      if (data) {
        this.form.groupingName = data.groupingName || ''
        this.form.id = data.id
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        const method = this.form.id ? editProductGroup : addProductGroup
        method(this.form).then(() => {
          this.$message.success(this.title + '成功')
          this.hide()
          this.$emit('reload')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>