<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listQuery"
      @reset="reset"
      @search="onSubmit"
    >
      <im-search-pad-item prop="account">
        <el-input v-model="listQuery.model.account" placeholder="请输入员工账号" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="listQuery.model.name" placeholder="请输入员工姓名" />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="在职" name="WORKING"></el-tab-pane>
        <el-tab-pane label="离职" name="QUIT"></el-tab-pane>
      </el-tabs>
      <div class="tab_btns">
        <el-button @click="reload">刷新</el-button>
        <el-button type="primary" @click="addOrEdit(1,'')">+ 新增员工</el-button>
      </div>
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <div slot-scope="props">
          <el-link @click="addOrEdit(2,props.row)" style="margin-right: 15px;">编辑</el-link>
          <el-link @click="handleLeave(props.row)" v-show="isWorking">离职</el-link>
        </div>

      </table-pager>
    </div>
    <el-dialog
      title="确认员工已离职"
      :visible.sync="dialogVisible"
      width="30%">
      <span>员工：{{employee.name}}</span>
      <!--<span>角色：</span>-->
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="leave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { employeeList,editEmployee } from '@/api/settingCenter'
  import AreaDialog from "../paySetting/areaDialogs";

  const TableColumns = [
    { label: "状态", prop: "positionStatus.desc" },
    { label: "员工账号", prop: "account" },
    { label: "员工名称", prop: "name" },
    { label: "员工联系号码", prop: "contactNumber"},
   /* { label: "角色", prop: "legalPerson" },*/
    { label: "创建时间", prop: "createTime" }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }

  export { TableColumnList };

  export default {
    name: "DragTable",
    components: {
      AreaDialog
    },
    inject:  ['reload'],
    data() {
      return {
        activeName: 'WORKING',
        showSelectTitle: false,
        tableTitle: TableColumnList,
        tableData: [],
        tableVal: [],
        list: [],
        total: 0,
        page: 0,
        listLoading: false,
        listQuery: {
          model: {
            account: '',
            name: '',
            positionStatus: 'WORKING'
          },
        },
        dialogVisible: false,
        employee: {},
        isWorking: true
    };
    },
    created() {
      /*    this.getSaleId()*/
      this.getArea()
    },
    methods: {
      handleClick(tab, event) {
        if(tab.name === 'WORKING') {
          this.isWorking = true
        } else {
          this.isWorking = false
        }
        this.listQuery.model.positionStatus = tab.name
        this.handleRefresh({
          page: 1,
          size: 10
        })
      },
      handleLeave(row) {
        this.dialogVisible = true
        this.employee = row
      },
      async leave() {
        const {data} = await editEmployee({
          "contactNumber": this.employee.contactNumber,
          "id": this.employee.id,
          "name": this.employee.name,
          "positionStatus": "QUIT"
        })
        this.$message.success('设置离职成功')
        this.load()
        this.dialogVisible = false

      },
      addOrEdit(type,row) {
        if(type === 1) {
          this.$router.push({path: './edit'})
        } else {
          this.$router.push({path: './edit',query: {id: row.id}})
        }
      },
      onSubmit() {
        this.handleRefresh({
          page: 1,
          pageSize: 10
        })
        this.load()

      },
      async load(params) {
        this.listLoading = true
        Object.assign(this.listQuery, params)
        return await employeeList(this.listQuery)
      },
      reset() {
        this.listQuery.model = {
            account: '',
            name: '',
            positionStatus: 'WORKING'
          }
        this.handleRefresh({
          page: 1,
          size: 10
        })
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
    },
  };
</script>

<style>
  .sortable-ghost {
    opacity: 0.8;
    color: #fff !important;
    background: #42b983 !important;
  }
</style>

<style scoped>
</style>
