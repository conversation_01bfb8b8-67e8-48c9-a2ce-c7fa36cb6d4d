  <template>
  <div class="paySettingPageContent">
    <!-- tab切换 begin -->
    <tabs-layout
        ref="tabs-layout"
        :tabs="tabs"
        v-model="currentTab"
        @change="handleChangeTab"
    >
    </tabs-layout>
    <!-- tab切换 end -->

    <!-- 微信支付 begin -->
    <div class="payitem flex_between_center" v-for="(item,index) in payList" :key="index">
      <div class="leftInfo flex_between_center">
        <div  class="flex_between_center">
          <div class="icon">
            <img :src="!item.merchantPcPict?item.pictIdS:item.merchantPcPict" alt="" />
          </div>
          <div class="itemInfo">
            <p>{{ item.name }}</p>
            <p>
              <!-- 微信支付，用户通过扫描二维码、微信内打开商品页面购买等多种方式调起微信支付模块完成支付。 -->
              {{ item.description }}
            </p>
          </div>
        </div>
        <div>
          <!-- <el-switch v-model="weChatPay" active-text="启动" inactive-text="关闭" @change="handleWeChatPay"> -->
          <el-switch v-if="checkPermission(['admin', 'sale-saas-finance-paySetting:switch', 'sale-platform-finance-paySetting:switch'])" v-model="item.paySwitch" active-text="启动" inactive-text="关闭" @change="handleSwitch(item)" />
        </div>
      </div>
      <!-- SaaS商家，支付配置应由商家自己进行配置 -->
      <div class="rightInfo" v-if="!isPlatform">
        <el-link class="link" type="primary" @click="configuration(item)">配置</el-link>
        <!-- <el-link class="link" type="primary">查看账单</el-link> -->
      </div>
    </div>
    <!-- 微信支付 end -->


    <!-- 设置弹出 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="showSetting" width="35%">
        <pay-setting-form :cardId="currentPayId" :payWayType ="payWayType" :FormList="FormList" @handleSure="handleSure"></pay-setting-form>
    </el-dialog>
    <!-- 设置弹出 -->

    <!-- 账单弹出 -->
    <!-- <el-dialog :visible.sync="showBill" width="65%" title="账单" >
        <pay-setting-table :cardId="1"></pay-setting-table>
    </el-dialog> -->
    <!-- 账单弹出 -->


  </div>
</template>
<script>
// import paySettingForm from './Form/paySetting';
// import paySettingTable from './Table/paySetting';
import paySettingForm from './form';
// import paySettingTable from '@/views/settingCenter/adjourned/paySetting/Table/paySetting';
import {paymentSetting,paymentSettingInfo, isEnabled} from '@/api/settingCenter';
import checkPermission from '@/utils/permission';
import { getLocalUser } from '@/utils/local-user';
export default {
  name: 'paySetting',
  data() {
    return {
      tabs: [],
      title:"配置信息",
      isSelect:true,
      showBill:false,
      showSetting:false,
      isUnionPay:true,// 银联支付
      weChatPay:true, // 微信支付
      isOffline:true, // 线下汇款
      isAlipay:true,// 支付宝
      isCollectPay:true, // 收钱吧支付
      dialogType:'default',
      payList:[],
      currentPayId:'',
      imgPath:require('@/assets/imgs/weixin.jpg'),
      FormList:[],
      payWayType:'',
      currentTab: '',
      isPlatform: false
    }
  },
  components:{
     paySettingForm,
    //  paySettingTable
  },
  watch:{
    showSetting(val){
      console.log('showSetting--->',val);
      if(!val){
        this.initData();
      }
    }
  },
  methods: {
    checkPermission,
    setIsPlatform() {
      const user = getLocalUser()
      if (user && Object.keys(user).length > 0) {
        this.isPlatform = user.commerceModel === 'SAAS_PLATFORM'
      }
    },
    async initData(){
      let params = {
        current: 1,
        order: "descending",
        model:{
          paySource: this.currentTab,
        },
        size: 100,
        sort: "id"
      };
      await paymentSetting(params).then(res=>{
        console.log('res===分页===>',res);
        if(res.code ==0 && res.msg=='ok'){
          this.payList = res.data.records.map(item=>{
            return Object.assign(
              {},
              item,
              {
                paySwitch:item.whetherEnabled.code=='Y'?true:false
              }
            )
          });
        }
      })
    },
    handleSure(val){
      console.log('关闭了',val);
      this.showSetting = val;
    },
    handleChangeTab(tab, index) {
      this.currentTab = tab.value;
      this.initData();
    },
    // 银联支付
    handleUnionPay(val){
      console.log('val--银联支付--->',val);
    },
    // 开关
    handleSwitch(val){
      console.log('val--微信支付--->',val);
      let params = {
        ids: val.id,
        whetherEnabled:val.paySwitch ? 'Y' : 'N'
      };
      isEnabled(params).then(res=>{
        if(res.code == 0 && res.msg == 'ok') {
          this.$message.success(`${val.name}支付模式${val.paySwitch?'启动':'关闭'}成功`);
        } else {
          this.initData();
        }
      })
    },
    configuration(val){
      this.showSetting = true;
      this.currentPayId = val.id;
      paymentSettingInfo(val.id, val.saleMerchantId).then(res=>{
        // console.log('详情----->',res);
        if(res.code == 0 && res.msg=='ok'){
          this.FormList = res.data.paramList;
          this.payWayType = res.data.payWayType.code;
        }
      })
    },
    // 线下汇款
    handleOffline(val){
      console.log('val---线下汇款---->',val);
    },
    // 配置
    handleDeploy(value){
      this.showSetting = true;
      this.dialogType = value;
    }
  },
  mounted() {
    const tabs = [
      {name: 'PC端', value: 'PC', hide: !checkPermission(['admin', 'sale-saas-finance-paySetting:webView', 'sale-platform-finance-paySetting:webView'])},
      {name: '小程序端', value: 'WECHAT', hide: !checkPermission(['admin', 'sale-saas-finance-paySetting:wechatView', 'sale-platform-finance-paySetting:wechatView'])}
    ].filter(item => !item.hide)
    this.tabs = tabs
    this.setIsPlatform()
    if (tabs.length > 0) this.currentTab = tabs[0].value
    this.$nextTick(()=>{
      this.initData();
    })
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/element-variables.scss";

.paySettingPageContent {
  background-color: #fff;
    padding: 15px;
    .payitem{
        height: 95px;
        background: #ecf0f5;
        border-radius: 4px;
        margin-bottom: 16px;
        .leftInfo{width:95%;}
        .rightInfo{
            width: 15%;
            text-align: center;
            .link{margin-right: 15px;}
        }
        .icon{padding: 16px;}
        .icon img{width: 43px;height: 43px;}
        .itemInfo>p:nth-child(1){
            line-height: 22px;
            font-size: 16px;
            font-weight: 400;
        }
        .itemInfo>p:nth-child(2){
            line-height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #7f7f7f;
        }
    }
}
</style>
