<template>
  <div>
    <im-search-pad :model="listQuery" @reset="handleReset" :hasExpand="false" @search="onSubmit">
      <im-search-pad-item prop="name">
        <el-input v-model.trim="listQuery.model.name" :placeholder="namePlaceholder" />
      </im-search-pad-item>
      <im-search-pad-item prop="publishStatus">
        <el-select v-model="listQuery.model.publishStatus" placeholder="请选择账户状态">
          <el-option v-for="item in publishStatusData" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[{ name: tabTitle }]">
        <!--tabs右上角相关按钮-->
        <template slot="button">
          <el-button type="primary" :disabled="idSet.length === 0" @click="showBatchUpdateCustomerPointDialog">批量启用/冻结</el-button>
          <el-button @click="showPointSettingDialog" v-if="objectType === 'PUR'">积分设置</el-button>
          <el-button @click="showBatchRechargeAccountDialog" v-if="objectType === 'SALESMAN'">批量充值扣减</el-button>
        </template>
      </tabs-layout>
      <table-pager
        ref="todoTable"
        :selection="true"
        :options="tableTitle"
        :data.sync="tableData"
        :operation-width="200"
        :remote-method="load"
        @selection-change="tableSelectionChange"
      >
        <el-table-column label="账户状态" width="150" slot="publishStatus">
          <slot slot-scope="{row}">
            <span v-if="row.publishStatus && row.publishStatus.code === 'Y'">已启用</span>
            <span v-else style="color: red">已冻结</span>
          </slot>
        </el-table-column>
        <div slot-scope="{row}">
          <span class="table-edit-row-item">
            <el-button type="text" @click="showRechargePointDialog(row.id, 'Add')">充值</el-button>
            <el-button type="text" @click="showRechargePointDialog(row.id, 'Reduce')">扣减</el-button>
            <el-button type="text" @click="showAccountDetailDialog(row.id, row.name)">账户明细</el-button>
          </span>
        </div>
      </table-pager>
    </div>
    <RechargeAccountDialog ref="rechargeAccountDialogRef" :amountType="amountType" @reload="handleRefresh" />
    <AccountDetail ref="accountDetailRef" />
    <PointSettingDialog ref="pointSettingDialogRef" @reload="handleRefresh" />
    <BatchUpdateCustomerPointDialog ref="batchUpdateCustomerPointDialogRef" @reload="handleRefresh" />
    <BatchRechargeAccountDialog ref="batchRechargeAccountDialogRef" :importType="objectType" />
  </div>
</template>

<script>
import RechargeAccountDialog from './rechargeAccountDialog.vue'
import AccountDetail from './accountDetail.vue'
import PointSettingDialog from './pointSettingDialog.vue'
import BatchUpdateCustomerPointDialog from './batchUpdateAccountStatusDialog.vue'
import BatchRechargeAccountDialog from './batchRechargeAccountDialog.vue'
import { getCustomerPointsList } from '@/api/retailStore'
import { getCustomerPointsTableTitle, getSalesmanCashAccountTableTitle } from '.'

const initListQuery = () => {
  return { model: { name: '', publishStatus: '' } }
}
export default {
  name: 'accountComponents',
  props: {
    objectType: {
      type: String
    },
  },
  components: {
    BatchUpdateCustomerPointDialog,
    PointSettingDialog,
    AccountDetail,
    RechargeAccountDialog,
    BatchRechargeAccountDialog
  },
  computed: {
    // 充值的类型
    amountType() {
      return this.objectType === 'PUR' ? 'POINTS' : 'CASH'
    },
    // 姓名搜索的文本
    namePlaceholder() {
      return '请输入' + (this.objectType === 'PUR' ? '客户名称' : '业务员姓名')
    },
    // tab的标题
    tabTitle() {
      return this.objectType === 'PUR' ? '客户积分' : '业务员现金金额'
    },
    // 表格字段
    tableTitle() {
      return this.objectType === 'PUR' ? getCustomerPointsTableTitle() : getSalesmanCashAccountTableTitle()
    }
  },
  data() {
    return {
      totalPage: 0,
      tableData: [],
      idSet: [],
      publishStatusData: [
        { label: '启用', value: 'Y' },
        { label: '冻结', value: 'N' },
      ],
      list: [],
      total: 0,
      listLoading: false,
      listQuery: initListQuery(),
    }
  },
  methods: {
    async load(params) {
      this.idSet = []
      Object.assign(this.listQuery, params)
      this.listLoading = true
      try {
        this.listQuery.model.objectType = this.objectType
        const res = await getCustomerPointsList(this.listQuery)
        this.totalPage = res.data.pages
        this.total = res.data.total
        return res
      } catch (e) {
        this.listLoading = false
      }
    },
    handleReset() {
      this.listQuery = initListQuery()
      this.onSubmit()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable.doRefresh(pageParams)
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
    tableSelectionChange(val) {
      this.idSet = val.map(item => item.id)
    },
    showRechargePointDialog(id, type) {
      this.$refs.rechargeAccountDialogRef.show(id, type)
    },
    showAccountDetailDialog(id, name) {
      this.$refs.accountDetailRef.show(id, name)
    },
    // 积分设置
    showPointSettingDialog() {
      this.$refs.pointSettingDialogRef.show()
    },
    // 批量启用/冻结
    showBatchUpdateCustomerPointDialog() {
      this.$refs.batchUpdateCustomerPointDialogRef.show(this.idSet)
    },
    // 批量充值扣减
    showBatchRechargeAccountDialog() {
      this.$refs.batchRechargeAccountDialogRef.show()
    },
  },
}

</script>

<style lang="scss" scoped></style>
