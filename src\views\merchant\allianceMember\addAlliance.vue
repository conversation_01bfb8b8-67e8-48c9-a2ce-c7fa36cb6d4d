<template>
  <div v-loading="loading">
    <clients-model  @close="hide" @ok="adds" :visible.sync="visible" />
  </div>
</template>

<script>

const TableColumns = [
  { label: 'ERP客户编码', prop: 'customerCode', width: '150' },
  { label: '客户名称', prop: 'name', width: '140' },
  { label: '企业类型', prop: 'merchantType' },
  { label: '联系人', prop: 'ceoName' },
  { label: '联系电话', prop: 'ceoMobile', width: '120' },
  { label: '所在地区', prop: 'region', width: '150' }
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}

import ClientsModel from '@/components/eyaolink/ClientsModel/ClientsModel.vue'
import { batchAdd, purMerchantPage } from "@/api/merchantApi/allianceMember"
export default {
  name: 'DragTable',
  props: ['saleMerchantId'],
  data() {
    return {
      loading: false,
      tableTitle: TableColumnList,
      list: null,
      total: null,
      sortable: null,
      tableData: [],
      ids: [],
      listQuery: {
        model: {
          name: '',
          approvalStatus: {
            code: 'ACCEPTED'
          }
        }
      },
      visible: false,
    }
  },
  created() {
    // this.getList();
  },
  methods: {
    show() {
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    async adds(list) {
      this.ids = list.map(v => v.purMerchantId);
      if (this.ids.length > 0) {
        let paramsList = this.ids.map(item => {
          return Object.assign(
            {},
            {
              purMerchantId: item
            }
          )
        });
        this.loading = true
        batchAdd(paramsList).then(res => {
          if (res.code == 0 && res.msg == 'ok') {
            this.$message.success('批量添加成功！');
            this.hide()
            this.$emit('addSuccess')
          }
        }).finally(() => {
          this.loading = false
        })
      }
    }
  },
  components: { ClientsModel }
}
</script>
