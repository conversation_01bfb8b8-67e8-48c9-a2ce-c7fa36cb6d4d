<template>
  <div id="poolDetail">
    <div class="search_box">
      <el-input
        v-model.trim="searchForm.purMerchantNameOrERP"
        placeholder="请输入客户名称/编码"
        style="width: 200px"
        clearable
        @keydown.enter.native="getData()"
        @blur="getData()"
        @clear="getData()"
      ></el-input>
      <el-select
        style="width: 200px"
        v-model="searchForm.saleManId"
        clearable
        filterable
        remote
        placeholder="原所属业务员"
        :remote-method="(t) => remoteMethod(2, t)"
        :loading="saleManLoading"
        @change="getData()"
      >
        <el-option
          v-for="item in saleManOptionsL"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-date-picker
        v-model="searchForm.recoveryTime"
        type="daterange"
        style="width: 240px; margin-right: 10px"
        start-placeholder="回收日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        value-format="yyyy-MM-dd HH:mm:ss"
        clearable
        @change="getData()"
      >
      </el-date-picker>
      <el-button type="primary" @click="getData">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </div>
    <p>
      <el-button
        :disabled="
          !(
            allotInfo.list.length &&
            allotInfo.list[0].whetherEnable.code === 'Y'
          )
        "
        @click="toAllotMore"
        type="primary"
        >批量分配</el-button
      >
    </p>
    <div class="main_box">
      <table-pager
        ref="pager-table"
        :pageSize="10"
        :options="tableTitle"
        :data.sync="tableData"
        :operation-width="180"
        :selection="true"
        :remote-method="load"
        @selectionChangeHandle="selectionChangeHandle"
      >
        <div slot-scope="scope" align="center">
          <el-button
            v-throttle
            :disabled="scope.row.whetherEnable.code === 'N'"
            type="text"
            @click="toAllotOne(scope.row)"
            >分配</el-button
          >
        </div>
      </table-pager>
    </div>
    <el-dialog title="客户分配" :visible.sync="dialogVisible" width="400">
      <div class="dialog_main">
        已选<span>{{ allotInfo.list.length || 0 }}</span
        >条数据,即将分配给
        <el-select
          style="width: 200px; margin-left: 12px"
          v-model="allotInfo.saleManId"
          filterable
          remote
          placeholder="员工姓名/手机号"
          :remote-method="(t) => remoteMethod(1, t)"
          :loading="saleManLoading"
        >
          <el-option
            v-for="item in saleManOptionsZ"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="allotLoading" @click="toAllot"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMerchantPoolDetailList,
  getSaleManList,
  merchantPoolAllot,
} from "@/api/merchantApi/customerPool.js";
export default {
  name: "poolDetail",
  props: {},
  data() {
    return {
      searchForm: {
        poolId: "",
        purMerchantNameOrERP: "",
        saleManNameOrMoblie: "",
        recoveryTime: [],
      },
      saleManOptionsL: [], // 包含离职的业务员
      saleManOptionsZ: [], // 在职业务员
      saleManLoading: false,
      allotInfo: {
        list: [],
        saleManId: "",
      },
      allotLoading: false,
      dialogVisible: false,
      paginationSet: {
        total: 1,
        current: 1,
        size: 5,
      },
      tableData: [],
      tableTitle: [
        {
          key: 1,
          prop: "erpCode",
          name: "erpCode",
          label: "ERP编码",
          width: "",
          disabled: true,
        },
        {
          key: 2,
          prop: "purMerchantName",
          name: "purMerchantName",
          label: "客户名称",
          width: "",
          disabled: true,
        },
        {
          key: 3,
          prop: "purMerchantTime",
          name: "purMerchantTime",
          label: "创建时间",
          width: "",
        },
        {
          key: 4,
          prop: "saleManName",
          name: "saleManName",
          label: "原所属业务员",
          width: "",
        },
        {
          key: 5,
          prop: "orderTime",
          name: "orderTime",
          label: "最近下单时间",
          width: "",
        },
        {
          key: 6,
          prop: "createTime",
          name: "createTime",
          label: "回收时间",
          width: "",
        },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm.poolId = this.$route.query.id ?? null;
  },
  mounted() {},
  methods: {
    remoteMethod(f, query) {
      if (query !== "") {
        this.saleManLoading = true;
        getSaleManList({
          name: query,
          whetherOnJob: f === 1 ? "1" : "",
        }).then((res) => {
          this.saleManLoading = false;
          f === 1
            ? (this.saleManOptionsZ = res.data ?? [])
            : (this.saleManOptionsL = res.data ?? []);
        });
      } else {
        this.saleManOptionsL = [];
        this.saleManOptionsZ = [];
      }
    },
    selectionChangeHandle(val, row) {
      console.log(val);
      this.allotInfo.list = val ?? [];
    },
    toAllotOne(row) {
      this.allotInfo.list = [{ ...row }];
      this.allotInfo.saleManId = "";
      this.dialogVisible = true;
    },
    toAllotMore() {
      this.allotInfo.saleManId = "";
      this.dialogVisible = true;
    },
    async load(params) {
      try {
        let temp = {
          ...(params ?? {}),
          model: {
            ...this.searchForm,
            recoveryStartTime: this.searchForm.recoveryTime?.[0] ?? "",
            recoveryEndTime: this.searchForm.recoveryTime?.[1] ?? "",
          },
        };
        this.loading = true;
        let res = await getMerchantPoolDetailList(temp);
        this.loading = false;
        return res || { data: {} };
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    getData() {
      this.$refs["pager-table"].doRefresh({
        page: 1,
        pageSize: 10,
      });
    },
    reset() {
      this.searchForm = Object.assign(this.searchForm, {
        purMerchantNameOrERP: "",
        saleManNameOrMoblie: "",
        recoveryTime: [],
        saleManId: "",
      });
      this.getData();
    },
    toAllot() {
      this.allotLoading = true;
      let purMerchantApplyIds = []; // 拓客表id
      let purMerchantIds = []; // 客户档案id
      let purMerchantPoolIds = []; //客户池关系主键
      let oldSaleManId = []; //原所属业务员
      this.allotInfo.list.forEach((item) => {
        if (item.purMerchantApplyId) {
          purMerchantApplyIds.push(item.purMerchantApplyId);
        }
        if (item.purMerchantId) {
          purMerchantIds.push(item.purMerchantId);
        }
        purMerchantPoolIds.push(item.id);
        oldSaleManId.push(item.saleManId);
      });
      oldSaleManId = [...new Set(oldSaleManId)];
      let temp = {
        merchantId: this.allotInfo.list[0].merchantId,
        saleManId: this.allotInfo.saleManId,
        purMerchantApplyIds,
        purMerchantIds,
        purMerchantPoolIds,
        oldSaleManId,
      };
      merchantPoolAllot(temp)
        .then((res) => {
          if (res.data) {
            this.getData();
            this.$message.success("分配成功！");
          } else {
            this.$message.error("分配失败！");
          }
          this.allotLoading = false;
          this.dialogVisible = false;
        })
        .catch((err) => {
          this.allotLoading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
#poolDetail {
  height: calc(100vh - 86px - 32px);
  display: flex;
  flex-direction: column;
  background: #fff;
  > div {
    padding: 16px;
  }
  > div + div {
    margin-top: 20px;
  }
  > p {
    padding-left: 16px;
  }
}
.search_box {
  height: 68px;
  // border-left: 4px solid #0056e5;
  display: flex;
  align-items: center;

  > .el-input + .el-button,
  > .el-input + .el-select,
  > .el-select + .el-date-editor {
    margin-left: 10px;
  }
}
.main_box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: scroll;
  .main_content {
    flex: 1;
  }
  .pagination-container {
    justify-content: right;
  }
}
.dialog_main {
  > span {
    margin: 0 4px;
    color: #0056e5;
  }
}
</style>
