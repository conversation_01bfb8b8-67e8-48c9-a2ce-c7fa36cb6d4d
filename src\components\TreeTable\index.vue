<template>
  <div class="statistics common-content">
    <el-row class="searchForm">
      <el-col :span="24">
        <div class="grid-content">
          <span class="label">查询条件：</span>
          <span class="label">过去</span>
          <el-input-number v-model="pastDays" controls-position="right" :min="0" :max="90"></el-input-number>
          <span class="label">天</span>
          <span class="attention">（注：0查询所有）</span>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
      </el-col>
    </el-row>

    <el-table :data="statisticDatas" border style="width: 100%;" size="medium" :row-class-name="rowClassNameHandler">
      <el-table-column label="分类名称" width="460" align="left" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="{marginLeft: scope.row.level * 23 + 'px'}">&ensp;</span>
          <i v-if="scope.row.showChildren" :class="{'el-icon-caret-top':scope.row.showChildren, 'el-icon-caret-bottom':!scope.row.hasChildren}" @click="onExpand(scope.row)" :style="{cursor:scope.row.hasChildren ? 'pointer' : 'normal'}"></i>
          <i v-else :class="{'el-icon-caret-bottom':scope.row.hasChildren, '':!scope.row.hasChildren}" @click="onExpand(scope.row)" :style="{cursor:scope.row.hasChildren ? 'pointer' : 'normal'}"></i>
          <span :data-level="scope.row.level" :style="{marginLeft: 4 + 'px'}">{{ scope.row.categoryName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="categoryid" label="分类ID" width="80" align="center"></el-table-column>
      <el-table-column prop="parentcategoryid" label="父类ID" width="80" align="center"></el-table-column>
      <el-table-column prop="undeployed" label="模板（未发布）" align="left"></el-table-column>
      <el-table-column prop="deployed" label="模板（已发布）" align="left"></el-table-column>
      <el-table-column prop="edit" label="合同实例（编辑中）" align="left"></el-table-column>
      <el-table-column prop="completed" label="合同实例（完成）" align="left"></el-table-column>
    </el-table>
  </div>
</template>

<script src="./ContractStatistic.js"></script>

<style lang="less">
.statistics {
    padding: 10px;
    .hiddenRow {
        display: none;
    }
    .searchForm{
        padding: 10px;
        span.label{
            margin-right: 10px;
        }
        span.attention{
            color: #e50021;
        }
    }
    
}
</style>