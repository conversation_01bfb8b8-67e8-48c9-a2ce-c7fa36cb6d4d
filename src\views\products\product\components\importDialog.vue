<!--
 * @: 批量导入组件
-->
<template>
  <div class="import_box">
    <el-dialog title="批量导入" :visible.sync="showImport" top="10vh" width="428px" :close-on-click-modal="false"
      :lock-scroll="true" :before-close="closeDialogFun">
      <div class="import_content">
        <div class="import_type_box">
          <el-radio-group v-model="radioType" class="radioGroup" @change="handleUploadType">
            <el-radio :label="'1'">发布商品</el-radio>
            <el-radio :label="'2'">商品图片导入</el-radio>
            <el-radio :label="'3'">区域价导入</el-radio>
            <el-radio :label="'4'" style="margin-top:10px">普通价/会员价/零售价导入</el-radio>
          </el-radio-group>
        </div>
        <div class="import_el-com">
          <el-upload ref="upload" class="avatar-uploader" drag :action="actionUploadUrl" :headers="headersProgram" :accept="accpetType"
            :data="uploadData" :on-change="beforeUpload" :on-remove="handleRemove" :file-list="fileList" :on-success="uploadSuccess"
            :on-progress="uploadProgram" :auto-upload="false" :multiple="false">

            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖拽至此区域或选择文件上传</div>
            <div slot="tip" class="el-upload__tip">
              <div v-if="radioType == '3'" style="padding-bottom: 10px;">
                <span style="font-size: 14px;">更新历史数据：</span>
                <el-radio-group v-model="importType">
                  <el-radio label="NORMAL">全量更新</el-radio>
                  <el-radio label="SPECIFIC">增量更新
                    <el-tooltip effect="light" placement="right">
                      <i class="el-icon-question" style="margin-left: 6px" />
                      <div slot="content" style="width: 200px" v-html="tip"></div>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </div>
              <div v-if="radioType == '1'">
                <p>1.商品是根据ERP商品编码导入，请留意商品是否已维护ERP商品编码。</p>
                <p>2.请确认仓库管理是否按多仓管理。非多仓，则模板内无需维护所在仓库，否则请维护准确的仓库名称。</p>
                <p>3.仅支持xlsx格式文件，文件大小1M以内且数据5000条以内。若超过限制，请分批导入
                  <el-link type="primary" :href="tempUrl" target="_blank">下载模板</el-link>
                </p>
              </div>
              <div v-else-if="radioType == '2'">
                <p>当前商品图片上传仅支持zip格式的压缩包，文件大小限制在150M以内 </p>
              </div>
              <div v-else-if="radioType == '3'">
                <p>1.商品是根据ERP商品编码导入，请留意商品是否已维护ERP商品编码。</p>
                <p>2.请确认仓库管理是否按多仓管理。非多仓，则模板内无需维护所在仓库，否则请维护准确的仓库名称。</p>
                <p>
                  3.仅支持xlsx格式文件，文件大小1M以内且数据5000条以内。若超过限制，请分批导入
                  <el-link type="primary" :href="tempUrl" target="_blank">下载模板</el-link>
                </p>
              </div>
              <div v-else>
                <p>1.商品是根据ERP商品编码导入，请留意商品是否已维护ERP商品编码。</p>
                <p>2.请确认仓库管理是否按多仓管理。非多仓，则模板内无需维护所在仓库，否则请维护准确的仓库名称。</p>
                <p>
                  3.仅支持xlsx格式文件，文件大小1M以内且数据5000条以内。若超过限制，请分批导入
                  <el-link type="primary" :href="tempUrl" target="_blank">下载模板</el-link>
                </p>
              </div>
            </div>
          </el-upload>
          <el-progress :stroke-width="2" v-if="showProcess" :percentage="processLen"></el-progress>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialogFun">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="tipsClose">
      <span>文件正在导入中，您可到导入记录页面查看导入结果。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tipsClose">取 消</el-button>
        <el-button type="primary" @click="checkImportResult">查看导入结果</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
  import {
    getParameterByKey
  } from '@/api/settingCenter'
  import {
    getToken
  } from '@/utils/auth'
  export default {
    name: 'importDialog',
    //import引入的组件
    components: {},

    data() {
      return {
        importType: 'NORMAL',
        showImport: false,
        radioType: '1',
        unitTime: 0, // 上传进度条每次加1的单位时间
        actionUploadUrl: '/api/product/admin/product/import',
        headersProgram: {
          token: `Bearer ${getToken()}`,
          Authorization: 'Basic c2FsZV91aTpzYWxlX3VpX3NlY3JldA=='
        },
        tempUrl: "https://eyaolink-dev-bucket.oss-cn-shenzhen.aliyuncs.com/0/2021/04/dea696ef-a598-4d7e-9dd3-ba080bd8dae0.xlsx",
        fileList: [],
        showProcess: false, // 上传进度条
        processLen: 0,
        dialogVisible: false,
        tip: `1、全量更新：本次导入数据覆盖已导入的所有区域价。<br /><br />
              2、增量更新：本次导入数据仅覆盖重复的区域价，以及新增其他区域价。`,
        accpetType: '.xlsx',
      }
    },
    computed: {
      uploadData() {
        if (this.radioType == '3') return { importType: this.importType }
        return {}
      }
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {
      this.getParameterByKeyFun();
    },
    //方法集合
    methods: {
      openDiaFn() {
        this.showImport = true;
        this.radioType = '1';
        this.handleUploadType(this.radioType)
        this.fileList = [];
        this.isSubmit = false;
        this.processLen = 0;
        this.showProcess = false;
      },
      tipsClose() {
        this.dialogVisible = false;
      },
      async getParameterByKeyFun() {
        if (this.radioType == '2') {
          return
        }
        let list = [
          'MERCHANT_IMPORT_PRODUCT_EXCEL_TEMP',
          '',
          'MERCHANT_IMPORT_POLICY_PRICE_EXCEL_TEMP',
          "MERCHANT_IMPORT_PRODUCT_EXCEL",

        ];
        let key = list[Number(this.radioType) - 1];
        let {
          code,
          data
        } = await getParameterByKey({
          key
        });
        if (code == 0) {
          this.tempUrl = data.value
        }
      },
      /**
       * <el-radio :label="'1'">发布商品</el-radio>
          <el-radio :label="'2'">商品图片导入</el-radio>
          <el-radio :label="'3'">区域价导入</el-radio>
          <el-radio :label="'4'" style="margin-top:10px">普通价/会员价/零售价导入</el-radio>
       * */
      handleUploadType(e) {
        let list = [
          '/api/product/admin/product/import',
          '/api/product/admin/product/uploadProductPic',
          '/api/product/admin/product/import/policy/price',
          '/api/product/admin/product/importPrice'
        ];
        this.actionUploadUrl = list[Number(e) - 1];
        this.getParameterByKeyFun();
        this.fileList = [];
        if (e ===  '2') {
          this.accpetType = '.zip';
        } else {
          this.accpetType = '.xlsx';
        }
      },
      beforeUpload(file) {
        console.log('file', file);
        this.unitTime = parseInt(file.size / 1024 / 1024 / 30) * 600;
        let isZIP = true;
        if (this.radioType == '2') {
          let list = ['application/zip', 'application/x-compressed', 'application/x-zip-compressed', 'multipart/x-zip'];
          if (list.includes(file.raw.type)) {
            isZIP = true;
          } else {
            isZIP = false;
          }
        }

        var uploadFiles = this.$refs.upload.uploadFiles
        if (uploadFiles.length > 1) {
          uploadFiles.splice(0, 1)
        }
        if (!isZIP) {
          this.$message.error('只能上传ZIP格式的压缩包');
          uploadFiles = [];
          this.fileList = [];
        }
        console.log('isZip', isZIP);
        return isZIP
        // return isLt1M
      },
      handleRemove(file, fileList) {
        console.log('文件列表移除文件时的钩子', file, fileList);
        if (fileList.length == 0) {
          this.isSubmit = false;
          this.processLen = 0;
          this.showProcess = false;
        }
      },
      uploadSuccess(res, file) {
        if (res.code == 0) {
          this.fileList = [];
          this.showImport = false;
          this.dialogVisible = true;
        } else {
          this.$message.error('上传失败!')
        }
        this.isSubmit = false;
        this.processFnShow();
        this.showProcess = false;
      },
      uploadProgram(event, file, fileList) {

      },
      submitUpload() {
        if (this.$refs.upload.uploadFiles.length == 0) {
          this.$message.warning('请先上传文件');
          return;
        }
        this.isSubmit = true;
        this.processFnShow();
        this.$refs.upload.submit();
      },
      processFnShow() {
        let interval;
        if (this.isSubmit) {
          this.showProcess = true;
          interval = setInterval(() => {
            if (this.processLen >= 99) {
              clearInterval(interval);
              return
            };
            this.processLen += 1;
          }, this.unitTime);
        } else {
          this.processLen = 100;
          clearInterval(interval);
        }
      },
      // 关闭弹窗
      closeDialogFun() {
        this.$emit('closeImportDia');
        this.showImport = false;
        this.fileList = [];
      },
      //   查看导入结果
      checkImportResult() {
        this.showImport = false;
        this.dialogVisible = false;
        this.$router.push('/importAndExport/import/index');
      },
    },

  }

</script>


<style lang='scss' scoped>
  .import_box ::v-deep .el-dialog__body {
    padding: 15px 20px;
  }

  .import_box ::v-deep .el-upload-dragger {
    width: 390px;
  }

  .import_box ::v-deep .el-progress {
    display: none;
  }

  .import_box ::v-deep .el-upload {
    width: 100%;
    height: auto;
  }

  .import_box ::v-deep .el-upload-dragger {
    width: 100%;
    height: 180px;
  }

  .import_box ::v-deep .el-upload__tip {
    width: 100%;
    margin-top: 10px;
    line-height: 20px;
  }

  .radioGroup {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .import_el-com {
    margin-top: 10px;
  }

</style>
