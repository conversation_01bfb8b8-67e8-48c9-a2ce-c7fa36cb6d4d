<template>
  <div class="detail-item">
    <p class="detail-head">{{itemName}}<span style="color: #999;font-size: 14px;margin-left: 6px;">{{tip}}</span></p>
  <div><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: "detail-item",
  props: {
    itemName: {
      type: String,
      default: '卡片名称'
    },
    tip: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .detail-head{
    background: #fafafb;
    padding: 13px 12px;
  }
</style>
