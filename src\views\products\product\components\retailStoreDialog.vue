<template>
  <el-dialog title="奖励设置" :visible.sync="visible" :close-on-click-modal="false" width="550px">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" >
      <el-form-item label="裂变奖励" prop="whetherReward">
        <el-radio-group v-model="form.whetherReward">
          <div style="margin-top: 12px;" v-for="(item, index) in whetherRewardData" :key="index">
            <el-radio :label="item.value">
              {{ item.label }}
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="hide">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { batchUpdateWhetherReward } from "@/api/products/product";
import { getWhetherRewardData } from "@/views/products/product/components/data";

export default {
  name: 'retailStoreDialog',
  data() { 
    return {
      visible: false,
      loading: false,
      productName: '',
      form: {
        ids: [],
        whetherReward: '',
      },
      rules: {
        whetherReward: [
          { required: true, message: '请选择分销奖励', trigger: 'change' },
        ],
      },
      whetherRewardData: getWhetherRewardData()
    }
  },
  methods: {
    show(row) {
      if (this.$refs.form) this.$refs.form.resetFields()
      this.row = row.productName
      this.form = {
        ids: [row.id],
        whetherReward: row.whetherReward?.code || 'N',
      }
      this.visible = true
    },
    hide() {
      this.visible = false
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.loading = true
        batchUpdateWhetherReward(this.form).then(res => {
          this.$emit('submitSuccess')
          this.hide()
        }).finally(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
