import store from '@/store'
import { getToken, getRefreshToken, getExpire } from '@/utils/auth'
import { MessageBox } from 'element-ui'

// 请求队列管理
let isRefreshing = false // 是否正在刷新token
let failedQueue = [] // 挂起的请求队列

// 处理队列中的请求
const processQueue = (error, token = null) => {
  console.log(failedQueue, error, '====================failedQueue');

  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error)
    } else {
      prom.resolve(token)
    }
  })

  failedQueue = []
}

// 修复URL重复问题的辅助函数
const fixDuplicateApiUrl = (config) => {
  if (config && config.url) {
    console.log('原始URL:', config.url, 'baseURL:', config.baseURL)

    // 如果URL以/api/api开头，移除重复的/api
    if (config.url.startsWith('/api/api/')) {
      config.url = config.url.replace('/api/api/', '/api/')
      console.log('修复重复/api后的URL:', config.url)
    }

    // 如果baseURL存在且URL已经包含baseURL，则移除URL中的baseURL部分
    if (config.baseURL && config.url.startsWith(config.baseURL)) {
      config.url = config.url.substring(config.baseURL.length)
      // 确保URL以/开头
      if (!config.url.startsWith('/')) {
        config.url = '/' + config.url
      }
      console.log('移除baseURL后的URL:', config.url)
    }

    console.log('最终URL:', config.url)
  }
  return config
}

// 检查token是否即将过期（提前5分钟刷新）
const isTokenExpiringSoon = () => {
  const expire = getExpire()
  if (!expire) return false
  const currentTime = new Date().getTime()
  const fiveMinutes = 1 * 60 * 1000 // 5分钟

  return (expire - currentTime) < fiveMinutes
}

// 刷新token
const refreshToken = () => {
  return store.dispatch('user/reloadToken')
}

// 处理token过期的响应拦截器
const handleTokenExpired = (error, axiosInstance) => {
  const tokenExpiredCodes = [400, 40001, 50012, 50014, 40005, 40007, 40000, 40003]
  const isTokenExpiredError = error.response &&
    (error.response.status === 401 ||
      (error.response.data && tokenExpiredCodes.includes(error.response.data.code)))

  if (!isTokenExpiredError) {
    return Promise.reject(error)
  }

  // 如果正在刷新token，将请求加入队列
  if (isRefreshing) {
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject })
    }).then(token => {
      error.config.headers['token'] = token
      // 修复URL重复问题
      const fixedConfig = fixDuplicateApiUrl({ ...error.config })
      return axiosInstance(fixedConfig)
    }).catch(err => {
      return Promise.reject(err)
    })
  }

  // 开始刷新token
  isRefreshing = true
  const refreshTokenValue = `Bearer ${getRefreshToken()}`

  if (refreshTokenValue) {
    return refreshToken().then(() => {
      isRefreshing = false
      processQueue(null, `Bearer ${getToken()}`)
      // 重新发送原请求
      error.config.headers['token'] = `Bearer ${getToken()}`
      // 修复URL重复问题
      const fixedConfig = fixDuplicateApiUrl({ ...error.config })
      return axiosInstance(fixedConfig)
    }).catch(refreshError => {
      isRefreshing = false
      processQueue(refreshError, null)
      // 刷新失败，提示用户重新登录
      MessageBox.confirm('登录状态已失效，请重新登录', {
        confirmButtonText: '前往登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      return Promise.reject(refreshError)
    })
  } else {
    // 没有refreshToken，直接提示登录
    isRefreshing = false
    MessageBox.confirm('登录状态已失效，请重新登录', {
      confirmButtonText: '前往登录',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      store.dispatch('user/resetToken').then(() => {
        location.reload()
      })
    })
    return Promise.reject(new Error('No refresh token available'))
  }
}

// 处理响应数据中的token过期
const handleResponseTokenExpired = (response, axiosInstance) => {
  const res = response.data
  const tokenExpiredCodes = [400, 40001, 50012, 50014, 40005, 40007, 40000, 40003]

  if (res.code !== 0 && tokenExpiredCodes.includes(res.code)) {
    // 如果正在刷新token，将请求加入队列
    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject })
      }).then(token => {
        response.config.headers['token'] = token
        // 修复URL重复问题
        const fixedConfig = fixDuplicateApiUrl({ ...response.config })
        return axiosInstance(fixedConfig)
      }).catch(err => {
        return Promise.reject(err)
      })
    }

    // 开始刷新token
    isRefreshing = true
    const refreshTokenValue = `Bearer ${getRefreshToken()}`

    if (refreshTokenValue) {
      return refreshToken().then(() => {
        isRefreshing = false
        processQueue(null, `Bearer ${getToken()}`)
        // 重新发送原请求
        response.config.headers['token'] = `Bearer ${getToken()}`
        // 修复URL重复问题
        const fixedConfig = fixDuplicateApiUrl({ ...response.config })
        return axiosInstance(fixedConfig)
      }).catch(error => {
        isRefreshing = false
        processQueue(error, null)
        // 刷新失败，提示用户重新登录
        MessageBox.confirm('登录状态已失效，请重新登录', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
        return Promise.reject(error)
      })
    } else {
      // 没有refreshToken，直接提示登录
      isRefreshing = false
      MessageBox.confirm('登录状态已失效，请重新登录', {
        confirmButtonText: '前往登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      return Promise.reject(new Error('No refresh token available'))
    }
  }

  return response
}

// 请求拦截器中检查token过期
const checkTokenInRequest = async () => {
  // 检查是否有token
  if (store.getters.token) {
    // 检查token是否即将过期
    if (isTokenExpiringSoon() && !isRefreshing) {
      isRefreshing = true
      try {
        await refreshToken()
        isRefreshing = false
        processQueue(null, `Bearer ${getToken()}`)
      } catch (error) {
        isRefreshing = false
        processQueue(error, null)
        // 刷新失败，跳转到登录页
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
        throw error
      }
    }
  }
}

export {
  isRefreshing,
  failedQueue,
  processQueue,
  isTokenExpiringSoon,
  refreshToken,
  handleTokenExpired,
  handleResponseTokenExpired,
  checkTokenInRequest
}
