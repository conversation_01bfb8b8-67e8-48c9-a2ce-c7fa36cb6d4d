<template>
  <div class="detail-wrapper">
    <page-title
      :title="id ? '编辑预售' : '新增预售'">
      <template slot="icon">
        <span
          :class="{'status-box0': form.presellStatus == 'NOT_START', 'status-box1': form.presellStatus == 'PROCEED', 'status-box2': form.presellStatus == 'OBSOLETE', 'status-box3': form.presellStatus == 'FINISHED'}"
          v-if="this.form.presellStatus"></span>
      </template>
      <template>
        <el-button type="primary" v-if="!formDisabled" @click="submit">保存</el-button>
      </template>
    </page-title>
    <el-form label-width="120px" ref="ruleForm" :model="form" :rules="rules" :disabled="formDisabled">
      <div class="detail-items" v-loading="loading">
        <page-module-card title="基础信息">
          <el-form-item label="预售商品：" prop="presellProductRelSaveDTOList">
            <div>
              <el-button type="primary" @click="openProducts">选择商品</el-button>
            </div>
            <div v-if="form.presellProductRelSaveDTOList.length > 0">
              <el-table style="margin-top:15px;" :data="form.presellProductRelSaveDTOList" border class="product-table">
                <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
                <el-table-column label="主图" align="center" width="80" class-name="img-cell">
                  <template slot-scope="{row}">
                    <img :src="splitString(row.pictIdS)" width="50px" v-if="row.pictIdS">
                    <img :src="pictImg" width="50px" v-if="row.pictIdS == null || row.pictIdS == ''">
                  </template>
                </el-table-column>
                <el-table-column label="ERP商品编码/仓库" prop="productCode" width="200px">
                  <template v-slot="{row}">
                    <span>编码：{{row.erpCode || '无'}}</span> <br />
                    <span>仓库：{{ row.warehouseName || '无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="productName" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="规格" prop="spec" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="生产厂家" prop="manufacturer" width="200px" show-overflow-tooltip></el-table-column>
                <el-table-column label="销售价" prop="salePrice" width="100" />
                <el-table-column label="最低起订量" prop="minBuyQuantity" width="100" />
                <el-table-column label="成本价" prop="costPrice" width="100" />
                <el-table-column label="库存" prop="stockQuantity" width="100" />
                <el-table-column label="操作" prop="stockQuantity" width="100" fixed="right" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="deleteRowGoods(scope)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item
            label="销售状态："
            required
            prop="presellEnabled"
          >
            <el-radio-group v-model="form.presellEnabled">
              <el-radio label="ON">上架</el-radio>
              <el-radio label="OFF">下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="预售销售价：" prop="presellPrice"
            style="width:370px;">
            <el-input v-model="form.presellPrice" placeholder="请输入预售销售价"></el-input>
          </el-form-item>
          <el-form-item label="定金比例：" prop="depositRatio" :rules="[{ type: 'number', min: 1, max: 100, required: true, message: '请填写正确的定金比例，1-100' }]"
                        style="width:370px;">
            <el-input v-model.number="form.depositRatio" placeholder="请填写定金比例">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="预售时间：" prop="preSellStartAndEndTime" :rules="[{ required: true, message: '请选择预售时间', trigger: 'blur' }]">
            <el-date-picker type="datetimerange" @input="dataPickerChange" range-separator="至" v-model="form.preSellStartAndEndTime"
              value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="是否可退定金："
            prop="depositRefundable"
            required
            :rules="[{ required: true, message: '请选择是否可退定金', trigger: 'blur' }]"
          >
            <el-radio-group v-model="form.depositRefundable">
              <el-radio label="Y">是</el-radio>
              <el-radio label="N">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="尾款支付时间：" prop="finalBalancePayStartAndEndTime" :rules="[{ required: true, message: '尾款支付时间', trigger: 'blur' }]">
            <el-date-picker type="datetimerange" @input="dataPickerChange" range-separator="至" v-model="form.finalBalancePayStartAndEndTime"
                            value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </el-form-item>
          <el-radio-group v-model="form.presellSendGoodsRule">
            <el-form-item label="预计发货时间：" prop="sendGoodsStartAndEndTime" :rules="[{ required: form.presellSendGoodsRule === 'TIME_RULE', message: '请选择预计发货时间', trigger: 'blur' }]">
              <div class="block-radio block-radio-top">
                <el-radio label="TIME_RULE">
                  <el-date-picker type="datetimerange" @input="dataPickerChange" range-separator="至" v-model="form.sendGoodsStartAndEndTime"
                                  value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间"
                                  :default-time="['00:00:00', '23:59:59']">
                  </el-date-picker>
                </el-radio>
              </div>
            </el-form-item>
            <div class="block-radio">
              <el-form-item prop="finalBalanceSendGoodsDays" :rules="[{ required: form.presellSendGoodsRule === 'DAY_RULE', type:form.presellSendGoodsRule === 'DAY_RULE' ? 'integer' : '', min: 0, message: '请填写整数型天数', trigger: 'blur' }]" style="display: inline-block;">
                <el-radio label="DAY_RULE">
                  尾款后 <el-input v-model.number="form.finalBalanceSendGoodsDays" placeholder="填写天数" style="width: 100px;"></el-input> 天发货
                </el-radio>
              </el-form-item>
            </div>
          </el-radio-group>
          <el-form-item
            label="是否已到货："
            required
            prop="arriveGoodsStatus"
            :rules="[{ required: true, message: '请选择是否已到货' }]"
          >
            <el-radio-group v-model="form.arriveGoodsStatus">
              <el-radio label="Y">是</el-radio>
              <el-radio label="N">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
        <page-module-card title="活动规则">
          <el-form-item label="参与人条件：" prop="limitObjectType" :rules="[{  required: true, message: '请选择以下选项' }]">
            <el-radio-group v-model="form.limitObjectType">
              <div class="block-radio block-radio-top">
                <el-radio label="NONE">不限制，所有客户可参与</el-radio>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_TYPE">
                  <span>指定客户类型可参与</span>
                </el-radio>
                <el-checkbox-group style="margin-top: 10px;" v-model="form.merchantTypeIds"
                  v-if="merchantList.length > 0 && form.limitObjectType === 'CUSTOMER_TYPE' ">
                  <el-checkbox v-for="(item) in merchantList" :key="item.id" :label="item.id">
                    {{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
              <div class="block-radio">
                <el-radio label="CUSTOMER_GROUP">指定客户分组可参与</el-radio>
                <div style="margin: 15px 0;" v-if="form.limitObjectType == 'CUSTOMER_GROUP'">
                  <el-button type="primary" @click="showAdd = true">选择客户分组</el-button>
                </div>
                <el-table v-if="groupTableData && form.limitObjectType == 'CUSTOMER_GROUP' " :data="groupTableData"
                  border>
                  <el-table-column prop="name" label="客户分组" width="234" />
                  <el-table-column prop="customerNumber" label="客户数量" width="120" />
                  <el-table-column label="操作" width="52">
                    <template slot-scope="scope">
                      <el-button @click="deleteRow(scope.$index)" type="text">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-radio-group>
          </el-form-item>
        </page-module-card>
      </div>
    </el-form>
    <div v-if="productVisible">
      <products-modal 
          :productVisible="productVisible" 
          ref="products-modal"  
          @closeProductDia="closeProductDia" 
          @change="handleAddProducts" 
          :checkList="form.presellProductRelSaveDTOList" 
          :limit='1'>
      </products-modal>
    </div>
    <add-group :visible="showAdd" v-bind:saleMerchantId="saleMerchantId" @changeShow="changeAddUser" :select-data="groupTableData"/>
  </div>
</template>

<script>
  import request from '@/utils/request'
  import addGroup from '../components/addGroup.vue'
  // import ProductsModal from '../components/productsModel'
  import ProductsModal from "@/components/eyaolink/productModel/index"
  import {
    merchantGroupListNew
  } from "@/api/group";

  import productImg from "@/assets/product.png";

  import {
    postGoodsList
  } from "@/api/limitTime"

  import {
    getPreSellInfo,
    addPreSell,
    editPreSell,
    searchRepeatProductPreSell
  } from '@/api/promotionCenter/presell'

  export default {
    components: {
      ProductsModal,
      addGroup,
    },
    data() {
      const validatePrice = (rule, value, callback) => {
        if (parseFloat(value) <= 0) {
          callback(new Error('请输入正确的预售销售价，大于0的数字'));
        } else {
          callback();
        }
      };
      const validateList = (rule, value, callback) => {
        if(value.length === 0){
          return callback(new Error('商品不能为空'));
        } else {
          callback();
        }
      }
      return {
        quitShow:false,
        pictImg: productImg,
        activeType:'preSell',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 3600 * 1000 * 24;
          }
        },
        id: '',
        loading: false,
        showAdd: false,
        saleMerchantId: '', //经销商id
        merchantList: [], // 客户类型列表
        groupTableData: [], // 已选分组数据
        form: {
          arriveGoodsStatus: '',
          depositRatio: '', // 定金比例
          depositRefundable: '', // 是否可退
          finalBalancePayEndTime: '',
          finalBalancePayStartTime: '',
          finalBalancePayStartAndEndTime: [],
          finalBalanceSendGoodsDays: '',
          presellEnabled: 'ON', // 上下架
          presellEndTime: '',
          presellStartTime: '',
          preSellStartAndEndTime: [],
          presellPrice: '',
          saleMerchantId: 0,
          sendGoodsEndTime: '',
          sendGoodsStartTime: '',
          sendGoodsStartAndEndTime: [],
          limitBuyNum: '',
          limitBuyType: 'N',
          limitObjectType: 'NONE', //指定人类型 NONE,CUSTOMER_TYPE,CUSTOMER_GROUP,CUSTOMER,UN_CUSTOMER
          merchantTypeIds: [], //客户类型
          merchantGroupIds: [], //分组集合
          presellProductRelSaveDTOList:[],
          presellSendGoodsRule: 'TIME_RULE'
        },
        rules: {
          presellPrice: [
            { required: true, pattern: /^\d+(\.\d+)?$/, message: '请输入正确的预售销售价，大于0的数字', trigger: 'blur' },
            { required: true, validator: validatePrice, trigger: 'blur' },
          ],
          presellProductRelSaveDTOList: [{required: true, validator: validateList, trigger: 'change'}]
        },
        notSearchId: '',
        productVisible: false,
        timer: null,
        formDisabled: false, // 表单是否可编辑
      }
    },
    created() {
      this.id = this.$route.query.id;
      this.getMerchantType()
      if (this.id) {
        this.getDetail()
      }
    },
    watch:{
    },
    methods: {
      /**
       * @describe 关闭弹窗
       */
      closeProductDia(){
        this.productVisible = false;
      },
      dataPickerChange(){
        this.$forceUpdate();
      },
      //打开商品
      openProducts() {
        if (this.id) {
          this.notSearchId = this.id
        } else {
          this.notSearchId = ''
        }
        this.productVisible = true
      },
      // 获取详情
      async getDetail() {
        this.loading = true
        if (this.id) {
          let {
            data
          } = await getPreSellInfo(this.id);
          this.form = {
            ...this.form,
            ...data,
            limitObjectType: data.limitObjectType.code,
            merchantTypeIds: data.merchantTypeIds || [],
            merchantGroupIds: data.merchantGroupIds || [],
            finalBalancePayStartAndEndTime: [ data.finalBalancePayStartTime, data.finalBalancePayEndTime ],
            preSellStartAndEndTime: [ data.presellStartTime, data.presellEndTime ],
            sendGoodsStartAndEndTime: [ data.sendGoodsStartTime || '', data.sendGoodsEndTime || ''],
            depositRefundable: data.depositRefundable.code,
            arriveGoodsStatus: data.arriveGoodsStatus.code,
            presellEnabled: data.presellEnabled.code,
            presellStatus: data.presellStatus.code,
            presellSendGoodsRule: data.presellSendGoodsRule.code
          }
          this.formDisabled = data.presellStatus && data.presellStatus.code == 'PROCEED'
          if (data.merchantGroupIds) {
            this.getGroupList();
          }
          this.getCheckGoodsList()
        }
      },
      // 获取已有客户分组列表
      async getGroupList() {
        this.loading = true;
        const query = {
          model: {}
        }
        const {
          data
        } = await merchantGroupListNew(query);
        this.loading = false;
        this.groupTableData = data.records.filter((items) => {
          if (this.form.merchantGroupIds.includes(items.id)) {
            return {
              items
            }
          }
        })
      },
      conflictTest() {
        const { preSellStartAndEndTime, presellProductRelSaveDTOList } = this.form
        const params = {
          startTime: preSellStartAndEndTime[0],
          endTime: preSellStartAndEndTime[1],
          'productIds[]': presellProductRelSaveDTOList[0].id,
          notSearchId: this.id !== undefined ? this.id : null
        }
        return searchRepeatProductPreSell(params)
      },
      // 提交表单
      submit() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            if (this.form.presellProductRelSaveDTOList.length === 0) {
              this.$message.warning('请选择商品！')
              return false
            }
            // 选择指定客户类型
            if (this.form.limitObjectType === 'CUSTOMER_TYPE') {
              this.form.merchantGroupIds = []
            }
            if (this.form.limitObjectType === 'CUSTOMER_GROUP' || this.form.limitObjectType === 'NONE') {
              this.form.merchantTypeIds = []
            }
            if (this.form.limitBuyType === 'N') {
              this.form.limitBuyNum = ''
            }
            // 是否有id
            if (this.id) {
              this.form.id = this.id
            } else {
              delete this.form.id
            }
            this.loading = true;
            const { finalBalancePayStartAndEndTime, preSellStartAndEndTime, sendGoodsStartAndEndTime, presellProductRelSaveDTOList } = this.form
            const form = {
              ...this.form,
              finalBalancePayStartTime: finalBalancePayStartAndEndTime[0],
              finalBalancePayEndTime: finalBalancePayStartAndEndTime[1],
              presellStartTime: preSellStartAndEndTime[0],
              presellEndTime: preSellStartAndEndTime[1],
              sendGoodsStartTime: (sendGoodsStartAndEndTime.length > 0 && sendGoodsStartAndEndTime[0]) || '',
              sendGoodsEndTime: (sendGoodsStartAndEndTime.length && sendGoodsStartAndEndTime[1]) || '',
              productId: presellProductRelSaveDTOList[0].id,
              presellProductRelSaveDTOList: presellProductRelSaveDTOList.map(product => {
                return {
                  productId: product.id
                }
              }),
              activityName: `预售活动-${+new Date()}`
            };
            ['finalBalancePayStartAndEndTime', 'preSellStartAndEndTime', 'sendGoodsStartAndEndTime', 'product'].forEach((key) => {
              delete form[key]
            })
            this.quitShow = true
            this.conflictTest().then((res) => {
              if (res.code === 0) {
                const method = this.id ? editPreSell(form) : addPreSell(form)
                method.then(() => {
                  this.loading = false;
                  this.$message.success('保存成功')
                  this.timer = setTimeout(() => {
                    this.$router.push({
                      path: '/promotion/presell'
                    })
                  }, 500)
                }).catch(() => {
                  this.loading = false
                })
              } else {
                this.loading = false
              }
            }).catch(() => {
              this.loading = false
            })
          } else {
            this.loading = false;
          }
        });
      },
      // 删除选中客户组
      deleteRow(i) {
        this.groupTableData.splice(i, 1)
      },
      async deleteRowGoods(scope) {
        let index = this.form.presellProductRelSaveDTOList.findIndex((item) => item.id === scope.row.id)
        this.form.presellProductRelSaveDTOList.splice(index, 1)
        this.$refs['products-modal'].isSelect = true;
      },
      // 改变客户分组回调
      changeAddUser(data) {
        this.groupTableData = data;
        this.form.merchantGroupIds = data.map((items) => items.id)
        this.showAdd = false
      },
      // 选择商品后回调
      handleAddProducts(list) {
        this.getCheckGoodsList(list)
      },
      // 获取已有的产品列表
      async getCheckGoodsList(ids) {
        const formData = new FormData()
        let idArr = ids ? ids : [this.form.product].map((items) => items.productId)
        formData.append('ids', idArr.toString())
        let {
          data
        } = await postGoodsList(formData)
        this.loading = false
        this.form.presellProductRelSaveDTOList = data
      },
      // 获取客户类型
      async getMerchantType() {
        const {
          data
        } = await request.get('merchant/admin/merchantType', {})
        this.merchantList = data
      },
      splitString(val) {
        return String(val).split(',')[0]
      }
    },
    beforeRouteLeave(to, from, next) {
      if(this.quitShow) {
        next();
      } else {
        if (confirm('确定退出吗？如果退出，您输入的内容将不会被保存') === true){
          next()
        }else{
          next(false)
        }
      }
    },
    beforeDestroy() {
      clearTimeout(this.timer);
    }
  }
</script>

<style lang="scss">
  .product-table {
    .el-input-number.is-controls-right .el-input__inner {
      padding-right: 15px;
    }
  }

  .limit-form .el-form-item {
    margin-bottom: 0;
  }

  .detail-wrapper {
    .el-pager li {
      width: 0;
    }

    .page-row-left {
      float: left;
    }

    .page-row-leftBlock {
      display: flex;

      .selectBlock {
        display: block;
        width: 14px;
        height: 14px;
        margin-right: 8px;

        img {
          width: 14px;
        }
      }
    }

    .page-row-right {
      float: right;
    }

    .status-box0 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box1 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box2 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/Icon_Revok.png') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }

    .status-box3 {
      width: 64px;
      height: 32px;
      display: inline-block;
      background: url('../../../assets/imgs/coupon/<EMAIL>') no-repeat;
      background-size: cover;
      margin-left: 12px;
    }
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    background: #f7f7f8;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 20px;
    color: #8c939d;
    line-height: 100px;
    text-align: center;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .detail-item {
    border: none !important;
  }

  .detail-tit {
    display: flex;
    align-items: center;
    margin: 0;
  }

  .inline-input {
    width: 80px;
    margin: 0 10px;
  }

  .inline-item {
    display: inline-block;
  }

  .block-radio {
    margin-bottom: 16px;

    &-top {
      margin-top: 11px;
    }

    &-none {
      margin: 0;
    }
  }

  .detail {
    &-header {
      // width: 100%;
      margin: 0 12px;
      padding: 19px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ddd;

      p {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  .tips {
    color: #999999;
    margin: 0;

    &-btn {
      border: none;
      margin: 0;
      padding: 0;
    }
  }

  .no-button {

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
  }
</style>
