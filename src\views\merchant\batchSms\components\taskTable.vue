<template>
  <div class="table_wrapper">
    <div class="tabs" style="padding: 5px 10px 0 0;background-color: #fff">
      <tabs-layout v-model="query.model.sendStatus" ref="tabs-layout" :tabs="taskSendStatusList" @change="search">
        <template slot="button">
          <el-button size="small" @click="startTask" v-if="taskStatus === 'NOT_STARTED'">开始任务</el-button>
          <template v-if="taskStatus === 'PROGRESS'">
            <el-button size="small" @click="pauseTask">暂停任务</el-button>
            <el-button size="small" @click="stopTask">结束任务</el-button>
          </template>
        </template>
      </tabs-layout>
    </div>
    <el-form :model="query.model" inline label-width="0">
      <el-form-item label="">
        <el-input v-model="query.model.name" placeholder="请输入客户名称" clearable style="width: 280px;" />
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="query.model.createTime"
          range-separator="-"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          start-placeholder="开始时间"
          :default-time="['00:00:00', '23:59:59']"
          unlink-panels
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-button type="primary" size="small" @click="search">搜索</el-button>
      <el-button size="small" @click="reset">重置</el-button>
    </el-form>
    <el-table :data="list" style="width: 100%;" v-loading="loading" border>
      <el-table-column prop="name" label="客户姓名">
        <template slot-scope="{row}">{{ row.name || '-' }}</template>
      </el-table-column>
      <el-table-column prop="phone" label="客户号码" width="120px">
        <template slot-scope="{row}">{{ row.phone || '-' }}</template>
      </el-table-column>
      <el-table-column prop="createTime" label="发送时间" width="160px">
        <template slot-scope="{row}">{{ row.sendTime || '-' }}</template>
      </el-table-column>
      <el-table-column prop="sendStatus" label="发送状态" width="90px">
        <template slot-scope="{row}">
          <span>{{ row.sendStatus && row.sendStatus.desc || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sendContent" label="发送内容" width="450px" />
    </el-table>
    <div class="page" v-if="list.length > 0">
      <el-pagination
        style="display: inline-block;"
        background
        @current-change="pageChange"
        :current-page="query.current"
        layout="total, prev, pager, next, jumper"
        :page-size="query.size"
        :total="listTotal"
      />
    </div>
  </div>
</template>

<script>
import { getSmsPersonList, setPauseTask, setStartTask, setStopTask } from "@/api/retailStore";
import { deepClone } from "@/utils";

export default {
  name: 'taskTable',
  props: {
    taskStatus: {
      type: String,
    }
  },
  data() {
    return {
      taskSendStatusList: [ // 发送状态列表
        { name: '待发送', value: 'PENDING' },
        { name: '发送成功', value: 'SUCCESS' },
        { name: '发送失败', value: 'FAIL' }
      ],
      query: {
        current: 1,
        size: 10,
        model: {
          taskId: '',
          name: '',
          createTime: [],
          sendStatus: 'PENDING'
        }
      },
      loading: false,
      list: [],
      listTotal: 0
    }
  },
  methods: {
    reset() {
      this.clear()
      this.setSmsPersonList()
    },
    clear() {
      this.query.current = 1
      this.query.model.name = ''
      this.query.model.createTime = []
      this.list = []
      this.listTotal = 0
    },
    search() {
      this.query.current = 1
      this.setSmsPersonList()
    },
    init(taskId) {
      this.query.model.taskId = taskId
      this.reset()
    },
    setSmsPersonList() {
      this.loading = true
      let query = deepClone(this.query)
      const createTime = query.model.createTime
      let startTime = undefined
      let sendTime = undefined
      if (Array.isArray(createTime) && createTime.length === 2) {
        startTime = createTime[0]
        sendTime = createTime[1]
      }
      query.model = { ...query.model, startTime, sendTime }
      getSmsPersonList(query).then(res => {
        this.list = res.records
        this.listTotal = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    pageChange(page) {
      this.query.current = page
      this.setSmsPersonList()
    },
    startTask() {
      this.$confirm('确定要开始任务吗', '开始任务', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        setStartTask(this.query.model.taskId).then(() =>{
          this.$message.success('开始任务成功')
          this.$emit('handleTaskSuccess')
        })
      }).catch(() => {})
    },
    pauseTask() {
      this.$confirm('确定要暂停任务吗', '暂停任务', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        setPauseTask(this.query.model.taskId).then(() =>{
          this.$emit('handleTaskSuccess')
        })
      }).catch(() => {})
    },
    stopTask() {
      this.$confirm('确定要结束任务吗', '结束任务', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        setStopTask(this.query.model.taskId).then(() =>{
          this.$emit('handleTaskSuccess')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.table_wrapper {
  box-sizing: border-box;
  padding: 10px 15px;
  background-color: #fff;
}
.page {
  text-align: center;
  margin-top: 10px;
}
</style>
