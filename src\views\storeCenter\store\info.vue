<template>
  <div class="info">
    <section class="info-qrcode">
      <div class="qrcode">
        <!-- <p>微信扫码访问页面效果</p>
        <img src="data:image/png;base64,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" alt=""> -->
      </div>
    </section>
    <mini-template ref="childMini" :header="queryForm" :detail="productList" :active="activeClass"></mini-template>
    <div class="info-edit">
      <div class="info-edit__header">
        <h3>店铺信息</h3>
        <el-button type="primary" @click="postShopHeader">保存并发布</el-button>
      </div>
      <div class="store">
        <p class="title">背景图</p>
        <p class="tips">建议尺寸：750x375，尺寸不匹配时，图片将被压缩货拉伸以铺满画面</p>
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          :action="uploadParams.action"
          :headers="uploadParams.headers"
          :data="uploadParams.data"
          :on-success="handleUploadSuccess"
          :on-remove="handleUploadRemove"
          >
          <img v-if="queryForm.picUrl" :src="queryForm.picUrl" class="avatar" width="80">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <div class="info-edit__flex">
          <p class="title">背景渐变</p>
          <div>
            <el-radio-group v-model="queryForm.backgroundGradient">
                <el-radio label="Y">渐变</el-radio>
                <el-radio label="N">无渐变</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="info-edit__flex">
          <p class="title">起配金额</p>
          <el-button type="text" @click="goAccount">设置</el-button>
        </div>
        <div class="info-edit__flex">
          <p class="title">店铺公告</p>
          <el-input type="textarea" rows="3" v-model="queryForm.notice"></el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MiniTemplate from "./components/mini"
import { getToken } from '@/utils/auth'
import { getPageShop, postShopHeader } from "@/api/store"
import { getLocalUser } from '@/utils/local-user'
export default {
  components: {
    MiniTemplate
  },
  data () {
    return {
      queryForm: {
        backgroundGradient: 'Y',
        picUrl: '',
        notice: ''
      },
      productList: [],
      uploadParams: {
        action: process.env.VUE_APP_BASE_API + '/file/file/upload',
        headers: {
          Authorization: 'Basic YWRtaW5fdWk6YWRtaW5fdWlfc2VjcmV0',
          token: `Bearer ${getToken()}`
        },
        data: {
          pur: 0,
          sale: 0,
          tenant: 0,
          userid: getLocalUser().userId,
          folderId: 0
        }
      },
      activeClass: 0,
      listLoading: false,
      timer: null
    }
  },
  created () {
    this.getPageInfo()
  },
  methods: {
    goAccount () {
      this.$router.push({path: '/settingCenter/paySetting/index'})
    },
    // 获取店铺编辑详情
    async getPageInfo () {
      this.listLoading = true
      const {data} = await getPageShop();
      this.queryForm = {...this.queryForm, ...data.header}
      this.queryForm.backgroundGradient = data.header==null?"Y":data.header.backgroundGradient.code
      this.productList = data.groupList
      this.listLoading = false
      this.timer = setTimeout(() => {
        this.$refs.childMini.init()
      })
    },
    // 保存店铺信息
    async postShopHeader () {
      let params = {
        ...this.queryForm
      }

      this.listLoading = true

      const { data } = await postShopHeader(params)
      this.$message.success('保存成功')
      this.getPageInfo()
    },
    handleUploadRemove (file, fileList) {
      this.queryForm.picUrl = ''
    },
    // 上传成功的回调
    handleUploadSuccess (response) {
      this.queryForm.picUrl = response.data.url
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="scss" scoped>
  .info{
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }
      .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
      }
      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        border: 1px solid #8c939d;
      }
      .avatar {
        width: 80px;
        height: 80px;
        display: block;
      }
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    &-qrcode{
      width: 20%;
      .qrcode{
        width: 200px;
        background: #fff;
        text-align: center;
        margin: 0 auto;
        p{
          margin: 0;
          padding: 10px 0 0 ;
        }
        img{
          width: 100%;
        }
      }
    }

    &-edit{
      width: 400px;
      height: 100vh;
      background: #fff;
      &__header{
        padding: 10px;
        border-bottom: 1px solid #eeeeee;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .store{
        padding: 15px 20px;
      }
      &__flex{
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .title{
        font-size: 14px;
        width: 120px;
      }
      .tips{
        font-size: 12px;
        color: #6666666c;
      }
    }
  }
</style>
