<!--
 * @: 收货地址
-->
<template>
   <div>
    <el-table :data="tableDate" style="width: 100%" border>
      <el-table-column prop="name" label="收货地址类型" min-width="220">
        <template slot-scope="{ row }" v-if="row">
          <el-radio-group v-if="row.isEdit" v-model="row.addressType.code">
            <el-radio label="PUR">采购下单</el-radio>
            <el-radio label="POINT">积分兑换</el-radio>
          </el-radio-group>
          <span v-else>{{ row.addressType.code === 'PUR' ? '采购下单' : '积分兑换'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="收货人姓名" min-width="120">
        <template slot-scope="{ row }" v-if="row">
          <el-input v-if="row.isEdit" placeholder="请输入收货人" v-model="row.name"></el-input>
          <span v-else>{{row.name}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="mobilPhone" label="联系手机" min-width="170">
        <template slot-scope="{ row }" v-if="row">
          <el-input v-if="row.isEdit" placeholder="请输入联系手机" v-model="row.mobilPhone"></el-input>
          <span v-else>{{row.mobilPhone}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fixedPhone" label="联系电话" min-width="170">
        <template slot-scope="{ row }" v-if="row">
          <el-input v-if="row.isEdit" placeholder="请输入联系电话" v-model="row.fixedPhone"></el-input>
          <span v-else>{{row.fixedPhone}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="region" label="收货区域" min-width="260">
        <template slot-scope="{ row }" v-if="row">
          <el-cascader :disabled="!row.isEdit" ref="addr" style="width:100%" v-model="row.address" placeholder="请选择所在区域" :props="{ value: 'id', label: 'label'}" :options="areasTree" clearable>
          </el-cascader>
        </template>
      </el-table-column>
      <el-table-column prop="detailedAddress" label="详细地址" min-width="220">
        <template slot-scope="{ row }" v-if="row">
          <el-input v-if="row.isEdit" placeholder="请输入详细地址" v-model="row.detailedAddress"></el-input>
          <span v-else>{{row.detailedAddress}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="isDefault" align="center" width="250" label="是否默认">
        <template slot-scope="{row}" v-if="row">
          <span v-if="row.isEdit && tableDate.length > 1">
            <el-radio v-model="row.defaultOrNot" label="Y" @change="handleSetDefault(row)">默认</el-radio>
          </span>
          <span v-else :style="row.defaultOrNot === 'Y'? '' : 'color:#ff0066'"> {{row.defaultOrNot == 'Y'? '默认' : '非默认'}} </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="isDefault" align="center" width="250" label="是否默认">
        <template slot-scope="scope" v-if="scope.row">
          <span v-if="scope.row.isEdit">
            <el-radio v-model="scope.row.defaultOrNot" label="Y" @change="handleSetDefault(scope.row,scope.$index)">默认</el-radio>
          </span>
          <span v-else :style="scope.row.defaultOrNot === 'Y'? '' : 'color:#ff0066'"> {{scope.row.defaultOrNot == 'Y'? '默认' : '非默认'}} </span>
        </template>
      </el-table-column>
      <el-table-column prop="region" align="center" width="250" label="是否启用">
        <template slot-scope="{row}" v-if="row">
          <span v-if="row.isEdit">
            <el-radio v-model="row.isOpen" label="Y">启用</el-radio>
            <el-radio v-model="row.isOpen" label="N">禁用</el-radio>
          </span>
          <span v-else :style="row.isOpen == 'Y'? '' : 'color:#ff0066'"> {{row.isOpen == 'Y' ? '启用' : '禁用'}} </span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" label="操作" width="130" class="itemAction">
        <template slot-scope="scope" v-if="scope.row">
          <el-button :disabled="$route.path.indexOf('clientDetail') != -1" type="text" @click="editAddrItem(scope.row,scope.$index)" v-show="!scope.row.isEdit">编 辑</el-button>
          <el-button type="text" @click="canceladdrEdit(scope.row, scope.$index)" v-show="scope.row.isEdit">取 消</el-button>
          <el-button type="text" @click="confirmaddrEdit(scope.row, scope.$index)" v-show="scope.row.isEdit">确 定</el-button>
          <el-button :disabled="$route.path.indexOf('clientDetail') != -1" type="text" @click="delAddr(scope.row,scope.$index)"  v-show="!scope.row.isEdit">删 除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="addBox">
      <el-button :disabled="$route.path.indexOf('clientDetail') != -1" @click="handleAddress" type="text"  slot="reference">+ 添加收货地址</el-button>
    </div>
   </div>
</template>


<script>
import {
  getAddrList,
  adaddr,
  deladdr,
  editaddr,
  areas,
} from "@/api/businessList";
export default {
   //import引入的组件
   components: {},
   props: {
    addrtableDate: {
      type: Array,
      required: true,
    },
    id: {
      type: String,
      required: false,
    },
    edit: {
      type: Array
    },
    delete: {
      type: Array
    },
    add: {
      type: Array
    },
    defaultAddr: {
      type: Array,
      default(){
        return ["110000","110100","110101"]
      }
    }
  },
   data() {
      return {
        areasTree: [],
        tableDate: [], // 列表数据
        oldAddrtableDate: [], // 旧的列表数据
      }
   },
   created() {
    // 获取省市区数据
    this.getareas();
    // 获取地址
    this.getaddrList();
   },

   //方法集合
   methods: {
    // 获取省市区数据
    async getareas() {
      let { data } = await areas();
      this.areasTree = data;
    },
    // 获取地址
    async getaddrList() {
      if(this.id) {
        getAddrList(this.id).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            let list = res.data || [];
            list.forEach(element => {
              element.isOpen = element.isOpen.code;
              element.defaultOrNot = element.defaultOrNot.code;
              element.isEdit = false;
              element.address = [element.provinceId, element.cityId, element.countyId];
            });
            this.tableDate = list;
            this.oldAddrtableDate = JSON.parse(JSON.stringify(list));
            console.log('---->',this.tableDate);
          }
        })
      }
    },
    // 编辑按钮
    editAddrItem(row,index) {
      console.log('row,index',row,index);
      let list = JSON.parse(JSON.stringify(this.tableDate));
      list = list.map(item=>{
        item.isEdit = false;
        if(item.id == row.id) {
          item.isEdit = true;
        };
        return item;
      });
      this.tableDate = list;
    },
    // 取消编辑按钮
    canceladdrEdit(row,index) {
      if(row.id){
        this.tableDate = this.tableDate.map(item=>{
          item.isEdit = false;
          return item
        });
      } else {
        this.tableDate.splice(index, 1);
      }
    },
    // 确定编辑按钮
    confirmaddrEdit(row,index) {
      if(!row.name) {
        this.$message.error('请填写收货人姓名');
        return
      };
      // 联系手机和联系电话二者有一个就行
      if(row.mobilPhone == null && row.fixedPhone == null) {
        this.$message.error('请填写联系电话或手机');
        return
      }
      if(row.mobilPhone == '' && row.fixedPhone == '') {
        this.$message.error('请填写联系电话或手机');
        return
      }
      if (!row.detailedAddress) {
        this.$message.error("请填写详细地址");
        return;
      }
      if (row.address.length == 0) {
        this.$message.error("请选择所在区域");
        return;
      }
      row.provinceId = row.address[0];
      row.cityId = row.address[1];
      row.countyId = row.address[2];
      // row.isEdit = false;
      let list = this.tableDate;
      list = list.map(item=>{
        item.isEdit = false;
        return item
      });
      this.tableDate = list;
      let rowResult = {
        ...row,
        addressType: row.addressType?.code || 'PUR'
      }
      if(this.id) {
        // 修改档案
        if(row.id) {
          // 修改已有地址
          editaddr(rowResult).then(res=>{
            if(res.code == 0 && res.msg == 'ok') {
              this.$message.success('修改地址成功');
              let targetObj = JSON.parse(JSON.stringify(res.data));
              targetObj.address = [targetObj.provinceId, targetObj.cityId, targetObj.countyId];
              targetObj.isOpen = targetObj.isOpen && targetObj.isOpen.code == "Y" ? "Y" : "N";
              targetObj.defaultOrNot = targetObj.defaultOrNot.code;
              targetObj.isEdit = false;

              this.$set(this.tableDate, index, targetObj);
              this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
              this.$emit("update:addrtableDate", this.oldAddrtableDate);

              // 重新获取一下地址列表(目的是更新默认状态，如果有多条地址设置了默认，后台会返回最新的默认数据)
              this.getaddrList();
            }
          })
        } else {
          // 新增地址 （修改档案）
          adaddr(rowResult).then(res=>{
            if(res.code == 0 && res.msg == 'ok') {
              this.$message.success('新增地址成功');
              let targetObjAdd = JSON.parse(JSON.stringify(res.data));
              targetObjAdd.address = [targetObjAdd.provinceId, targetObjAdd.cityId, targetObjAdd.countyId];
              targetObjAdd.isOpen = targetObjAdd.isOpen && targetObjAdd.isOpen.code == "Y" ? "Y" : "N";
              targetObjAdd.defaultOrNot = targetObjAdd.defaultOrNot.code;
              targetObjAdd.isEdit = false;

              this.$set(this.tableDate, index, targetObjAdd);
              this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
              this.$emit("update:addrtableDate", JSON.parse(JSON.stringify(this.tableDate)));

              // 重新获取一下地址列表(目的是更新默认状态，如果有多条地址设置了默认，后台会返回最新的默认数据)
              this.getaddrList();
            }
          })
        }
      } else {
        // 新增地址 (新增档案)
        let tableList = JSON.parse(JSON.stringify(this.tableDate));
        this.$emit("update:addrtableDate", tableList);
      }
    },
    // 删除地址按钮
    delAddr(row,index) {
      if(row.id) {
        // 删除的是已经有的地址(提交过的地址)
        deladdr({
          ids: [row.id]
        }).then(res=>{
          if(res.code == 0 && res.msg == 'ok') {
            this.$message.success('已删除该地址');
            this.tableDate.splice(index, 1);
            this.getaddrList();
          }
        })
      } else {
        // 删除本地保存的地址(没有提交过的地址)
        this.tableDate.splice(index, 1);
      };
      this.oldAddrtableDate = JSON.parse(JSON.stringify(this.tableDate));
      this.$emit('update:addrtableDate', this.oldAddrtableDate);
      // 只有一条收货地址的时候，自动设置为默认并启用
      if (this.tableDate.length == 1) {
        this.tableDate[0].defaultOrNot = "Y";
        this.tableDate[0].isOpen = "Y";
        // 调用编辑方案(修改它的默认状态)
        this.confirmaddrEdit(this.tableDate[0]);
      }
    },
    // 添加地址
    handleAddress() {
      // 先判断地址现有地址的长度(来设置是否默认的初始值)
      let isSetDefault = this.tableDate.length === 0 ? 'Y' : 'N';
      let addObj = {
        addressType: {
          code: 'PUR'
        },
        cityId: '',
        countyId: '',
        detailedAddress: '',
        fixedPhone: '',
        isOpen: '',
        defaultOrNot: isSetDefault,
        merchantId: this.$route.query.id || "",
        isEdit: true,
        mobilPhone: "",
        name: "",
        provinceId: "",
        address: this.defaultAddr,
      };
      this.tableDate.push(addObj);
    },
    // 是否默认
    handleSetDefault(row, index){
      // console.log('row',row,index);
      let currentRow = JSON.parse(JSON.stringify(row));
      currentRow.defaultOrNot = "Y";
      this.$set(this.tableDate, index, currentRow);
    },
   },

}
</script>


<style lang='scss' scoped>
.addBox {
  text-align: center;
  line-height: 38px;
  border: 1px solid #dfe6ec;
  border-top: 0;
}
</style>