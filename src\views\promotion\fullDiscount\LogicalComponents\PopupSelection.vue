<template>
    <div :class="$style.container">
        <div :class="$style.btn" v-if="!disabled">
            <el-button type="primary" @click="onSelectClick()">选择{{ label }}</el-button>
            <slot name="header"></slot>
        </div>

        <!-- 已选择数据列表 start -->
        <table-pager :paging="false" :options="tableColumns" :data="data" :is-need-button="!disabled">
            <template v-for="item in tableColumns">
                <template v-if="item.slot" :slot="item.prop">
                    <el-table-column :label="item.label" :width="item.width" :key="item.prop">
                        <template slot-scope="{ row }">
                            <template v-if="!disabled && item.prop !== 'pictIdS'">
                                <el-input v-if="item.edit === 'input'" :disabled="disabled" :class="$style['w-full']"
                                    v-model="row[item.prop]"></el-input>

                                <el-input-number v-else-if="item.edit === 'number'" :disabled="disabled" :min="1"
                                    :max="item.enableMaxCheck ? Number(row[item.maxprop]) : 999999"
                                    :class="$style['w-full']" :controls="false" v-model="row[item.prop]">
                                </el-input-number>
                            </template>
                            <template v-else>{{ row[item.prop] }}</template>
                            <img v-if="item.prop === 'pictIdS'" :src="row[item.name] | imgFilter" width="50px"
                                height="50px" />
                            <div v-if="item.render" v-html="item.render(row)"></div>

                        </template>
                    </el-table-column>
                </template>
            </template>
            <template slot-scope="{ row }">
                <del-button @handleDel="handleDelete" :text="`确认删除该${label}吗？`" btnText="删除"
                    :targetId="row[matchField]">
                </del-button>
            </template>
        </table-pager>
        <!-- 已选择数据列表 end -->


        <select-model :formatQueryParamsBeforeFetch="formatQueryParamsBeforeFetch" :formatResponse="formatResponse" :visible.sync="visible" :tabs="tabs" :data="data" :fields="searchFields" :columns="selectTableColumns_" :limit="limit" @ok="onConfirmSelectClick">
            
        </select-model>


        <!-- 弹窗选择数据 start -->
        <!--  :visible.sync="visible" -->
        <el-dialog append-to-body :title="`选择${label}`" v-dialogDrag width="75%">
            <!-- 搜索表单 start -->
            <im-search-pad v-if="searchFields.length" :has-expand="false" :model="queryParams.model"
                @reset="handleReset" @search="handleSearch">
                <im-search-pad-item v-for="item in searchFields" prop="productCode" :key="item.prop">
                    <el-input v-show="!item.hidden" v-if="item.type === 'input'" v-model="queryParams.model[item.prop]"
                        :placeholder="item.placeholder" />
                    <el-select v-show="!item.hidden" v-if="item.type === 'select'"
                        v-model="queryParams.model[item.prop]">
                        <el-option v-for="opt in item.options" :key="opt.value" :label="opt.text" :value="opt.value">
                        </el-option>
                    </el-select>
                    <region-picker v-show="!item.hidden" v-if="item.type === 'region'" v-model="queryParams.model">
                    </region-picker>

                </im-search-pad-item>
            </im-search-pad>
            <!-- 搜索表单 end -->

            <!-- 数据列表 start -->
            <el-table ref="table" v-loading="dialogLoading" border style="width: 100%"
                @selection-change="handleSelectionChange" @select-all="handleSelectAll" @select="handleSelect"
                :data="dialogTableData">
                <el-table-column width="50" align="center">
                    <template slot="header">
                        <el-button @click="onHeaderSerialNumberColumn" style="color: #333" type="text"
                            icon="el-icon-menu" />
                    </template>
                    <template slot-scope="scope">
                        <span>{{ scope.$index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" type="selection" width="50"></el-table-column>
                <template v-for="item in selectTableColumns_">
                    <el-table-column v-if="item.slot && item.name === 'pictIdS'" :label="item.label" :prop="item.prop"
                        :width="item.width">
                        <template slot-scope="{ row }">
                            <img :src="row[item.name] | imgFilter" width="50px" height="50px">
                        </template>
                    </el-table-column>
                    <el-table-column v-if="item.render" :label="item.label" :prop="item.prop" :width="item.width">
                        <template slot-scope="{ row }">
                            <div v-if="item.render" v-html="item.render(row)"></div>
                        </template>
                    </el-table-column>

                    <el-table-column show-overflow-tooltip v-else :label="item.label" :prop="item.prop">
                    </el-table-column>
                </template>
            </el-table>
            <!-- 数据列表 end -->

            <!-- 分页器 start -->
            <div :class="$style.pagination">
                <div :class="$style.total">已勾选：<span>{{ selects.length }}</span></div>
                <el-pagination :disabled="dialogLoading" background @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" :current-page="queryParams.current"
                    :pageSizes="[10, 20, 50, 100]" :page-size="queryParams.size"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
            <!-- 分页器 end -->


            <template slot="footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onConfirmSelectClick">确定</el-button>
            </template>
        </el-dialog>
        <!-- 弹窗选择数据 end -->
        <field-picker @change="onFieldPickerChange" :show.sync="visibleFieldPicker" :data="selectTableColumns" visible>
        </field-picker>
    </div>
</template>

<script>
import _ from "lodash"

import SelectModel from "@/components/SelectModel/SelectModel.vue"
import DelButton from '@/components/eyaolink/delElButton/index.vue'
import RegionPicker from './RegionPicker.vue'
import FieldPicker from './FieldPicker.vue'


export default {
    props: {
        label: {
            type: String,
            default: ''
        },
        request: {
            type: Function
        },
        limit: {
            type: Number,
            default: 9999
        },

        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        },
        // 数据列表
        data: {
            type: Array,
            default: () => []
        },
        // 匹配字段
        matchField: {
            type: String,
            default: 'id'
        },
        // 搜索表单字段
        searchFields: {
            type: Array,
            default: () => []
        },

        // 选择列表头列
        selectTableColumns: {
            type: Array,
            default: () => []
        },

        // 列表显示表头列
        tableColumns: {
            type: Array,
            default: () => []
        },
        formatQueryParamsBeforeFetch: {
            type: Function,
            default: function (params) {
                return params
            }
        },
        formatResponse: {
            type: Function,
            default: data => data
        },
    },
    watch: {
        selectTableColumns: {
            handler(data) {
                if (data instanceof Array) {
                    this.selectTableColumns_ = _.cloneDeep(data);
                }
            },
            immediate: true,
            deep: true
        },
        // 初始化赋值给 已选择数组（select）供内部使用
        data: {
            handler(data) {
                if (data instanceof Array) {
                    this.selects = _.cloneDeep(data);
                }
            },
            immediate: true,
            deep: true
        },
        searchFields: {
            handler(fields) {
                let model = {};
                fields.forEach(({ prop, value }) => {
                    model[prop] = value
                })
                this.queryParams.model = model;
            },
            immediate: true,
            deep: true
        }

    },
    computed: {
        tabs() {
            const tab = {
                value: '_NUIQUE_', 
                text: `选择${this.label}`,
                request: this.request
            };
            return [tab]
        }
    },
    data() {
        return {
            selectTableColumns_: [],

            visibleFieldPicker: false,
            // 显示弹窗
            visible: false,
            // 弹窗数据列表表头
            // dialogTableColumns: DialogTableColumns,
            // // 弹窗数据列表数据
            dialogTableData: [],
            // 弹窗数据列表数据loading
            dialogLoading: false,


            // 弹窗数据列表搜索表单
            queryParams: {
                current: 1,
                size: 10,
                model: {}
            },
            total: 0,
            // 已选择的数据列表
            // tableColumns: TableColumns,
            // 已选择的数据列表表头
            selects: []
        }
    },
   
    methods: {
        onFieldPickerChange(columns) {
            this.selectTableColumns_ = columns;
        },
        /**
         * 标题序列号按钮
         */
        onHeaderSerialNumberColumn() {

            this.visibleFieldPicker = true
            // if(!this._selectColumnsBackup) {
            //     this._selectColumnsBackup = _.cloneDeep(this.selectTableColumns)
            // }
            // this.selectTableColumns.splice(1, 1)
        },
        /**
         * 点击【选择商品】
         */
        onSelectClick() {
            this.visible = true;
            this.$emit('open')
            // this.fetch();
        },
        /**
         * 删除已选择商品
         */
        handleDelete(id) {
            let matchField = this.matchField;
            let index = this.selects.findIndex(v => v[matchField] === id)
            if (index >= 0) {
                this.selects.splice(index, 1)
                this.$emit('change', this.selects)
                this.$emit('update:data', this.selects)
            }
        },



        /**
         * 切换列表选中状态
         */
        toggleSelection() {
            this.$nextTick(() => {
                let matchField = this.matchField;
                this.selects.forEach(item => {
                    let row = this.dialogTableData.find(v => v[matchField] === item[matchField]);
                    row && this.$refs.table.toggleRowSelection(row, true)
                })
            })
        },

        /**
         * 确认选择商品
         */
        onConfirmSelectClick(selects) {
            this.selects = selects
            if (!this.selects.length) {
                this.$message.warning(`请选择${this.label}`)
                return
            }
            if (this.limit !== null && this.limit < this.selects.length) {
                this.$message.warning(`最多可选择${this.limit}个${this.label}，当前已选择${this.selects.length}个`)
                return
            }
            this.visible = false;
            this.$emit('change', this.selects)
            this.$emit('update:data', this.selects)
            this.$emit('ok', this.selects)
        },



        /**
         * 搜索栏重置按钮
         */
        handleReset() {
            this.queryParams = {
                size: this.queryParams.size,
                current: 1,
                model: {
                    productCode: '',
                    productName: ''
                }
            }
            this.fetch();
        },

        /**
         * 搜索栏搜索按钮
         * */
        handleSearch() {
            this.queryParams = {
                ...this.queryParams,
                current: 1
            }
            this.fetch()
        },

        /**
         * 页码大小变化
         */
        handleSizeChange(size) {
            this.queryParams = {
                ...this.queryParams,
                current: 1,
                size,

            }
            this.fetch();

        },

        /**
         * 页码变化
         */
        handleCurrentChange(current) {
            this.queryParams = {
                ...this.queryParams,
                current
            }
            this.fetch();
        },

        /**
         *  列表[单选/取消单选]事件
         */
        handleSelect(selection, row) {
            let matchField = this.matchField;
            let isAdd = selection.some(v => v[matchField] === row[matchField]);
            if (isAdd) {
                this.selects.push(row)
            } else {
                let index = this.selects.findIndex(v => v[matchField] === row[matchField]);
                if (~index) {
                    this.selects.splice(index, 1)
                }
            }
        },

        /**
         *  列表[全选/取消全选]事件
         */
        handleSelectAll(selection) {
            let isAdd = !!selection.length;
            let matchField = this.matchField;
            if (isAdd) {
                selection.forEach(item => {
                    if (this.selects.every(v => v[matchField] !== item[matchField])) {
                        this.selects.push(item)
                    }
                })
            } else {
                this.dialogTableData.forEach(item => {
                    let index = this.selects.findIndex(v => v[matchField] === item[matchField])
                    if (index >= 0) {
                        this.selects.splice(index, 1)
                    }
                })
            }
        },
        /**
         * 列表商品选择
         */
        handleSelectionChange(data) {
            // this.selects = data
        },



        /**
         * 请求接口获取弹窗商品列表数据
         */
        async fetch() {
            if (!this.request) {
                throw new Error('Missing request method')
            }
            if (this.dialogLoading) return
            try {
                this.dialogLoading = true;
                let { code, data } = await this.request(this.queryParams)
                if (code !== 0) return
                let { records, total } = data;
                this.dialogTableData = records;
                this.total = total;
                this.toggleSelection();
            } catch (error) {
                console.log("🚀 ~ file: PopupSelection.vue ~ line 342 ~ fetch ~ error", error)

            } finally {
                this.dialogLoading = false;
            }
        }

    },
    components: {
        RegionPicker,
        FieldPicker,
        DelButton,
        SelectModel
    }
}
</script>
<style lang="scss" module>
.container {
    width: 100%;

    .btn {
        margin-bottom: 10px;
    }

    li {
        width: fit-content;
    }

    .w-full {
        width: 100%;
    }
}

.pagination {
    position: relative;
    margin-top: 10px;
    text-align: right;

    .total {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);

        span {
            color: red;
        }
    }
}
</style>
