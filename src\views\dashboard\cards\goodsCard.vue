<template>
  <div class="goodsCard">
    <div class="totalContent">
      <div class="item">
        <div class="title">
          商品资质警告数
          <el-tooltip
            content="商品资质警告数包含即将过期数、已过期数"
            placement="bottom-start"
            effect="light"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip>
        </div>
        <div class="number">{{ goodsDetail.qualificationProduct }}</div>
      </div>

      <div class="item">
        <div class="title">
          库存不足
          <el-tooltip
            effect="light"
            content="商品库存不足"
            placement="bottom-start"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip>
        </div>
        <div class="number">{{ goodsDetail.understockProduct }}</div>
      </div>

      <div class="item">
        <div class="title">
          已审核商品数
          <!-- <el-tooltip
            effect="light"
            content="商品资质警告数包含即将过期数、已过期数"
            placement="bottom-start"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip> -->
        </div>
        <div class="number">{{ goodsDetail.passProduct }}</div>
      </div>
      <div class="item">
        <div class="title">
          已上架商品数
          <!-- <el-tooltip
            effect="light"
            content="商品资质警告数包含即将过期数、已过期数"
            placement="bottom-start"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip> -->
        </div>
        <div class="number">{{ goodsDetail.putAwayProduct }}</div>
      </div>
      <div class="item">
        <div class="title">
          已下架商品数
          <!-- <el-tooltip
            effect="light"
            content="商品资质警告数包含即将过期数、已过期数"
            placement="bottom-start"
          >
            <img src="@/assets/home/<USER>" alt="" />
          </el-tooltip> -->
        </div>
        <div class="number">{{ goodsDetail.outProduct }}</div>
      </div>
    </div>
    <div class="cardTitle">
      商品销售排行
      <!-- <el-button type="text">查看更多商品</el-button> -->
    </div>
    <div class="togContent">
      <div class="togtap">
        <el-radio @change="setGoodsList" v-model="togTab" label="1"
          >按销售数量排行</el-radio
        >
        <el-radio @change="setGoodsList" v-model="togTab" label="2"
          >按销售金额排行</el-radio
        >
      </div>
      <selectTime
        :orderTypes="false"
        :Query.sync="query"
      ></selectTime>
    </div>
    <div>
      <el-table
        ref="table"
        v-if="list"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" width="80" label="排名">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="100"
          class="itemAction"
        >
          <template slot-scope="{ row }">
            <el-button type="text"  @click="row"
              >查看详情</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
      <Pagination v-show="total>0" :total="total" :page.sync="current" :limit.sync="pageSize" @pagination="setGoodsList" />
    </div>
  </div>
</template>

<script>
import selectTime from "@/views/dashboard/cards/components/selectTime";
import { formatTime, parseTime } from "@/utils/index";
import { goodsCard, productList } from "@/api/dashboard";
import Pagination from '@/components/Pagination'
const tableTitle = [
  { label: "商品名称", name: "productName", width: "170px" },
  { label: "规格", name: "spec", width: "170px" },
  { label: "生产厂家 ", name: "manufacturer", width: "200px" },
  { label: "商品编码", name: "productNo", width: "120px" },
  { label: "销售数量", name: "salesQuantity", width: "100px" },
  { label: "销售金额", name: "salesAmount", width: "100px" },
];
export default {
  name: 'goodsCard',
  data() {
    return {
      togTab: "1",
      list: [1, 23, 4],
      tableTitle,
      listLoading: false,
      options: [
        {
          value: 1,
          label: "近七天",
        },
        {
          value: 2,
          label: "近三十天",
        },
        {
          value: 3,
          label: "自然周",
        },
        {
          value: 4,
          label: "自然月",
        },
        {
          value: 5,
          label: "自定义",
        },
      ],
      orderType: [
        { value: 1, label: "待审核" },
        { value: 2, label: "待付款" },
        { value: 3, label: "待发货" },
        { value: 4, label: "发货中" },
        { value: 5, label: "已发货" },
        { value: 6, label: "已完成" },
        { value: 7, label: "已取消" },
      ],
      selectOrderType: 1,
      selectOptions: 1,
      orderTime: [],
      orderWeek: "",
      orderMonth: "",
      disAbledFun: {
        disabledDate(date) {
          return date.getTime() > Date.now();
        },
      },
      current: 1,
      total: 0,
      pageSize: 10,
      goodsDetail: {},
      query: {},
    };
  },
  mounted() {
    this.setInitGoodsList()
  },
  methods: {
    setInitGoodsList() {
      this.current = 1
      this.setGoodsList()
    },
    async setGoodsList(val) {
      let obj = {
        current: this.current,
        model: {
          endTime: this.query.endTime + " 23:59:59",
          manufacturer: "",
          productName: "",
          salesMoney: this.togTab == "2" ? "Y" : "N",
          salesQuantity: this.togTab == "1" ? "Y" : "N",
          startTime: this.query.startTime + " 00:00:00",
        },
        size: this.pageSize,
      };
      // 没时间就不传
      if (!this.query.startTime || !this.query.endTime) {
        obj.model.startTime = undefined
        obj.model.endTime = undefined
      }
      this.listLoading = true;
      this.list = [];
      let { data } = await productList(obj);
      this.listLoading = false;
      this.list = data.records;
      this.total = data.total
    },
    async getDetail() {
      let { data } = await goodsCard();
      this.goodsDetail = data;
    },
  },
  created() {
    this.getDetail();
    this.orderTime = [
      parseTime(new Date().getTime() - 3600 * 24 * 7 * 1000, "{y}-{m}-{d}"),
      parseTime(new Date().getTime(), "{y}-{m}-{d}"),
    ];
  },
  components: {
    selectTime,
    Pagination
  },
};
</script>

<style lang="scss" scoped>
.goodsCard {
  position: relative;
  .totalContent {
    display: flex;
    justify-content: flex-start;
    background-color: #f7f7f8;
    padding: 24px 0;
    border-radius: 5px;
    .item {
      padding-left: 30px;
      width: 300px;
      .number {
        font-size: 24px;
        color: #0f1831;
        font-weight: 600;
        padding-top: 10px;
      }
      .title {
        vertical-align: top;
        font-size: 14px;
        color: #051632;
        img {
          width: 16px;
          vertical-align: top;
        }
      }
    }
  }
  .cardTitle {
    color: #1e2439;
    font-size: 14px;
    padding-left: 30px;
    background-color: #f7f7f8;
    border-radius: 5px;
    margin-top: 20px;
    height: 40px;
    line-height: 40px;
    font-weight: 600;
  }
  .togContent {
    padding-left: 30px;
    display: flex;
    justify-content: space-between;
    height: 64px;
    line-height: 64px;
    position: relative;
    .btn {
      position: absolute;
      right: 0;
    }
  }
}
</style>
