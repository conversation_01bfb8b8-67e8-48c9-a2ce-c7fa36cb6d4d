import request from '@/utils/request'
var qs = require('qs')
import { html2Text } from "@/utils";
export function merchantGroupList(query) {
  return request({
    url: `/merchant/admin/merchantGroup/page`,
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function merchantGroupListNew(query) {
  return request({
    url: `/merchant/admin/merchantGroup/pageMerchantGroupList`,
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function addGroup(query) {
  return request({
    url: '/merchant/admin/merchantGroup',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function editGroup(query) {
  return request({
    url: '/merchant/admin/merchantGroup',
    method: 'put',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据用户查询销售商信息
export function findByUserIdSale(userId) {
  return request({
    url: `/merchant/admin/saleMerchant/findByUserIdSale/${userId}`,
    method: 'get'
  })
}
//删除分组
export function deleteGroup(ids, groupType) {
  return request({
    url: `/merchant/admin/merchantGroup`,
    method: 'delete',
    params: {
      ids: ids,
      groupType: groupType
    }
  })
}
//查看分组下的客户
export function queryPageSaleMerchantGroupCustomerListDTO(query) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/queryPageSaleMerchantGroupCustomerListDTO',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

//添加客户
export function updateMerchantPurSaleRelMerchantGroup(params) {
  // let params = {
  //   ids: ids,
  //   merchantGroupId: merchantGroupId
  // };
  // return
  return request({
    url: '/merchant/admin/merchantPurSaleRel/batchUpdateMerchantPurSaleRelMerchantGroup',
    method: 'post',
    data: params,
    transformRequest: [function () {
      return qs.stringify(params, { arrayFormat: 'brackets' })
    }]
  })
}
//客户档案列表(新增控销：指定客户)
export function merchantPurSaleRelList(query) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/pagePurMerchantBySaleMerchant`,
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
export function merchantUpdatePurOrder(query) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/pagePurMerchantBySaleMerchant`,
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//客户档案详情
export function getPurMerchantLicenseDetail(purMerchantId) {
  return request({
    url: `/merchant/admin/purMerchant/findPurMerchantDetail/${purMerchantId}`,
    method: 'get'
  })
}
export function getMerchantDetailsNew(purMerchantId) {
  return request({
    url: `/merchant/admin/saleMerchant/getMerchantDetails/${purMerchantId}`,
    method: 'get'
  })
}
//获取资质 PRODUCT(商品）,BUYER（采购商），MERCHANT（销售商）
export function getListByLicenseBaseType(type) {
  return request({
    url: `/merchant/admin/licenseBase/anno/listByLicenseBaseType?type=${type}`,
    method: 'get'
  })
}
//根据商家id查询该商家的客户分组
export function listMerchantGroupBySaleMerchantId(saleMerchantId) {
  return request({
    url: `/merchant/admin/merchantGroup/listMerchantGroupBySaleMerchantId/${saleMerchantId}`,
    method: 'get'
  })
}
//地区
export function trees() {
  return request({
    url: '/authority/area/anno/tree',
    method: 'get'
  })
}
//根据商家类型查找客户
export function merchantTypeList(query) {
  return request({
    url: '/merchant/admin/merchantType/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//根据id删除客户分组
export function delGroup(data) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/updateMerchantPurSaleRelMerchantGroup`,
    method: 'POST',
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' },
    transformRequest: [function () {
      return qs.stringify(data)
    }],
    data
  })
}
//修改商家资质表
export function editMerchantLicense(query) {
  return request({
    url: '/merchant/admin/merchantLicense',
    method: 'PUT',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

//公告
export function merchantNoticeList(query) {
  return request({
    url: '/general/admin/article/pageMerchantArticle',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除
export function delNoitice(ids) {
  return request({
    url: '/merchant/admin/merchantNotice',
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//详情
export function detailNotice(id) {
  return request({
    url: `/general/admin/article/${id}`,
    method: 'get'
  })
}
//修改首营状态，变更备注
export function updateSetFirstCampStatusAndRemark(query) {
  return new Promise((resolve, reject) => {
    request({
      url: '/merchant/admin/merchantPurSaleRel/updateSetFirstCampStatusAndRemark',
      method: 'post',
      params: query
    }).then(res => {
      if (res.isSuccess) {
        resolve()
      } else {
        reject(res)
      }
    }).catch(err => {
      reject(err)
    })
  })
}

// 根据采购商名称和信用代码查看采购商是否已存在-返回用户信息
export function queryByNameAndCreditCode(query) {
  return request({
    url: '/merchant/admin/purMerchant/queryByNameAndCreditCode',
    method: 'post',
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' },
    params: query
  })
}

// 商家手动绑定首营关系
export function savePurSaleRel(query) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/savePurSaleRel',
    method: 'post',
    headers: { 'Content-type': 'application/x-www-form-urlencoded ' },
    params: query
  })
}

//关联销售单
export function orderInfopage(query) {
  return request({
    url: `/order/merchant/orderInfo/page`,
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

// 根据ids批量修改采购方式
export function batchPurChasingByIds(data) {
  return request({
    url: `/merchant/admin/merchantPurSaleRel/batchPurChasingByIds?customerPurchasingMethod=${data.customerPurchasingMethod}&ids=${data.ids}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
}

// 根据企业名称查询企业档案是否存在
export function getByCreditName(name) {
  return request({
    url: `/merchant/admin/enterprise/getByCreditName/${name}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
}

// 新增采购商弹窗确定按钮
export function getPurMerchantName(purMerchantName) {
  return request({
    url: `/merchant/admin/purMerchant/getPurMerchantName/${purMerchantName}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    }
  })
}



//批量修改客户状态
export function updatePublishStatusByIds(data) {
  return request({
    url: '/merchant/admin/purMerchant/updatePublishStatusByIds',
    method: 'post',
    params: data
  })
}

/*
* 采购商列表导出
* */
export function exportMerchantPurSaleRel(data) {
  return request({
    url: "/merchant/admin/merchantPurSaleRel/export",
    method: "post",
    data
  })
}

/**
 * 客户分组列表
 */
export function fetchGroupedCustomers(data) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/queryPageSaleMerchantGroupCustomerListDTO',
    method: 'post',
    data: data
  })
}

/**
 * 获取分组客户数量（已关联/未关联）统计
 */
export function fetchGroupedCoustomerCount(data) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/sum',
    method: 'post',
    data
  })
}



/**
 * 添加客户进分组
 */
export function addCustomersToGroups(data) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/batchUpdateMerchantGroup',
    method: 'post',
    data,
    transformRequest: [function () {
      data['ids[]'] = data.ids.join(',')
      delete data.ids
      return qs.stringify(data)
    }],
  })
}

/**
 * 移除分组客户
 */
export function removeGroupedCustomers(data) {
  return request({
    url: '/merchant/admin/merchantPurSaleRel/batchUpdateMerchantPurSaleRelMerchantGroup',
    method: 'post',
    data,
    transformRequest: [function () {
      data['ids[]'] = data.ids.join(',')
      delete data.ids
      return qs.stringify(data)
    }],
  })
}

// 查看关联客户
export function seeAssociation(data) {
  return request({
    url: "/merchant/admin/customerTag/relevance",
    method: "post",
    data
  })
}

/*
* 采购商档案列表，获取采购商统计
* */
export function getPurLicenseCount (data) {
  return request({
    url: "/merchant/admin/merchantPurSaleRel/getPurLicenseCount",
    method: "post",
    data
  })
}

// 获取近几个月未下单的客户统计
export function getNotOrderCustomer(data) {
  return request({
    url: "/merchant/admin/merchantPurSaleRel/getTimeoutNoOrderPurCount",
    method: "post",
    data
  })
}

// 合伙人裂变列表
export function getDistributionPartnerList(data) {
  return request({
    url: "/merchant/distributionPartner/page",
    method: "post",
    data
  })
}

// 合伙人团队列表
export function getTeamList(data) {
  return request({
    url: "/merchant/distributionPartner/teamMemberPage",
    method: "post",
    data
  })
}

// 批量驳回采购商
export function updatePurMerchantRejected(ids) {
  return request({
    url: "/merchant/admin/purMerchant/updatePurMerchantRejectedByIds?ids[]="+ids,
    method: 'post',
  })
}

// 批量审核
export function updatePurMerchantAcceptedByIds(ids) {
  return request({
    url: "/merchant/admin/purMerchant/updatePurMerchantAcceptedByIds?ids[]="+ids,
    method: 'post',
  })
}