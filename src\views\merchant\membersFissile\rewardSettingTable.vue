<template>
  <div class="reward_setting">
    <el-table :data="list" border v-loading="loading" style="width: 100%">
      <el-table-column prop="date" label="奖励名称" width="150">
        <template v-slot="{row}">
          <div>
            <div>{{ row.rewardName }}</div>
            <el-switch
              :disabled="!getRewardTypeIsEdit(row.rewardType.code)"
              v-model="row.rewardStatus.code"
              active-value="ENABLE"
              inactive-value="DISABLE"
              active-color="#0056e5"
              inactive-color="#999999"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="奖励方式" width="100">
        <template v-slot="{row}">
          <div>{{ row.rewardWay && row.rewardWay.desc }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="奖励人" width="100px">
        <template v-slot="{row}">
          <div>{{ row.rewardPerson && row.rewardPerson.desc }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="奖励规则" width="360px">
        <template v-slot="{row}">
          <!-- 裂变奖励 -->
          <template v-if="row.rewardType.code === 'FISSION'">
            <div v-for="(item, index) in row.rewardSettingRuleList" :key="item.id">
              <span>层级{{ getLevelText(index + 1) }}：按商品成交金额的比例</span>
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="item.amount" style="width: 80px;margin: 0 5px 10px" size="mini" />
              <span>%</span>
            </div>
          </template>
          <!-- 管理佣金 -->
          <template v-else-if="row.rewardType.code === 'TEAM'">
            <div v-for="(item, index) in row.rewardSettingRuleList" :key="index">
              <span>月度{{ item.levelName }}：按整个团队当月业绩（不含团长本人）的比例</span>
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="item.amount" style="width: 80px;margin: 0 5px 10px" size="mini" />
              <span>%</span>
            </div>
          </template>
          <!-- 一次性奖励 -->
          <template v-else-if="row.rewardType.code === 'ONCE'">
            <div v-for="(item, index) in row.rewardSettingRuleList" :key="index">
              <span>成为【{{ item.levelName }}】，可获得一次性奖励</span>
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="item.amount" class="input" size="mini" />
              <span>元</span>
            </div>
          </template>
          <!-- 年度奖励 -->
          <template v-else-if="row.rewardType.code === 'YEAR'">
            <div style="display: flex;margin-bottom: 10px;align-items: center;" v-for="(item, index) in row.rewardSettingRuleList" :key="index">
              <span>年度{{ item.levelName }}：</span>
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" type="textarea" v-model="item.textContent" style="width: 200px;" size="mini" />
            </div>
          </template>
          <!-- 业务员职责奖励 -->
          <template v-else-if="row.rewardType.code === 'DUTY'">
            <div v-for="(item, index) in row.rewardSettingRuleList" :key="index">
              {{ index === 0 ? '团长下单：按商品成交金额的比例' : '团队成员下单：按商品成交金额（不含团长本人）的比例' }}
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="item.amount" class="input" size="mini" />
              <span>%</span>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="结算规则" width="400px">
        <template v-slot="{row}">
          <!-- 业务员职责和裂变奖励-->
          <div v-if="['DUTY', 'FISSION'].includes(row.rewardType.code)">
            <span>预结算：</span>
            <span>
              <span>订单支付T+</span>
              <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.preGenerateDay" class="input" size="mini" />
              <span>预结算</span>
            </span>
          </div>
          <template v-else>预结算：-</template>
          <div style="display: flex;">
            <span>可结算：</span>
            <div style="position: relative;top: -2px;">
              <div>
                <span>
                  <!-- 一次性奖励, 管理佣金 -->
                  <templata v-if="['ONCE', 'TEAM'].includes(row.rewardType.code)">当月奖励于下个月</templata>
                  <!-- 年度奖励 -->
                  <templata v-else-if="row.rewardType.code === 'YEAR'">当月奖励于次年</templata>
                  <!-- 业务员职责,裂变 -->
                  <span v-else>订单完成T+</span>
                </span>
                <!-- 年度奖励需要加上具体月份 -->
                <span v-if="row.rewardType.code === 'YEAR'">
                  <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.settleGenerateMonth" class="input" size="mini" />
                  <span>月</span>
                </span>
                <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.settleGenerateDay" class="input" size="mini" />
                <span>日可结算</span>
              </div>
              <!-- 业务员职责和裂变 需要加上退款单完成T+ -->
              <div v-if="['DUTY', 'FISSION'].includes(row.rewardType.code)">
                <span>退款单完成T+</span>
                <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.refundGenerateDay" class="input" size="mini" />
                <span>可结算</span>
              </div>
            </div>
          </div>
          <!-- 一次性奖励和年度奖励只支持人工结算 -->
          <template v-if="!['ONCE', 'YEAR'].includes(row.rewardType.code)">
            <div style="margin-bottom: 10px">
              <span>结算方式：</span>
              <el-radio-group :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.settleWay.code">
                <el-radio label="AUTO">自动结算</el-radio>
                <el-radio label="MANUAL">人工结算</el-radio>
              </el-radio-group>
            </div>
            <div v-if="row.settleWay.code === 'AUTO'">
              <span>自动结算：</span>
              <el-radio-group :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.settleMoment.code" style="margin-top: 10px;">
                <el-radio label="NOW">可结算时立即结算</el-radio>
                <el-radio label="MONTH">
                  <span>每月</span>
                  <el-input :disabled="!getRewardTypeIsEdit(row.rewardType.code)" v-model="row.settleDay" class="input" size="mini" />
                  <span>日</span>
                </el-radio>
              </el-radio-group>
            </div>
          </template>
          <template v-else>结算方式：人工结算</template>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="备注">
        <template v-slot="{row}">
          <el-input v-if="getRewardTypeIsEdit(row.rewardType.code)" type="textarea" :rows="3" placeholder="请输入备注" v-model="row.remark" />
          <div v-else>{{ row.remark }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150px" fixed="right" align="center" v-if="canEdit">
        <template v-slot="{row}">
          <el-button type="text" v-if="!getRewardTypeIsEdit(row.rewardType.code)" @click="setEdit(row)">编辑</el-button>
          <div v-else>
            <el-button type="text" @click="reload">取消</el-button>
            <el-button type="text" :loading="getRewardTypeIsEdit(row.rewardType.code) && saveLoading"  @click="save">保存</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { saveRewardSetting } from "@/api/retailStore";

export default {
  name: "rewardSettingTable",
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    canEdit: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    tableDataComputed() {
      return this.tableData || []
    }
  },
  watch: {
    tableDataComputed(newVal) {
      this.list = newVal
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      saveLoading: false,
      rewardTypeCode: null,
    }
  },
  methods: {
    getLevelText(num) {
      const levelObject = {
        1: '一',
        2: '二',
        3: '三'
      }
      return levelObject[num] || '未知'
    },
    getRewardTypeIsEdit(rewardTypeCode) {
      return this.rewardTypeCode === rewardTypeCode
    },
    // 设置编辑
    setEdit(row) {
      this.rewardTypeCode = row.rewardType.code
    },
    // 重新加载
    reload() {
      this.rewardTypeCode = null
      this.$emit('reload')
    },
    // 保存奖励item
    save() {
      const data = this.list.find(item => item.rewardType?.code === this.rewardTypeCode)
      let rewardSettingRuleList = data.rewardSettingRuleList
      if (Array.isArray(rewardSettingRuleList) && rewardSettingRuleList.length > 0) {
        // 非年度奖励没有textContent字段
        if (data?.rewardType?.code !== 'YEAR') {
          rewardSettingRuleList.forEach(item => {
            delete item.textContent
          })
        }
        rewardSettingRuleList.forEach(item => {
          if (item && !item.levelName) return
          if (item && (Object.values(item).includes('')) || Object.values(item).includes(null)) {
            this.$message.error('请完善奖励条件和结算规则')
            throw new Error('')
          }
        })
      }
      this.saveLoading = true
      saveRewardSetting(data).then(() => {
        this.$message.success('保存成功')
        this.reload()
      }).finally(() => {
        this.saveLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.input {
  width: 60px;
  margin: 0 5px 10px;
}
</style>
