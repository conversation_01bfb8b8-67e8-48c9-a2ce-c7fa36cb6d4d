<template>
  <div class="archivesPageContent">
    <div v-loading="listTotal" class="amoutTotal">
      <div>
        <p>可用余额（元）</p>
        <p>{{ statistics.amount | getDecimals }}</p>
        <p></p>
      </div>
      <div>
        <p>
          待结算收入（元）
          <el-button
            v-if="false"
            type="text"
            @click="$router.push('/finance/funds/pendingIncome')"
            >明细</el-button
          >
        </p>
        <p>{{ statistics.settlementAmount | getDecimals }}</p>
      </div>
      <div>
        <p>已缴保证金（元）</p>
        <p>{{ statistics.depositAmount | getDecimals }}</p>
        <p><el-button v-if="false">申请退还</el-button></p>
      </div>
    </div>
    <im-search-pad
      :is-expand.sync="isExpand"
      :model="listQuery"
      @reset="resetForm('searchForm')"
      @search="onSearchSubmitFun"
    >
      <im-search-pad-item prop="settlementOrderId">
        <el-input
          v-model.trim="listQuery.model.settlementOrderId"
          placeholder="请输入业务单号"
        />
      </im-search-pad-item>
      <im-search-pad-item prop="paymentsType">
        <el-select
          v-model="listQuery.model.paymentsType"
          placeholder="收支类型"
        >
          <el-option
            v-for="item in paymentsType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="detailType">
        <el-select v-model="listQuery.model.detailType" placeholder="明细类型">
          <el-option
            v-for="item in detailType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item v-show="isExpand" prop="timePicker">
        <el-date-picker
          v-model="timePicker"
          type="datetimerange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="查询起始日期"
          end-placeholder="查询结束日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="selectTime"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout :tabs="[{ name: '资金账户收支明细', value: 'list' }]" />
      <div class="table">
        <el-table
          v-if="list"
          ref="table"
          v-loading="listLoading"
          :data="list"
          row-key="id"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            align="center"
            width="60"
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }} </span>
            </template>
          </el-table-column>

          <el-table-column
            v-for="(item, index) in tableTitle"
            :key="index"
            :min-width="item.width ? item.width : '350px'"
            :label="item.label"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="{ row }">
              <span
                v-if="item.name == 'detailType' || item.name == 'paymentsType'"
              >
                {{ row[item.name].desc }}
              </span>
              <span v-else-if="item.name == 'amount'">
                <span
                  v-if="row.paymentsType.code == 'COLLECT'"
                  style="color: #70b603"
                  >{{ row[item.name] | getDecimals }}</span
                >
                <span v-else style="color: #f59a23">{{
                  row[item.name] | getDecimals
                }}</span>
              </span>
              <span v-else>{{ row[item.name] }}</span>
            </template>
          </el-table-column>
          <!--
          <el-table-column
            fixed="right"
            align="center"
            label="操作"
            width="150"
            class="itemAction"
          >
            <template slot-scope="scope">
              <el-button type="text" >查看详情</el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div style="display: flex; justify-content: flex-end">
          <pagination
            v-if="total > 0"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page.sync="listQuery.current"
            :limit.sync="listQuery.size"
            @pagination="getlist"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const tableInfo = {
  list: [
    {
      key: 0,
      label: "记账时间",
      name: "createTime",
      width: "170px",
      disabled: true,
    },
    {
      key: 1,
      label: "业务单号",
      name: "settlementOrderId",
      width: "180px",
      disabled: true,
    },
    {
      key: 2,
      label: "收支类型",
      name: "paymentsType",
      width: "100px",
      disabled: true,
    },
    {
      key: 3,
      label: "明细类型",
      name: "detailType",
      width: "140px",
      disabled: true,
    },
    {
      key: 4,
      label: "记账金额",
      name: "amount",
      width: "140px",
      disabled: true,
    },
    {
      key: 5,
      label: "备注",
      name: "remarks",
      width: "200px",
      disabled: true,
    },
  ],
};
import { list, statistics } from "@/api/capitalAccount";
import Pagination from "@/components/Pagination";
export default {
  name: "MerchantsBalance",
  components: {
    Pagination,
  },
  data() {
    return {
      isExpand: false,
      listLoading: false,
      listTotal: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      statistics: { amount: 0, depositAmount: 0, settlementAmount: 0 },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      detailType: [
        {
          index: 0,
          value: "ORDER",
          label: "订单交易",
        },
        {
          index: 1,
          value: "REFUND",
          label: "退货退款",
        },
        {
          index: 2,
          value: "RECHARGE",
          label: "余额充值",
        },
        {
          index: 3,
          value: "CASH",
          label: "余额提现",
        },
        {
          index: 4,
          value: "CASHCHONGZHI",
          label: "提现冲正",
        },
        {
          index: 5,
          value: "SERVICE",
          label: "交易服务费",
        },
        {
          index: 6,
          value: "PROCEDURES",
          label: "交易手续费",
        },
        {
          index: 7,
          value: "COMMISSION",
          label: "品种推广佣金",
        },
      ],
      paymentsType: [
        {
          index: 0,
          value: "COLLECT",
          label: "收入(收款)",
        },
        {
          index: 1,
          value: "PAY",
          label: "支出(付款)",
        },
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      total: 0,
      timePicker: [],
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
    };
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
    this.gestatistics();
  },
  methods: {
    resetForm() {
      this.timePicker = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.getlist();
    },
    selectTime(e) {
      if (e) {
        this.listQuery.model.startTime = e[0];
        this.listQuery.model.endTime = e[1];
      } else {
        this.listQuery.model.startTime = "";
        this.listQuery.model.endTime = "";
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    onSearchSubmitFun() {
      this.getlist();
    },
    async getlist() {
      this.listLoading = true;
      const { data } = await list(this.listQuery);
      if (!data) {
        this.$set(this, "listLoading", false);
        this.total = 0;
        this.list = [];
        return;
      }
      this.$set(this, "listLoading", false);
      this.total = data.total;
      this.list = data.records;
    },
    async gestatistics() {
      this.listTotal = true;
      const { data } = await statistics();
      if (!data) {
        this.listTotal = false;
        return;
      }
      this.listTotal = false;
      this.statistics = data;
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .amoutTotal {
    border-bottom: 16px solid #f2f3f4;
    padding: 30px;
    display: flex;
    justify-content: flex-start;
    background-color: #fff;
    div {
      box-sizing: border-box;
      padding-left: 30px;
      min-width: 400px;
      p:nth-of-type(1) {
        color: #929292;
        line-height: 36px;
      }
      p:nth-of-type(2) {
        font-size: 32px;
      }
      p:nth-of-type(3) {
        line-height: 36px;
      }
    }
  }
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-bottom: 2px solid #ebecee;
    margin-bottom: 16px;
    padding: 0 12px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
