<template>
  <div class="list—index">
    <el-dialog title="提示" v-dialogDrag :visible.sync="dialogVisible" width="70%">
      <!--搜索Form-->
      <im-search-pad :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload" @search="searchLoad">
        <im-search-pad-item prop="name">
          <el-input v-model="model.name" @keyup.enter.native="searchLoad" placeholder="请输入企业名称" />
        </im-search-pad-item>
      </im-search-pad>
      <div class="tab_bg">

        <!-- 分页tab -->
        <table-pager ref="pager-table" :options="tableColumns" :remote-method="load" :data.sync="tableData"
          :selection="true" @selection-change="onSelect" @selection-all="onAllSelect" :pageSize="pageSize"
          :isNeedButton="false">
        </table-pager>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getBrandShop,
    batchSave
  } from '@/api/product';
  const TableColumns = [{
      label: "企业编码",
      name: "code",
      prop: "code",
      width: "150"
    },
    {
      label: "企业名称",
      name: "name",
      prop: "name",
      width: "200"
    },
    {
      label: "负责人",
      name: "ceoName",
      prop: 'ceoName',
      width: "150"
    },
    {
      label: "负责人手机",
      name: "ceoMobile",
      prop: 'ceoMobile',
      width: "100"
    }
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({
      key: i,
      ...TableColumns[i]
    });
  }
  export default {
    data() {
      return {
        dialogVisible: false,
        isExpand: false,
        model: {
          name: ''
        },
        tableData: [],
        multipleSelection: [],
        tableColumns: TableColumnList,
        pageSize: 10,
      }
    },
    //方法集合
    methods: {
      async load(params) {
        let listQuery = {
          model: this.model,
          sort: 'id',
          order: 'descending',
        };
        Object.assign(listQuery, params);
        return await getBrandShop(listQuery);
      },
      openDia() {
        this.dialogVisible = true;
      },
      // table 选中
      onAllSelect(selection) {
        this.onSelect(selection);
      },
      onSelect(val) {
        this.multipleSelection = val;
      },
      // 搜索
      searchLoad() {
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
      },
      // 重置
      reload() {
        this.model = {
          name: ''
        };
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
        this.load();
      },
      handleRefresh(pageParams) {
        this.$refs['pager-table'].doRefresh(pageParams)
      },
      //   确定按钮
      submit() {
        console.log('multipleSelection', this.multipleSelection);
        if (this.multipleSelection.length > 0) {
          let list = [];
          this.multipleSelection.forEach(item=>{
              if(item.id){
                  list.push(item.id);
              }
          });
          let params = {
              ids:list.toString()
          };
          batchSave(params).then(res=>{
            this.$emit("closeDialog");
              if(res.code==0 && res.msg == 'ok'){
                  this.dialogVisible = false;
                  this.$message.success('批量关联企业成功');
              }
          })
        }
      },

    },

  }

</script>


<style lang='scss' scoped>

</style>
