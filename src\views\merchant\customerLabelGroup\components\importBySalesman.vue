<template>
  <el-dialog title="按业务员选定"  :visible.sync="dialogVisible" width="800px" close="closeDialog">
    <div class="import-by-salesman">
      <ol class="header-panel">
        <li>
          <p>部门：</p>
          <el-cascader :options="cascadeOptions" v-model="cascadeValue" clearable :props="cascadeProps" @change="handleChange">
          </el-cascader>
        </li>
        <li>
          <p>员工手机号：</p>
          <el-input v-model="searchByPhone" placeholder="请输入内容" clearable @input="onSearch"></el-input>
        </li>
        <li>
          <p>员工姓名：</p>
          <el-input v-model="searchByName" placeholder="请输入内容" clearable @input="onSearch"></el-input>
        </li>
      </ol>
      <div class="transfer-panel " v-if="dialogVisible">
        <div class="content" v-loading="isLoading">
          <el-transfer v-model="transferValue"
                       :right-default-checked="[1]"
                       :data="transferOptions"
                       :titles="titles"
                       @change="handleTransferChange"></el-transfer>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitLoading" @click="handlePrimary">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getOrganizationTreeById,getOrganizationList,peopleList ,settingPurMerchantBySaleManId} from '@/api/organization/index';

export default {
  components: {},
  props: {
    isVisibleImportBySalesman: {
      type: Boolean,
      default: false
    },
    customerTagId: { //客户标签id
      type: String,
      default: () => {
        return "";
      }
    },
    saleMerchantId: { //销售商id
      type: String,
      default: () => {
        return "";
      }
    }
  },
  data() {
    return {
      isLoading: false, // 控制loading状态
      dialogVisible: false, // 导入的弹窗是否显示
      submitLoading: false,
      cascadeOptions:[],
      cascadeProps:{
        label:'name',
        value:'id'
      },
      cascadeValue:null,
      transferOptions:[],
      transferValue:[],
      searchByPhone:'',
      searchByName:'',
      titles:['未选择员工列表','已选择员工列表'],//列表标题
      selectedIndex:null,
      organizationList:{},
      departmentId:"",
      orgStructure:null,
      peopleList:null,
      selectedItems:[],
      openType:'',
    };
  },
  created() {
    this.getOrganizationListFun()
  },
  mounted() {
  },
  watch: {
    dialogVisible(value){
      if(!value){
        this.selectedItems = [];
        this.isLoading =false
        this.transferValue = []
        this.peopleList = []
        // 更新左侧数据（当前部门的人员列表）
        this.transferOptions = []
        this.cascadeValue = null
        this.searchByPhone = ''
        this.searchByName = ''
      }
    },
  },
  computed: {},
  methods: {
    openDialog(type){
      this.openType = type
      this.dialogVisible = true
      this.initDialog()
    },
    closeDialog(){
      this.dialogVisible = false
    },
    initDialog(){
      this.getOrganizationListFun()
    },
    handlePrimary(){
      if(this.transferValue.length){
        this.submitLoading = true
        let parmas = {
          "customerTagId": this.customerTagId,
          "saleManIds": this.transferValue,
          "saleMerchantId": this.saleMerchantId,
          "specifyType": this.openType
        }
        settingPurMerchantBySaleManId(parmas).then(res=>{
          const {code,data,isSuccess,msg} = res
          this.submitLoading = false
          if(code === 0 && isSuccess){
            if(data > 0){
              this.$emit('sendTransferValue',{infos:data,type:this.openType})
              this.closeDialog()
              this.$message.success('导入成功');
            }else {
              this.$message.error('选中的业务员关联客户为空');
              this.closeDialog()
            }
          }else{
            this.$message.error(msg);
          }
        })
        // console.log('确定',this.transferValue)
        // this.$emit('sendTransferValue',{infos:this.transferValue,type:this.openType})
      }
    },
    handleChange(value) {
      // 清空 selectedItems
      this.selectedItems = [];
      if(this.transferValue && this.transferValue.length){
        // 获取选中的值
        const selectedIds = this.transferValue;
        this.selectedItems = this.transferOptions.filter(item => selectedIds.includes(item.key));
      }
      // 输出已选项
      console.log(this.selectedItems, 'this.selectedItems???');
      if(value && value.length){
        const selectId = value[value.length - 1]
        this.getPeopleList(selectId)
      }else{
        this.getPeopleList(this.departmentId) //获取到最大的组织架构成员列表
      }
    },
    onSearch(){
      let filterData = this.peopleList.filter(item => {
        // 根据手机号和姓名进行搜索
        const matchesMobile = this.searchByPhone ? item.mobile.includes(this.searchByPhone) : true;
        const matchesName = this.searchByName ? item.salesmanName.includes(this.searchByName) : true;
        return matchesMobile && matchesName;  // 满足条件的项目
      });

      this.transferOptions = [...filterData] || []
    },
    handleTransferChange(value, direction, movedKeys) {
      // console.log(value, direction, movedKeys);
    },
    // 获取所有组织
    getOrganizationListFun(){
      this.isLoading = true
      getOrganizationList().then(res=>{
        let {code,data} = res;
        console.log(data,'结果')
        if(code === 0 && data!=null&& data.length > 0){
          this.selectedIndex =  this.selectedIndex|| 0;
          this.organizationList = data
          this.departmentId = data[0].id
          this.getOrganizationTreeByIdFun(this.departmentId) //获取当前组织架构树结构
          this.getPeopleList(this.departmentId) //获取到最大的组织架构成员列表
        }else{
          this.isLoading = false
        }
      })
    },
    // 获取当前组织树组织
    getOrganizationTreeByIdFun(id){
      getOrganizationTreeById(id).then(res=>{
        let {code,data} = res;
        if(code === 0 ){
          this.orgStructure = data
          this.cascadeOptions = data[0].children
        }
      })
    },
    getPeopleList(id){
      this.isLoading = true
      peopleList({"id":id,"mbOrDepName":""}).then(res=>{
        this.isLoading = false
        let {code,data} = res;
        if(code === 0 && data && data.length > 0){
          // 添加字段，设置新的业务员数据
          const peopleList = this.addFieldsToArray(data) //全部数据
          // console.log(peopleList,'全部数据')
          this.peopleList = [...peopleList]
          // 更新左侧数据（当前部门的人员列表）
          this.transferOptions = [...peopleList]
          // 保持右侧已选项（不清空右侧数据）
          // this.updateTransferValueForDepartment(data);
        }
      })
    },
    addFieldsToArray(data) {
      // 插入已选择的项，并保持选中的顺序
      let finalData = [...data];
      if(this.selectedItems && this.selectedItems.length){
        const selectedKeys = new Set(this.selectedItems.map(item => item.salesmanId));
        console.log(selectedKeys,'selectedKeys')
        const uniqueData = data.filter(item => !selectedKeys.has(item.salesmanId));

        // 将选中的项插入到最前面，确保顺序不变
        finalData = [...this.selectedItems, ...uniqueData];
        this.transferValue = [...selectedKeys]
      }else{
        this.transferValue = []
      }
      // 返回更新后的数据，确保返回的数据结构是按需要的格式处理的
      return finalData.map((item, index) => ({
        ...item,  // 保留原有字段
        key:  item.salesmanId,  // 确保 key 唯一
        label: item.salesmanName,  // 使用 salesmanName 作为 label
        disabled: false  // 设置 disabled 为 false
      }));
    },
},
};
</script>

<style scoped lang="scss">
.import-by-salesman{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;
}
.header-panel{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  li {
    width: 49%;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    align-items: center;
    p {
      flex-shrink: 0;
      width: 100px;
      text-align: right;
      white-space: nowrap;
      word-break: keep-all;
    }
    div{
      flex: 1;
      ::v-deep input{
        border-radius: 6px;
      }
    }
  }
}
.transfer-panel{
    width: 100%;
  display: flex;
  justify-content: center;
  .content{
    width: 80%;
    ::v-deep .el-transfer{
      .el-transfer__buttons{
        padding: 0;
        .el-button{
          width: 25px;
          height: 25px;
          padding: 0;
          //background: #EEEEEE;
          //border-color: #EEEEEE;
          border-radius: 6px;
          margin: 0 5px;
        }
      }
      .el-transfer-panel{
        border-radius: 6px;

      }
      .el-checkbox__label{
        font-size: 14px;
      }
    }
  }
}
</style>
