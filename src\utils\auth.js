import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function getRefreshToken() {
  return Cookies.get('merchantRefreshToken')
}

export function setRefreshToken(refreshToken) {
  return Cookies.set('merchantRefreshToken', refreshToken)
}

export function removeRefreshToken() {
  return Cookies.remove('merchantRefreshToken')
}
export function getExpire() {
  return Cookies.get('merchantExpire')
}

export function setExpire(expire) {
  return Cookies.set('merchantExpire', expire)
}

export function removeExpire() {
  return Cookies.remove('merchantExpire')
}
//给sessionStorage存值
export function setContextData(key, value) {
  if (typeof value == "string") {
    sessionStorage.setItem(key, value);
  } else {
    sessionStorage.setItem(key, JSON.stringify(value));
  }
}
// 从sessionStorage取值
export function getContextData(key) {
  const str = sessionStorage.getItem(key);
  if (typeof str == "string") {
    try {
      return JSON.parse(str);
    } catch (e) {
      return str;
    }
  }
  return "";
}
