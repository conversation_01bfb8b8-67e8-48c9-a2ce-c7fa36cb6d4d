<template>
  <div>
    <div class="tab_bg">
      <tabs-layout
        v-if="checkPermission(['admin', 'notice:view'])"
        :tabs="[ { name: '系统公告', value: 'first' } ]"
      />
      <table-pager ref="todoTable" :options="tableTitle" :data.sync="tableData" :remote-method="load">
        <div slot-scope="props">
          <el-row class="table-edit-row">
            <span v-if="checkPermission(['admin', 'sale-saas-systemNotice:detail', 'sale-platform-systemNotice:detail'])" class="table-edit-row-item">
              <el-button type="text" @click="addOrEdit(props.row)">查看详情</el-button>
            </span>
          </el-row>
          <!-- <el-link @click="del(props.row.id)">删除</el-link> -->
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
  import {merchantNoticeList,delNoitice} from '@/api/group'
  import checkPermission from '../../utils/permission';
  const TableColumns = [
    { label: "公告标题", prop: "title",name: "title"},
    { label: "时间", prop: "createTime",name: "createTime"},
  ];
  const TableColumnList = [];
  for (let i = 0; i < TableColumns.length; i++) {
    TableColumnList.push({ key: i, ...TableColumns[i] });
  }

  export default {
    name: "DragTable",
    data() {
      return {
        showSelectTitle: false,
        tableTitle: TableColumnList,
        tableData: [],
        tableVal: [],
        list: [],
        total: 0,
        page: 0,
        listLoading: false,
        sortable: null,
        activeName: 'first',
        options: [],
        listQuery: {
          model: {}
        }
      };
    },
    created() {
    },
    methods: {
      checkPermission,
      del(id) {
        this.$confirm('是否确定删除？').then(_ => {
          delNoitice([id]).then(res=>{
            this.$message.success('删除成功！')
            this.handleRefresh({
              page: 1,
              pageSize: 10
            })
          }).catch(error=>{})

        }).catch(_ => {});

      },
      addOrEdit(row) {
        this.$router.push({path: '/notice/detail',query: {id: row.id}})
      },

      async load(params) {
        this.listLoading = true
        Object.assign(this.listQuery, params)
        return await merchantNoticeList(this.listQuery)
      },
      handleRefresh(pageParams) {
        this.$refs.todoTable.doRefresh(pageParams)
      },
    },
  };
</script>

<style>
  .sortable-ghost {
    opacity: 0.8;
    color: #fff !important;
    background: #42b983 !important;
  }
</style>

<style scoped>
</style>
