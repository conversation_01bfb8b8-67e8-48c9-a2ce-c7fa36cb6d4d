<template>
  <el-dialog title="团队成员" :visible.sync="visible" :close-on-click-modal="false" width="80%">
    <el-tabs v-model="listQuery.model.level" @tab-click="changeLevel">
      <el-tab-pane v-for="item in memberData" :label="item.label" :name="item.value" />
    </el-tabs>
    <div class="member_list">
      <im-search-pad :model="listQuery" @reset="handleReset" :hasExpand="false" @search="onSubmit">
        <im-search-pad-item prop="code">
          <el-input v-model.trim="listQuery.model.partnerOrInvite" placeholder="请输入推广员/邀请方" />
        </im-search-pad-item>
        <im-search-pad-item prop="code">
        <el-select v-model="listQuery.model.isTeam" placeholder="是否合伙人">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </im-search-pad-item>
        <im-search-pad-item prop="timeRange">
          <el-date-picker
            v-model="listQuery.model.timeRange"
            range-separator="-"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            start-placeholder="开始时间"
            :default-time="['00:00:00', '23:59:59']"
            unlink-panels
            end-placeholder="结束时间"
          />
        </im-search-pad-item>
      </im-search-pad>
      <div class="tab_bg">
        <table-pager
          ref="todoTable"
          :options="tableTitle"
          :data.sync="tableData"
          :operation-width="240"
          :remote-method="load"
          :isNeedButton="false"
        >
          <el-table-column label="是否合伙人" width="150" slot="auditStatus">
            <slot slot-scope="{row}">
              {{ row.auditStatus && row.auditStatus.code === 'ACCEPTED' ? '是' : '否' }}
            </slot>
          </el-table-column>  
        </table-pager>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { deepClone } from "@/utils";
import { getTeamList } from "@/api/group";

const TableColumns = [
  {
    label: "推广员",
    prop: 'partnerName',
  },
  {
    label: '邀请方',
    prop: 'inviteName',
  },
  {
    label: '是否合伙人',
    name: 'auditStatus',
    slot: true
  },
  {
    label: '加入时间',
    prop: 'joinTime',
    width: 160
  },
  {
    label: '合伙人时间',
    prop: 'partnerTime',
    width: 160
  },
  {
    label: '累计本人下单金额(元)',
    prop: 'totalRewardMoney',
    width: 160
  },
  {
    label: '累计本人下单笔数',
    prop: 'totalOrderCount'
  },
]
const TableColumnList = []
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  })
}

export default {
  name: 'memberTeamDialog',
  data() {
    return {
      totalPage: 0,
      tableTitle: TableColumnList,
      tableData: [],
      options: [],
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        model: {
          partnerOrInvite: "",
          teamId: "",
          level: '1',
          isTeam: '',
          timeRange: []
        }
      },
      loading: false,
      visible: false,
      memberData: [
        {
          label: '一层成员',
          value: '1'
        },
        {
          label: '二层成员',
          value: '2'
        },
        {
          label: '三层成员',
          value: '3'
        },
      ]
    }
  },
  methods: {
    show(teamId) {
      this.visible = true
      this.listQuery.model.teamId = teamId
      this.listQuery.model.level = '1'
      this.handleReset()
    },
    hide() {
      this.visible = false
    },
    changeLevel() {
      this.handleReset()
    },
    async load(params) {
      Object.assign(this.listQuery, params)
      let listQuery = deepClone(this.listQuery)
      const model = listQuery.model
      if (Array.isArray(model.timeRange) && model.timeRange.length > 0) {
        listQuery.model.startTime = model.timeRange[0]
        listQuery.model.endTime = model.timeRange[1]
      } else {
        listQuery.model.startTime = undefined
        listQuery.model.endTime = undefined
      }
      this.listLoading = true
      try {
        const res = await getTeamList(listQuery)
        this.totalPage = res.data.pages
        this.total = res.data.total
        return res
      } catch (e) {
        this.listLoading = false
      }
    },
    handleReset() {
      this.listQuery.model = {
        ...this.listQuery.model,
        timeRange: [],
        partnerOrInvite: ''
      }
      this.onSubmit()
    },
    handleRefresh(pageParams) {
      this.$refs.todoTable?.doRefresh(pageParams)
    },
    onSubmit() {
      this.handleRefresh({
        page: 1,
        pageSize: 10
      })
    },
  }
}
</script>

<style lang="scss" scoped></style>
