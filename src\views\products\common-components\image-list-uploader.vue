<template>
  <div class="image-list-uploader">
    <img ref="image" :src="imgUrl" alt="">
    <input ref="input" @change="handleFileChange" type="file" accept="image/*">
  </div>
</template>

<script>
export default {
  data () {
    return {
      imgUrl: ''
    }
  },
  methods: {
    handleFileChange (ev) {
      const file = ev.target.files[0]
      const formData = new FormData()
      formData.append('file', file)
      const objectURL = URL.createObjectURL(file)
      this.imgUrl = objectURL
      this.$refs.image.onload = function () {
        URL.revokeObjectURL(objectURL)
      }
    }
  }
}
</script>
