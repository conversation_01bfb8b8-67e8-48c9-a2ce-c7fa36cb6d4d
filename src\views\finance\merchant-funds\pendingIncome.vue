<template>
  <div class="archivesPageContent">
    <div class="temp_searchBox">
      <el-form
        :inline="true"
        ref="searchForm"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="listQuery.model.shopname"
            placeholder="请输入店铺名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-select
            v-model="listQuery.model.detailType"
            placeholder="明细类型"
          >
            <el-option
              v-for="item in detailType"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="">
          <el-date-picker
            v-model="timePicker"
            type="datetimerange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="查询起始日期"
            end-placeholder="查询结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="selectTime"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchSubmitFun">搜索</el-button>
          <el-button @click="resetForm('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="title flex_between_center">
      <div>
        <el-tabs v-model="tabType" class="typeTabs">
          <el-tab-pane label="商家余额" name="list"></el-tab-pane>
        </el-tabs>
      </div>
      <div></div>
    </div>

    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        @selection-change="selectTableItemFun"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="80"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end">
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
  </div>
</template>

<script>
const tableInfo = {
  list: [
    {
      type: 0,
      label: "业务单时间",
      name: "createTime",
      width: "170px",
      disabled: true,
    },
    {
      type: 1,
      label: "业务单号",
      name: "createTime",
      width: "170px",
    },
    {
      type: 2,
      label: "收支类型",
      name: "createTime",
      width: "100px",
    },
    {
      type: 3,
      label: "明细类型",
      name: "detailsType",
      width: "170px",
    },
    {
      type: 4,
      label: "预计收支金额（元）",
      name: "amount",
      width: "170px",
    },
  ],
};
import { pendinglist } from "@/api/capitalAccount";
import Pagination from "@/components/Pagination";
export default {
  name: "merchantsBalance",
  data() {
    return {
      timePicker: "",
      listLoading: false,
      list: [1, 2, 3],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      detailType: [
        {
          index: 0,
          value: "ORDER",
          label: "订单交易",
        },
        {
          index: 1,
          value: "REFUND",
          label: "退货退款",
        },
        {
          index: 2,
          value: "RECHARGE",
          label: "余额充值",
        },
        {
          index: 3,
          value: "CASH",
          label: "余额提现",
        },
        {
          index: 4,
          value: "CASHCHONGZHI",
          label: "提现冲正",
        },
        {
          index: 5,
          value: "SERVICE",
          label: "交易服务费",
        },
        {
          index: 6,
          value: "PROCEDURES",
          label: "交易手续费",
        },
        {
          index: 7,
          value: "COMMISSION",
          label: "品种推广佣金",
        },
      ],
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
      props: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          let id = node.data ? node.data.id : "";
          let res = await areas({ parentId: id });
          let list = res.data;
          list.forEach((item) => {
            item.value = item.id;
            item.leaf = level >= 2;
          });
          resolve(list);
        },
      },
    };
  },
  methods: {
    resetForm() {
      this.timePicker = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.getlist();
    },
    selectTime(e) {
      if (e) {
        this.listQuery.model.startTime = e[0];
        this.listQuery.model.endTime = e[1];
      } else {
        this.listQuery.model.startTime = "";
        this.listQuery.model.endTime = "";
      }
    },
    cityChange(e) {
      this.listQuery.model.provinceId = e[0];
      this.listQuery.model.cityId = e[1];
      this.listQuery.model.countyId = e[2];
    },
    selectTableItemFun: function (val) {
      // let arr = [];
      // val.forEach((item) => {
      //   arr.push(item.id);
      // });
      // this.multipleSelection = val;
      // this.multipleSelectionId = arr;
    },
    onSearchSubmitFun() {},
    async getlist() {
      let { data } = await pendinglist(this.listQuery);
    },
    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  },
  components: {
    Pagination,
  },
};
</script>

<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-top: 16px solid #f2f3f4;
    border-bottom: 2px solid #ebecee;
    margin-bottom: 35px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
    padding: 0 12px;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
