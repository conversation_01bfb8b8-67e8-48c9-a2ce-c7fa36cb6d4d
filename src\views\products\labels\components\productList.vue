<template>
  <div class="productModelRelevance">
    <tabs-layout ref="tabs-layout" :tabs="tabs" v-model="tabCode" @change="handleChangeTab"></tabs-layout>
    <!--搜索Form-->
    <div class="content_box">
      <im-search-pad-dialog :has-expand="false" :is-expand.sync="isExpand" :model="model" @reset="reload"
        @search="searchLoad">
        <im-search-pad-item-dialog prop="keyword">
          <el-input v-model.trim="model.keyword" @keyup.enter.native="searchLoad" placeholder=" 商品名称/商品编码" />
        </im-search-pad-item-dialog>
        <im-search-pad-item-dialog prop="manufacturer">
          <el-input v-model.trim="model.manufacturer" @keyup.enter.native="searchLoad" placeholder="生产厂家" />
        </im-search-pad-item-dialog>
        <im-search-pad-item-dialog prop="warehouseIds" v-if="storeArr.length">
          <el-select v-model="model.warehouseIds" class="width160" collapse-tags multiple placeholder="所在仓库" clearable>
            <el-option v-for="item in storeArr" :key="item.value" :value="item.value" :label="item.label" />
          </el-select>
        </im-search-pad-item-dialog>
      </im-search-pad-dialog>
      <table-pager :rowKey="rowKey" :reserveSelection="true" ref="pager-table" :height="tableHeight" :options="allColumn"
        :remote-method="load" :data.sync="tableData" :selection="true" @selection-change="handleSelectionChange"
        @selection-all="handleSelectAll" :pageSize="pageSize" :isNeedButton="false" :paginationIsCenter="true" :selectedNum="multipleSelection.length || 0">
        <el-table-column slot="pictIdS" label="主图" align="center" width="80">
          <template slot-scope="scope">
            <el-image style="width:40px;height:40px" :src="scope.row.pictIdS | imgFilter" :preview-src-list="scope.row.pictIdS|imageFilterPreview"></el-image>
          </template>
        </el-table-column>
        <el-table-column slot="erpCode" width="180" label="商品编码/仓库">
          <template v-slot="{row}">
            <div class="omit_one" style="width:160px">{{ row.erpCode || '无' }}</div>
            <div v-if="row.warehouseName && row.warehouseName.length < 10">{{ row.warehouseName }}</div>
            <el-tooltip v-else class="item" effect="dark" :content="row.warehouseName" placement="top">
              <div class="omit_one" style="width:160px">{{ row.warehouseName || '无'}}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column slot="productName" width="220" label="商品名称">
          <template v-slot="{row}">
            <div v-if="row.productName && row.productName.length < 24">{{ row.productName }}</div>
            <el-tooltip v-else class="item" effect="dark" :content="row.productName" placement="top">
              <div class="omit_two" style="width:200px">{{ row.productName || '无'}}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column slot="manufacturer" width="220" label="生产厂家">
          <template v-slot="{row}">
            <div v-if="row.manufacturer && row.manufacturer.length < 24">{{ row.manufacturer }}</div>
            <el-tooltip v-else class="item" effect="dark" :content="row.manufacturer" placement="top">
              <div class="omit_two" style="width:200px">{{ row.manufacturer || '无'}}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column slot="salePrice" width="120" label="销售价">
          <template v-slot="{row}">
            <span style="color:#E6A23C">{{ row.salePrice }}</span>
          </template>
        </el-table-column>
        <el-table-column slot="costPrice" width="120" label="成本价">
          <template v-slot="{row}">
            <span style="color:green">{{ row.costPrice }}</span>
          </template>
        </el-table-column>
        <div slot="handleButton">
          <el-button class="btn_class" @click="handleCancel">取消</el-button>
          <el-button class="btn_class" type="primary" @click="submit">
            {{ tabCode == "ALREADY" ? "移除" : "添加"}}{{multipleSelection.length == 0 ? "": `(${multipleSelection.length})`}}
          </el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
  import ImSearchPadDialog from "@/components/eyaolink/ImSearchPadDialog/index"
  import ImSearchPadItemDialog from "@/components/eyaolink/ImSearchPadItemDialog/index"
  import {
    fetchLabelProductSum,
    fetchAssociatedLabeledProdcutList,
    fetchUnassociatedLabeledProdcutList,
    addAssociatedLabelProduct,
    deleteAssociatedLabelProduct
  } from "@/api/products/label"
  const TableColumns = [
  {  prop: 'pictIdS',  name: 'pictIdS',  label: '主图',  slot: true},
  {  prop: 'erpCode', name: 'erpCode', label: 'ERP商品编码/仓库', width: 120, slot: true},
  {  prop: 'productName',  name: 'productName',  label: '商品名称',  width: 200, slot: true},
  {  prop: 'manufacturer',  name: 'manufacturer',  label: '生产厂家',  width: 200, slot: true},
  {  prop: 'spec',  name: 'spec',  label: '规格',  width: 100},
  {  prop: 'unit',  name: 'unit',  label: '单位',  width: 100},
  {  prop: 'salePrice',  name: 'salePrice',  label: '销售价',  width: 100, slot: true},
  {  prop: 'costPrice',  name: 'costPrice',  label: '成本价',  width: 100, slot: true},
  {  prop: 'stockQuantity',  name: 'stockQuantity',  label: '可卖库存',  width: 100},
]
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({
    key: i,
    ...TableColumns[i]
  });
}
  import {
    getAllStore
  } from "@/api/products/store";
  export default {
    //import引入的组件
    components: {
      ImSearchPadDialog,
      ImSearchPadItemDialog
    },
    props: {
      // 标签Id
      productLabelId: {
        type: String,
        default: "",
      },
    },

    data() {
      return {
        loading: false,
        // 获取row的key值
        rowKey: "id",
        isExpand: false,
        pageSize: 10,
        model: {
          keyword: "",
          manufacturer: "",
          warehouseIds: []
        },
        tabCode: "ALREADY",
        page: 1,
        limit: 10,
        totalCount: 0,
        allColumn: TableColumnList, // 所有表列
        tableData: [],
        multipleSelection: [],
        tabNum: {
          bindQuantity: 0,
          unbindQuantity: 0,
        },
        storeArr: [],
        tableHeight: 0, // 表格的高度
      };
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
    async mounted() {
      this.tableHeight = Number((window.screen.height * 0.52).toFixed(0)) || 500;
      await this.getStoreList()
      await this.load();
      await this.tabNumFn();
    },
    computed: {
      tabs() {
        return [{
            name: `已关联商品(${this.tabNum.bindQuantity})`,
            value: "ALREADY",
          },
          {
            name: `未关联商品(${this.tabNum.unbindQuantity})`,
            value: "NOT",
          },
        ];
      },
    },
    //方法集合
    methods: {
      /**
       * @description 获取全部仓库的下拉列表
       * <AUTHOR>
       */
      getStoreList() {
        getAllStore().then(res => {
          const data = res.data;
          if (data && data.length) {
            data.forEach(item => {
              const obj = {};
              obj.label = item.name;
              obj.value = item.id;
              this.storeArr.push(obj)
            })
          }
        })
      },
      async load(params) {
        // let params = {
        //   current: this.page,
        //   map: {},
        //   model: {
        //     ...this.model,
        //     productLabelId: this.productLabelId,
        //   },
        //   order: "descending",
        //   size: this.limit,
        //   sort: "id",
        // };
        let listQuery = {
          model:{
            ...this.model,
            productLabelId: this.productLabelId,
          }
        };
        Object.assign(listQuery, params);
        if (this.tabCode == "ALREADY") {
          // 已关联
          // fetchAssociatedLabeledProdcutList(params).then((res) => {
          //   if (res.code == 0 && res.msg == "ok") {
          //     this.tableData = res.data.records || [];
          //     this.totalCount = res.data.total;
          //   }
          // }).finally(() => {
          //   this.loading = false
          // });
          return fetchAssociatedLabeledProdcutList(listQuery);
        } else {
          // 未关联
          return fetchUnassociatedLabeledProdcutList(listQuery)
        }
      },
      tabNumFn() {
        fetchLabelProductSum({
          current: this.page,
          map: {},
          model: {
            ...this.model,
            productLabelId: this.productLabelId,
          },
          order: "descending",
          size: this.limit,
          sort: "id",
        }).then((res) => {
          if (res.code == 0 && res.msg == "ok") {
            this.tabNum = res.data;
          }
        });
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      handleSelectAll(val) {
        this.multipleSelection = val;
      },
      handleSizeChange(val) {
        this.limit = val;
        this.load();
        this.tabNumFn();
      },
      handleCurrentChange(val) {
        this.page = val;
        this.$refs['pager-table'].clearSelection()
        this.load();
        this.tabNumFn();
      },
      handleCancel() {
        this.$emit("closeProduct");
      },
      submit() {
        if (this.multipleSelection.length == 0) {
          this.$emit("closeProduct");
          return;
        }
        let list = this.multipleSelection.map(({
          productId
        }) => productId);
        let params = {
          productIds: list,
          productLabelId: this.productLabelId,
        };

        if (this.tabCode == "ALREADY") {
          // 批量移除
          this.$confirm("您确定批量移除这些商品吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then((res) => {
            deleteAssociatedLabelProduct(params).then((res) => {
              this.reload();
              this.tabNumFn();
              this.$refs['pager-table'].clearSelection()
            });
          });
        } else {
          // 批量添加
          this.$confirm("您确定批量添加这些商品吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then((res) => {
            addAssociatedLabelProduct(params).then((res) => {
              this.reload();
              this.tabNumFn();
              this.$refs['pager-table'].clearSelection()
            });
          });
        }
      },

      //   刷新
      reload() {
        this.model = {
          keyword: "",
          manufacturer: "",
        };
        this.handleRefresh({
          page: 1,
          pageSize: this.pageSize
        })
        // this.handleRefresh();
      },
      searchLoad() {
        this.handleRefresh();
        // this.tabNumFn();
      },
      handleRefresh(pageParams) {
        // this.page = 1;
        // this.load();
        this.$refs['pager-table'].doRefresh(pageParams)
        this.tabNumFn();
      },
      // tab切换
      handleChangeTab(tab) {
        this.$refs['pager-table'].clearSelection();
        this.multipleSelection = [];
        this.tabCode = tab.value;
        this.handleRefresh();
      },
    },
  };

</script>

<style lang="scss" scoped>
  .page-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #505465;
    font-size: 13px;
    margin-top: 16px;
  }

  .bottom_btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .content_box {
    padding: 0 14px;
  }
  .productModelRelevance {
    position: relative;
    .bottom_box {
      position: absolute;
      bottom: 13px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 98%;
    }
  }
  .productModelRelevance ::v-deep  .el-form-item {
  margin-bottom: 14px;
}
.btn_class {
  border-radius: 2px;
}
</style>
