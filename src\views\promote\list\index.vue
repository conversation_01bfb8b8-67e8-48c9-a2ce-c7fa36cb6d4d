<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="listParams"
      @reset="load"
      @search="onSubmit"
    >
      <im-search-pad-item prop="code">
        <el-input v-model="listParams.code" placeholder="请输入商品名称/通用名/商品编码" />
      </im-search-pad-item>
      <im-search-pad-item prop="name">
        <el-input v-model="listParams.name" placeholder="请输入客户名称" />
      </im-search-pad-item>
    </im-search-pad>

    <div class="tab_bg">
      <el-tabs v-model="activeName">
        <el-tab-pane label="已发布推广品种" name="first"></el-tab-pane>
        <el-tab-pane label="已下架推广品种" name="second"></el-tab-pane>
      </el-tabs>
      <div class="tab_btns">
        <el-button @click="getList">刷新</el-button>
      </div>
       <el-table
        ref="dragTable"
        v-loading="listLoading"
        :data="list"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" :render-header="renderHeader" width="60">
          <template slot-scope="{ row }">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column
         label="图片"
        >
          <template>
            <span>
              <img src="https://ss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=1731403064,2211042829&fm=11&gp=0.jpg" alt="" width="50">
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in activeName == 'first' ? tableTitle : tableTitle2"
          :key="index"
          :width="item.width"
          :label="item.label"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column width="140" label="操作">
          <template slot-scope="{ row }">
            <el-row class="table-edit-row">
              <span v-if="activeName === 'first'" class="table-edit-row-item">
                <el-button type="text" @click="showDetail(row.id)">编辑</el-button>
              </span>
              <span v-else>
                <el-link type="primary">编辑</el-link>
                <el-link type="danger" style="margin-left: 10px;">删除</el-link>
              </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-bind:total="total" v-bind:page="page" @pagination="pagination" class="fr"></pagination>
    </div>
  </div>
</template>
<script>
import { fetchList } from "@/api/article";
import Sortable from "sortablejs";
import Pagination from '@/components/Pagination/index.vue'

const TableColumns = [
  { label: "状态", name: "status" },
  { label: "商品编码", name: "productCode" },
  { label: "商品名称", name: "productName" },
  { label: "规格", name: "specifications" },
  { label: "生产厂家", name: "manufacturer" },
  { label: "销售价（元）", name: "salePrice" },
  { label: "推广费（元）", name: "promotionFee" },
  { label: "商品分类", name: "goodsClass" },
  { label: "已代理销售区域", name: "salesArea" },
  { label: "业务员", name: "salesman" }
];

const TableColumns2 = [
  { label: "状态", name: "status" },
  { label: "商品编码", name: "productCode" },
  { label: "商品名称", name: "productName" },
  { label: "规格", name: "specifications" },
  { label: "生产厂家", name: "manufacturer" },
  { label: "销售价（元）", name: "salePrice" },
  { label: "推广费（元）", name: "promotionFee" },
  { label: "商品分类", name: "goodsClass" },
  { label: "销售区域", name: "salesArea" }
];

const TableColumnList = [];
const TableColumn2List = [];

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] });
}

for (let i = 0; i < TableColumns2.length; i++) {
  TableColumn2List.push({ key: i, ...TableColumns2[i] });
}

export { TableColumnList, TableColumn2List };

export default {
  components: {
    Pagination
  },
  data () {
    return {
      listParams: {
        code: '',
        name: ''
      },
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableTitle2: TableColumn2List,
      tableVal: [],
      list: [{
        id:1,
        status: '推广中',
        productCode: 'YP00001',
        productName: '枸橼酸莫沙必利分散片',
        specifications: '120mg*21粒',
        manufacturer: '成都康弘药业集团.',
        salePrice: '20000.00',
        promotionFee: '200.00~300.00',
        goodsClass: '../补益安神/补气补血',
        salesArea: '省份1 市3 区4',
        salesman: 5
      },{
        id:2,
        status: '推广中',
        productCode: 'YP00001',
        productName: '枸橼酸莫沙必利分散片2',
        specifications: '120mg*21粒',
        manufacturer: '成都康弘药业集团.',
        salePrice: '20000.00',
        promotionFee: '200.00~300.00',
        goodsClass: '../补益安神/补气补血',
        salesArea: '省份1 市3 区4',
        salesman: 5
      }],
      total: 0,
      page: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
      },
      form: {},
      sortable: null,
      oldList: [],
      newList: [],
      activeName: 'first'
    }
  },
  methods: {
    showDetail (id) {
      this.$router.push({
        name: 'promoteDetails',
        query: {
          id: id
        }
      })
    },
    load () {

    },
    onSubmit () {

    },
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return (
        <div style="position:relative">
          <div onClick={this.setHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
          >
            <el-transfer
              vModel={this.tableVal}
              data={this.tableTitle}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },

    setHeaer: function () {
      this.showSelectTitle = !this.showSelectTitle;
    },
    async getList() {
      this.listLoading = true;
      const { data } = await fetchList(this.listQuery);
      this.list = data.items;
      this.total = data.total;
      this.listLoading = false;
      this.oldList = this.list.map((v) => v.id);
      this.newList = this.oldList.slice();
      this.$nextTick(() => {
        this.setSort();
      });
    },
    pagination(val) {
      this.listQuery.current = val.page
      this.listQuery.size = val.limit
      this.getList()
    },
    setSort() {
      const el = this.$refs.dragTable.$el.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      this.sortable = Sortable.create(el, {
        ghostClass: "sortable-ghost", // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData("Text", "");
        },
        onEnd: (evt) => {
          const targetRow = this.list.splice(evt.oldIndex, 1)[0];
          this.list.splice(evt.newIndex, 0, targetRow);

          // for show the changes, you can delete in you code
          const tempIndex = this.newList.splice(evt.oldIndex, 1)[0];
          this.newList.splice(evt.newIndex, 0, tempIndex);
        },
      });
    }
  }
}
</script>
