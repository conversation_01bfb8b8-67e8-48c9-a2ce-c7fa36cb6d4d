<template>
  <div class="pcmall-content" id="mallContainer">
    <page-title title="商城装修" :show-back="false">
      <el-button  @click="() => { $router.push('/pcmall/preview') }" style="padding: 9px 15px;">预览</el-button>
    </page-title>
    <div id="mallInnerContainer">
      <div class="businessbg edit-posi" :style="{'background-image':  'url(' + require('@/assets/imgs/pcIndex/businessbg.png')}">
        <div class="businesswrap wrap clearfix">
          <div class="buslogo" :style="{'background-image':  'url(' + require('@/assets/imgs/pcIndex/buslogo.png')}"></div>
          <div class="bustext">
            <h2>华东医药股份有限公司</h2>
            <dl>
              <dd>上架品种<em>12720</em><b></b></dd>
              <dd>起配金额<em>¥500</em></dd>
            </dl>
          </div>
          <div class="touchmerchant">
            <h4>联系商家</h4>
            <p>有任何问题欢迎电话或在线交谈</p>
            <div class="tchmtbtn clearfix">
              <a href="javascript:;" class="fl" id="telbtn"><i class="thicon_01"></i>电话联系</a>
              <a href="javascript:;" class="fr"><i class="thicon_02"></i>QQ交谈</a>
            </div>
          </div>
        </div>
        <div class="edit-pop" @click="activeEdit('header_bg')">点击编辑背景</div>
      </div>
      <div class="businessGroup">
        <div class="busHd">
          <div class="wrap clearfix">
            <ul>
              <li>首页</li>
              <li>全部商品</li>
              <li>商家资质</li>
            </ul>
            <div class="bussearch">
              <input type="text" name="" id="" value="" placeholder="搜索店铺商品">
              <a href="javascript:;" class="busseabtn"><i></i></a>
            </div>
          </div>
        </div>
        <div class="busBd wrap">
          <div class="busBox">
            <div class="ShopNotices edit-posi">
              <h2><i class="spnticon"></i>店铺公告</h2>
              <div class="spnttext">
                1.订单满300+运费发货，500元起包邮；客服工作时间：9:00-18:00，6点前订单隔天发出，周六休息，周六订单顺延至收周一发货；<br>
                2.首次合作客户，所有证件加盖公章，并邮寄至：浙江省杭州市延安路468号1号楼1号门7、9、10楼0571-256200 马苏(收)；<br>
                3.客服热线：0571-230021
              </div>
              <div class="edit-pop" @click="activeEdit('notice')">点击编辑公告</div>
            </div>
            <div class="Coupondiscount">
              <h2>领券优惠</h2>
              <div class="couplist clearfix">
                <ul>
                  <li>
                    <div class="coupname">
                      <b>9.5</b>
                      <em>折</em>
                      无使用门槛
                    </div>
                    <div class="couptime">2021.03.22-2021.03.30</div>
                    <a href="javascript:;" class="coupdraw">立即领取</a>
                  </li>
                  <li>
                    <div class="coupname">
                      <span>¥</span>
                      <b>10</b>
                      满199元可用
                    </div>
                    <div class="couptime">2021.03.22-2021.03.30</div>
                    <a href="javascript:;" class="coupdraw">立即领取</a>
                  </li>
                  <li>
                    <div class="coupname">
                      <b>9.5</b>
                      <em>折</em>
                      无使用门槛
                    </div>
                    <div class="couptime">2021.03.22-2021.03.30</div>
                    <a href="javascript:;" class="coupdraw">立即领取</a>
                  </li>
                  <li>
                    <div class="coupname">
                      <span>¥</span>
                      <b>10</b>
                      满199元可用
                    </div>
                    <div class="couptime">2021.03.22-2021.03.30</div>
                    <a href="javascript:;" class="coupdraw">立即领取</a>
                  </li>
                  <li>
                    <div class="coupname">
                      <b>9.5</b>
                      <em>折</em>
                      无使用门槛
                    </div>
                    <div class="couptime">2021.03.22-2021.03.30</div>
                    <a href="javascript:;" class="coupdraw">立即领取</a>
                  </li>
                </ul>
              </div>
              <div class="Congratulations">
                <i class="success"></i>
                恭喜您，领取成功
              </div>
            </div>
            <div class="productGroup">
              <div class="pdHd clearfix edit-posi">
                <ul>
                  <li class="on">商家推荐</li>
                  <li>新品</li>
                  <li>高毛利</li>
                  <li>非药品</li>
                </ul>
                <div class="edit-pop" @click="activeEdit('product')">点击编辑商品分组</div>
              </div>
              <div class="pdBd">
                <div class="pdBox clearfix">
                  <div class="gooscontent clearfix">
                    <ul>
                      <li v-for="item in goodsList">
                        <div class="recommenimg" :style="{'background-image':  'url(' + require('@/assets/imgs/pcIndex/img_4.png')}">
                        </div>
                        <div class="recommentext">
                          <h4 class="title"><a href="#">以岭连花清瘟胶囊治疗新型肺 炎宣肺泄热流行性感冒药</a></h4>
                          <div class="lable">0.35g*24支</div>
                          <div class="text">石家庄以岭药业股份有限公司</div>
                          <div class="price">￥<b>28.80</b></div>
                        </div>
                        <div class="joinshop"><a href="#">加入常购</a></div>
                        <div class="operationBox clearfix">
                          <em>中包装：10盒</em>
                          <div class="opera clearfix">
                            <div class="add_on">
                              <span class="jian">-</span>
                              <span class="num">1</span>
                              <span class="jia">+</span>
                            </div>
                            <div class="operabtn">
                              <a href="signin.html" class="Login">去登录</a>
                              <a href="javascript:;" class="addcart">加入购物车</a>
                              <a href="javascript:;" class="improve">完善资料</a>
                            </div>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--头部背景-->
    <import-image ref="importImage" :title="this.activeName" :type="activeType" @confirm="confirm" width="1200px" />
    <!--公告-->
    <notice ref="notice" :title="this.activeName" :type="activeType" @confirm="confirm" />
    <!--商品分组-->
    <product-group ref="productGroup" :title="this.activeName" :type="activeType" width="800px" @confirm="confirm" />
  </div>
</template>

<script>
import importImage from './components/importImage'
import productGroup from './components/productGroup'
import notice from './components/notice'
import { getPageData } from '@/api/pcmall'
export default {
  components: {
    importImage,
    productGroup,
    notice
  },
  data() {
    return {
      goodsList: [{},{},{},{},{},{},{},{},{},{},],
      activeName: '',
      activeType: '',
      backgroundPicUrl: '',
      content: '',
      pageDataProductGroupVoList: []
    }
  },
  mounted() {
    this.init()
    this.resizeContainer()
  },
  methods: {
    init() {
      getPageData().then(res => {
        this.backgroundPicUrl = res.data.backgroundPicUrl
        this.content = res.data.content
        this.pageDataProductGroupVoList = res.data.pageDataProductGroupVoList
      })
    },
    // 快速实现外部内容适配
    resizeContainer() {
      const maineContainer = document.querySelector('#mallContainer')
      const innerContainer = document.querySelector('#mallInnerContainer')
      const { width } = maineContainer.getBoundingClientRect()
      const PADDING = 40
      innerContainer.style.zoom = (width - PADDING) / 1200 > 1 ? 1 : (width - PADDING) / 1200
      innerContainer.style.overflowX = 'hidden'
    },
    activeEdit(type) {
      this.activeType = type
      switch (type) {
        case 'header_bg':
          this.activeName = '店铺背景'
          this.$refs.importImage.init(this.backgroundPicUrl)
          break;
        case 'notice':
          this.activeName = '店铺公告'
          this.$refs.notice.init(this.content)
            break;
        case 'product':
          this.activeName = '商品分组'
          this.$refs.productGroup.init(this.pageDataProductGroupVoList)
          break;
      }
    },
    confirm() {
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/home.scss'

</style>
