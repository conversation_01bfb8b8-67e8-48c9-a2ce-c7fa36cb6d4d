import request from '@/utils/request'
import qs from 'qs'
import requestExport from '@/utils/requestExport'
import { id } from 'html-webpack-plugin/lib/chunksorter'

export function fetchList(query) {
  return request({
    url: '/product/admin/productDescriptionRel/page',
    method: 'get',
    params: query
  })
}
//渠道控销列表
export function productControlSaleList(query) {
  return request({
    url: '/product/admin/productControlSale/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除
export function deleteProductControlSale(ids) {
  return request({
    url: `/product/admin/productControlSale`,
    method: 'delete',
    params: {
      ids: ids
    }
  })
}
//保存控销
export function productControlSaleAdd(query) {
  return request({
    url: '/product/admin/productControlSale',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//控销详情
export function productControlSaleById(id) {
  return request({
    url: `/product/admin/productControlSale/${id}`,
    method: 'get'
  })
}
//修改控销
export function productControlSaleEdit(query) {
  return request({
    url: '/product/admin/productControlSale',
    method: 'PUT',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}
//删除控销里的某种客户规则数据
export function deleteControlItems(id, selectCustomer) {
  return request({
    url: '/product/admin/productControlSale/deleteControlItem',
    method: 'post',
    params: {
      id: id,
      selectCustomer: selectCustomer
    }

  })
}
//分页查询工具
export function productList(query) {
  return request({
    url: '/product/admin/product/anno/page',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}

export function shareProductPage(query) {
  return request({
    url: '/product/admin/product/shareProductPage',
    method: 'post',
    data: query,
    transformRequest: [function () {
      return JSON.stringify(query)
    }],
    headers: { 'Content-Type': 'application/json;' }
  })
}


// 导入记录
export function recordImport(data) {
  return request({
    method: 'post',
    url: '/product/admin/productImport/importPage',
    data
  })
}

// 下载导入结果
export function downloadImportResults(importId) {
  return requestExport({
    method: 'post',
    url:`/product/admin/productImport/downloadImportResults/${importId}`,
    headers: { responseType: 'blob' }
  })
}

// 刪除导入记录
export function deleteRecodeInport(id) {
  return request({
    method: 'Delete',
    url:"/product/admin/productImport?ids[]=" + id,
  })
}

//商品导入记录接口
export function importProductList(data) {
  return request({
    method: 'post',
    url: '/product/admin/productUpload/page',
    data
  })
}


//商品导入接口
export function importProductFile(data) {
  return request({
    method: 'post',
    url: '/product/admin/product/import',
    data
  })
}

//商品导入记录删除接口
export function importProductDelete(id) {
  // return request({
  //   method: 'post',
  //   url: '/product/admin/productPlatform/import/delete',
  //   data
  // })
  return request({
    method: 'delete',
    url: '/product/admin/productUpload?ids[]=' + id
  })
}

//商品导出记录
export function exportProductLog(data) {
  return requestExport({
    method: 'post',
    url: '/product/admin/productUploadLog/export',
    data,
    headers: { responseType: 'blob' }
  })
}

/**
 * 选择店铺接口
 * @param data
 */
export function outList(data) {
  let str = JSON.stringify(data)
  let params = JSON.parse(str)
  if (data.model.approvalStatus.code == "stale") {
    params.model.whetherExpired = "Y";
    delete params.model.approvalStatus
  }
  return request({
    url: "/merchant/admin/saleMerchant/page",
    method: 'post',
    data: params
  })
}

// 商品分类
export function getProductCategoryPageList(data) {
  return request({
    url: '/product/admin/categoryPlatform/page',
    method: 'post',
    data
  })
}

// 商家端的商品分类
//
export function getCategoryType(data) {
  return request({
    url: "/product/admin/category/query",
    method: "post",
    data
  })
}

export function deleteProductCategoryApi(id) {
  return request({
    url: '/api/product/admin/categoryPlatform?ids[]=' + id,
    method: 'delete'
  })
}

export function getCategoryChildren(data) {
  return request({
    url: '/product/admin/categoryPlatform/listChildren',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    method: 'post',
    data
  })
}

// 开启品牌店铺企业列表
export function getBrandShop(data) {
  return request({
    url: '/merchant/admin/saleMerchant/brand/shop/page',
    method: 'post',
    data
  })
}

// 批量关联企业
export function batchSave(data) {
  return request({
    url: '/merchant/admin/merchantRel/batchSave',
    method: "post",
    data: qs.stringify(data)
  })
}

// 目标商家分页
export function targetPage(data) {
  return request({
    url: '/merchant/admin/merchantRel/sale/target/page',
    method: 'post',
    data
  })
}

// 来源商家分页列表（销售商）
export function sourcePage(data) {
  return request({
    url: '/merchant/admin/merchantRel/sale/source/page',
    method: 'post',
    data
  })
}

// 启动数据同步
export function batchUpdateSyncY(data) {
  return request({
    url: '/merchant/admin/merchantRel/batchUpdateSyncY',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 关闭数据同步
export function batchUpdateSyncN(data) {
  return request({
    url: '/merchant/admin/merchantRel/batchUpdateSyncN',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 删除数据同步
export function delMerchantRel(id) {
  return request({
    url: '/merchant/admin/merchantRel',
    method: 'delete',
    params: {
      'ids': id
    },
  })
}

/**
 * 赠品管理 start
 */
export function fetchBrandList(data) {
  return request({
    url: '/product/admin/brand/page',
    method: 'post',
    data
  })

}

/**
 * 赠品管理 end
 */

// 导出记录列表
export function exportRecodeList(data) {
  return request({
    url: '/product/admin/productExport/page',
    method: 'post',
    data
  })
}

// 导出记录删除
export function deleteExportRecode(id) {
  return request({
    url: '/product/admin/productExport?ids[]=' + id,
    method: 'Delete',
  })
}
