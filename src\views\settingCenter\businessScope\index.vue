<template>
  <div class="productTypeListPageContent">
    <im-search-pad :has-expand="false" :model="model" @reset="resetForm" @search="onSearchSubmitFun">
      <im-search-pad-item prop="label">
        <el-input v-model="model.label" placeholder="请输入经营类目"/>
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout ref="tabs-layout" :tabs="[{ name: '经营类目' }]">
        <template slot="button">
          <div>
            <el-button @click="resetForm">刷新</el-button>
            <el-button v-if="checkPermission(['admin', 'sale-saas-setting-manage-businessScope:add', 'sale-platform-setting-manage-businessScope:add'])" type="primary" @click="addType">+ 新增经营类目</el-button>
          </div>
        </template>
      </tabs-layout>

      <!--   表格   -->
      <el-table ref="tableDom" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" v-loading="listLoading" :data="list" row-key="id" border fit highlight-current-row style="width: 100%">
        <el-table-column align="center" width="65" show-overflow-tooltip :render-header="renderHeader" fixed>
          <template slot-scope="scope">
<!--            <span>{{ scope.$index + 1 }}</span>-->
          </template>
        </el-table-column>
        <el-table-column v-for="(item,index) in tableTitles" :key="item.name" :prop="item.prop" :min-width="item.width" show-overflow-tooltip :label="item.label"></el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="140" class="itemAction">
          <template slot-scope="{row}">
            <el-row class="table-edit-row">
                <span class="table-edit-row-item">
                  <el-button v-if="row.parentCategoryCode === '0' && checkPermission(['admin', 'sale-saas-setting-manage-businessScope:addChild', 'sale-platform-setting-manage-businessScope:addChild'])" @click="addChildType(row,'addChild')" type="text">新增子分类</el-button>
                  <el-button v-if="checkPermission(['admin', 'sale-saas-setting-manage-businessScope:edit', 'sale-platform-setting-manage-businessScope:edit'])" @click="editFun(row,'edit')" type="text">编辑</el-button>
<!--                  <del-el-button style="margin-left: 10px" :target-id="row.id" :text="text" @handleDel="handleDel"></del-el-button>-->
                </span>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <edit-dia v-if="showEdit" :visible.sync="showEdit" :row="currnetItem" @resetForm="resetForm"></edit-dia>
  </div>
</template>


<script>
const TableColumns = [
  { label: "经营范围编码", name: "categoryCode", prop: "categoryCode", width: "180" },
  { label: "经营范围名称", name: "categoryName", prop: "categoryName", width: "180" },
  // { label: "排序", name: "sortValue", prop: "sortValue", width: "130" },
  { label: "操作人", name: "updateUser", prop: "updateUser", width: "150" },
  { label: "操作时间", name: "updateTime", prop: "updateTime", width: "180" }
];
const TableColumnList = [];
for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[ i ] });
}
import { listBusinessCategory, deleteBusinessCategory } from "@/api/settingCenter";
import editDia from "@/views/settingCenter/businessScope/editDia";
import delElButton from "@/components/eyaolink/delElButton";
import checkPermission from '@/utils/permission';

export default {
  //import引入的组件
  components: {
    editDia,
    delElButton
  },

  data() {
    return {
      model: {
        label: ""
      },
      list: [],
      tableTitles: TableColumnList,
      showEdit: false,
      currnetItem: {},
      listLoading: false,
      text: "您确定删除此经营范围吗？"
    };
  },
  watch: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},

  computed: {},

  created() {
    this.load();
  },

  filters: {},

  //方法集合
  methods: {
    checkPermission,
    async load(params) {
      this.listLoading = true;
      let listQuery = {
        ...this.model
      };
      let { data } = await listBusinessCategory(listQuery);
      this.listLoading = false;
      this.dealData(data || []);
      // this.list = data || [];
    },
    // 将数据处理成树形结构
    dealData(list = []) {
      let parentList = [];
      let childList = [];
      list.forEach(item=>{
        if (item.parentCategoryCode === '0') {
          parentList.push(item);
        } else {
          childList.push(item);
        }
      });

      parentList = parentList.map(item => {
        let tarObj = {
          ...item,
          children:[]
        };
        let listArr = childList.filter(o=> o.parentCategoryCode === item.categoryCode );
        tarObj.children = listArr;
        return tarObj;
      });
      this.list = parentList || [];
    },
    // 新增经营类目
    addType() {
      this.currnetItem = {}
      this.showEdit = true;
    },
    addChildType(row, type) {
      this.currnetItem = {
        ...row,
        type
      };
      this.showEdit = true;
    },
    handleDel(id) {
      deleteBusinessCategory(id).then(res =>{
        if (res.code === 0 && res.msg === 'ok') {
          this.$message.success('删除经营访问成功');
          this.resetForm();
        }
      })
    },
    editFun(row, type) {
      this.currnetItem = {
        ...row,
        type
      };
      this.showEdit = true;
    },
    renderHeader(h, { column }) {
      return (
        <div style="position:relative">
          <div>
            <i class="el-icon-menu"/>
          </div>
        </div>
      );
    },
    resetForm() {
      this.model = {
        label: ""
      };
      this.load();
    },
    onSearchSubmitFun() {
      this.load();
    },
  },

};
</script>


<style lang="scss" scoped>

</style>
