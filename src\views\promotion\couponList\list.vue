<template>
  <div class="archivesPageContent">
    <div class="temp_searchBox">
      <el-form
        :inline="true"
        ref="searchForm"
        :model="listQuery"
        class="form-inline"
      >
        <el-form-item label="" prop="merchant">
          <el-input
            v-model.trim="listQuery.model.merchant"
            placeholder="请输入商家名称/电话"
          ></el-input>
        </el-form-item>
        <el-form-item label="" prop="coupon">
          <el-input
            v-model.trim="listQuery.model.coupon"
            placeholder="请输入优惠券名称/券码"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearchSubmitFun">搜索</el-button>
          <el-button @click="resetForm('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="title flex_between_center">
      <div>
        <el-tabs v-model="tabType" class="typeTabs">
          <el-tab-pane label="领取列表" name="list"></el-tab-pane>
        </el-tabs>
      </div>
      <div></div>
    </div>

    <div class="table">
      <el-table
        ref="table"
        v-if="list"
        v-loading="listLoading"
        :data="list"
        row-key="index"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column
          align="center"
          width="80"
          :render-header="renderHeader"
        >
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableTitle"
          :key="index"
          :min-width="item.width ? item.width : '350px'"
          :label="item.label"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span
              v-if="item.name == 'used'"
              :style="row[item.name] == 1 ? 'color:#f7a945;' : 'color:#86c12a'"
              >{{ row[item.name] == 1 ? "已使用" : "未使用" }}</span
            >
            <span v-else-if="item.name == 'name'">{{
              row.merchant[item.name]
            }}</span>
            <span v-else-if="item.name == 'ceo_mobile'">{{
              row.merchant[item.name]
            }}</span>
            <span
              v-else-if="item.name == 'status'"
              :style="
                row[item.name] == 'ENABLED' ? 'color:#86c12a;' : 'color:#f00'
              "
              >{{
                row[item.name] == "ENABLED" ? "可用" : "已禁用"
              }}</span
            >
            <span v-else>{{ row[item.name] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          label="操作"
          width="150"
          class="itemAction"
        >
          <template slot-scope="{ row }">
            <el-button @click="disableFun(row)" type="text"  :disabled="row.status != 'ENABLED'"
              >禁用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="flex_between_center">
        <div></div>
        <pagination
          v-if="total > 0"
          :pageSizes="[10, 20, 50, 100]"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.size"
          @pagination="getlist"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { MessageBox, Message } from "element-ui";
import axios from "axios";
import Pagination from "@/components/Pagination";
import tableInfo from "@/views/promotion/couponList/tableInfo";
export default {
  data() {
    return {
      listLoading: false,
      list: [],
      tabType: "list",
      listQuery: {
        current: 1,
        size: 10,
        model: {},
      },
      total: 100,
      cityValue: [],
      tableTitle: [],
      tableSelectTitle: [0, 1, 2, 3],
      multipleSelection: [],
      multipleSelectionId: [],
      showSelectTitle: false,
    };
  },
  methods: {
    resetForm() {
      this.list = [];
      this.listQuery = {
        current: 1,
        size: 10,
        model: {},
      };
      this.getlist();
    },
    disableFun(row) {
      axios
        .get("https://erp.qlksc.cn/api/disable_coupon?cid=" + row.id)
        .then((res) => {
          if(res.data.code== 200) {
            row.status = "DISABLED"
            this.$message.success('操作成功')
          }
        })
        .catch((req) => {});
    },
    onSearchSubmitFun() {
      this.list = [];
      this.getlist();
    },
    getlist() {
      let that = this;
      this.listLoading = true;
      axios
        .get(
          `https://erp.qlksc.cn/api/coupon?page=${
            that.listQuery.current
          }&pagesize=${that.listQuery.size}&merchant=${
            that.listQuery.model.merchant ? that.listQuery.model.merchant : ""
          }&coupon=${
            that.listQuery.model.coupon ? that.listQuery.model.coupon : ""
          }`
        )
        .then((res) => {
          that.list = res.data.data;
          that.total = res.data.total;
          that.listLoading = false;
        })
        .catch((req) => {
          Message({
            message: req.message|| '系统维护中！',
            type: "error",
            duration: 5 * 1000,
          });
        });
    },

    initTbaleTitle() {
      this.tableSelectTitle = [];
      this.tableTitle = tableInfo[this.tabType];
    },
    renderHeader(h, { column }) {
      var titles = tableInfo[this.tabType];
      var titlesName = ["显示字段项", "隐藏字段项"];
      return (
        <div style="position:relative">
          <div onClick={this.showHeaer}>
            <i class="el-icon-menu" />
          </div>
          <el-dialog
            title="设置显示列表"
            showClose={false}
            visible={this.showSelectTitle}
            width="640px"
            center
            append-to-body={true}
          >
            <el-transfer
              vModel={this.tableSelectTitle}
              data={titles}
              titles={titlesName}
              onChange={this.setleftTitleFun}
            ></el-transfer>
            <div style="margin-top: 25px;text-align: center;">
              <el-button onClick={this.closeHeaer}>取消</el-button>
              <el-button type="primary" onClick={this.setHeaer}>
                确认
              </el-button>
            </div>
          </el-dialog>
        </div>
      );
    },
    setleftTitleFun(val) {
      this.tableSelectTitle = val;
    },
    showHeaer: function () {
      this.showSelectTitle = true;
    },
    closeHeaer: function () {
      this.showSelectTitle = false;
      this.tableSelectTitle = [];
    },
    setHeaer: function () {
      var titles = tableInfo[this.tabType];
      var listinfo = titles.filter((element, index, self) => {
        return !this.tableSelectTitle.includes(element.key);
      });
      this.tableTitle = listinfo;
      this.showSelectTitle = !this.showSelectTitle;
    },
  },
  created() {
    this.initTbaleTitle();
    this.getlist();
  },
  components: {
    Pagination,
  },
};
</script>


<style lang="scss" scoped>
.archivesPageContent {
  padding: 0;
  .temp_searchBox {
    height: 64px;
    overflow: hidden;
    margin-bottom: 0;
  }
  .form-inline {
    height: 60px;
    overflow: hidden;
  }
  .title {
    border-top: 16px solid #f2f3f4;
    border-bottom: 2px solid #ebecee;
    margin-bottom: 35px;
    padding: 0 12px;
    padding-top: 10px;
    span {
      margin-bottom: -2px;
      padding: 0 15px;
      height: 40px;
      line-height: 30px;
      display: block;
      background: rgba(255, 255, 255, 0);
      border-bottom: 2px solid rgb(64, 158, 255);
      font-size: 16px;
      font-family: "PingFangSC-Regular", "PingFang SC", "PingFangSC-Regular",
        "PingFang SC"-400;
      font-weight: 400;
      color: rgb(64, 158, 255);
    }
  }

  .table {
    padding: 0 12px;
  }
  .formItem {
    width: 586px;
  }
  .line {
    color: #dfe6ec;
    margin: 0 6px;
  }
  .typeTabs {
    height: 40px;
    margin-bottom: -2px;
  }
}
</style>
