<template>
  <div>
    <im-search-pad
      :has-expand="false"
      :model="model"
      @reset="reset"
      @search="onSearch"
    >
      <im-search-pad-item prop="productName">
        <el-input v-model.trim="model.productName" placeholder="请输入商品名称" />
      </im-search-pad-item>
      <im-search-pad-item prop="depositRefundable">
        <el-select v-model="model.depositRefundable" placeholder="是否可退定金">
          <el-option label="可退定金" value="Y" />
          <el-option label="不可退定金" value="N" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="arriveGoodsStatus">
        <el-select v-model="model.arriveGoodsStatus" placeholder="是否已到货">
          <el-option label="未到货" value="N" />
          <el-option label="已到货" value="Y" />
        </el-select>
      </im-search-pad-item>
      <im-search-pad-item prop="during">
        <el-date-picker
          v-model="during"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </im-search-pad-item>
    </im-search-pad>
    <div class="tab_bg">
      <tabs-layout
        ref="tabs-layout"
        v-model="preSellStatus"
        :tabs="tabs"
        @change="handleChangeTab"
      >
        <template slot="button">
          <el-button @click="onSearch">刷新</el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:add', 'sale-platform-promotion-presell:add'])" type="primary" @click="editPreSell()">+ 新增预售商品</el-button>
        </template>
      </tabs-layout>
      <table-pager ref="todoTable" :options="tableTitle" :remote-method="getList" :data.sync="tableData" :operationWidth="200">
        <el-table-column slot="depositRatio" label="定金比例">
          <template slot-scope="scope">
            {{ scope.row.depositRatio }}%
          </template>
        </el-table-column>
        <el-table-column slot="depositRatio" label="预售时间" width="200">
          <template slot-scope="scope">
            {{ scope.row.presellStartTime }} -  {{ scope.row.presellEndTime }}
          </template>
        </el-table-column>
        <div slot-scope="props">
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:edit', 'sale-saas-promotion-presell:edit']) && props.row.presellStatus && props.row.presellStatus.code === 'NOT_START'" type="text" @click="editPreSell(props.row.id)">编辑</el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:detail', 'sale-platform-promotion-presell:detail']) && props.row.presellStatus.code !== 'NOT_START'" type="text" @click="editPreSell(props.row.id)">查看</el-button>
          <el-divider v-if="checkPermission(['admin','sale-saas-promotion-presell:detail', 'sale-platform-promotion-presell:detail'])" direction="vertical" />
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:pullOffShelves', 'sale-platform-promotion-presell:pullOffShelves']) && props.row.presellEnabled.code === 'ON'" type="text" @click="handleOnOff(props.row)">
            下架
          </el-button>
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:putOnSale', 'sale-platform-promotion-presell:putOnSale']) && props.row.presellEnabled.code !== 'ON'" type="text" @click="handleOnOff(props.row)">
            上架
          </el-button>
          <template  v-if="props.row.arriveGoodsStatus.code === 'N'">
            <el-divider v-if="checkPermission(['admin','sale-saas-promotion-presell:arrive', 'sale-platform-promotion-presell:arrive'])" direction="vertical" />
            <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:arrive', 'sale-platform-promotion-presell:arrive'])" type="text" @click="updateGoodStatus(props.row)">到货</el-button>
          </template>
          <el-divider v-if="checkPermission(['admin','sale-saas-promotion-presell:del', 'sale-platform-promotion-presell:del'])" direction="vertical" />
          <el-button v-if="checkPermission(['admin','sale-saas-promotion-presell:del', 'sale-platform-promotion-presell:del'])" type="text" @click="deletePreSell(props.row.id)">删除</el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import { getPreSellPage, delPreSell, updateSetPreSellStatus } from '@/api/promotionCenter/presell'

const TableColumns = [
  { label: '活动编码', name: 'activityCode', prop: 'activityCode', width: '100' },
  { label: '商品ERP编码', name: 'productCode', prop: 'product.productCode', width: "130" },
  { label: '商品名称', name: 'productName', prop: 'product.productName' , width: '150' },
  { label: '规格', name: 'spec', prop: 'product.spec', width: "100" },
  { label: '生产厂家', name: '', prop: 'product.manufacturer', width: '100' },
  { label: '预售销售价', name: 'presellPrice', prop: 'presellPrice', width: '120' },
  { label: '定金比例', name: 'depositRatio', prop: 'depositRatio', width: '80', slot: true },
  { label: '是否可退定金', name: 'depositRefundable', prop: 'depositRefundable.desc', width: '120' },
  { label: '预售时间', name: 'presellTime', prop: 'presellTime', slot: true }
]

const TableColumnList = []

for (let i = 0; i < TableColumns.length; i++) {
  TableColumnList.push({ key: i, ...TableColumns[i] })
}
export { TableColumnList }

export default {
  name: 'depositBooking',
  data() {
    return {
      // table配置
      showSelectTitle: false,
      tableTitle: TableColumnList,
      tableVal: [],
      loading: false,
      showCoupon: false,
      showAdd: false,
      preSellStatus: 'first',
      model: {
        productName: '',
        depositRefundable: '',
        arriveGoodsStatus: '',
        presellStartTime: '',
        presellEndTime: '',
        presellStatus: ''
      },
      during: '',
      tableData: [],
      goodsList: [],
      itemDetail: ''
    }
  },
  computed:{
    tabs () {
      return [
        {
          name: '全部',
          value:'first',
          hide: !checkPermission(['admin', 'sale-saas-promotion-presell:allView','sale-platform-promotion-presell:allView'])
        },
        {
          name: '未开始',
          value:'NOT_START',
          hide: !checkPermission(['admin', 'sale-saas-promotion-presell:pendingView','sale-platform-promotion-presell:pendingView'])
        },
        {
          name: '进行中',
          value:'PROCEED',
          hide: !checkPermission(['admin', 'sale-saas-promotion-presell:processingView','sale-platform-promotion-presell:processingView'])
        },
        {
          name: '已结束',
          value:'FINISHED',
          hide: !checkPermission(['admin', 'sale-saas-promotion-presell:finishedView','sale-platform-promotion-presell:finishedView'])
        },
        {
          name: '已下架',
          value:'OBSOLETE',
          hide: !checkPermission(['admin', 'sale-saas-promotion-presell:pullOffShelvesView','sale-platform-promotion-presell:pullOffShelvesView'])
        }
      ]
    }
  },
  methods: {
    checkPermission,
    async getList(params) {
      const listQuery = {
        model: {
          ...this.model,
          presellStartTime: this.during[0],
          presellEndTime: this.during[1]
        }
      }
      Object.assign(listQuery, params)
      this.loading = true
      return await getPreSellPage(listQuery)
    },
    onSearch() {
      this.$refs.todoTable.doRefresh({
        page: 1,
        pageSize: 10
      })
    },
    reset() {
      this.during = ''
      this.onSearch()
    },
    handleChangeTab(tab){
      this.preSellStatus = tab.value;
      if(tab.value === 'first') {
        this.model.presellStatus = '';
      } else {
        this.model.presellStatus = tab.value;
      }
      this.onSearch()
    },
    deletePreSell(id) {
      this.$confirm('确定要删除此预售?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const data = await delPreSell([id])
        if (data.code === 0 && data.msg === 'ok') {
          this.$message.success('删除成功！')
          this.onSearch()
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /**
     * 上下架
     * @param preSell
     * @returns {Promise.<void>}
     */
    async handleOnOff(preSell) {
      const { id, presellEnabled } = preSell
      const enabled = presellEnabled.code === 'ON' ? 'OFF' : 'ON'
      const param = {
        id,
        presellEnabled: enabled
      }
      const data = await updateSetPreSellStatus(param)
      if (data.code === 0 && data.msg === 'ok') {
        this.$message.success('操作成功！')
        this.onSearch()
      }
    },
    /**
     * 更新产品是否到货
     * @param preSell
     * @returns {Promise.<void>}
     */
    async updateGoodStatus(preSell) {
      const { id } = preSell
      const param = {
        id,
        arriveGoodsStatus: 'Y'
      }
      const data = await updateSetPreSellStatus(param)
      if (data.code === 0 && data.msg === 'ok') {
        this.$message.success('操作成功！')
        this.onSearch()
      }
    },
    // 新增或者编辑
    editPreSell(id, type) {
      this.$router.push({
        path: '/promotion/presell/edit',
        query: {
          saleMerchantId: this.saleMerchantId,
          id: id,
          type: type
        }
      })
    }
  }
}
</script>
