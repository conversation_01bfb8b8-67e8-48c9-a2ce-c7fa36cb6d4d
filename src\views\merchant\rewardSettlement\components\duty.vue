<template>
  <div>
    <div class="search" style="margin-bottom: 10px;">
      <Search :model="query.model" :fields="searchFields" omit @search="search" @reset="reload" />
    </div>
    <div class="table" style="padding: 10px;background-color: #fff">
      <TabsLayout ref="tabs-layout" v-model="query.model.settleStatus" :tabs="tabList" @change="search">
        <template slot="button">
          <el-button @click="reload">刷新</el-button>
          <el-button type="primary" v-if="query.model.settleStatus === 'WAIT'" @click="batchSettlement" :loading="batchLoading">批量结算</el-button>
          <PagedExport controllable text-type="primary" :max-usable="total" :max="20000" :before-close="(params) => setRewardListExport(params, 'list')" text="导出奖励列表" />
          <PagedExport controllable text-type="primary" :max-usable="total" :max="20000" :before-close="(params) => setRewardItemListExport(params, 'list')" text="导出奖励明细" />
        </template>
      </TabsLayout>
      <StatisticsText :statisticsData="statisticsData" />
      <table-pager ref="table" selection :pageSize="query.size" :remote-method="load" :options="tableTitle"
        :data.sync="tableData" :operationWidth="100" @selection-change="tableSelectionChange">
        <el-table-column slot="name" label="奖励项目">
          <template slot-scope="{row}">
            <div>奖励名称: {{ row.name }}</div>
            <div>奖励方式: {{ row.rewardWayEnum.desc }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="objectName" label="奖励人">
          <template slot-scope="{row}">
            <div>采购人: {{ row.purName }}</div>
            <div>奖励人: {{ row.objectName }}</div>
            <div>奖励时间: {{ row.createTime }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="orderNo" label="订单信息">
          <template slot-scope="{row}">
            <div>订单编号: {{ row.billNo }}</div>
            <div>支付时间: {{ row.billPayTime }}</div>
            <div>完成时间: {{ row.billSuccessTime }}</div>
          </template>
        </el-table-column>
        <el-table-column slot="amount" label="奖励结果">
          <template slot-scope="{row}">
            <div>奖励现金: {{ row.amount }}</div>
            <div>成交金额: {{ row.realAmount }}</div>
            <div>奖励比例: {{ row.settingRatio }}%</div>
          </template>
        </el-table-column>
        <el-table-column slot="id" label="结算处理">
          <template slot-scope="{row}">
            <div>结算编号: {{ row.id }}</div>
            <div>结算状态: {{ row.settleStatus.desc }}</div>
            <div>结算时间: {{ row.settleTime }}</div>
          </template>
        </el-table-column>
        <div slot-scope="{row}">
          <el-button type="text" @click="gotoDetail(row.id)">查看奖励明细</el-button>
          <el-button type="text" @click="gotoOrderDetail(row.orderId)">查看订单</el-button>
        </div>
      </table-pager>
    </div>
  </div>
</template>

<script>
import { getDutyPage, setBatchSettlement, rewardListExport, rewardItemListExport, getRewardStatistics } from '@/api/retailStore';
import TabsLayout from '@/components/TabsLayout/index.vue';
import { MessageConfirmExport, deepClone } from '@/utils';
import PagedExport from "@/components/PagedExport/index.vue"
import Search from '@/views/tradeCenter/sales/components/LogicalComponents/Search.vue';
import StatisticsText from '../statisticsText.vue';

const TableColumns = [{
  label: '奖励项目',
  name: 'name',
  prop: 'name',
  slot: true
},
{
  label: '奖励人',
  name: 'objectName',
  prop: 'objectName',
  slot: true
},
{
  label: '订单信息',
  name: 'orderNo',
  prop: 'orderNo',
  slot: true
},
{
  label: '奖励结果',
  name: 'amount',
  prop: 'amount',
  slot: true
},
{
  label: '结算处理',
  name: 'id',
  prop: 'id',
  slot: true
}].map((v, i) => ({ key: i, ...v }))

export default {
  name: 'duty',
  components: {
    Search,
    TabsLayout,
    PagedExport,
    StatisticsText
  },
  data() {
    return {
      settingMethod: 'DUTY',
      statisticsData: {},
      batchLoading: false,
      tableData: [],
      tableTitle: TableColumns,
      total: 0,
      tabList: [
        { name: '全部', value: '' },
        { name: '预结算', value: 'PREPARE' },
        { name: '待结算', value: 'WAIT' },
        { name: '已结算', value: 'HAD' },
      ],
      searchFields: [
        {
          type: 'DateRange',
          startPlaceholder: '奖励时间开始',
          endPlaceholder: '奖励时间结束',
          prop: 'createTimeRange'
        },
        {
          prop: 'objectName',
          placeholder: '奖励人',
        },
        {
          prop: 'purName',
          placeholder: '采购人',
        },
        {
          type: 'DateRange',
          startPlaceholder: '结算时间开始',
          endPlaceholder: '结算时间结束',
          prop: 'settleTimeRange',
          omit: true,
        },
        {
          prop: 'orderNo',
          placeholder: '订单编号',
          omit: true,
        },
        {
          prop: 'id',
          placeholder: '结算编号',
          omit: true,
        },
      ],
      selectedIdList: [],
      query: {
        size: 10,
        model: {
          createTimeRange: [],
          objectName: '',
          purName: '',
          settleTimeRange: [],
          orderNo: '',
          settleStatus: '',
          id: ''
        }
      }
    }
  },
  methods: {
    // 选中每一项
    tableSelectionChange(val) {
      this.selectedIdList = val.map(item => item.id)
    },
    // 刷新
    handleRefresh(pageParams) {
      this.$refs.table.doRefresh(pageParams)
    },
    // 加载方法
    async load(params) {
      Object.assign(this.query, params)
      let query = deepClone(this.query)
      let model = query.model
      if (model.createTimeRange.length === 2) {
        model.beginCreateTime = model.createTimeRange[0]
        model.endCreateTime = model.createTimeRange[1]
      }
      if (model.settleTimeRange.length === 2) {
        model.beginSettleTime = model.settleTimeRange[0]
        model.endSettleTime = model.settleTimeRange[1]
      }
      this.setRewardStatistics()
      const result = await getDutyPage(query)
      this.total = result.data.total
      return result
    },
    // 获取参数
    getQueryModel() {
      return { ...this.query.model, settingMethod: this.settingMethod }
    },
    // 设置统计数据
    setRewardStatistics() {
      getRewardStatistics({ model: this.getQueryModel() }).then(res => {
        this.statisticsData = res
      })
    },
    // 搜索
    search() {
      this.handleRefresh({ page: 1, pageSize: 10 })
    },
    // 重置
    reload() {
      const settleStatus = this.query.model.settleStatus
      Object.assign(this.$data.query, this.$options.data().query)
      this.query.model.settleStatus = settleStatus
      this.search()
    },
    // 去奖励详情
    gotoDetail(id) {
      this.$router.push('/merchant/rewardSettlementDetail?id=' + id)
    },
    // 去订单详情
    gotoOrderDetail(id) {
      this.$router.push('/sale/detail?id=' + id)
    },
    // 批量结算
    batchSettlement() {
      if (this.selectedIdList.length === 0) {
        this.$message.error('请选择一条记录')
        return
      }
      this.$confirm('确定要批量结算吗', '批量结算', {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning',
      }).then(() => {
        this.batchLoading = true
        setBatchSettlement({ ids: this.selectedIdList }).then(() => {
          this.$message.success('批量结算成功')
          this.search()
        }).finally(() => {
          this.batchLoading = false
        })
      }).catch(() => { })
    },
    // 导出奖励列表
    setRewardListExport(params) {
      rewardListExport({ ...this.getQueryModel(), ...params }).then(() => {
        MessageConfirmExport()
      })
    },
    // 导出奖励明细
    setRewardItemListExport(params) {
      rewardItemListExport({ ...this.getQueryModel(), ...params }).then(() => {
        MessageConfirmExport()
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>