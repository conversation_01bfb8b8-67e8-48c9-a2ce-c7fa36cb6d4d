<template>
  <div class="basic-file">
    <form-item-title title="基础档案"/>
    <el-form-item>
      <el-input v-model="form.production" placeholder="请选择商品名称">
        <el-button plain slot="append" @click="openCloudProductDialog">选择云库产品</el-button>
      </el-input>
    </el-form-item>
    <cloud-product-dialog ref="cloudProductDialog" @getData="setData"/>
  </div>
</template>

<script>
import formItemTitle from '@/views/products/common-components/form-item-title'
import cloudProductDialog from './dialogs/cloud-product-dialog'
import {
    getAllStore
  } from "@/api/products/store";
export default {
  components: { formItemTitle, cloudProductDialog },
  data () {
    return {
      form: {
        production: ''
      },
      platformProductId: '',
      warehouseId: ''
    }
  },
  mounted() {
    this.getStoreList()
  },
  methods: {
    getStoreList() {
        getAllStore().then(res => {
          const data = res.data;
          if(data && data.length) {
            data.forEach(item => {
              if(item.defaultWarehouse.code === 'Y') {
                this.warehouseId = item.id
              }
            })
          }
        })
      },
    openCloudProductDialog () {
      this.$refs.cloudProductDialog.visible = true
    },
    setData (data) {
      console.log('------categoryId---->',data,data.categoryId);
      this.platformProductId = data.id;
      data.productCode = ''
      delete data.id;
      this.$parent.$parent.restore({...data,warehouseId: this.warehouseId, brand: { name: data.brandName, id: data.brandId }})
      this.form.production = data.productName;
      // this.form.platformProductId=data.categoryId;
    },
    getData () {
      if (this.platformProductId) {
        return {platformProductId: this.platformProductId}
      }
      return {}
    },
    setForm (data) {
      if (!data) {
        return;
      }
      this.form.production = data.production
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-file{
  display: flex;
  align-items: center;
  .el-form-item{
    ::v-deep{
      .el-button{
        &:hover{
          border-color: transparent;
        }
      }
    }
  }
}
</style>
